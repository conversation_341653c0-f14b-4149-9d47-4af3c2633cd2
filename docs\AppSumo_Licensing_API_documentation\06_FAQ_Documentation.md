# AppSumo Licensing API (v2) - FAQ Documentation

**Source URLs:**
- https://docs.licensing.appsumo.com/faq/faq__oauth.html
- https://docs.licensing.appsumo.com/faq/faq__other.html

---

## OAuth FAQ

### 403 Forbidden Error

**What's Happening**:

You're seeing a **403 Forbidden error** when trying to activate your license.

![403 Forbidden OAuth Error](https://docs.licensing.appsumo.com/assets/img/403_forbidden.462945d1.png)

**Troubleshooting Tips**:

1. **Check Your Redirect URL's:**
   - Make sure the `redirect_uri` you include in your request is **exactly the same** as the OAuth Redirect URL you set up in your [AppSumo Partner Portal](https://www.appsumo.com/partners/products/). Both URLs must match perfectly for OAuth to be successful.

2. **OAuth Code Usage**
   - **One-Time Use:** The OAuth `code` can only be used **once** and will expire after it's been used, either successful or through error.
   - **New Code Issuance:** A **new** `code` will be generated each time you authorize through OAuth.
   - **Re-attempt Activation:** To get a **new** `code`, simply re-attempt the license activation process.

3. **Re-validate Your OAuth Keys**
   - **Double-Check Your Keys:** Ensure that your `client_id` and `client_secret` keys are correct. You can find both keys in your [AppSumo Partner Portal](https://www.appsumo.com/partners/products/).

For detailed information, see [OAuth Getting Started](https://docs.licensing.appsumo.com/licensing/licensing__getting_started.html) and [OAuth Connect to AppSumo](https://docs.licensing.appsumo.com/licensing/licensing__connect.html)

---

## Other FAQ

### How Can I Reset My OAuth and Webhook URLs?

**What's Happening**:

You may need to reset your OAuth and Webhook URLs for various reasons within the [AppSumo Partner Portal](https://www.appsumo.com/partners/products/).

![Oauth Redirect Config](https://docs.licensing.appsumo.com/assets/img/oauth_redirect_config.fe888aac.png)
![Webhook URL](https://docs.licensing.appsumo.com/assets/img/Webhook_url.74266dc8.png)

---

To update your OAuth and Webhook URLs in the [AppSumo Partner Portal](https://www.appsumo.com/partners/products/), contact your **Launch Operations Associate** directly. They will invalidate your current URLs and reopen them for further testing.

**Important**:

1. When updating your URLs in the [AppSumo Partner Portal](https://www.appsumo.com/partners/products/), it's **essential** to also update them on your back end to ensure they **match exactly**

2. It's **crucial** that once your product is live on AppSumo, you do not change these URLs, as this will break the connection between AppSumo and your system.

For detailed information, see [OAuth Getting Started](https://docs.licensing.appsumo.com/licensing/licensing__getting_started.html).

---

## Common Issues and Solutions

### OAuth Issues

#### Issue: 403 Forbidden Error
**Solution:**
- Verify redirect_uri matches exactly with Partner Portal
- Check OAuth keys (client_id and client_secret)
- Ensure OAuth code is fresh (single-use only)

#### Issue: OAuth Code Expired
**Solution:**
- Re-attempt the license activation process
- New code will be generated automatically
- Don't reuse old codes

#### Issue: Invalid Client Credentials
**Solution:**
- Double-check client_id and client_secret in Partner Portal
- Ensure keys are copied correctly (no extra spaces)
- Verify API key is active

### Webhook Issues

#### Issue: Webhook URL Not Validating
**Solution:**
- Ensure endpoint returns 200 OK status
- Return proper JSON response with success: true
- Handle test webhooks properly (test: true)

#### Issue: Missing Webhook Events
**Solution:**
- Check webhook URL is accessible from AppSumo
- Verify server accepts requests from appsumo.com
- Check webhook response format

#### Issue: Webhook Security Validation Failing
**Solution:**
- Implement HMAC SHA256 verification
- Use correct API key for signature validation
- Verify timestamp and body combination

### License Management Issues

#### Issue: License Status Mismatch
**Solution:**
- Use API endpoints to verify license status
- Implement proper status synchronization
- Handle upgrade/downgrade events correctly

#### Issue: User Account Linking Problems
**Solution:**
- Store license_key as primary identifier
- Handle new vs existing user scenarios
- Implement proper account creation flow

### Integration Issues

#### Issue: Rate Limiting
**Solution:**
- Respect 20 requests per minute limit
- Implement proper retry logic
- Cache license data when appropriate

#### Issue: URL Configuration Problems
**Solution:**
- Contact Launch Operations Associate for URL resets
- Ensure backend URLs match Partner Portal exactly
- Don't change URLs after going live

---

## Best Practices

### OAuth Implementation
- Always validate redirect_uri matches exactly
- Handle OAuth code expiration gracefully
- Implement proper error handling for 403 errors
- Store and refresh access tokens properly

### Webhook Implementation
- Always return 200 OK for successful processing
- Implement HMAC SHA256 verification for security
- Handle test webhooks without processing
- Log webhook events for debugging

### License Management
- Use license_key as primary identifier
- Sync license status regularly via API
- Handle all event types (purchase, activate, upgrade, downgrade, deactivate)
- Implement proper tier management

### Security
- Validate all webhook requests using HMAC SHA256
- Store API keys securely
- Use HTTPS for all endpoints
- Implement proper error handling without exposing sensitive data

### Testing
- Test with AppSumo developer credits
- Validate all webhook event types
- Test OAuth flow end-to-end
- Monitor License History UI for issues
