-- Allow public (anonymous) access to the 'images' bucket
CREATE POLICY "Allow public read access to images bucket"
ON storage.objects
FOR SELECT
USING (bucket_id = 'images');

-- Allow authenticated users to upload to the 'images' bucket
CREATE POLICY "Allow authenticated users to upload to images bucket"
ON storage.objects
FOR INSERT
WITH CHECK (bucket_id = 'images' AND auth.role() = 'authenticated');

-- Allow anonymous users to upload to the 'images' bucket
CREATE POLICY "Allow anonymous users to upload to images bucket"
ON storage.objects
FOR INSERT
WITH CHECK (bucket_id = 'images');

-- Allow users to update their own objects
CREATE POLICY "Allow users to update their own objects in images bucket"
ON storage.objects
FOR UPDATE
USING (bucket_id = 'images' AND (auth.uid() = owner OR auth.role() = 'anon'));

-- Allow users to delete their own objects
CREATE POLICY "Allow users to delete their own objects in images bucket"
ON storage.objects
FOR DELETE
USING (bucket_id = 'images' AND (auth.uid() = owner OR auth.role() = 'anon'));
