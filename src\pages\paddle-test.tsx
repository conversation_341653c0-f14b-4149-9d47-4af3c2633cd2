import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useStore } from '@/lib/store';
import {
  setupPaddle,
  initializePaddleCheckout,
  getPaddleProducts,
  getCurrentEnvironment,
  getEnvironmentInfo,
  validateEnvironmentSetup,
  switchEnvironment,
  type PaddleEnvironment
} from '@/lib/paddle';
import { toast } from 'react-hot-toast';

export function PaddleTestPage() {
  const { user } = useStore();
  const [isSetup, setIsSetup] = useState(false);
  const [setupError, setSetupError] = useState<string | null>(null);
  const [paddleReady, setPaddleReady] = useState(false);
  const [currentEnvironment, setCurrentEnvironment] = useState<PaddleEnvironment>(getCurrentEnvironment());
  const [environmentInfo, setEnvironmentInfo] = useState(getEnvironmentInfo());
  const [validationResult, setValidationResult] = useState(validateEnvironmentSetup());

  useEffect(() => {
    // Update environment info when environment changes
    setEnvironmentInfo(getEnvironmentInfo(currentEnvironment));
    setValidationResult(validateEnvironmentSetup(currentEnvironment));
  }, [currentEnvironment]);

  useEffect(() => {
    // Check if Paddle script is loaded
    const checkPaddleScript = () => {
      if (typeof window !== 'undefined' && window.Paddle) {
        setPaddleReady(true);
        console.log('Paddle script detected');

        // Setup Paddle when script is ready
        try {
          setupPaddle(currentEnvironment);
          setIsSetup(true);
          console.log(`Paddle setup completed successfully for ${currentEnvironment} environment`);
        } catch (error) {
          console.error('Paddle setup error:', error);
          setSetupError(error instanceof Error ? error.message : 'Unknown setup error');
        }
      } else {
        console.log('Paddle script not yet available, retrying...');
        setTimeout(checkPaddleScript, 500);
      }
    };

    // Reset setup state when environment changes
    setIsSetup(false);
    setSetupError(null);

    // Start checking after a brief delay
    const timer = setTimeout(checkPaddleScript, 1000);
    return () => clearTimeout(timer);
  }, [currentEnvironment]);

  const testCheckout = (plan: string, priceId: string) => {
    if (!user) {
      toast.error('Please sign in to test checkout');
      return;
    }

    if (!isSetup) {
      toast.error('Paddle is still loading, please wait...');
      return;
    }

    try {
      initializePaddleCheckout(
        priceId,
        user.email,
        {
          userId: user.id,
          plan: plan,
          billingPeriod: plan.includes('Monthly') ? 'monthly' : 'yearly',
          testMode: currentEnvironment === 'sandbox',
          environment: currentEnvironment
        },
        undefined,
        currentEnvironment
      );
      toast.success(`Opening ${plan} checkout in ${currentEnvironment} environment...`);
    } catch (error) {
      console.error('Error opening checkout:', error);
      toast.error('Failed to open checkout');
    }
  };

  const testCheckoutWithDiscount = (plan: string, priceId: string, discountCode: string) => {
    if (!user) {
      toast.error('Please sign in to test checkout');
      return;
    }

    if (!isSetup) {
      toast.error('Paddle is still loading, please wait...');
      return;
    }

    try {
      initializePaddleCheckout(
        priceId,
        user.email,
        {
          userId: user.id,
          plan: plan,
          billingPeriod: plan.includes('Monthly') ? 'monthly' : 'yearly',
          testMode: currentEnvironment === 'sandbox',
          discountCode: discountCode,
          environment: currentEnvironment
        },
        discountCode,
        currentEnvironment
      );
      toast.success(`Opening ${plan} checkout with ${discountCode} discount in ${currentEnvironment} environment...`);
    } catch (error) {
      console.error('Error opening checkout:', error);
      toast.error('Failed to open checkout');
    }
  };

  const handleEnvironmentSwitch = (newEnvironment: PaddleEnvironment) => {
    console.log(`🔄 Switching from ${currentEnvironment} to ${newEnvironment}`);

    const success = switchEnvironment(newEnvironment);
    if (success) {
      setCurrentEnvironment(newEnvironment);
      toast.success(`Switched to ${newEnvironment} environment`);
    } else {
      toast.error(`Failed to switch to ${newEnvironment} environment`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-4">Paddle Integration Test</h1>
          <p className="text-gray-300 mb-4">
            Test the complete Paddle checkout integration with environment switching support
          </p>

          {/* Environment Switcher */}
          <div className="mb-6">
            <div className="flex justify-center gap-4">
              <Button
                onClick={() => handleEnvironmentSwitch('production')}
                className={`px-6 py-2 ${currentEnvironment === 'production'
                  ? 'bg-green-600 hover:bg-green-700'
                  : 'bg-gray-600 hover:bg-gray-700'}`}
              >
                Production
              </Button>
              <Button
                onClick={() => handleEnvironmentSwitch('sandbox')}
                className={`px-6 py-2 ${currentEnvironment === 'sandbox'
                  ? 'bg-blue-600 hover:bg-blue-700'
                  : 'bg-gray-600 hover:bg-gray-700'}`}
              >
                Sandbox
              </Button>
            </div>
          </div>

          <div className="bg-gray-800 p-4 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-400">
                  Environment: <span className={`font-mono ${currentEnvironment === 'production' ? 'text-green-400' : 'text-blue-400'}`}>
                    {currentEnvironment.toUpperCase()}
                  </span>
                </p>
                <p className="text-gray-400">
                  API Endpoint: <span className="text-blue-400 font-mono text-xs">{environmentInfo.apiEndpoint}</span>
                </p>
                <p className="text-gray-400">
                  Token Type: <span className={`font-mono ${environmentInfo.tokenType === 'production' ? 'text-green-400' : 'text-blue-400'}`}>
                    {environmentInfo.tokenType}
                  </span>
                </p>
              </div>
              <div>
                <p className="text-gray-400">
                  User: <span className="text-yellow-400 font-mono">{user?.email || 'Not signed in'}</span>
                </p>
                <p className="text-gray-400">
                  Paddle Script: <span className={`font-mono ${paddleReady ? 'text-green-400' : 'text-yellow-400'}`}>
                    {paddleReady ? 'Loaded' : 'Loading...'}
                  </span>
                </p>
                <p className="text-gray-400">
                  Paddle Status: <span className={`font-mono ${isSetup ? 'text-green-400' : setupError ? 'text-red-400' : 'text-yellow-400'}`}>
                    {setupError ? `Error: ${setupError}` : isSetup ? 'Ready' : 'Initializing...'}
                  </span>
                </p>
              </div>
            </div>

            {/* Validation Status */}
            {!validationResult.isValid && (
              <div className="mt-4 p-3 bg-red-900/50 border border-red-500 rounded-lg">
                <h4 className="text-red-400 font-semibold mb-2">⚠️ Configuration Issues:</h4>
                <ul className="text-red-300 text-sm space-y-1">
                  {validationResult.errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>

        {!user && (
          <div className="bg-red-900/50 border border-red-500 rounded-lg p-4 mb-8">
            <h3 className="text-red-400 font-semibold mb-2">Authentication Required</h3>
            <p className="text-red-300">Please sign in to test the Paddle checkout integration.</p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Pay-As-You-Go */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-white mb-2">Pay-As-You-Go</h3>
            <p className="text-gray-300 mb-4">{currentEnvironment === 'sandbox' ? '$2.00' : '$2.00'} for 50 credits</p>
            <div className="space-y-2 text-sm text-gray-400 mb-4">
              <p>Product ID: <span className="font-mono text-xs">{getPaddleProducts(currentEnvironment).payAsYouGo.productId}</span></p>
              <p>Price ID: <span className="font-mono text-xs">{getPaddleProducts(currentEnvironment).payAsYouGo.priceId}</span></p>
            </div>
            <Button
              onClick={() => testCheckout('Pay-As-You-Go', getPaddleProducts(currentEnvironment).payAsYouGo.priceId)}
              className="w-full bg-blue-600 hover:bg-blue-700"
              disabled={!user || !isSetup || !validationResult.isValid}
            >
              Test Pay-As-You-Go Checkout
            </Button>
          </div>

          {/* Pro Monthly */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-white mb-2">Pro Monthly</h3>
            <p className="text-gray-300 mb-4">{currentEnvironment === 'sandbox' ? '$10.00' : '$10.00'}/month</p>
            <div className="space-y-2 text-sm text-gray-400 mb-4">
              <p>Product ID: <span className="font-mono text-xs">{getPaddleProducts(currentEnvironment).pro.productId}</span></p>
              <p>Price ID: <span className="font-mono text-xs">{getPaddleProducts(currentEnvironment).pro.monthlyPriceId}</span></p>
            </div>
            <Button
              onClick={() => testCheckout('Pro Monthly', getPaddleProducts(currentEnvironment).pro.monthlyPriceId)}
              className="w-full bg-purple-600 hover:bg-purple-700"
              disabled={!user || !isSetup || !validationResult.isValid}
            >
              Test Pro Monthly Checkout
            </Button>
          </div>

          {/* Pro Yearly */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-white mb-2">Pro Yearly</h3>
            <p className="text-gray-300 mb-4">{currentEnvironment === 'sandbox' ? '$96.00' : '$96.00'}/year</p>
            <div className="space-y-2 text-sm text-gray-400 mb-4">
              <p>Product ID: <span className="font-mono text-xs">{getPaddleProducts(currentEnvironment).pro.productId}</span></p>
              <p>Price ID: <span className="font-mono text-xs">{getPaddleProducts(currentEnvironment).pro.yearlyPriceId}</span></p>
            </div>
            <Button
              onClick={() => testCheckout('Pro Yearly', getPaddleProducts(currentEnvironment).pro.yearlyPriceId)}
              className="w-full bg-purple-600 hover:bg-purple-700"
              disabled={!user || !isSetup || !validationResult.isValid}
            >
              Test Pro Yearly Checkout
            </Button>
          </div>

          {/* Enterprise Monthly */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-white mb-2">Enterprise Monthly</h3>
            <p className="text-gray-300 mb-4">{currentEnvironment === 'sandbox' ? '$100.00' : '$100.00'}/month</p>
            <div className="space-y-2 text-sm text-gray-400 mb-4">
              <p>Product ID: <span className="font-mono text-xs">{getPaddleProducts(currentEnvironment).enterprise.productId}</span></p>
              <p>Price ID: <span className="font-mono text-xs">{getPaddleProducts(currentEnvironment).enterprise.monthlyPriceId}</span></p>
            </div>
            <Button
              onClick={() => testCheckout('Enterprise Monthly', getPaddleProducts(currentEnvironment).enterprise.monthlyPriceId)}
              className="w-full bg-green-600 hover:bg-green-700"
              disabled={!user || !isSetup || !validationResult.isValid}
            >
              Test Enterprise Monthly Checkout
            </Button>
          </div>

          {/* Enterprise Yearly */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-white mb-2">Enterprise Yearly</h3>
            <p className="text-gray-300 mb-4">{currentEnvironment === 'sandbox' ? '$960.00' : '$960.00'}/year</p>
            <div className="space-y-2 text-sm text-gray-400 mb-4">
              <p>Product ID: <span className="font-mono text-xs">{getPaddleProducts(currentEnvironment).enterprise.productId}</span></p>
              <p>Price ID: <span className="font-mono text-xs">{getPaddleProducts(currentEnvironment).enterprise.yearlyPriceId}</span></p>
            </div>
            <Button
              onClick={() => testCheckout('Enterprise Yearly', getPaddleProducts(currentEnvironment).enterprise.yearlyPriceId)}
              className="w-full bg-green-600 hover:bg-green-700"
              disabled={!user || !isSetup || !validationResult.isValid}
            >
              Test Enterprise Yearly Checkout
            </Button>
          </div>        </div>

        {/* Discount Testing Section */}
        <div className="mt-8 bg-gradient-to-r from-purple-900/20 to-blue-900/20 border border-purple-500/30 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">🎫 Discount Code Testing</h3>
          <p className="text-gray-300 mb-4">
            Test discount functionality with Paddle integration. Available discount codes:
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-gray-800/50 border border-gray-600 rounded-lg p-4">
              <h4 className="text-green-400 font-semibold mb-2">KKK</h4>
              <p className="text-gray-300 text-sm">20% discount</p>
            </div>
            <div className="bg-gray-800/50 border border-gray-600 rounded-lg p-4">
              <h4 className="text-blue-400 font-semibold mb-2">SAVE10</h4>
              <p className="text-gray-300 text-sm">10% discount</p>
            </div>
            <div className="bg-gray-800/50 border border-gray-600 rounded-lg p-4">
              <h4 className="text-yellow-400 font-semibold mb-2">WELCOME</h4>
              <p className="text-gray-300 text-sm">15% discount</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              onClick={() => testCheckoutWithDiscount('Pro Monthly', getPaddleProducts(currentEnvironment).pro.monthlyPriceId, 'KKK')}
              className="w-full bg-purple-600 hover:bg-purple-700 flex items-center justify-center gap-2"
              disabled={!user || !isSetup || !validationResult.isValid}
            >
              <span>Test Pro Monthly with KKK (20% off)</span>
            </Button>
            <Button
              onClick={() => testCheckoutWithDiscount('Pro Yearly', getPaddleProducts(currentEnvironment).pro.yearlyPriceId, 'SAVE10')}
              className="w-full bg-blue-600 hover:bg-blue-700 flex items-center justify-center gap-2"
              disabled={!user || !isSetup || !validationResult.isValid}
            >
              <span>Test Pro Yearly with SAVE10 (10% off)</span>
            </Button>
          </div>

          <div className="mt-4 p-4 bg-green-900/20 border border-green-500/30 rounded-lg">
            <h4 className="text-green-400 font-semibold mb-2">✅ Expected Results:</h4>
            <ul className="text-gray-300 text-sm space-y-1">
              <li>• Pro Monthly ($10.00) with KKK → Should show $8.00 in Paddle checkout</li>
              <li>• Pro Yearly ($96.00) with SAVE10 → Should show $86.40 in Paddle checkout</li>
              <li>• Discount codes are now synced between Firebase and Paddle</li>
              <li>• Paddle checkout should display the discounted price correctly</li>
            </ul>
          </div>
        </div>

        <div className="mt-8 bg-gray-800 border border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Integration Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="text-green-400 font-semibold mb-2">✅ Completed</h4>
              <ul className="space-y-1 text-gray-300">
                <li>• Paddle products created</li>
                <li>• Paddle prices created</li>
                <li>• Frontend integration updated</li>
                <li>• Webhook handler deployed</li>
                <li>• Custom data with userId included</li>
                <li>• Error handling implemented</li>
              </ul>
            </div>
            <div>
              <h4 className="text-blue-400 font-semibold mb-2">🔄 Testing</h4>
              <ul className="space-y-1 text-gray-300">
                <li>• Webhook receives events</li>
                <li>• User subscription updates</li>
                <li>• Transaction logging</li>
                <li>• End-to-end payment flow</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="mt-6 text-center">
          <p className="text-gray-400 text-sm">
            Current environment: <span className={`font-semibold ${currentEnvironment === 'production' ? 'text-green-400' : 'text-blue-400'}`}>
              {currentEnvironment.toUpperCase()}
            </span>
          </p>
          <p className={`text-sm mt-2 ${currentEnvironment === 'production' ? 'text-red-400' : 'text-blue-400'}`}>
            {currentEnvironment === 'production'
              ? '⚠️ Production environment - Real payments will be processed!'
              : '🧪 Sandbox environment - Safe for testing with test payment methods'}
          </p>
        </div>
      </div>
    </div>
  );
}
