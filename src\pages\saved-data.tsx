import { useState, useEffect } from 'react';
import { Trash2, Calendar, Globe, MessageSquare, BookOpen, AlertTriangle } from 'lucide-react'; // Removed ChevronDown, ChevronUp
import { Button } from '@/components/ui/button';
import { toast } from 'react-hot-toast';
import { db } from '@/lib/firebase';
import { collection, getDocs, deleteDoc, doc, query, where, orderBy } from 'firebase/firestore';
// Import Dialog components if not already imported (assuming they exist based on other files)
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog'; 
import { useStore } from '@/lib/store';
import { motion, AnimatePresence } from 'framer-motion';
import { decrementSavedPromptsCount } from '@/lib/userLimits';

interface SavedItem {
  id: string;
  type: 'caption' | 'ecommerce';
  content: string;
  image: string;
  images?: string[];
  prompt?: string;
  promptName?: string;
  savedTime?: string;
  language?: string;
  tone?: string;
  selectionMode?: string;
  userId: string;
  createdAt: any; // Firestore timestamp
  expanded?: boolean; // For accordion functionality
}



export function SavedData() {
  const [savedItems, setSavedItems] = useState<SavedItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const { user } = useStore();
  const [expandedId, setExpandedId] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false); // State for delete confirmation dialog
  const [itemToDeleteId, setItemToDeleteId] = useState<string | null>(null); // State for ID of item to delete

  useEffect(() => {
    if (user) {
      loadSavedCaptions();
    } else {
      setSavedItems([]);
      setLoading(false);
    }
  }, [user]);

  const loadSavedCaptions = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      const captionsQuery = query(
        collection(db, 'savedCaptions'), 
        where('userId', '==', user.id),
        orderBy('createdAt', 'desc')
      );
      
      const querySnapshot = await getDocs(captionsQuery);
      
      const items: SavedItem[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        items.push({
          id: doc.id,
          ...data,
          expanded: false,
          createdAt: data.createdAt?.toDate?.() || new Date()
        } as SavedItem);
      });
      
      setSavedItems(items);
    } catch (error: any) {
      console.error('Error loading saved captions:', error);
      
      // Check if the error is related to index building
      if (error.code === 'failed-precondition' && error.message.includes('index')) {
        toast.error('Database index is still building. Please try again in a few minutes.');
      } else {
        toast.error('Error loading saved data');
      }
    } finally {
      setLoading(false);
    }
  };

  // Renamed to confirmDelete to avoid conflict with button handler
  const confirmDelete = async () => { 
    if (!itemToDeleteId || !user) return;
    try {
      await deleteDoc(doc(db, 'savedCaptions', itemToDeleteId));
      setSavedItems((prev) => prev.filter((item) => item.id !== itemToDeleteId));
      
      // Decrement the user's saved prompts count
      await decrementSavedPromptsCount(user.id);
      
      toast.success('Item deleted successfully');
    } catch (error) {
      console.error('Error deleting item:', error);
      toast.error('Error deleting item');
    } finally {
      // Close dialog and reset state
      setShowDeleteConfirm(false); 
      setItemToDeleteId(null);
    }
  };

  // Handler to open the confirmation dialog
  const handleDeleteClick = (id: string) => {
    setItemToDeleteId(id);
    setShowDeleteConfirm(true);
  };

  const toggleAccordion = (id: string) => {
    setExpandedId(expandedId === id ? null : id);
  };

  return (
    <div className="space-y-6 p-4 md:p-6"> {/* Add padding */}
      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <DialogContent className="sm:max-w-md bg-gray-800 border-gray-700 text-gray-100">
          <DialogHeader>
            <DialogTitle className="text-gray-100">Confirm Deletion</DialogTitle>
            <DialogDescription className="text-gray-400">
              Are you sure you want to delete this item? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:justify-end">
            <Button 
              variant="outline" 
              onClick={() => setShowDeleteConfirm(false)}
              className="border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-gray-100"
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <h1 className="text-xl md:text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
        Saved Data
      </h1>

      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div> {/* Add spinner */}
          <p className="text-gray-300">Loading saved data...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4">
          {savedItems.map((item) => (
            <div
              key={item.id}
              className="bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-700 hover:shadow-lg transition-shadow duration-200 relative" // Added relative positioning
            >
              {/* Delete Button - Positioned Absolutely */}
              <Button
                size="sm"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent accordion toggle
                  handleDeleteClick(item.id); // Open confirmation dialog
                }}
                className="absolute top-2 right-2 z-10 bg-black/20 text-black hover:text-white hover:bg-black p-1.5 rounded-md transition-colors duration-200" 
                aria-label="Delete item"
              >
                <Trash2 className="w-4 h-4" />
              </Button>

              {/* Accordion Header */}
              <div 
                className="p-3 md:p-4 cursor-pointer bg-gradient-to-r from-gray-800 to-gray-700 hover:from-gray-700 hover:to-gray-600" 
                onClick={() => toggleAccordion(item.id)}
              >
                {/* Adjust padding-right to avoid overlap with delete button */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-4 pr-8 sm:pr-0"> 
                  <div className="flex items-center space-x-2 md:space-x-3 flex-grow min-w-0"> 
                    <div className="w-12 h-12 md:w-16 md:h-16 rounded-md overflow-hidden flex-shrink-0"> 
                      {item.image ? (
                        <img
                          src={item.image}
                          alt="Saved item preview" // Add alt text
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-700 flex items-center justify-center"> {/* Dark theme placeholder */}
                          <span className="text-gray-400 text-xs">No image</span>
                        </div>
                      )}
                    </div>
                    <div className="min-w-0 flex-grow"> {/* Allow text to wrap/truncate */}
                      <div className="flex flex-wrap items-center gap-1 md:gap-2 mb-1"> {/* Allow wrapping */}
                        <span className="inline-block px-2 py-0.5 text-xs md:text-sm font-medium rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white"> {/* Dark theme badge */}
                          {item.type === 'caption' ? 'Caption' : 'Product Data'}
                        </span>
                        {item.language && item.tone && (
                          <span className="hidden sm:inline-block px-2 py-0.5 text-xs font-medium rounded-full bg-gray-600 text-gray-200"> {/* Dark theme badge */}
                            {item.language} / {item.tone}
                          </span>
                        )}
                      </div>
                      {/* Stack metadata vertically on small screens */}
                      <div className="flex flex-col sm:flex-row sm:flex-wrap items-start sm:items-center mt-1 text-xs md:text-sm text-gray-400 gap-x-3 gap-y-1"> {/* Adjust gap/size */}
                        <div className="flex items-center">
                          <Calendar className="w-3 h-3 mr-1 flex-shrink-0" />
                          <span> {/* Wrap text */}
                            {item.createdAt instanceof Date ? item.createdAt.toLocaleDateString() : 'Unknown date'}
                            {item.savedTime && ` ${item.savedTime}`}
                          </span>
                        </div>
                        {item.language && (
                          <div className="flex items-center">
                            <Globe className="w-3 h-3 mr-1 flex-shrink-0" />
                            {item.language}
                          </div>
                        )}
                        {item.tone && (
                          <div className="flex items-center">
                            <MessageSquare className="w-3 h-3 mr-1 flex-shrink-0" />
                            {item.tone}
                          </div>
                        )}
                        {item.promptName && (
                          <div className="flex items-center truncate" title={item.promptName}> {/* Add truncate and title */}
                            <BookOpen className="w-3 h-3 mr-1 flex-shrink-0" />
                            <span className="truncate">{item.promptName}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  {/* Removed chevron icons */}
                </div>
              </div>
              
              {/* Click To Expand text at bottom center */}
              <div className="flex justify-center border-t border-gray-700 bg-gray-800/50">
                <div 
                  className="py-2 px-4 text-xs text-gray-400 hover:text-gray-200 cursor-pointer transition-colors duration-200 flex items-center"
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent event bubbling
                    toggleAccordion(item.id);
                  }}
                >
                  {expandedId === item.id ? "Click To Collapse" : "Click To Expand"}
                </div>
              </div>
              
              {/* Accordion Content */}
              <AnimatePresence initial={false}> {/* Set initial={false} for better exit animation */}
                {expandedId === item.id && (
                  <motion.div
                    key="content" // Add key for AnimatePresence
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1, transition: { duration: 0.3, ease: 'easeInOut' } }}
                    exit={{ height: 0, opacity: 0, transition: { duration: 0.2, ease: 'easeInOut' } }}
                    className="overflow-hidden border-t border-gray-700" 
                  >
                    <div className="p-3 md:p-4 bg-gray-800"> 
                      {/* Apply prose styles for dark theme */}
                      <div className="prose prose-sm prose-invert max-w-none text-gray-300 break-words"> {/* Added break-words */}
                        <div dangerouslySetInnerHTML={{ __html: item.content || '' }} /> {/* Added fallback for content */}
                      </div>
                      {item.images && item.images.length > 1 && (
                        <div className="mt-4">
                          <p className="text-xs md:text-sm font-medium text-gray-400 mb-2">Associated Images:</p> {/* Adjust text */}
                          <div className="flex flex-wrap gap-2">
                            {item.images.map((imgUrl, idx) => (
                              <img 
                                key={idx} 
                                src={imgUrl} 
                                alt={`Associated image ${idx + 1}`} // Add alt text
                                className="w-12 h-12 md:w-16 md:h-16 object-cover rounded-md border border-gray-600" // Dark theme border
                              />
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          ))}

          {savedItems.length === 0 && !loading && ( // Check loading state
            <div className="text-center py-12 bg-gray-800 rounded-lg shadow-sm border border-dashed border-gray-700 p-4 md:p-8"> {/* Dark theme, dashed border */}
              <p className="text-gray-400">No saved data yet</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
