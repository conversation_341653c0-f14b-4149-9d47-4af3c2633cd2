import { useState, useEffect } from 'react'; // Import useState and useEffect
import { Outlet, NavLink, Link, useNavigate, useLocation } from 'react-router-dom';
import { Upload, Image, Save, LogOut, Menu, X, User, LayoutDashboard, Settings, Users, CreditCard } from 'lucide-react'; // Import Menu, X, User, Settings, Users and LayoutDashboard icons
import { useStore } from '../lib/store';
import { Button } from '../components/ui/button';
import { getUserPackage, isTeamMember, hasEnterpriseAccess } from '../lib/userLimits';
import { isEnterpriseAdminOwner } from '../lib/userRoles';
import { toast } from 'react-hot-toast';
import { UpgradePlanModal } from './ui/upgrade-plan-modal';

export function Layout() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false); // State for sidebar visibility
  const [isEnterpriseUser, setIsEnterpriseUser] = useState(false);
  const [isEnterpriseAdminOwnerUser, setIsEnterpriseAdminOwnerUser] = useState(false);
  const [isUpgradeModalOpen, setIsUpgradeModalOpen] = useState(false);
  const [userPackage, setUserPackage] = useState('Free');
  const [isCheckingPermissions, setIsCheckingPermissions] = useState(true); // Track permission checking state
  const signOut = useStore((state) => state.signOut);
  const navigate = useNavigate();
  const location = useLocation();
  const user = useStore((state) => state.user);

  useEffect(() => {
    const checkUserPackage = async () => {
      if (user) {
        setIsCheckingPermissions(true);
        try {
          // Check if user has Enterprise package or Enterprise-based custom package
          const packageName = await getUserPackage(user.id);
          setUserPackage(packageName);

          // Check if user has Enterprise-level access (regular Enterprise or Enterprise-based custom package)
          const hasEnterpriseLevel = await hasEnterpriseAccess(user.id);
          setIsEnterpriseUser(hasEnterpriseLevel);
          console.log('🔍 Layout: Enterprise access check:', {
            packageName,
            hasEnterpriseLevel,
            userId: user.id
          });

          // Check if user is an Enterprise Admin Owner (for both regular Enterprise and Enterprise-based custom packages)
          if (hasEnterpriseLevel) {
            const isAdminOwner = await isEnterpriseAdminOwner(user.id);
            setIsEnterpriseAdminOwnerUser(isAdminOwner);
            console.log('🔍 Layout: Enterprise Admin Owner check:', {
              isAdminOwner,
              hasEnterpriseLevel
            });
          } else {
            setIsEnterpriseAdminOwnerUser(false);
          }
        } catch (error) {
          console.error('Error checking user package:', error);
          setIsEnterpriseUser(false);
          setIsEnterpriseAdminOwnerUser(false);
        } finally {
          setIsCheckingPermissions(false);
        }
      } else {
        // Reset states when no user
        setIsCheckingPermissions(false);
        setIsEnterpriseUser(false);
        setIsEnterpriseAdminOwnerUser(false);
        setUserPackage('Free');
      }
    };

    checkUserPackage();
  }, [user]);

  // Check for openUpgrade parameter in URL
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    if (searchParams.get('openUpgrade') === 'true') {
      setIsUpgradeModalOpen(true);
      // Clean up the URL parameter after opening the modal
      searchParams.delete('openUpgrade');
      const newUrl = `${location.pathname}${searchParams.toString() ? '?' + searchParams.toString() : ''}`;
      navigate(newUrl, { replace: true });
    }
  }, [location.search, navigate]);

  const handleSignOut = () => {
    setIsSidebarOpen(false); // Close sidebar on sign out
    signOut();
    navigate('/');
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="flex min-h-screen bg-gray-900 text-gray-100 overflow-hidden">
      {/* Sidebar */}
      {/* Added fixed positioning, z-index, transition, and conditional classes for mobile */}
      <nav
        className={`fixed inset-y-0 left-0 z-30 w-64 bg-gray-800 border-r border-gray-700 p-4 space-y-4 transform transition-transform duration-300 ease-in-out md:relative md:translate-x-0 md:flex md:flex-col ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex justify-between items-center md:justify-center"> {/* Add container for logo and close button */}
          <Link to="/app" className="block" onClick={() => setIsSidebarOpen(false)}> {/* Close on logo click */}
            <div className="text-xl md:text-2xl font-bold bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent p-2 md:p-4 text-center">
              eComEasyAI
            </div>
          </Link>
          {/* Close button for mobile */}
          <button onClick={toggleSidebar} className="md:hidden p-2 text-gray-400 hover:text-white">
            <X size={24} />
          </button>
        </div>
        {/* Navigation Links - Added onClick to close sidebar on mobile */}
        <NavLink
          to="/app/dashboard"
          onClick={() => setIsSidebarOpen(false)}
          className={({ isActive }) =>
            `flex items-center space-x-2 p-2 rounded-lg ${
              isActive
                ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white'
                : 'text-gray-300 hover:bg-gray-700'
            }`
          }
        >
          <LayoutDashboard size={20} />
          <span>Dashboard</span>
        </NavLink>
        <NavLink
          to="/app"
          end
          onClick={() => setIsSidebarOpen(false)}
          className={({ isActive }) =>
            `flex items-center space-x-2 p-2 rounded-lg ${
              isActive
                ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white'
                : 'text-gray-300 hover:bg-gray-700'
            }`
          }
        >
          <Upload size={20} />
          <span>Upload Product Image</span> {/* Slightly shorter text */}
        </NavLink>
        <NavLink
          to="/app/caption"
          onClick={() => setIsSidebarOpen(false)}
          className={({ isActive }) =>
            `flex items-center space-x-2 p-2 rounded-lg ${
              isActive
                ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white'
                : 'text-gray-300 hover:bg-gray-700'
            }`
          }
        >
          <Image size={20} />
          <span>Generate Description</span>
        </NavLink>
        <NavLink
          to="/app/saved"
          onClick={() => setIsSidebarOpen(false)}
          className={({ isActive }) =>
            `flex items-center space-x-2 p-2 rounded-lg ${
              isActive
                ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white'
                : 'text-gray-300 hover:bg-gray-700'
            }`
          }
        >
          <Save size={20} />
          <span>Saved Data</span>
        </NavLink>
        {/* Profile and Sign Out Buttons - Use mt-auto to push to bottom in flex container */}
        <div className="mt-auto space-y-2">
          {/* Team Management Button - Only show after permissions are checked and user is authorized */}
          {!isCheckingPermissions && isEnterpriseAdminOwnerUser && (
            <Button
              variant="outline"
              size="sm"
              className="w-full flex items-center justify-center space-x-2 border-gray-600 text-gray-300 hover:bg-gray-700" // Full width
              onClick={() => {
                try {
                  // Close sidebar first
                  setIsSidebarOpen(false);
                  // Add a small delay before navigation to ensure UI state is updated
                  setTimeout(() => {
                    navigate('/app/team');
                  }, 50);
                } catch (error) {
                  console.error('Error navigating to team management:', error);
                  toast.error('Error accessing Team Management. Please try again.');
                }
              }}
            >
              <Users size={16} />
              <span>Team Management</span>
            </Button>
          )}
          {/* Upgrade Now Button */}
          <Button
            variant="outline"
            size="sm"
            className="w-full flex items-center justify-center space-x-2 border-purple-600 bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white hover:bg-gradient-to-r hover:from-purple-500/30 hover:to-pink-500/30" // Highlighted button
            onClick={() => {
              setIsSidebarOpen(false);
              setIsUpgradeModalOpen(true);
            }}
          >
            <CreditCard size={16} />
            <span>Upgrade Now</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="w-full flex items-center justify-center space-x-2 border-gray-600 text-gray-300 hover:bg-gray-700" // Full width
            onClick={() => {
              setIsSidebarOpen(false);
              navigate('/app/profile');
            }}
          >
            <Settings size={16} />
            <span>Profile and Settings</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="w-full flex items-center justify-center space-x-2 border-gray-600 text-gray-300 hover:bg-gray-700" // Full width
            onClick={handleSignOut}
          >
            <LogOut size={16} />
            <span>Sign Out</span>
          </Button>
        </div>
      </nav>

      {/* Overlay for mobile */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/60 z-20 md:hidden"
          onClick={toggleSidebar}
        ></div>
      )}

      {/* Main Content Area */}
      {/* Removed md:ml-64 as flex-1 handles width */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Header for mobile toggle */}
        <header className="sticky top-0 z-10 flex items-center justify-between p-4 bg-gray-800 border-b border-gray-700 md:hidden">
           <Link to="/app" className="block">
             <div className="text-lg font-bold bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent">
               eComEasyAI
             </div>
           </Link>
          <button onClick={toggleSidebar} className="p-1 text-gray-400 hover:text-white">
            <Menu size={24} />
          </button>
        </header>
        {/* Added overflow-y-auto for scrolling */}
        <main className="flex-1 p-4 md:p-8 overflow-y-auto max-h-[calc(100vh-4rem)] md:max-h-screen">
          <Outlet />
        </main>
      </div>

      {/* Upgrade Plan Modal */}
      <UpgradePlanModal
        isOpen={isUpgradeModalOpen}
        onClose={() => setIsUpgradeModalOpen(false)}
        currentPlan={userPackage}
      />
    </div>
  );
}
