<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle Environment Debug</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #ffffff;
            line-height: 1.6;
        }
        .container {
            background: #2a2a2a;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #8b5cf6;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-box {
            background: #374151;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #8b5cf6;
        }
        .success { border-left-color: #10b981; }
        .warning { border-left-color: #f59e0b; }
        .error { border-left-color: #ef4444; }
        button {
            background: #8b5cf6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #7c3aed;
        }
        .danger {
            background: #ef4444;
        }
        .danger:hover {
            background: #dc2626;
        }
        code {
            background: #1f2937;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        .log {
            background: #1f2937;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Paddle Environment Debug Tool</h1>
        
        <div id="status-container">
            <!-- Status will be populated by JavaScript -->
        </div>
        
        <div class="status-box">
            <h3>🛠️ Quick Actions</h3>
            <button onclick="clearEnvironmentCache()">Clear Environment Cache</button>
            <button onclick="checkEnvironment()">Refresh Status</button>
            <button onclick="testPaddleSetup()">Test Paddle Setup</button>
        </div>
        
        <div class="status-box">
            <h3>🔄 Environment Switching</h3>
            <button onclick="switchToProduction()">Switch to Production</button>
            <button onclick="switchToSandbox()">Switch to Sandbox</button>
            <button class="danger" onclick="forceProduction()">Force Production (Clear Cache)</button>
        </div>
        
        <div class="status-box">
            <h3>📋 Debug Log</h3>
            <div id="debug-log" class="log"></div>
            <button onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <script>
        let debugLog = [];
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            debugLog.push(logEntry);
            updateLogDisplay();
            console.log(logEntry);
        }
        
        function updateLogDisplay() {
            const logElement = document.getElementById('debug-log');
            logElement.innerHTML = debugLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            debugLog = [];
            updateLogDisplay();
        }
        
        function checkEnvironment() {
            log('🔍 Checking environment status...');
            
            const statusContainer = document.getElementById('status-container');
            
            // Check localStorage
            const storedEnv = localStorage.getItem('paddle_environment');
            log(`localStorage paddle_environment: ${storedEnv || 'not set'}`);
            
            // Check if we can access environment variables (this won't work in static HTML)
            log('Note: Environment variables not accessible in static HTML');
            
            // Check Paddle availability
            const paddleAvailable = typeof window.Paddle !== 'undefined';
            log(`Paddle SDK available: ${paddleAvailable}`);
            
            // Update status display
            let statusHTML = '';
            
            // Environment status
            if (storedEnv) {
                const isProduction = storedEnv === 'production';
                statusHTML += `
                    <div class="status-box ${isProduction ? 'success' : 'warning'}">
                        <h3>🌍 Current Environment</h3>
                        <p><strong>Environment:</strong> ${storedEnv.toUpperCase()}</p>
                        <p><strong>Mode:</strong> ${isProduction ? '🚀 PRODUCTION (Live payments)' : '🧪 SANDBOX (Test payments)'}</p>
                        ${isProduction ? '<p style="color: #ef4444;">⚠️ Real credit cards will be charged!</p>' : '<p style="color: #10b981;">✅ Safe for testing</p>'}
                    </div>
                `;
            } else {
                statusHTML += `
                    <div class="status-box warning">
                        <h3>🌍 Environment Status</h3>
                        <p>No environment cached in localStorage</p>
                        <p>Will use default from environment variables</p>
                    </div>
                `;
            }
            
            // Paddle status
            statusHTML += `
                <div class="status-box ${paddleAvailable ? 'success' : 'error'}">
                    <h3>🎯 Paddle SDK Status</h3>
                    <p><strong>Available:</strong> ${paddleAvailable ? '✅ YES' : '❌ NO'}</p>
                    ${paddleAvailable ? '<p>Paddle SDK is loaded and ready</p>' : '<p>Paddle SDK not loaded. Make sure you\'re on the main app.</p>'}
                </div>
            `;
            
            statusContainer.innerHTML = statusHTML;
            log('✅ Environment check complete');
        }
        
        function clearEnvironmentCache() {
            log('🧹 Clearing environment cache...');
            localStorage.removeItem('paddle_environment');
            log('✅ Environment cache cleared');
            checkEnvironment();
        }
        
        function switchToProduction() {
            log('🚀 Switching to production environment...');
            localStorage.setItem('paddle_environment', 'production');
            log('✅ Switched to production');
            checkEnvironment();
        }
        
        function switchToSandbox() {
            log('🧪 Switching to sandbox environment...');
            localStorage.setItem('paddle_environment', 'sandbox');
            log('✅ Switched to sandbox');
            checkEnvironment();
        }
        
        function forceProduction() {
            log('🔥 Force switching to production (clearing cache first)...');
            localStorage.removeItem('paddle_environment');
            localStorage.setItem('paddle_environment', 'production');
            log('✅ Forced production mode');
            checkEnvironment();
        }
        
        function testPaddleSetup() {
            log('🧪 Testing Paddle setup...');
            
            if (typeof window.Paddle === 'undefined') {
                log('❌ Paddle SDK not available');
                return;
            }
            
            try {
                // Check if Paddle has environment methods
                if (window.Paddle.Environment) {
                    log('✅ Paddle.Environment available');
                } else {
                    log('⚠️ Paddle.Environment not available');
                }
                
                // Check if Paddle has checkout methods
                if (window.Paddle.Checkout) {
                    log('✅ Paddle.Checkout available');
                } else {
                    log('⚠️ Paddle.Checkout not available');
                }
                
                log('✅ Paddle setup test complete');
            } catch (error) {
                log(`❌ Paddle setup test failed: ${error.message}`);
            }
        }
        
        // Initialize on page load
        window.addEventListener('load', function() {
            log('🔍 Paddle Environment Debug Tool initialized');
            checkEnvironment();
        });
    </script>
</body>
</html>
