// Test script to create a sample payment log entry
const admin = require('firebase-admin');

// Initialize Firebase Admin (using the same config as Cloud Functions)
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

async function createTestPaymentLog() {
  try {
    console.log('Creating test payment log entry...');
    
    const testPaymentLog = {
      transactionId: 'TEST_' + Date.now(),
      userId: 'SO43DGRA3ebNRv5AsKPJ8uqUoeB3', // The user ID from the error
      status: 'SUCCESS',
      packageName: 'Pro',
      amount: 500,
      currency: 'BDT',
      paymentMethod: 'SSLCOMMERZ',
      billingPeriod: 'monthly',
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      userEmail: '<EMAIL>',
      userName: 'Test User',
      validationResponse: {
        status: 'VALID',
        tran_date: new Date().toISOString()
      }
    };

    const docRef = await db.collection('paymentLogs').add(testPaymentLog);
    console.log('✅ Test payment log created with ID:', docRef.id);
    
    // Create another test entry with different status
    const testFailedLog = {
      transactionId: 'TEST_FAILED_' + Date.now(),
      userId: 'SO43DGRA3ebNRv5AsKPJ8uqUoeB3',
      status: 'FAILED',
      packageName: 'Enterprise',
      amount: 1000,
      currency: 'BDT',
      paymentMethod: 'SSLCOMMERZ',
      billingPeriod: 'yearly',
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      userEmail: '<EMAIL>',
      userName: 'Test User',
      failureDetails: {
        error: 'Card declined',
        reason: 'Insufficient funds'
      }
    };

    const docRef2 = await db.collection('paymentLogs').add(testFailedLog);
    console.log('✅ Test failed payment log created with ID:', docRef2.id);
    
    console.log('🎉 Test payment logs created successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error creating test payment log:', error);
    process.exit(1);
  }
}

createTestPaymentLog();
