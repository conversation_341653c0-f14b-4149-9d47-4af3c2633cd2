import React from 'react';

interface TeamManagementSkeletonProps {
  showTabs?: boolean;
}

export const TeamManagementSkeleton: React.FC<TeamManagementSkeletonProps> = ({ 
  showTabs = true 
}) => {
  return (
    <div className="max-w-4xl mx-auto animate-pulse">
      {/* Header Skeleton */}
      <div className="mb-8">
        <div className="h-9 bg-gray-700 rounded-md w-64 mb-2"></div>
      </div>

      {/* Tabs Skeleton */}
      {showTabs && (
        <div className="mb-6">
          <div className="flex space-x-1 bg-gray-800 rounded-lg p-1 w-full max-w-md">
            <div className="h-10 bg-gray-600 rounded-md flex-1"></div>
            <div className="h-10 bg-gray-700 rounded-md flex-1"></div>
          </div>
        </div>
      )}

      {/* Content Area Skeleton */}
      <div className="space-y-8">
        {/* Team Members Section */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          {/* Section Header */}
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center space-x-2">
              <div className="h-5 w-5 bg-gray-600 rounded"></div>
              <div className="h-6 bg-gray-600 rounded-md w-48"></div>
            </div>
            <div className="h-8 bg-gray-600 rounded-md w-20"></div>
          </div>

          {/* Member Cards */}
          <div className="space-y-3">
            {[1, 2, 3].map((index) => (
              <div key={index} className="bg-gray-700 rounded-lg p-4 border border-gray-600">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 bg-gray-600 rounded-full"></div>
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-600 rounded-md w-32"></div>
                      <div className="h-3 bg-gray-600 rounded-md w-24"></div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="h-6 bg-gray-600 rounded-full w-16"></div>
                    <div className="h-8 bg-gray-600 rounded-md w-20"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Add Member Button */}
          <div className="mt-4 pt-4 border-t border-gray-600">
            <div className="h-10 bg-gray-600 rounded-md w-40"></div>
          </div>
        </div>

        {/* Invite Links Section */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          {/* Section Header */}
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center space-x-2">
              <div className="h-5 w-5 bg-gray-600 rounded"></div>
              <div className="h-6 bg-gray-600 rounded-md w-40"></div>
            </div>
            <div className="h-8 bg-gray-600 rounded-md w-24"></div>
          </div>

          {/* Invite Cards */}
          <div className="space-y-3">
            {[1, 2].map((index) => (
              <div key={index} className="bg-gray-700 rounded-lg p-4 border border-gray-600">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-600 rounded-md w-48"></div>
                    <div className="h-3 bg-gray-600 rounded-md w-32"></div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="h-8 bg-gray-600 rounded-md w-16"></div>
                    <div className="h-8 bg-gray-600 rounded-md w-16"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Generate Invite Button */}
          <div className="mt-4 pt-4 border-t border-gray-600">
            <div className="h-10 bg-gray-600 rounded-md w-44"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Compact skeleton for route loading
export const TeamManagementRouteSkeleton: React.FC = () => {
  return (
    <div className="max-w-4xl mx-auto animate-pulse">
      <div className="mb-8">
        <div className="h-9 bg-gray-700 rounded-md w-64"></div>
      </div>
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex justify-center items-center py-12">
          <div className="space-y-4 text-center">
            <div className="h-8 w-8 bg-gray-600 rounded-full mx-auto"></div>
            <div className="h-4 bg-gray-600 rounded-md w-48 mx-auto"></div>
          </div>
        </div>
      </div>
    </div>
  );
};
