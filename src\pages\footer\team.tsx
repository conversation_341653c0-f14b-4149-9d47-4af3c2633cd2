import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Footer } from '../../components/footer';

export function TeamPage() {
  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'Co-Founder & CEO',
      bio: 'A visionary entrepreneur with extensive e-commerce experience and AI technology expertise. As a former online store owner, <PERSON> founded eComEasyAI to revolutionize product content creation, combining practical business knowledge with innovative solutions to transform how businesses create compelling product descriptions.',
      image: 'https://eiijjilntzgicgyhfbge.supabase.co/storage/v1/object/public/images/01/KKK.jpg'
    },
    {
      name: '<PERSON><PERSON>',
      role: 'Co-Founder & CTO',
      bio: 'A visionary technologist with over 15 years of pioneering experience across cutting-edge fields, <PERSON><PERSON> architects the future of e-commerce solutions. His unique ability to synthesize complex technologies into elegant solutions has been instrumental in revolutionizing eComEasyAI\'s technical infrastructure. As a passionate advocate for innovation, he leads our engineering teams in pushing the boundaries of what\'s possible in AI-driven content creation.',
      image: 'https://eiijjilntzgicgyhfbge.supabase.co/storage/v1/object/public/images/01/hbm0.jpg'
    },
    
  ];

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-12">
        <Link to="/" className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Home
        </Link>
        
        <div className="max-w-6xl mx-auto">
          <h1 className="text-4xl font-bold text-white mb-4">Meet Our Team</h1>
          <p className="text-gray-300 mb-12 text-xl">
            The passionate individuals behind eComEasyAI who are revolutionizing e-commerce content creation.
          </p>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <div key={index} className="bg-gray-800 rounded-xl overflow-hidden border border-gray-700 transition-all duration-300 hover:scale-[1.02] hover:shadow-lg hover:shadow-purple-500/20 group relative">
                <div className="relative overflow-hidden">  
                  <img 
                    src={member.image} 
                    alt={member.name} 
                    className="w-full h-64 object-cover object-center transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-300 bg-gradient-to-t from-purple-600 to-pink-500 pointer-events-none"></div>
                </div>
                <div className="p-6 relative z-10 transition-colors duration-300 group-hover:bg-gradient-to-r group-hover:from-gray-800 group-hover:to-gray-800/90">
                  <h3 className="text-xl font-semibold text-white mb-1 transition-colors duration-300 group-hover:text-purple-300">{member.name}</h3>
                  <p className="text-purple-400 mb-4 transition-colors duration-300 group-hover:text-pink-400">{member.role}</p>
                  <p className="text-gray-400 transition-colors duration-300 group-hover:text-gray-300">{member.bio}</p>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-16 bg-gradient-to-r from-purple-500/10 to-pink-500/10 p-8 rounded-xl border border-purple-500/20">
            <h2 className="text-2xl font-semibold text-white mb-4">Join Our Team</h2>
            <p className="text-gray-300 mb-6">
              We're always looking for talented individuals who are passionate about AI, e-commerce, and creating amazing user experiences.
              Check out our careers page to see current openings.
            </p>
            <Link to="/footer/careers" className="inline-block">
              <button className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                View Open Positions
              </button>
            </Link>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

export default TeamPage;