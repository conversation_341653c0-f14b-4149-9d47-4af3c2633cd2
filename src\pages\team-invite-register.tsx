import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { validateInviteToken, registerTeamMember } from '@/lib/inviteUtils';
import { createUserWithEmailAndPassword, signInWithEmailAndPassword, fetchSignInMethodsForEmail, sendEmailVerification } from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { toast } from 'react-hot-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AlertCircle, CheckCircle, Loader2 } from 'lucide-react';

export function TeamInviteRegister() {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [validating, setValidating] = useState(true);
  const [registering, setRegistering] = useState(false);
  const [inviteValid, setInviteValid] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [inviteDetails, setInviteDetails] = useState<any>(null);

  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isEmailRegistered, setIsEmailRegistered] = useState(false);
  const [checkingEmail, setCheckingEmail] = useState(false);

  // Function to check if an email is already registered
  const checkEmailRegistration = async (emailToCheck: string) => {
    if (!emailToCheck || !emailToCheck.includes('@')) return;

    setCheckingEmail(true);
    try {
      // Use Firebase's fetchSignInMethodsForEmail to check if the email is registered
      const methods = await fetchSignInMethodsForEmail(auth, emailToCheck);
      setIsEmailRegistered(methods.length > 0);
      console.log('Email registration check:', emailToCheck, methods.length > 0 ? 'registered' : 'not registered');
    } catch (error) {
      console.error('Error checking email registration:', error);
      // If there's an error, assume the email is not registered
      setIsEmailRegistered(false);
    } finally {
      setCheckingEmail(false);
    }
  };

  // Check email registration when email changes
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (email) {
        checkEmailRegistration(email);
      }
    }, 500); // Debounce for 500ms

    return () => clearTimeout(delayDebounceFn);
  }, [email]);

  useEffect(() => {
    const checkInvite = async () => {
      console.log('Checking invite with token:', token);
      if (!token) {
        console.log('No token provided');
        setInviteValid(false);
        setErrorMessage('Invalid invite link');
        setValidating(false);
        return;
      }

      try {
        console.log('Validating token:', token);
        const result = await validateInviteToken(token);
        console.log('Validation result:', result);

        if (result.valid && result.invite) {
          console.log('Invite is valid, setting details:', result.invite);
          setInviteValid(true);
          setInviteDetails(result.invite);
          // Pre-fill email if it's in the invite
          if (result.invite.email) {
            setEmail(result.invite.email);
          }
        } else {
          console.log('Invite is invalid:', result.message);
          setInviteValid(false);
          setErrorMessage(result.message || 'Invalid invite link');
        }
      } catch (error) {
        console.error('Error validating invite:', error);
        setInviteValid(false);
        setErrorMessage('An error occurred while validating the invite');
      } finally {
        console.log('Finished validation, setting loading to false');
        setValidating(false);
        setLoading(false);
      }
    };

    checkInvite();
  }, [token]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!name.trim()) {
      toast.error('Please enter your name');
      return;
    }

    if (!email.includes('@')) {
      toast.error('Please enter a valid email address');
      return;
    }

    if (!phone.trim()) {
      toast.error('Please enter your phone number');
      return;
    }

    if (password.length < 6) {
      toast.error('Password should be at least 6 characters');
      return;
    }

    // Only validate confirm password if creating a new account
    if (!isEmailRegistered && password !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    setRegistering(true);

    try {
      let user;

      if (isEmailRegistered) {
        // Sign in with existing account
        console.log('Signing in with existing account:', email);
        try {
          const userCredential = await signInWithEmailAndPassword(auth, email, password);
          user = userCredential.user;
          console.log('User signed in successfully:', user.uid);
        } catch (signInError: any) {
          console.error('Error signing in:', signInError);

          if (signInError.code === 'auth/wrong-password') {
            toast.error('Incorrect password. Please try again.');
          } else if (signInError.code === 'auth/too-many-requests') {
            toast.error('Too many failed attempts. Please try again later.');
          } else {
            toast.error('Failed to sign in. Please check your credentials.');
          }

          setRegistering(false);
          return;
        }
      } else {
        // Create new user account
        console.log('Creating new Firebase auth user with email:', email);
        try {
          const userCredential = await createUserWithEmailAndPassword(auth, email, password);
          user = userCredential.user;
          console.log('User created successfully:', user.uid);

          // Send email verification for new users
          try {
            await sendEmailVerification(user);
            console.log('Email verification sent for new team member');
          } catch (verificationError) {
            console.error('Error sending email verification:', verificationError);
            // Don't fail the registration if email verification fails
          }
        } catch (createError: any) {
          console.error('Error creating user:', createError);

          if (createError.code === 'auth/email-already-in-use') {
            toast.error('This email is already registered. Please sign in instead.');
            setIsEmailRegistered(true);
          } else {
            toast.error('Failed to create account. Please try again.');
          }

          setRegistering(false);
          return;
        }
      }

      // Register team member in Firestore
      console.log('Registering team member with invite ID:', inviteDetails.id);
      const result = await registerTeamMember(
        inviteDetails.id,
        name,
        email,
        phone,
        password,
        user.uid
      );

      if (result.success) {
        console.log('Registration successful', result);

        // Show appropriate success message
        if (result.isOneTimeInvite) {
          toast.success(isEmailRegistered
            ? 'You have joined the team successfully! This one-time invite link is now deactivated. Redirecting to dashboard...'
            : 'Registration successful! This one-time invite link is now deactivated. Redirecting to dashboard...');
        } else {
          toast.success(isEmailRegistered
            ? 'You have joined the team successfully! Redirecting to dashboard...'
            : 'Registration successful! Redirecting to dashboard...');
        }

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          navigate('/app/dashboard');
        }, 2000);
      } else {
        console.log('Registration failed:', result.message);
        toast.error(result.message || 'Registration failed');
      }
    } catch (error: any) {
      console.error('Error during registration process:', error);
      toast.error('An unexpected error occurred. Please try again.');
    } finally {
      setRegistering(false);
    }
  };

  if (loading || validating) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="w-full max-w-md p-8 space-y-8 bg-gray-800 rounded-xl shadow-lg">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-bold text-white">Team Invitation</h2>
            <p className="mt-2 text-gray-400">Validating your invite link...</p>
          </div>
          <div className="flex justify-center">
            <Loader2 className="h-8 w-8 text-purple-500 animate-spin" />
          </div>
        </div>
      </div>
    );
  }

  if (!inviteValid) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="w-full max-w-md p-8 space-y-8 bg-gray-800 rounded-xl shadow-lg">
          <div className="text-center">
            <AlertCircle className="mx-auto h-12 w-12 text-red-500" />
            <h2 className="mt-6 text-3xl font-bold text-white">Invalid Invite</h2>
            <p className="mt-2 text-red-400">{errorMessage}</p>
          </div>
          <div className="pt-4">
            <Button
              onClick={() => navigate('/auth')}
              className="w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white"
            >
              Go to Login
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900">
      <div className="w-full max-w-md p-8 space-y-8 bg-gray-800 rounded-xl shadow-lg">
        <div className="text-center">
          <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
          <h2 className="mt-6 text-3xl font-bold text-white">Join the Team</h2>
          <p className="mt-2 text-gray-400">
            {inviteDetails.companyName
              ? `You've been invited to join ${inviteDetails.companyName}`
              : `You've been invited to join ${inviteDetails.ownerEmail}'s team`
            }
          </p>
          {inviteDetails.limits?.isMultipleRegistrations && (
            <div className="mt-2 p-2 bg-purple-900/30 border border-purple-700 rounded-lg text-sm text-purple-300 flex items-center">
              <AlertCircle className="h-4 w-4 mr-2 text-purple-400" />
              This is a multiple-use invite link that never expires and can be shared with other team members
            </div>
          )}
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name" className="text-white">Full Name</Label>
              <Input
                id="name"
                name="name"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                className="bg-gray-700 border-gray-600 text-white"
                placeholder="Enter your full name"
              />
            </div>

            <div>
              <Label htmlFor="email" className="text-white">Email Address</Label>
              <div className="relative">
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className={`bg-gray-700 border-gray-600 text-white ${checkingEmail ? 'pr-10' : ''}`}
                  placeholder="Enter your email address"
                />
                {checkingEmail && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                  </div>
                )}
              </div>
              {isEmailRegistered && (
                <p className="text-sm text-blue-400 mt-1">
                  This email is already registered. You'll be signed in to join the team.
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="phone" className="text-white">Phone Number</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                required
                className="bg-gray-700 border-gray-600 text-white"
                placeholder="Enter your phone number"
              />
            </div>

            {/* Only show password fields if email is not registered */}
            {!isEmailRegistered && (
              <>
                <div>
                  <Label htmlFor="password" className="text-white">Password</Label>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="bg-gray-700 border-gray-600 text-white"
                    placeholder="Create a password (min. 6 characters)"
                  />
                </div>

                <div>
                  <Label htmlFor="confirmPassword" className="text-white">Confirm Password</Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    required
                    className="bg-gray-700 border-gray-600 text-white"
                    placeholder="Confirm your password"
                  />
                </div>
              </>
            )}

            {/* If email is registered, show password field for sign in */}
            {isEmailRegistered && (
              <div>
                <Label htmlFor="password" className="text-white">Password</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="bg-gray-700 border-gray-600 text-white"
                  placeholder="Enter your password to sign in"
                />
              </div>
            )}
          </div>

          <div>
            <Button
              type="submit"
              disabled={registering}
              className="w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white"
            >
              {registering ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isEmailRegistered ? 'Signing In...' : 'Creating Account...'}
                </>
              ) : (
                isEmailRegistered ? 'Sign In & Join Team' : 'Create Account & Join Team'
              )}
            </Button>
          </div>

          <div className="text-center text-sm">
            <p className="text-gray-400">
              Already have an account?{' '}
              <a href="/auth" className="text-purple-400 hover:text-purple-300">
                Sign in
              </a>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
}
