const functions = require('firebase-functions');
const admin = require('firebase-admin');
// Configure CORS to explicitly allow requests from Netlify domain
const cors = require('cors')({origin: ['https://eComEasyAI.netlify.app', 'https://ecomeasy.ai', 'http://localhost:5173', true]});
admin.initializeApp();
require('dotenv').config();

// Import coupon functions
const couponFunctions = require('./coupon-functions');

// Import SSLCOMMERZ functions
const sslcommerzFunctions = require('./sslcommerz-functions');

// Import Paddle webhook functions
const paddleWebhookFunctions = require('./paddle-webhook-functions');

// Export coupon functions
exports.completeCouponUsage = couponFunctions.completeCouponUsage;
exports.validateCoupon = couponFunctions.validateCoupon;
exports.validateCouponUpdate = couponFunctions.validateCouponUpdate;

// Export SSLCOMMERZ functions
exports.initSSLCommerzPayment = sslcommerzFunctions.initSSLCommerzPayment;
exports.handleSSLCommerzSuccess = sslcommerzFunctions.handleSSLCommerzSuccess;
exports.handleSSLCommerzFail = sslcommerzFunctions.handleSSLCommerzFail;
exports.handleSSLCommerzCancel = sslcommerzFunctions.handleSSLCommerzCancel;
exports.handleSSLCommerzIPN = sslcommerzFunctions.handleSSLCommerzIPN;

// Export Paddle webhook functions
exports.handlePaddleWebhook = paddleWebhookFunctions.handlePaddleWebhook;

/**
 * Cloud Function to delete a user from Firebase Authentication
 * This function is called from the client-side when a user is deleted from the admin panel
 * It requires the user to be authenticated as an admin
 */
exports.deleteUserAccount = functions.https.onCall(async (data, context) => {
  try {
    // Check if the user is authenticated
    if (!context.auth) {
      throw new functions.https.HttpsError(
        'unauthenticated',
        'The function must be called while authenticated.'
      );
    }

    // Check if the authenticated user is an admin
    const adminEmail = process.env.ADMIN_EMAIL;
    if (context.auth.token.email !== adminEmail) {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only admins can delete users.'
      );
    }

    // Get the UID from the data
    const { uid } = data;
    if (!uid) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Missing uid parameter'
      );
    }

    // Delete the user from Firebase Authentication
    await admin.auth().deleteUser(uid);

    // Optionally, you can also delete the user from Firestore here
    // This is useful if the client-side deletion fails for some reason
    // const userQuery = await admin.firestore().collection('users').where('uid', '==', uid).get();
    // userQuery.forEach(async (doc) => {
    //   await doc.ref.delete();
    // });

    return { success: true, message: 'User deleted successfully from Authentication' };
  } catch (error) {
    console.error('Error deleting user:', error);
    throw new functions.https.HttpsError(
      'internal',
      'An error occurred while deleting the user.',
      error
    );
  }
});