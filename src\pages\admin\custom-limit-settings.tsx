import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { toast } from 'react-hot-toast';
import { 
  Settings, 
  Plus, 
  Edit, 
  Trash, 
  Package, 
  Users, 
  RefreshCw,
  Save,
  X
} from 'lucide-react';
import { 
  getAllCustomPackages,
  createCustomPackage,
  updateCustomPackage,
  deleteCustomPackage,
  isCustomPackageNameExists,
  getDefaultCustomProLimits,
  getDefaultCustomEnterpriseLimits
} from '@/lib/customPackages';
import { 
  CustomPackage, 
  CustomProLimitSettings, 
  CustomEnterpriseLimitSettings 
} from '@/lib/userLimits';
import { getAuth } from 'firebase/auth';

interface CustomPackageForm {
  name: string;
  type: 'pro-based' | 'enterprise-based';
  limits: CustomProLimitSettings | CustomEnterpriseLimitSettings;
}

export function CustomLimitSettingsManagement() {
  const [packages, setPackages] = useState<CustomPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentPackage, setCurrentPackage] = useState<CustomPackage | null>(null);
  const [formData, setFormData] = useState<CustomPackageForm>({
    name: '',
    type: 'pro-based',
    limits: getDefaultCustomProLimits()
  });

  useEffect(() => {
    fetchCustomPackages();
  }, []);

  const fetchCustomPackages = async () => {
    setLoading(true);
    try {
      const packagesData = await getAllCustomPackages();
      setPackages(packagesData);
    } catch (error) {
      console.error('Error fetching custom packages:', error);
      toast.error('Failed to load custom packages');
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePackage = () => {
    setFormData({
      name: '',
      type: 'pro-based',
      limits: getDefaultCustomProLimits()
    });
    setIsCreateDialogOpen(true);
  };

  const handleEditPackage = (pkg: CustomPackage) => {
    setCurrentPackage(pkg);
    setFormData({
      name: pkg.name,
      type: pkg.type,
      limits: pkg.limits
    });
    setIsEditDialogOpen(true);
  };

  const handleDeletePackage = (pkg: CustomPackage) => {
    setCurrentPackage(pkg);
    setIsDeleteDialogOpen(true);
  };

  const handleTypeChange = (newType: 'pro-based' | 'enterprise-based') => {
    setFormData({
      ...formData,
      type: newType,
      limits: newType === 'pro-based' 
        ? getDefaultCustomProLimits() 
        : getDefaultCustomEnterpriseLimits()
    });
  };

  const handleLimitChange = (field: string, value: number) => {
    setFormData({
      ...formData,
      limits: {
        ...formData.limits,
        [field]: value
      }
    });
  };

  const validateForm = async (): Promise<boolean> => {
    if (!formData.name.trim()) {
      toast.error('Package name is required');
      return false;
    }

    // Check if name already exists (excluding current package when editing)
    const nameExists = await isCustomPackageNameExists(
      formData.name.trim(), 
      currentPackage?.id
    );
    
    if (nameExists) {
      toast.error('A package with this name already exists');
      return false;
    }

    return true;
  };

  const handleSavePackage = async () => {
    if (!(await validateForm())) return;

    setSaving(true);
    try {
      const auth = getAuth();
      const currentUser = auth.currentUser;
      
      if (!currentUser) {
        toast.error('You must be logged in to perform this action');
        return;
      }

      if (currentPackage) {
        // Update existing package
        await updateCustomPackage(currentPackage.id, {
          name: formData.name.trim(),
          type: formData.type,
          limits: formData.limits,
          isActive: true
        });
        setIsEditDialogOpen(false);
      } else {
        // Create new package
        await createCustomPackage(
          formData.name.trim(),
          formData.type,
          formData.limits,
          currentUser.uid
        );
        setIsCreateDialogOpen(false);
      }

      await fetchCustomPackages();
    } catch (error) {
      console.error('Error saving package:', error);
      toast.error('Failed to save package');
    } finally {
      setSaving(false);
    }
  };

  const handleConfirmDelete = async () => {
    if (!currentPackage) return;

    setSaving(true);
    try {
      await deleteCustomPackage(currentPackage.id);
      setIsDeleteDialogOpen(false);
      setCurrentPackage(null);
      await fetchCustomPackages();
    } catch (error) {
      console.error('Error deleting package:', error);
      toast.error('Failed to delete package');
    } finally {
      setSaving(false);
    }
  };

  const closeDialogs = () => {
    setIsCreateDialogOpen(false);
    setIsEditDialogOpen(false);
    setIsDeleteDialogOpen(false);
    setCurrentPackage(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-white">Custom Limit Settings</h2>
          <p className="text-gray-400 mt-1">Create and manage custom packages with tailored limits</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={fetchCustomPackages}
            className="bg-gray-800 border-gray-700 text-gray-200 hover:bg-gray-700 hover:text-white"
            disabled={loading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            onClick={handleCreatePackage}
            className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white border-0"
          >
            <Plus className="mr-2 h-4 w-4" />
            Create Custom Package
          </Button>
        </div>
      </div>

      {/* Custom Packages Table */}
      <Card className="bg-gray-800 border-gray-700 text-white">
        <CardHeader>
          <CardTitle className="text-lg font-medium flex items-center">
            <Package className="h-5 w-5 mr-2 text-purple-400" />
            Custom Packages
          </CardTitle>
          <CardDescription className="text-gray-400">
            Manage your custom packages and their limit configurations
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin text-purple-400" />
              <span className="ml-2 text-gray-400">Loading packages...</span>
            </div>
          ) : packages.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <Package className="h-12 w-12 mx-auto mb-4 text-gray-600" />
              <p>No custom packages created yet</p>
              <p className="text-sm">Click "Create Custom Package" to get started</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow className="border-gray-700">
                  <TableHead className="text-gray-300">Name</TableHead>
                  <TableHead className="text-gray-300">Type</TableHead>
                  <TableHead className="text-gray-300">Status</TableHead>
                  <TableHead className="text-gray-300">Created</TableHead>
                  <TableHead className="text-gray-300 text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {packages.map((pkg) => (
                  <TableRow key={pkg.id} className="border-gray-700">
                    <TableCell className="font-medium text-white">{pkg.name}</TableCell>
                    <TableCell>
                      <span
                        className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold ${
                          pkg.type === 'pro-based'
                            ? 'bg-blue-500/20 text-blue-300 border-blue-500/30'
                            : 'bg-purple-500/20 text-purple-300 border-purple-500/30'
                        }`}
                      >
                        {pkg.type === 'pro-based' ? 'Pro-based' : 'Enterprise-based'}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span
                        className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold ${
                          pkg.isActive
                            ? 'bg-green-500/20 text-green-300 border-green-500/30'
                            : 'bg-gray-500/20 text-gray-300 border-gray-500/30'
                        }`}
                      >
                        {pkg.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </TableCell>
                    <TableCell className="text-gray-400">
                      {new Date(pkg.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditPackage(pkg)}
                        className="text-gray-400 hover:text-white hover:bg-gray-700 mr-2"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeletePackage(pkg)}
                        className="text-gray-400 hover:text-red-400 hover:bg-gray-700"
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Create Package Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="bg-gray-800 text-white border-gray-700 max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-white">Create Custom Package</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <label className="text-sm font-medium text-gray-300 mb-1 block">Package Name</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter package name"
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>

            <div>
              <label className="text-sm font-medium text-gray-300 mb-1 block">Package Type</label>
              <select
                value={formData.type}
                onChange={(e) => handleTypeChange(e.target.value as 'pro-based' | 'enterprise-based')}
                className="w-full rounded-md bg-gray-700 border-gray-600 text-white py-2 px-3"
              >
                <option value="pro-based">Pro-based (Individual users)</option>
                <option value="enterprise-based">Enterprise-based (Team management)</option>
              </select>
            </div>

            {/* Limits Configuration */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-300">Limit Configuration</h4>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-xs text-gray-400 mb-1 block">Max Images</label>
                  <Input
                    type="number"
                    value={formData.limits.maxImages}
                    onChange={(e) => handleLimitChange('maxImages', parseInt(e.target.value) || 0)}
                    min="0"
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>

                <div>
                  <label className="text-xs text-gray-400 mb-1 block">Delete Delay (Hours)</label>
                  <Input
                    type="number"
                    value={formData.limits.deleteDelayHours}
                    onChange={(e) => handleLimitChange('deleteDelayHours', parseInt(e.target.value) || 0)}
                    min="0"
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>

                <div>
                  <label className="text-xs text-gray-400 mb-1 block">Max Saved Prompts</label>
                  <Input
                    type="number"
                    value={formData.limits.maxSavedPrompts}
                    onChange={(e) => handleLimitChange('maxSavedPrompts', parseInt(e.target.value) || 0)}
                    min="0"
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>

                <div>
                  <label className="text-xs text-gray-400 mb-1 block">Max Custom Prompts</label>
                  <Input
                    type="number"
                    value={formData.limits.maxCustomPrompts}
                    onChange={(e) => handleLimitChange('maxCustomPrompts', parseInt(e.target.value) || 0)}
                    min="0"
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>

                <div>
                  <label className="text-xs text-gray-400 mb-1 block">Monthly Generation Limit</label>
                  <Input
                    type="number"
                    value={formData.limits.monthlyGenerationLimit}
                    onChange={(e) => handleLimitChange('monthlyGenerationLimit', parseInt(e.target.value) || 0)}
                    min="0"
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>

                {formData.type === 'enterprise-based' && (
                  <div>
                    <label className="text-xs text-gray-400 mb-1 block">Max Team Members</label>
                    <Input
                      type="number"
                      value={(formData.limits as CustomEnterpriseLimitSettings).maxTeamMembers}
                      onChange={(e) => handleLimitChange('maxTeamMembers', parseInt(e.target.value) || 0)}
                      min="0"
                      className="bg-gray-700 border-gray-600 text-white"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={closeDialogs}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              onClick={handleSavePackage}
              disabled={saving}
              className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700"
            >
              {saving ? (
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              Create Package
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Package Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="bg-gray-800 text-white border-gray-700 max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-white">Edit Custom Package</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <label className="text-sm font-medium text-gray-300 mb-1 block">Package Name</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter package name"
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>

            <div>
              <label className="text-sm font-medium text-gray-300 mb-1 block">Package Type</label>
              <select
                value={formData.type}
                onChange={(e) => handleTypeChange(e.target.value as 'pro-based' | 'enterprise-based')}
                className="w-full rounded-md bg-gray-700 border-gray-600 text-white py-2 px-3"
              >
                <option value="pro-based">Pro-based (Individual users)</option>
                <option value="enterprise-based">Enterprise-based (Team management)</option>
              </select>
            </div>

            {/* Same limits configuration as create dialog */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-300">Limit Configuration</h4>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-xs text-gray-400 mb-1 block">Max Images</label>
                  <Input
                    type="number"
                    value={formData.limits.maxImages}
                    onChange={(e) => handleLimitChange('maxImages', parseInt(e.target.value) || 0)}
                    min="0"
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>

                <div>
                  <label className="text-xs text-gray-400 mb-1 block">Delete Delay (Hours)</label>
                  <Input
                    type="number"
                    value={formData.limits.deleteDelayHours}
                    onChange={(e) => handleLimitChange('deleteDelayHours', parseInt(e.target.value) || 0)}
                    min="0"
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>

                <div>
                  <label className="text-xs text-gray-400 mb-1 block">Max Saved Prompts</label>
                  <Input
                    type="number"
                    value={formData.limits.maxSavedPrompts}
                    onChange={(e) => handleLimitChange('maxSavedPrompts', parseInt(e.target.value) || 0)}
                    min="0"
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>

                <div>
                  <label className="text-xs text-gray-400 mb-1 block">Max Custom Prompts</label>
                  <Input
                    type="number"
                    value={formData.limits.maxCustomPrompts}
                    onChange={(e) => handleLimitChange('maxCustomPrompts', parseInt(e.target.value) || 0)}
                    min="0"
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>

                <div>
                  <label className="text-xs text-gray-400 mb-1 block">Monthly Generation Limit</label>
                  <Input
                    type="number"
                    value={formData.limits.monthlyGenerationLimit}
                    onChange={(e) => handleLimitChange('monthlyGenerationLimit', parseInt(e.target.value) || 0)}
                    min="0"
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>

                {formData.type === 'enterprise-based' && (
                  <div>
                    <label className="text-xs text-gray-400 mb-1 block">Max Team Members</label>
                    <Input
                      type="number"
                      value={(formData.limits as CustomEnterpriseLimitSettings).maxTeamMembers}
                      onChange={(e) => handleLimitChange('maxTeamMembers', parseInt(e.target.value) || 0)}
                      min="0"
                      className="bg-gray-700 border-gray-600 text-white"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={closeDialogs}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              onClick={handleSavePackage}
              disabled={saving}
              className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700"
            >
              {saving ? (
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              Update Package
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="bg-gray-800 text-white border-gray-700">
          <DialogHeader>
            <DialogTitle className="text-white">Delete Custom Package</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-gray-300">
              Are you sure you want to delete the package "{currentPackage?.name}"?
              This action cannot be undone.
            </p>
            <p className="text-sm text-yellow-400 mt-2">
              Warning: Users currently assigned to this package will be downgraded to Free users.
            </p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={closeDialogs}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmDelete}
              disabled={saving}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {saving ? (
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Trash className="mr-2 h-4 w-4" />
              )}
              Delete Package
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
