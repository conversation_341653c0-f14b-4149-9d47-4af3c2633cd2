import { Link } from 'react-router-dom';
import {
  ImageIcon, ShoppingBag, Sparkles, Zap, Menu, X, Globe, Search,
  Layers, Camera, Save, BookOpen, Eye, MessageSquare, FileText,
  PenTool, Mail, CheckCircle, Clock, Shield, Award, HeartHandshake
} from '../components/ui/optimized-icons';
import { LanguageToggle, useLanguage } from '../lib/languageContext';
import { Button } from '../components/ui/button';
import { useState, useEffect, useRef, lazy, Suspense } from 'react';
import { OptimizedCanvas } from '../components/ui/optimized-canvas';
import { MasonryGrid, MasonryItem } from '../components/ui/masonry-grid';
import { fetchMasonryData } from '../lib/masonryDataService';
import { useAuth } from '../lib/authProvider';
import { DEFAULT_PROMPTS_CATEGORIES } from '../lib/gemini';
import { useCountry } from '../lib/countryContext';
import { CountryIndicator } from '../components/ui/country-indicator';

// Lazy load heavy components
const Footer = lazy(() => import('../components/footer').then(module => ({ default: module.Footer })));
const GlowingEffect = lazy(() => import('../components/ui/glowing-effect').then(module => ({ default: module.GlowingEffect })));
const CountrySelectionPopup = lazy(() => import('../components/ui/country-selection-popup').then(module => ({ default: module.CountrySelectionPopup })));
const ContentModal = lazy(() => import('../components/ui/content-modal').then(module => ({ default: module.ContentModal })));

// Optimized lazy loading hook with performance improvements
function useLazyLoad(rootMargin = '200px') {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    // Use passive observer for better performance
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      {
        root: null,
        rootMargin,
        threshold: 0.1
      }
    );

    observer.observe(element);

    return () => {
      observer.disconnect();
    };
  }, [rootMargin]);

  return { ref, isVisible };
}

export function LandingPage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { isFirstVisit, setIsFirstVisit } = useCountry();
  const [showCountryPopup, setShowCountryPopup] = useState(false);
  const { currentUser } = useAuth();

  // Dynamic hero text state
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [isTextFading, setIsTextFading] = useState(false);

  // Billing toggle state
  const [isYearlyBilling, setIsYearlyBilling] = useState(false);

  // Masonry grid state
  const [masonryItems, setMasonryItems] = useState<MasonryItem[]>([]);
  const [masonryLoading, setMasonryLoading] = useState(false);
  const [masonryError, setMasonryError] = useState<string | null>(null);
  const [selectedItem, setSelectedItem] = useState<MasonryItem | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Show country selection popup for first-time visitors
  useEffect(() => {
    if (isFirstVisit) {
      setShowCountryPopup(true);
    }
  }, [isFirstVisit]);

  // Array of dynamic hero text phrases
  const dynamicHeroTexts = [
    "আপনার প্রোডাক্ট লিস্টিং ১০ গুণ শক্তিশালী করুন",
    "কনভার্সন রেট ৩ গুণ বাড়ান",
    "৯৫% বেশি কাস্টমারকে আকর্ষণ করুন",
    "৫ গুণ বেশি প্রোডাক্ট বিক্রি করুন",
    "বিক্রি বাড়ান ২০০% পর্যন্ত",
    "ই-কমার্স ইনকাম ১০ গুণ করুন",
    "৫০ গুণ আকর্ষণীয় ডেসক্রিপশন তৈরি করুন",
    "১০০০+ প্রতিযোগীর ভিড়ে আলাদা হন",
    "প্রতি মাসে ৪০+ ঘণ্টার লেখার ঝামেলা কমান",
    "এক ক্লিকে ১০০+ দেশের বাজারে পণ্য পৌঁছান",
    "বিক্রি বাড়ান ৪০০% পর্যন্ত",
    "বিজনেস ২০ গুণ বড় করুন",
    "ইনকাম বাড়ান ৫ গুণ!"
  ];

  // Lazy load refs for each section
  const featuresSection = useLazyLoad();
  const masonrySection = useLazyLoad();
  const languagesSection = useLazyLoad();
  const benefitsSection = useLazyLoad(); // New section for How to Get Benefited
  const pricingSection = useLazyLoad();
  const affiliateSection = useLazyLoad();
  const contactSection = useLazyLoad();

  // Remove the heavy canvas rendering from useEffect
  // It's now handled by the OptimizedCanvas component

  // Effect for rotating hero text
  useEffect(() => {
    const textInterval = setInterval(() => {
      // Start fade out animation
      setIsTextFading(true);

      // After fade out completes, change text and fade in
      setTimeout(() => {
        setCurrentTextIndex((prevIndex) => (prevIndex + 1) % dynamicHeroTexts.length);
        setIsTextFading(false);
      }, 500); // 500ms for fade out transition

    }, 5000); // Change text every 5 seconds

    return () => clearInterval(textInterval);
  }, []);

  // Effect for loading masonry data
  useEffect(() => {
    const loadMasonryData = async () => {
      if (!masonrySection.isVisible) return;

      setMasonryLoading(true);
      setMasonryError(null);

      try {
        const data = await fetchMasonryData({
          maxItems: 15,
          userId: currentUser?.uid
        });
        setMasonryItems(data);
      } catch (error) {
        console.error('Error loading masonry data:', error);
        setMasonryError(error instanceof Error ? error.message : 'কন্টেন্ট লোড করতে ব্যর্থ');
      } finally {
        setMasonryLoading(false);
      }
    };

    loadMasonryData();
  }, [masonrySection.isVisible, currentUser?.uid]);

  // Handle modal functions
  const handleItemClick = (item: MasonryItem) => {
    setSelectedItem(item);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedItem(null);
  };

  // Function to handle smooth scrolling to sections
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setIsMenuOpen(false); // Close mobile menu when navigating
    }
  };

  const navItems = [
    { name: 'Home', path: '#', action: () => window.scrollTo({ top: 0, behavior: 'smooth' }) },
    { name: 'Features', path: '#features', action: () => scrollToSection('features') },
    { name: 'Pricing', path: '#pricing', action: () => scrollToSection('pricing') },
    { name: 'Affiliate', path: '#affiliate', action: () => scrollToSection('affiliate') },
    { name: 'Contact', path: '#contact', action: () => scrollToSection('contact') },
  ];

  return (
    <div className="min-h-screen bg-gray-900 relative">
      {/* Optimized Canvas Background */}
      <OptimizedCanvas />
      {/* Header */}
      <header className="fixed w-full top-0 bg-gray-900/80 backdrop-blur-lg border-b border-gray-800 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <div className="text-xl font-bold bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent">
                eComEasyAI
              </div>
              <nav className="hidden md:flex ml-10 space-x-8">
                {navItems.map((item) => (
                  <a
                    key={item.name}
                    href={item.path}
                    onClick={(e) => {
                      if (item.action) {
                        e.preventDefault();
                        item.action();
                      }
                    }}
                    className="text-gray-300 hover:text-white transition-colors cursor-pointer"
                  >
                    {item.name}
                  </a>
                ))}
              </nav>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="mr-1">
                  <CountryIndicator />
                </div>
                <div className="mr-1">
                  <LanguageToggle />
                </div>
              </div>
              <Link to="/auth">
                <Button variant="outline" size="sm" className="bg-gradient-to-r from-white to-gray-100 text-black border-transparent hover:from-gray-100 hover:to-gray-200">Sign In</Button>
              </Link>
              <Link to="/auth?mode=signup">
                <Button size="sm">Get Started</Button>
              </Link>
            </div>
            <button
              className="md:hidden text-gray-400 hover:text-white"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
          {/* Mobile menu */}
          {isMenuOpen && (
            <div className="md:hidden py-4">
              <div className="flex flex-col space-y-4">
                {navItems.map((item) => (
                  <a
                    key={item.name}
                    href={item.path}
                    className="text-gray-300 hover:text-white transition-colors cursor-pointer"
                    onClick={(e) => {
                      if (item.action) {
                        e.preventDefault();
                        item.action();
                      } else {
                        setIsMenuOpen(false);
                      }
                    }}
                  >
                    {item.name}
                  </a>
                ))}
                <div className="pt-4 space-y-4">
                  <div className="flex justify-center items-center space-x-4 mb-4">
                    <CountryIndicator showLabel={false} />
                    <LanguageToggle />
                  </div>
                  <Link to="/auth" className="block">
                    <Button variant="outline" className="w-full">Sign In</Button>
                  </Link>
                  <Link to="/auth?mode=signup" className="block">
                    <Button className="w-full">Get Started</Button>
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6">
            <span className={`bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent transition-opacity duration-500 ${isTextFading ? 'opacity-0' : 'opacity-100'}`}>
              {dynamicHeroTexts[currentTextIndex]}
            </span>
            <br />
            <span className="text-white">with Product Marketing Description AI Writer</span>
          </h1>
          <p className="text-gray-400 text-xl mb-8 max-w-2xl mx-auto">
          আপনার প্রোডাক্টের ছবিগুলোকে এমন আকর্ষণীয় গল্পে বদলে দিন, যা বিক্রি বাড়াতে সাহায্য করে! আমাদের যুগান্তকারী এআই (AI) প্রত্যেকটা খুঁটিনাটি বিশ্লেষণ করে, আর সাথে সাথেই এমন দারুণ বর্ণনা তৈরি করে, যা শুধু ব্রাউজ করা মানুষদের ক্রেতায় পরিণত করে। হাজার হাজার সফল ব্যবসা আমাদের এই অসাধারণ প্রযুক্তি ব্যবহার করে তাদের বিক্রির লক্ষ্য পূরণ করছে। আপনিও তাদের সাথে যোগ দিন!
          </p>
          <Link to="/auth?mode=signup">
            <div className="flex flex-col items-center">
              <Button size="lg" className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 animate-pulse shadow-lg hover:shadow-purple-500/25 transition-all duration-300">
                Try Pro For FREE
                <Sparkles className="ml-2 h-5 w-5" />
              </Button>
              <span className="text-xs text-gray-400 mt-2">No Credit Card Required</span>
            </div>
          </Link>
        </div>
      </section>

      {/* Inspiring Examples - Masonry Grid */}
      <section
        className="py-20 px-4 bg-gradient-to-b from-gray-800 to-gray-900"
        ref={masonrySection.ref}
      >
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent">
                অনুপ্রেরণামূলক উদাহরণ
              </span>
            </h2>
            <p className="text-gray-400 text-lg max-w-2xl mx-auto">
              দেখুন কীভাবে আমাদের এআই সাধারণ পণ্যের ছবিগুলোকে বিক্রি বাড়ানোর জন্য আকর্ষণীয় বর্ণনায় রূপান্তরিত করে।
              আমাদের কমিউনিটির বাস্তব উদাহরণ থেকে অনুপ্রেরণা নিন!
            </p>
          </div>

          <MasonryGrid
            items={masonryItems}
            loading={masonryLoading}
            error={masonryError}
            onItemClick={handleItemClick}
            className="max-w-7xl mx-auto"
          />

          {/* Call to Action */}
          {masonryItems.length > 0 && (
            <div className="text-center mt-12">
              <p className="text-gray-400 mb-6">
                এই উদাহরণগুলো দেখে অনুপ্রাণিত হয়েছেন? আপনিও তৈরি করুন দারুণ পণ্যের বর্ণনা!
              </p>
              <Link to="/auth?mode=signup">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700
                           transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-purple-500/25"
                >
                  এখনই শুরু করুন
                  <Sparkles className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          )}
        </div>
      </section>

      {/* Features */}
      <section
        id="features"
        className="py-20 px-4 bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 scroll-mt-20"
        ref={featuresSection.ref}
      >
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center text-white mb-12">
            <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent">
              আয় বাড়ানোর দারুণ সব ফিচার
            </span>
            <p className="text-lg text-gray-400 mt-2 font-normal">
              প্রতিটি ফিচার আপনার মুনাফা বাড়ানো আর প্রতিযোগীদের হারানোর জন্য ডিজাইন করা
            </p>
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(139,92,246,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-purple-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-purple-500/30 transition-colors">
                <Zap className="h-6 w-6 text-purple-400 group-hover:text-purple-300" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-purple-300 transition-colors">AI দিয়ে দারুণ ক্যাপশন</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors">
              কয়েক সেকেন্ডেই এআই ব্যবহার করে আপনার পণ্যের ছবির জন্য আকর্ষণীয় বর্ণনা তৈরি করুন।
              </p>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(236,72,153,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-pink-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-pink-500/30 transition-colors">
                <Camera className="h-6 w-6 text-pink-400 group-hover:text-pink-300" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-pink-300 transition-colors">ক্যামেরা থেকেই সরাসরি ছবি</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors">
              আপনার মোবাইল বা ক্যামেরার মাধ্যমে সরাসরি পণ্যের ছবি তুলে সাথে সাথেই আপলোড করুন।
              </p>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(239,68,68,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-red-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-red-500/30 transition-colors">
                <ShoppingBag className="h-6 w-6 text-red-400 group-hover:text-red-300" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-red-300 transition-colors">ই-কমার্সের জন্য একদম তৈরি</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors">
              যেকোনো অনলাইন বাজারের জন্য আপনার পছন্দমতো স্টাইল আর টোনে দারুণ সব পণ্যের বর্ণনা তৈরি করুন।
              </p>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(59,130,246,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-blue-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-blue-500/30 transition-colors">
                <Globe className="h-6 w-6 text-blue-400 group-hover:text-blue-300" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-blue-300 transition-colors">অনেক ভাষায় সাপোর্ট</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors">
              এক ক্লিকেই ৩৫টারও বেশি ভাষায় কনটেন্ট তৈরি করুন, আর খুব সহজে পুরো দুনিয়ার বাজারে আপনার পণ্য বিক্রি করুন।
              </p>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(34,197,94,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-green-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-green-500/30 transition-colors">
                <BookOpen className="h-6 w-6 text-green-400 group-hover:text-green-300" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-green-300 transition-colors">নিজের মতো করে নির্দেশনা</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors">
              আপনার ব্র্যান্ডের কথা মাথায় রেখে, একই ধরনের লেখা তৈরি করার জন্য, নিজের মতো করে নির্দেশনা তৈরি করে সেগুলোকে সেভ করে রাখুন।
              </p>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(234,179,8,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-yellow-500/30 transition-colors">
                <Save className="h-6 w-6 text-yellow-400 group-hover:text-yellow-300" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-yellow-300 transition-colors">কন্টেন্ট ম্যানেজ করার সুবিধা</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors">
              আপনার সব বর্ণনা আর পণ্যের বিবরণ এক জায়গায় সহজে সেভ করুন, গুছিয়ে রাখুন, আর যখন দরকার তখনই ব্যবহার করুন।
              </p>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(99,102,241,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-indigo-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-indigo-500/30 transition-colors">
                <Layers className="h-6 w-6 text-indigo-400 group-hover:text-indigo-300" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-indigo-300 transition-colors">স্মার্ট ইমোজি ফিচার 🎯</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors">
              এআই বুদ্ধি খাটিয়ে ইমোজি সাজেস্ট করবে 🤖, আর অটোমেটিক্যালি সঠিক জায়গায় ইমোজি বসিয়ে দেবে 🎯। এখন খুব সহজেই ক্রেতাদের 🛍️ মন জয় করার মতো সুন্দর আর আকর্ষণীয় বর্ণনা তৈরি করুন।
              </p>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(14,165,233,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-cyan-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-cyan-500/30 transition-colors">
                <Eye className="h-6 w-6 text-cyan-400 group-hover:text-cyan-300" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-cyan-300 transition-colors">লাইভ প্রিভিউ</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors">
              চূড়ান্ত করার আগে বিভিন্ন ডিভাইস ফরম্যাট এবং থিমে আপনার তৈরি করা কন্টেন্টের প্রিভিউ দেখুন, যাতে সবকিছু একদম নিখুঁতভাবে উপস্থাপন করা যায়।
              </p>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(244,63,94,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-rose-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-rose-500/30 transition-colors">
                <Search className="h-6 w-6 text-rose-400 group-hover:text-rose-300" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-rose-300 transition-colors">স্মার্ট এসইও অপটিমাইজেশন</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors">
              সার্চ ইঞ্জিনে আপনার পণ্যের র‍্যাঙ্কিং ও দৃশ্যমানতা বাড়ানোর জন্য, অটোমেটিক্যালি এসইও-ফ্রেন্ডলি পণ্যের বর্ণনা আর মেটা ট্যাগ তৈরি করুন।
              </p>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>
          </div>

        </div>
      </section>

      {/* Languages Section */}
      <section
        className="py-20 px-4 bg-gradient-to-b from-gray-800 via-gray-900 to-gray-800 relative overflow-hidden"
        ref={languagesSection.ref}
      >
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center text-white mb-12">
          আপনার পণ্যের জন্য ৩৫টিরও বেশি ভাষায় এআই দিয়ে লিখুন
          </h2>
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-wrap justify-center gap-3 sm:gap-4">
              {/* Arabic */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                العربية
              </div>

              {/* Bengali */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                বাংলা
              </div>

              {/* Bulgarian */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Български
              </div>

              {/* Chinese simplified */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                简体中文
              </div>

              {/* Chinese traditional */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-yellow-500 to-yellow-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                繁體中文
              </div>

              {/* Croatian */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-pink-500 to-pink-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Hrvatski
              </div>

              {/* Czech */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-red-500 to-red-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Čeština
              </div>

              {/* Danish */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-indigo-500 to-indigo-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Dansk
              </div>

              {/* Dutch */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-cyan-500 to-cyan-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Nederlands
              </div>

              {/* English */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-teal-500 to-teal-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                English
              </div>

              {/* Estonian */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Eesti
              </div>

              {/* Finnish */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-violet-500 to-violet-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Suomi
              </div>

              {/* French */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-amber-500 to-amber-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Français
              </div>

              {/* German */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Deutsch
              </div>

              {/* Greek */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Ελληνικά
              </div>

              {/* Hebrew */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                עברית
              </div>

              {/* Hindi */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                हिंदी
              </div>

              {/* Hungarian */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-yellow-500 to-yellow-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Magyar
              </div>

              {/* Indonesian */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-pink-500 to-pink-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Bahasa Indonesia
              </div>

              {/* Italian */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-red-500 to-red-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Italiano
              </div>

              {/* Japanese */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-indigo-500 to-indigo-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                日本語
              </div>

              {/* Korean */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-cyan-500 to-cyan-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                한국어
              </div>

              {/* Latvian */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-teal-500 to-teal-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Latviešu
              </div>

              {/* Lithuanian */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Lietuvių
              </div>

              {/* Norwegian */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-violet-500 to-violet-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Norsk
              </div>

              {/* Polish */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-amber-500 to-amber-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Polski
              </div>

              {/* Portuguese */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Português
              </div>

              {/* Romanian */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Română
              </div>

              {/* Russian */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Русский
              </div>

              {/* Serbian */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Српски
              </div>

              {/* Slovak */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-yellow-500 to-yellow-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Slovenčina
              </div>

              {/* Slovenian */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-pink-500 to-pink-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Slovenščina
              </div>

              {/* Spanish */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-red-500 to-red-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Español
              </div>

              {/* Swahili */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-indigo-500 to-indigo-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Kiswahili
              </div>

              {/* Swedish */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-cyan-500 to-cyan-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Svenska
              </div>

              {/* Thai */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-teal-500 to-teal-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                ภาษาไทย
              </div>

              {/* Turkish */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Türkçe
              </div>

              {/* Ukrainian */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-violet-500 to-violet-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Українська
              </div>

              {/* Vietnamese */}
              <div className="h-10 min-w-[80px] flex items-center justify-center text-sm sm:text-base bg-gradient-to-r from-amber-500 to-amber-600 text-white rounded-full px-4 py-2 hover:scale-105 transition-transform duration-200 whitespace-nowrap">
                Tiếng Việt
              </div>
            </div>
          </div>
          <p className="text-gray-400 text-center mt-8 max-w-2xl mx-auto">
          আমাদের এআই প্রডাক্টের ছবি দেখে বিশ্লেষণ করে, আর সেটা ৩৫টারও বেশি ভাষায় কাজ করে। এর ফলে, আপনি আপনার জিনিসপত্র পুরো দুনিয়ার মানুষের কাছে তাদের নিজেদের ভাষায় পৌঁছে দিতে পারবেন।
          </p>
        </div>
      </section>

      {/* How to Get Benefited */}
      <section
        id="benefits"
        className="py-20 px-4 bg-gradient-to-b from-gray-800 via-gray-900 to-gray-800"
        ref={benefitsSection.ref}
      >
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center text-white mb-12">
            <span className="relative">
            আপনার <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-full after:bg-gradient-to-r after:from-purple-400 after:via-pink-500 after:to-red-500 after:scale-x-0 after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left">পণ্যের ছবিগুলোকে</span> শক্তিশালী <span className="bg-gradient-to-r from-blue-400 via-cyan-500 to-teal-500 bg-clip-text text-transparent relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-full after:bg-gradient-to-r after:from-blue-400 after:via-cyan-500 after:to-teal-500 after:scale-x-0 after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left">মার্কেটিং</span> <span className="bg-gradient-to-r from-emerald-400 via-green-500 to-lime-500 bg-clip-text text-transparent relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-full after:bg-gradient-to-r after:from-emerald-400 after:via-green-500 after:to-lime-500 after:scale-x-0 after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left">কন্টেন্টে রূপান্তর করুন</span>
            </span>
            <p className="text-lg text-gray-400 mt-2 mb-6">
            এআই ব্যবহার করে আপনার পণ্যের লিস্টিং এমনভাবে তৈরি করুন, যাতে বিক্রি বাড়ে আর কাস্টমারদের নজরে আসে।
            </p>
          </h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* eCommerce Card */}
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(139,92,246,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-purple-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-purple-500/30 transition-colors">
                <ShoppingBag className="h-6 w-6 text-purple-400 group-hover:text-purple-300" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-purple-300 transition-colors">ই-কমার্স ব্যবসার জন্য বর্ণনা</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors mb-4">
              আপনার অনলাইন স্টোর, অ্যামাজন লিস্টিং এবং আরও অনেক কিছুর জন্য আকর্ষণীয় পণ্যের বর্ণনা তৈরি করুন।
              </p>
              <div className="bg-gray-900 p-3 rounded-lg">
                <p className="text-sm text-gray-400 italic">"🛍️ স্ট্যান্ডার্ড প্রোডাক্ট ডেসক্রিপশন"</p>
                <p className="text-xs text-gray-300 mt-1">একদম পারফেক্ট বর্ণনার মধ্যে পণ্যের খুঁটিনাটি তথ্য, সুবিধা, গল্প আর ক্রেতাকে পণ্যটি কিনতে উৎসাহিত করার মতো কথাগুলো একসাথে।</p>
              </div>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>

            {/* Social Media Card */}
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(236,72,153,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-pink-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-pink-500/30 transition-colors">
                <MessageSquare className="h-6 w-6 text-pink-400 group-hover:text-pink-300" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-pink-300 transition-colors">সোশ্যাল মিডিয়া কন্টেন্ট</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors mb-4">
              ইনস্টাগ্রাম, ফেসবুক, টুইটার এবং টিকটকের জন্য আকর্ষণীয় ক্যাপশন তৈরি করুন, যা মানুষের মনোযোগ আকর্ষণ করে।
              </p>
              <div className="bg-gray-900 p-3 rounded-lg">
                <p className="text-sm text-gray-400 italic">"📱 সোশ্যাল মিডিয়ার জন্য"</p>
                <p className="text-xs text-gray-300 mt-1">চোখ ধাঁধানো শুরু, ইমোজি আর হ্যাশট্যাগ ব্যবহার করে এমন ক্যাপশন তৈরি করুন, যা মানুষের মনোযোগ কাড়বে।</p>
              </div>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>

            {/* Ad Copywriting Card */}
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(239,68,68,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-red-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-red-500/30 transition-colors">
                <PenTool className="h-6 w-6 text-red-400 group-hover:text-red-300" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-red-300 transition-colors">বিজ্ঞাপনের জন্য আকর্ষণীয় লেখা</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors mb-4">
              গুগল, ফেসবুক আর ডিসপ্লে বিজ্ঞাপনের জন্য এমন লেখা তৈরি করুন, যা দেখে মানুষ ক্লিক করতে আর কিনতে আগ্রহী হয়।
              </p>
              <div className="bg-gray-900 p-3 rounded-lg">
                <p className="text-sm text-gray-400 italic">"📢 বিজ্ঞাপনের জন্য লেখা"</p>
                <p className="text-xs text-gray-300 mt-1">আপনার প্রডাক্টের দিকে খেয়াল রেখে এমন শিরোনাম আর বর্ণনা তৈরি করে, যাতে মানুষ বেশি ক্লিক করে আর বেশি বিক্রি হয়।</p>
              </div>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>

            {/* Voice Over Script Card */}
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(59,130,246,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-blue-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-blue-500/30 transition-colors">
                <FileText className="h-6 w-6 text-blue-400 group-hover:text-blue-300" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-blue-300 transition-colors">ভয়েস ওভারের জন্য স্ক্রিপ্ট</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors mb-4">
              কোনো কিছু বুঝিয়ে বলার ভিডিও আর সোশ্যাল মিডিয়ার বিজ্ঞাপনের জন্য দারুণ স্ক্রিপ্ট তৈরি করুন।
              </p>
              <div className="bg-gray-900 p-3 rounded-lg">
                <p className="text-sm text-gray-400 italic">"🎤 বিজ্ঞাপনের জন্য আকর্ষণীয় ভয়েস ওভার"</p>
                <p className="text-xs text-gray-300 mt-1">গল্পের মতো করে লেখা আর এমন কথা, যা শুনে মানুষ আপনার পন্য কিনতে আগ্রহী হবে।</p>
              </div>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>

            {/* Blog Article Card */}
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(34,197,94,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-green-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-green-500/30 transition-colors">
                <BookOpen className="h-6 w-6 text-green-400 group-hover:text-green-300" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-green-300 transition-colors">ব্লগ আর্টিকেল লেখা</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors mb-4">
              আপনার ব্লগের জন্য পণ্যের রিভিউ, তুলনা আর কীভাবে ব্যবহার করতে হয়, সেসব নিয়ে বিস্তারিত লেখা তৈরি করুন।
              </p>
              <div className="bg-gray-900 p-3 rounded-lg">
                <p className="text-sm text-gray-400 italic">"📝 পণ্যের রিভিউ ব্লগ পোস্ট"</p>
                <p className="text-xs text-gray-300 mt-1">৮০০-১০০০ শব্দের বিস্তারিত লেখা, যেখানে পণ্যের বৈশিষ্ট্য, সুবিধা, ভালো-মন্দ দিকগুলো নিয়ে আলোচনা থাকবে।</p>
              </div>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>

            {/* Email Copywriting Card */}
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(234,179,8,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-yellow-500/30 transition-colors">
                <Mail className="h-6 w-6 text-yellow-400 group-hover:text-yellow-300" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-yellow-300 transition-colors">ইমেইল আর চ্যাটের মাধ্যমে মার্কেটিং</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors mb-4">
              এমন ইমেইল আর চ্যাটের স্ক্রিপ্ট তৈরি করুন, যা সম্ভাব্য ক্রেতাদের আসল ক্রেতায় পরিণত করে।
              </p>
              <div className="bg-gray-900 p-3 rounded-lg">
                <p className="text-sm text-gray-400 italic">"📧 নতুন পণ্য বাজারে আনার ইমেইল"</p>
                <p className="text-xs text-gray-300 mt-1">চোখ ধাঁধানো সাবজেক্ট লাইন আর এমন কথা, যা শুনে মানুষ পণ্যটি কিনতে আগ্রহী হবে, এমন ইমেইল তৈরি করুন।</p>
              </div>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>
          </div>

          <div className="mt-12 text-center">
            <Link to="/auth?mode=signup">
              <Button
                size="lg"
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600
                transform hover:scale-105 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/25
                animate-pulse hover:animate-none hover:-translate-y-1"
              >
                Start Creating Magic - Free
                <Sparkles className="ml-2 h-5 w-5 animate-bounce" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* How We Compare */}
      <section className="py-20 px-4 bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center text-white mb-8">
            <span className="relative">
            কেন আমরা অন্যান্য <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-full after:bg-gradient-to-r after:from-purple-400 after:via-pink-500 after:to-red-500 after:scale-x-0 after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left">এআই টুল থেকে ১০ গুণ এগিয়ে</span> ? 🚀
            </span>
          </h2>
          <p className="text-gray-400 text-center mb-12 max-w-3xl mx-auto text-lg">
          অন্যান্য এআই টুলের চেয়ে ১০ গুণ দ্রুত কন্টেন্ট তৈরি করুন, প্রায় ১০০% নির্ভুলতা, আর অনেক ভাষার সাপোর্ট পাবেন। ১০,০০০ এরও বেশি সফল অনলাইন ব্যবসা প্রতি সপ্তাহে ১৫ ঘণ্টারও বেশি সময় বাঁচাচ্ছে, আপনিও তাদের দলে যোগ দিন।
          </p>

          <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 p-8 rounded-xl border border-purple-500/20 mb-12 transform hover:scale-[1.01] transition-all duration-300">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left py-3 px-4 text-gray-300">Feature</th>
                    <th className="text-center py-3 px-4 text-white">eComEasyAI</th>
                    <th className="text-center py-3 px-4 text-gray-300">Generic AI Tools</th>
                    <th className="text-center py-3 px-4 text-gray-300">Manual Copywriting</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-700 hover:bg-gray-800/50 transition-colors">
                    <td className="py-3 px-4 text-gray-300">Processing Time</td>
                    <td className="py-3 px-4 text-center text-white">
                      <div className="flex flex-col items-center">
                        <span>Seconds</span>
                        <div className="w-full bg-gray-700 rounded-full h-2 mt-1">
                          <div className="bg-green-500 h-2 rounded-full" style={{width: '95%'}}></div>
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex flex-col items-center">
                        <span>Minutes</span>
                        <div className="w-full bg-gray-700 rounded-full h-2 mt-1">
                          <div className="bg-yellow-500 h-2 rounded-full" style={{width: '60%'}}></div>
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex flex-col items-center">
                        <span>Hours</span>
                        <div className="w-full bg-gray-700 rounded-full h-2 mt-1">
                          <div className="bg-red-500 h-2 rounded-full" style={{width: '20%'}}></div>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <tr className="border-b border-gray-700 hover:bg-gray-800/50 transition-colors">
                    <td className="py-3 px-4 text-gray-300">E-commerce Specific</td>
                    <td className="py-3 px-4 text-center text-white">
                      <div className="flex items-center justify-center">
                        <CheckCircle className="h-6 w-6 text-green-500" />
                        <span className="ml-2">Advanced</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-yellow-500 opacity-50" />
                        <span className="ml-2">Limited</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-gray-500 opacity-50" />
                        <span className="ml-2">Varies</span>
                      </div>
                    </td>
                  </tr>
                  <tr className="border-b border-gray-700 hover:bg-gray-800/50 transition-colors">
                    <td className="py-3 px-4 text-gray-300">Visual Analysis</td>
                    <td className="py-3 px-4 text-center text-white">
                      <div className="flex items-center justify-center">
                        <CheckCircle className="h-6 w-6 text-green-500" />
                        <span className="ml-2">Deep Analysis</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-yellow-500 opacity-50" />
                        <span className="ml-2">Basic</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-red-500 opacity-30" />
                        <span className="ml-2">Manual</span>
                      </div>
                    </td>
                  </tr>
                  <tr className="border-b border-gray-700 hover:bg-gray-800/50 transition-colors">
                    <td className="py-3 px-4 text-gray-300">Language Support</td>
                    <td className="py-3 px-4 text-center text-white">
                      <div className="flex items-center justify-center">
                        <span className="font-bold">35+ Languages</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex items-center justify-center">
                        <span>5-10 Languages</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex items-center justify-center">
                        <span>Single Language</span>
                      </div>
                    </td>
                  </tr>
                  <tr className="border-b border-gray-700 hover:bg-gray-800/50 transition-colors">
                    <td className="py-3 px-4 text-gray-300">Cost Efficiency</td>
                    <td className="py-3 px-4 text-center text-white">
                      <div className="flex flex-col items-center">
                        <span>High</span>
                        <div className="flex mt-1">
                          <span className="text-green-500">$</span>
                          <span className="text-gray-500">$$</span>
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex flex-col items-center">
                        <span>Medium</span>
                        <div className="flex mt-1">
                          <span className="text-yellow-500">$$</span>
                          <span className="text-gray-500">$</span>
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex flex-col items-center">
                        <span>Low</span>
                        <div className="flex mt-1">
                          <span className="text-red-500">$$$</span>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <tr className="border-b border-gray-700 hover:bg-gray-800/50 transition-colors">
                    <td className="py-3 px-4 text-gray-300">Consistency</td>
                    <td className="py-3 px-4 text-center text-white">
                      <div className="flex items-center justify-center">
                        <CheckCircle className="h-6 w-6 text-green-500" />
                        <span className="ml-2">Excellent</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-yellow-500 opacity-70" />
                        <span className="ml-2">Good</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-red-500 opacity-50" />
                        <span className="ml-2">Variable</span>
                      </div>
                    </td>
                  </tr>
                  <tr className="border-b border-gray-700 hover:bg-gray-800/50 transition-colors">
                    <td className="py-3 px-4 text-gray-300">SEO Optimization</td>
                    <td className="py-3 px-4 text-center text-white">
                      <div className="flex items-center justify-center">
                        <CheckCircle className="h-6 w-6 text-green-500" />
                        <span className="ml-2">Built-in</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-yellow-500 opacity-50" />
                        <span className="ml-2">Requires Setup</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-gray-500 opacity-50" />
                        <span className="ml-2">Requires Expertise</span>
                      </div>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-800/50 transition-colors">
                    <td className="py-3 px-4 text-gray-300">Scalability</td>
                    <td className="py-3 px-4 text-center text-white">
                      <div className="flex items-center justify-center">
                        <CheckCircle className="h-6 w-6 text-green-500" />
                        <span className="ml-2">Unlimited</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-yellow-500 opacity-70" />
                        <span className="ml-2">Token Limited</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-center text-gray-300">
                      <div className="flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-red-500 opacity-30" />
                        <span className="ml-2">Resource Limited</span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div className="text-center">
            <Link to="/footer/why-choose-us">
              <Button
                size="lg"
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold shadow-lg hover:shadow-purple-500/25 transition-all duration-300 transform hover:scale-105"
              >
                Discover Why We're 10x Better ⚡
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Why Choose eComEasyAI */}
      <section className="py-20 px-4 bg-gradient-to-b from-gray-800 via-gray-900 to-gray-800 relative overflow-hidden">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center text-white mb-6">
            <span className="relative">
            আপনার <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-full after:bg-gradient-to-r after:from-purple-400 after:via-pink-500 after:to-red-500 after:scale-x-0 after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left">পণ্যের ছবিগুলোকে</span> রূপান্তর করুন <span className="bg-gradient-to-r from-blue-400 via-cyan-500 to-teal-500 bg-clip-text text-transparent relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-full after:bg-gradient-to-r after:from-blue-400 after:via-cyan-500 after:to-teal-500 after:scale-x-0 after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left">বিক্রির</span> <span className="bg-gradient-to-r from-emerald-400 via-green-500 to-lime-500 bg-clip-text text-transparent relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-full after:bg-gradient-to-r after:from-emerald-400 after:via-green-500 after:to-lime-500 after:scale-x-0 after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left">মেশিনে</span>
            </span>
          </h2>
          <p className="text-gray-300 text-center mb-12 text-xl max-w-3xl mx-auto">
          আমাদের এআই ব্যবহার করে আপনার পণ্যের লিস্টিংগুলো এমনভাবে তৈরি করুন, যাতে বিক্রি অনেক বেড়ে যায়। ১০,০০০ এরও বেশি সফল অনলাইন ব্যবসা আমাদের ওপর ভরসা করে।
          </p>

          {/* Benefits Cards with Enhanced Animation */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {[
              {
                icon: Zap,
                title: "এআই-এর দারুণ নির্ভুলতা",
                description: `ভুল পণ্যের বর্ণনা নিয়ে ঝামেলায় আছেন? আমাদের এআই লেটেস্ট কম্পিউটার ভিশন ব্যবহার করে ৯৯.৯% নির্ভুলভাবে ছবি দেখে। খুব সহজে দারুণ সব বর্ণনা তৈরি করুন, যা বিক্রি বাড়াতে সাহায্য করে। আজই আপনার পণ্যের লিস্টিং বদলে ফেলুন।`,
                color: "purple"
              },
              {
                icon: Clock,
                title: "প্রতি সপ্তাহে ১৫ ঘণ্টা বাঁচান",
                description: `কন্টেন্ট তৈরি করতে গিয়ে কি খুব বেশি সময় নষ্ট হচ্ছে? আমাদের কাস্টমাররা প্রতি সপ্তাহে পণ্যের বর্ণনা লিখতে ১৫ ঘণ্টা বাঁচায়। ভাবুন তো, এই বাড়তি সময়টা আপনি অন্য কাজে লাগাতে পারবেন। এখনই আপনার কন্টেন্ট তৈরির কাজ অটোমেটিক করে নিন।`,
                color: "pink"
              },
              {
                icon: Globe,
                title: "মুহূর্তেই পুরো দুনিয়ায় পৌঁছে যান",
                description: `অন্য দেশের বাজারগুলো কি হাতছাড়া হয়ে যাচ্ছে? অনুবাদক ছাড়াই ৩৫টিরও বেশি ভাষায় কাস্টমারদের কাছে পৌঁছে যান। তাদের ভাষায় কন্টেন্ট তৈরি করে আপনার ব্যবসাকে পুরো দুনিয়ায় ছড়িয়ে দিন। ভাষার বাধা আর থাকবে না।`,
                color: "blue"
              },
              {
                icon: Shield,
                title: "একদম নিরাপদ ব্যবস্থা",
                description: `আপনার ডেটা নিয়ে কি চিন্তা হচ্ছে? আমরা আপনার কন্টেন্ট সুরক্ষিত রাখতে ব্যাংকের মতো নিরাপত্তা ব্যবস্থা ব্যবহার করি। আপনার ডেটা একদম নিরাপদ, এটা জেনে নিশ্চিন্ত থাকুন। এখনই আপনার ব্যবসার জিনিসপত্র সুরক্ষিত করুন।`,
                color: "green"
              },
              {
                icon: Award,
                title: "সেরা প্ল্যাটফর্ম",
                description: `আপনার ব্যবসার জন্য কি সেরাটা দরকার? আমাদের প্ল্যাটফর্ম বিভিন্ন পুরস্কার পেয়েছে আর বিশেষজ্ঞরা একে সেরা বলেছেন। সফল অনলাইন ব্যবসায়ীদের সাথে যোগ দিন। আজই সেরা অভিজ্ঞতা নিন।`,
                color: "yellow"
              },
              {
                icon: HeartHandshake,
                title: "২৪/৭ সহায়তা",
                description: `এআই টুল ব্যবহার করতে গিয়ে কোনো সমস্যা হচ্ছে? আমাদের অভিজ্ঞ টিম আপনাকে সাহায্য করার জন্য সবসময় প্রস্তুত। বিশেষজ্ঞদের কাছ থেকে সাহায্য নিয়ে আপনার প্ল্যাটফর্মের সব সুবিধা কাজে লাগান। এখনই আমাদের স্পেশাল সাপোর্ট নিন।`,
                color: "red"
              }
            ].map(({icon: Icon, title, description, color}, index) => (
              <div
                key={index}
                className={`bg-gray-800 p-6 rounded-xl border border-gray-700 relative group
                  hover:scale-[1.03] hover:shadow-[0_0_30px_rgba(var(--${color}-rgb),0.3)]
                  transition-all duration-300 ease-out overflow-hidden`}
              >
                <div className={`absolute inset-0 bg-gradient-to-r from-${color}-500/5 to-transparent
                  opacity-0 group-hover:opacity-100 transition-opacity duration-300`}
                />
                <div className={`h-14 w-14 bg-${color}-500/20 rounded-lg flex items-center justify-center
                  mb-4 group-hover:bg-${color}-500/30 transition-colors duration-300 relative z-10`}
                >
                  <Icon className={`h-8 w-8 text-${color}-400 group-hover:text-${color}-300
                    transition-transform duration-300 group-hover:scale-110`}
                  />
                </div>
                <h3 className={`text-xl font-semibold text-white mb-3 group-hover:text-${color}-300
                  transition-colors duration-300 relative z-10`}
                >
                  {title}
                </h3>
                <p className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300 relative z-10">
                  {description}
                </p>
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                  <GlowingEffect
                    spread={15}
                    glow={true}
                    disabled={false}
                    proximity={30}
                    inactiveZone={0.01}
                    borderWidth={1}
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Testimonials with Enhanced Design */}
          <h3 className="text-2xl font-semibold text-white mb-8 text-center">আমাদের কাস্টমারদের মতামত</h3>

          <div className="relative mb-12 overflow-hidden">
            {/* Horizontal scrolling container */}
            <div className="flex overflow-x-auto pb-6 custom-scrollbar testimonial-scroll">
              <div className="flex space-x-6 animate-scroll">
                {/* Original testimonials */}
                <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(139,92,246,0.2)] transition-all duration-300 ease-out overflow-hidden min-w-[320px] max-w-[320px]">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-pink-500"></div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-lg">ST</div>
                    <div className="ml-3">
                      <p className="text-white font-medium">Sarah T.</p>
                      <p className="text-gray-400 text-sm">Fashion Retailer, 10,000+ SKUs</p>
                    </div>
                  </div>
                  <p className="text-gray-300 italic relative">
                    <span className="text-4xl text-purple-500/20 absolute top-0 left-0">"</span>
                    <span className="pl-6">eComEasyAI has transformed our product listing process. What used to take our team days now happens in minutes, and our conversion rates have increased by 28%.</span>
                    <span className="text-4xl text-purple-500/20 absolute bottom-0 right-0">„</span>
                  </p>
                </div>

                <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(236,72,153,0.2)] transition-all duration-300 ease-out overflow-hidden min-w-[320px] max-w-[320px]">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-pink-500 to-red-500"></div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-pink-500 to-red-500 flex items-center justify-center text-white font-bold text-lg">MR</div>
                    <div className="ml-3">
                      <p className="text-white font-medium">Michael R.</p>
                      <p className="text-gray-400 text-sm">Boutique Home Goods Store</p>
                    </div>
                  </div>
                  <p className="text-gray-300 italic relative">
                    <span className="text-4xl text-pink-500/20 absolute top-0 left-0">"</span>
                    <span className="pl-6">As a small business owner, I couldn't afford to hire a copywriter. eComEasyAI gives me professional-quality descriptions at a fraction of the cost.</span>
                    <span className="text-4xl text-pink-500/20 absolute bottom-0 right-0">„</span>
                  </p>
                </div>

                <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(59,130,246,0.2)] transition-all duration-300 ease-out overflow-hidden min-w-[320px] max-w-[320px]">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-indigo-500"></div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 flex items-center justify-center text-white font-bold text-lg">EK</div>
                    <div className="ml-3">
                      <p className="text-white font-medium">Elena K.</p>
                      <p className="text-gray-400 text-sm">Global Electronics Distributor</p>
                    </div>
                  </div>
                  <p className="text-gray-300 italic relative">
                    <span className="text-4xl text-blue-500/20 absolute top-0 left-0">"</span>
                    <span className="pl-6">The multilingual support is a game-changer. We've expanded to 5 new international markets without having to hire additional content creators.</span>
                    <span className="text-4xl text-blue-500/20 absolute bottom-0 right-0">„</span>
                  </p>
                </div>

                {/* New testimonials */}
                <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(34,197,94,0.2)] transition-all duration-300 ease-out overflow-hidden min-w-[320px] max-w-[320px]">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-teal-500"></div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-teal-500 flex items-center justify-center text-white font-bold text-lg">JL</div>
                    <div className="ml-3">
                      <p className="text-white font-medium">James L.</p>
                      <p className="text-gray-400 text-sm">Outdoor Equipment Retailer</p>
                    </div>
                  </div>
                  <p className="text-gray-300 italic relative">
                    <span className="text-4xl text-green-500/20 absolute top-0 left-0">"</span>
                    <span className="pl-6">The product descriptions generated by eComEasyAI have increased our click-through rates by 45%. The AI understands our products better than some of our staff!</span>
                    <span className="text-4xl text-green-500/20 absolute bottom-0 right-0">„</span>
                  </p>
                </div>

                <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(234,179,8,0.2)] transition-all duration-300 ease-out overflow-hidden min-w-[320px] max-w-[320px]">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-500 to-amber-500"></div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-yellow-500 to-amber-500 flex items-center justify-center text-white font-bold text-lg">AP</div>
                    <div className="ml-3">
                      <p className="text-white font-medium">Aisha P.</p>
                      <p className="text-gray-400 text-sm">Handmade Jewelry Creator</p>
                    </div>
                  </div>
                  <p className="text-gray-300 italic relative">
                    <span className="text-4xl text-yellow-500/20 absolute top-0 left-0">"</span>
                    <span className="pl-6">My handcrafted jewelry deserves descriptions as unique as each piece. eComEasyAI captures the essence and artistry in ways I never could express myself.</span>
                    <span className="text-4xl text-yellow-500/20 absolute bottom-0 right-0">„</span>
                  </p>
                </div>

                <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(99,102,241,0.2)] transition-all duration-300 ease-out overflow-hidden min-w-[320px] max-w-[320px]">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-indigo-500 to-violet-500"></div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-indigo-500 to-violet-500 flex items-center justify-center text-white font-bold text-lg">DM</div>
                    <div className="ml-3">
                      <p className="text-white font-medium">David M.</p>
                      <p className="text-gray-400 text-sm">Tech Gadget Marketplace</p>
                    </div>
                  </div>
                  <p className="text-gray-300 italic relative">
                    <span className="text-4xl text-indigo-500/20 absolute top-0 left-0">"</span>
                    <span className="pl-6">We've seen a 32% reduction in product return rates since implementing eComEasyAI. The accurate descriptions set proper expectations for customers.</span>
                    <span className="text-4xl text-indigo-500/20 absolute bottom-0 right-0">„</span>
                  </p>
                </div>

                <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(14,165,233,0.2)] transition-all duration-300 ease-out overflow-hidden min-w-[320px] max-w-[320px]">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-cyan-500 to-blue-500"></div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center text-white font-bold text-lg">RJ</div>
                    <div className="ml-3">
                      <p className="text-white font-medium">Rachel J.</p>
                      <p className="text-gray-400 text-sm">Beauty Products Brand</p>
                    </div>
                  </div>
                  <p className="text-gray-300 italic relative">
                    <span className="text-4xl text-cyan-500/20 absolute top-0 left-0">"</span>
                    <span className="pl-6">Our skincare line needed descriptions that convey both science and luxury. eComEasyAI nails this balance perfectly, and our sales have increased by 40% since switching.</span>
                    <span className="text-4xl text-cyan-500/20 absolute bottom-0 right-0">„</span>
                  </p>
                </div>

                <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(239,68,68,0.2)] transition-all duration-300 ease-out overflow-hidden min-w-[320px] max-w-[320px]">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-red-500 to-orange-500"></div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-red-500 to-orange-500 flex items-center justify-center text-white font-bold text-lg">TN</div>
                    <div className="ml-3">
                      <p className="text-white font-medium">Thomas N.</p>
                      <p className="text-gray-400 text-sm">Specialty Food Importer</p>
                    </div>
                  </div>
                  <p className="text-gray-300 italic relative">
                    <span className="text-4xl text-red-500/20 absolute top-0 left-0">"</span>
                    <span className="pl-6">Describing gourmet foods requires a special touch. eComEasyAI captures flavors, textures, and aromas in ways that make customers almost taste the products through their screens.</span>
                    <span className="text-4xl text-red-500/20 absolute bottom-0 right-0">„</span>
                  </p>
                </div>

                <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(236,72,153,0.2)] transition-all duration-300 ease-out overflow-hidden min-w-[320px] max-w-[320px]">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-pink-500 to-rose-500"></div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-pink-500 to-rose-500 flex items-center justify-center text-white font-bold text-lg">LW</div>
                    <div className="ml-3">
                      <p className="text-white font-medium">Lisa W.</p>
                      <p className="text-gray-400 text-sm">Children's Toy Company</p>
                    </div>
                  </div>
                  <p className="text-gray-300 italic relative">
                    <span className="text-4xl text-pink-500/20 absolute top-0 left-0">"</span>
                    <span className="pl-6">Parents love our detailed product descriptions that highlight safety features and educational benefits. Our conversion rate has doubled since using eComEasyAI.</span>
                    <span className="text-4xl text-pink-500/20 absolute bottom-0 right-0">„</span>
                  </p>
                </div>

                <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(139,92,246,0.2)] transition-all duration-300 ease-out overflow-hidden min-w-[320px] max-w-[320px]">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-violet-500"></div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-violet-500 flex items-center justify-center text-white font-bold text-lg">KS</div>
                    <div className="ml-3">
                      <p className="text-white font-medium">Kevin S.</p>
                      <p className="text-gray-400 text-sm">Fitness Equipment Distributor</p>
                    </div>
                  </div>
                  <p className="text-gray-300 italic relative">
                    <span className="text-4xl text-purple-500/20 absolute top-0 left-0">"</span>
                    <span className="pl-6">The technical specifications of our fitness equipment are perfectly balanced with motivational content. eComEasyAI understands our audience's needs perfectly.</span>
                    <span className="text-4xl text-purple-500/20 absolute bottom-0 right-0">„</span>
                  </p>
                </div>

                <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(34,197,94,0.2)] transition-all duration-300 ease-out overflow-hidden min-w-[320px] max-w-[320px]">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-emerald-500"></div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center text-white font-bold text-lg">MH</div>
                    <div className="ml-3">
                      <p className="text-white font-medium">Maria H.</p>
                      <p className="text-gray-400 text-sm">Sustainable Home Products</p>
                    </div>
                  </div>
                  <p className="text-gray-300 italic relative">
                    <span className="text-4xl text-green-500/20 absolute top-0 left-0">"</span>
                    <span className="pl-6">Our eco-friendly products need descriptions that highlight both functionality and environmental benefits. eComEasyAI delivers this perfectly every time.</span>
                    <span className="text-4xl text-green-500/20 absolute bottom-0 right-0">„</span>
                  </p>
                </div>

                <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(234,179,8,0.2)] transition-all duration-300 ease-out overflow-hidden min-w-[320px] max-w-[320px]">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-500 to-orange-500"></div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center text-white font-bold text-lg">JP</div>
                    <div className="ml-3">
                      <p className="text-white font-medium">Jason P.</p>
                      <p className="text-gray-400 text-sm">Vintage Furniture Restoration</p>
                    </div>
                  </div>
                  <p className="text-gray-300 italic relative">
                    <span className="text-4xl text-yellow-500/20 absolute top-0 left-0">"</span>
                    <span className="pl-6">Each of our restored pieces has a unique story. eComEasyAI captures the history, craftsmanship, and character in ways that connect with collectors and designers.</span>
                    <span className="text-4xl text-yellow-500/20 absolute bottom-0 right-0">„</span>
                  </p>
                </div>

                <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(59,130,246,0.2)] transition-all duration-300 ease-out overflow-hidden min-w-[320px] max-w-[320px]">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-sky-500"></div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-sky-500 flex items-center justify-center text-white font-bold text-lg">CL</div>
                    <div className="ml-3">
                      <p className="text-white font-medium">Carlos L.</p>
                      <p className="text-gray-400 text-sm">Automotive Parts Supplier</p>
                    </div>
                  </div>
                  <p className="text-gray-300 italic relative">
                    <span className="text-4xl text-blue-500/20 absolute top-0 left-0">"</span>
                    <span className="pl-6">Technical accuracy is critical in our industry. eComEasyAI creates descriptions that are both technically precise and easy for non-experts to understand. It's the perfect balance.</span>
                    <span className="text-4xl text-blue-500/20 absolute bottom-0 right-0">„</span>
                  </p>
                </div>

                <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(236,72,153,0.2)] transition-all duration-300 ease-out overflow-hidden min-w-[320px] max-w-[320px]">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-pink-500 to-purple-500"></div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-pink-500 to-purple-500 flex items-center justify-center text-white font-bold text-lg">AZ</div>
                    <div className="ml-3">
                      <p className="text-white font-medium">Amelia Z.</p>
                      <p className="text-gray-400 text-sm">Luxury Handbag Designer</p>
                    </div>
                  </div>
                  <p className="text-gray-300 italic relative">
                    <span className="text-4xl text-pink-500/20 absolute top-0 left-0">"</span>
                    <span className="pl-6">Our luxury products demand sophisticated descriptions that convey exclusivity and craftsmanship. eComEasyAI delivers content that resonates with our high-end clientele perfectly.</span>
                    <span className="text-4xl text-pink-500/20 absolute bottom-0 right-0">„</span>
                  </p>
                </div>
              </div>
            </div>

            {/* Navigation controls */}
            <div className="flex justify-center mt-6 space-x-2">
              <button className="w-3 h-3 rounded-full bg-gray-600 hover:bg-purple-500 transition-colors duration-300"></button>
              <button className="w-3 h-3 rounded-full bg-gray-600 hover:bg-purple-500 transition-colors duration-300"></button>
              <button className="w-3 h-3 rounded-full bg-gray-600 hover:bg-purple-500 transition-colors duration-300"></button>
              <button className="w-3 h-3 rounded-full bg-gray-600 hover:bg-purple-500 transition-colors duration-300"></button>
            </div>
          </div>

          <div className="text-center">
            <Link to="/auth?mode=signup" className="inline-block">
              <Button
                size="lg"
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold shadow-lg hover:shadow-purple-500/25 transition-all duration-300 transform hover:scale-105"
              >
                Start Your Free Trial Today
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section
        id="pricing"
        className="py-20 px-4 bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden scroll-mt-20"
        ref={pricingSection.ref}
      >
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden opacity-20 pointer-events-none">
          <div className="absolute -top-24 -left-24 w-96 h-96 bg-purple-500 rounded-full filter blur-3xl"></div>
          <div className="absolute top-1/2 -right-24 w-96 h-96 bg-pink-500 rounded-full filter blur-3xl"></div>
          <div className="absolute -bottom-24 left-1/3 w-96 h-96 bg-blue-500 rounded-full filter blur-3xl"></div>
        </div>

        <div className="container mx-auto text-center relative z-10">
          <div className="inline-block mb-4">
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-px rounded-full inline-block">
              <div className="bg-gray-900 rounded-full px-4 py-1">
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 font-semibold">Choose Your Plan</span>
              </div>
            </div>
          </div>

          <h2 className="text-4xl font-bold text-white mb-6">
            <span className="relative inline-block">
            সিম্পল <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-500 to-red-500">প্রাইসিং</span>
              <div className="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 rounded-full"></div>
            </span>
          </h2>

          <p className="text-gray-300 max-w-2xl mx-auto mb-12 text-lg">
          আপনার ব্যবসার জন্য সবচেয়ে ভালো প্ল্যানটি বেছে নিন
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center mb-12">
            <span className={`text-gray-300 mr-3 font-medium transition-colors duration-300 ${!isYearlyBilling ? 'text-white font-semibold' : ''}`}>Monthly</span>
            <div className="relative inline-block w-16 h-8 transition duration-300 ease-in-out">
              <input
                type="checkbox"
                id="billing-toggle"
                className="absolute w-8 h-8 opacity-0 z-10 cursor-pointer peer"
                checked={isYearlyBilling}
                onChange={() => setIsYearlyBilling(!isYearlyBilling)}
              />
              <label
                htmlFor="billing-toggle"
                className="block w-16 h-8 overflow-hidden rounded-full bg-gradient-to-r from-gray-800 to-gray-700 cursor-pointer transition-all duration-500 ease-in-out peer-checked:bg-gradient-to-r peer-checked:from-purple-600 peer-checked:to-pink-600 border border-gray-600 peer-checked:border-purple-400/30 shadow-inner"
              >
                {/* Track inner glow effect */}
                <div className={`absolute inset-0 rounded-full transition-opacity duration-500 ${isYearlyBilling ? 'opacity-100' : 'opacity-0'}`}
                  style={{
                    background: 'radial-gradient(circle at center, rgba(168, 85, 247, 0.3) 0%, rgba(217, 70, 239, 0.1) 70%, transparent 100%)',
                    filter: 'blur(2px)'
                  }}>
                </div>

                {/* Toggle knob with premium styling */}
                <span
                  className={`absolute top-1 w-6 h-6 rounded-full transition-all duration-500 ease-out transform ${isYearlyBilling ? 'left-9 translate-x-0' : 'left-1 -translate-x-0'} shadow-lg hover:scale-105`}
                  style={{
                    background: isYearlyBilling
                      ? 'linear-gradient(135deg, #d946ef 0%, #a855f7 50%, #8b5cf6 100%)'
                      : 'linear-gradient(135deg, #ffffff 0%, #f3f4f6 100%)',
                    boxShadow: isYearlyBilling
                      ? '0 0 15px rgba(168, 85, 247, 0.6), inset 0 1px 1px rgba(255, 255, 255, 0.5)'
                      : '0 2px 5px rgba(0, 0, 0, 0.2), inset 0 1px 1px rgba(255, 255, 255, 0.8)',
                  }}
                >
                  {/* Inner highlight for 3D effect */}
                  <span className="absolute inset-0 rounded-full opacity-60"
                    style={{
                      background: 'linear-gradient(135deg, rgba(255,255,255,0.5) 0%, rgba(255,255,255,0.2) 50%, transparent 100%)',
                      filter: 'blur(0.5px)'
                    }}>
                  </span>
                </span>

                {/* Text indicators with improved styling */}
                <span className="absolute inset-0 flex items-center justify-around px-1 text-[8px] font-bold">
                  <span className={`transition-all duration-500 ${!isYearlyBilling ? 'opacity-0 translate-y-1' : 'opacity-90 translate-y-0'} text-white drop-shadow-md`}>Y</span>
                  <span className={`transition-all duration-500 ${isYearlyBilling ? 'opacity-0 translate-y-1' : 'opacity-90 translate-y-0'} text-gray-200 drop-shadow-sm`}>M</span>
                </span>
              </label>
            </div>
            <span className={`text-gray-300 ml-3 font-medium transition-colors duration-300 ${isYearlyBilling ? 'text-white font-semibold' : ''}`}>
              Yearly
              <span className="ml-1 text-xs text-green-400 font-normal inline-flex items-center">
                <span className="mr-1">Save</span>
                <span className="bg-green-400/20 text-green-400 px-1 py-0.5 rounded-sm animate-pulse">20%</span>
              </span>
            </span>
          </div>

          {/* Pricing cards - 2 columns on tablet, 4 columns (single row) on desktop */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-6xl mx-auto">
            {/* Starter Plan */}
            <div className="group relative bg-gray-800/80 rounded-xl border border-gray-700 p-6 transition-all duration-300 hover:border-purple-500/50 hover:shadow-lg hover:shadow-purple-500/20 hover:-translate-y-1">
              <div className="absolute inset-0 bg-gradient-to-b from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none"></div>

              <div className="relative">
                <div className="h-12 w-12 bg-purple-500/10 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-purple-500/30 transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
              </div>

              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-purple-300 transition-colors duration-300">Starter</h3>
              <div className="text-sm text-gray-400 mb-6">ছোট ব্যবসার জন্য উপযুক্ত</div>

              <div className="text-4xl font-bold text-white mb-1 flex items-center justify-center">
                <span className="text-2xl mr-1">৳</span>0
                <span className="text-sm text-green-400 ml-2">ফ্রি</span>
              </div>
              <div className="text-sm text-gray-400 mb-2">প্রতি {isYearlyBilling ? 'বছর' : 'মাস'}</div>
              <div className="text-xs text-green-400 mb-6">ক্রেডিট কার্ড লাগবে না!</div>

              <ul className="text-gray-400 space-y-3 mb-8 text-left">
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span><strong>১০</strong> টি ছবি সংরক্ষণ</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span><strong>১০</strong> টি জেনারেশন (লাইফটাইম)</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span><strong>১০</strong> টি সেভ করা ডাটা</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  <span className="text-gray-500">কাস্টম প্রম্পট নেই</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>ছবি ডিলিট করতে ৬ ঘন্টা সময় লাগবে</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>এআই-পাওয়ার্ড এসইও অপটিমাইজেশন</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>দ্রুত জেনারেশন</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>বেসিক সাপোর্ট</span>
                </li>
              </ul>

              <Link to="/auth?mode=signup">
                <Button
                  className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 shadow-lg shadow-purple-500/20 hover:shadow-purple-500/40 transition-all duration-300"
                >
                  শুরু করুন
                </Button>
              </Link>
            </div>

            {/* Pay-As-You-Go Plan */}
            <div className="group relative bg-gray-800/80 rounded-xl border border-gray-700 p-6 transition-all duration-300 hover:border-cyan-500/50 hover:shadow-lg hover:shadow-cyan-500/20 hover:-translate-y-1">
              <div className="absolute -top-4 left-0 right-0 mx-auto w-max px-3 py-1 bg-gradient-to-r from-cyan-500 to-blue-500 text-white text-xs font-semibold rounded-full">ক্রেডিট-ভিত্তিক</div>
              <div className="absolute inset-0 bg-gradient-to-b from-cyan-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none"></div>

              <div className="relative">
                <div className="h-12 w-12 bg-cyan-500/10 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-cyan-500/30 transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>

              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-cyan-300 transition-colors duration-300">Pay-As-You-Go</h3>
              <div className="text-sm text-gray-400 mb-6">অল্প ব্যবহারকারীদের জন্য</div>

              <div className="text-4xl font-bold text-white mb-3 flex items-center justify-center">
                <div className="flex flex-col items-center">
                  <div className="flex items-center">
                    <span className="text-2xl mr-1">৳</span>২০০
                    <span className="text-xl text-cyan-400 ml-2">=</span>
                  </div>
                  <div className="h-px w-full bg-gradient-to-r from-transparent via-cyan-500 to-transparent my-2"></div>
                  <div className="flex items-center">
                    <span className="text-5xl text-cyan-400 font-bold animate-pulse relative">15
                      <span className="absolute inset-0 blur-md bg-cyan-400/20 -z-10 rounded-full"></span>
                    </span>
                    <span className="text-lg text-cyan-400 ml-2">জেনারেশন</span>
                  </div>
                </div>
              </div>
              <div className="text-sm text-gray-400 mb-2">মাসিক কোন চার্জ নেই</div>
              <div className="text-xs text-cyan-400 mb-6">শুধু যা দরকার তার জন্য পেমেন্ট করুন!</div>

              <ul className="text-gray-400 space-y-3 mb-8 text-left">
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>মাসিক সাবস্ক্রিপশন নেই</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span><strong>২০</strong> টি ছবি সংরক্ষণ</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>
                    <strong className="text-white">৳২০০</strong> =
                    <span className="text-cyan-400 font-bold text-lg relative inline-block px-1">
                      ১৫ জেনারেশন
                      <span className="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-to-r from-transparent via-cyan-400 to-transparent"></span>
                    </span>
                  </span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span><strong>৫</strong> টি কাস্টম প্রম্পট</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>ছবি ডিলিট করতে ২৪ ঘন্টা সময় লাগবে</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>এআই-পাওয়ার্ড এসইও অপটিমাইজেশন</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>স্ট্যান্ডার্ড জেনারেশন স্পিড</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>ক্রেডিট কখনো মেয়াদ শেষ হয় না</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>বেসিক সাপোর্ট</span>
                </li>
              </ul>

              <Link to="/auth?mode=signup">
                <Button
                  className="w-full bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 shadow-lg shadow-cyan-500/20 hover:shadow-cyan-500/40 transition-all duration-300"
                >
                  ক্রেডিট কিনুন
                </Button>
              </Link>
            </div>

            {/* Pro Plan - Most Popular */}
            <div className="group relative bg-gradient-to-b from-purple-500/10 to-pink-500/10 rounded-xl border border-purple-500/30 p-6 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/20 hover:-translate-y-1">
              <div className="absolute -top-4 left-0 right-0 mx-auto w-max px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-semibold rounded-full">MOST POPULAR</div>

              <div className="absolute inset-0 bg-gradient-to-b from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none"></div>

              <div className="h-12 w-12 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-purple-500/30 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
              </div>

              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-purple-300 transition-colors duration-300">Pro</h3>
              <div className="text-sm text-gray-400 mb-6">বর্ধনশীল ব্যবসার জন্য</div>

              <div className="text-4xl font-bold text-white mb-1 flex items-center justify-center">
                <span className="text-2xl mr-1">৳</span>{isYearlyBilling ? '৯৬০০' : '১০০০'}
              </div>
              <div className="text-sm text-gray-400 mb-6">প্রতি {isYearlyBilling ? 'বছর' : 'মাস'}{isYearlyBilling && <span className="ml-1 text-green-400">(20% ছাড়)</span>}</div>

              <ul className="text-gray-300 space-y-3 mb-8 text-left">
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span><strong>৫০</strong> টি ছবি সংরক্ষণ</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>প্রতি মাসে <strong>১৫০</strong> টি জেনারেশন</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span><strong>১৫০</strong> টি সেভ করা ডাটা</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span><strong>১০</strong> টি কাস্টম প্রম্পট</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>তাৎক্ষণিক ছবি ডিলিট</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>অতি দ্রুত জেনারেশন</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>উন্নত এআই-পাওয়ার্ড এসইও অপটিমাইজেশন</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>অগ্রাধিকার সাপোর্ট</span>
                </li>
              </ul>

              <Link to="/auth?mode=signup">
                <Button
                  className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 shadow-lg shadow-purple-500/20 hover:shadow-purple-500/40 transition-all duration-300"
                >
                  প্রো-তে আপগ্রেড করুন
                </Button>
              </Link>
            </div>

            {/* Enterprise Plan */}
            <div className="group relative bg-gray-800 rounded-xl border border-gray-700 p-6 transition-all duration-300 hover:border-pink-500/50 hover:shadow-lg hover:shadow-pink-500/10 hover:-translate-y-1">
              <div className="absolute inset-0 bg-gradient-to-b from-pink-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none"></div>

              <div className="h-12 w-12 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-pink-500/20 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-pink-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>

              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-pink-300 transition-colors duration-300">Enterprise</h3>
              <div className="text-sm text-gray-400 mb-6">বড় প্রতিষ্ঠানের জন্য</div>

              <div className="text-4xl font-bold text-white mb-1 flex items-center justify-center">
                <span className="text-2xl mr-1">৳</span>{isYearlyBilling ? '৯৬০০০' : '১০০০০'}
              </div>
              <div className="text-sm text-gray-400 mb-6">প্রতি {isYearlyBilling ? 'বছর' : 'মাস'}{isYearlyBilling && <span className="ml-1 text-green-400">(20% ছাড়)</span>}</div>

              <ul className="text-gray-400 space-y-3 mb-8 text-left">
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span><strong>১০</strong> জন টিম মেম্বার</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>প্রতি মেম্বার <strong>৫০</strong> টি ছবি সংরক্ষণ</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>প্রতি মেম্বার মাসিক <strong>২০০</strong> টি জেনারেশন</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>প্রতি মেম্বার <strong>২০০</strong> টি সেভ করা প্রম্পট</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>প্রতি মেম্বার <strong>১০</strong> টি কাস্টম প্রম্পট</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>তাৎক্ষণিক ছবি ডিলিট</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>টিম ম্যানেজমেন্ট ড্যাশবোর্ড</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>ডেডিকেটেড সাপোর্ট ম্যানেজার</span>
                </li>
              </ul>

              <Link to="/auth?mode=signup">
                <Button
                  className="w-full group-hover:bg-gradient-to-r group-hover:from-pink-500 group-hover:to-purple-500 transition-all duration-300"
                >
                  এন্টারপ্রাইজে আপগ্রেড করুন
                </Button>
              </Link>
            </div>
          </div>

          <div className="mt-16 max-w-3xl mx-auto bg-gray-800/50 rounded-xl p-6 border border-gray-700">
            <h3 className="text-xl font-semibold text-white mb-4">আপনার জন্য আলাদা কিছু দরকার?</h3>
            <p className="text-gray-300 mb-6">আপনার প্রয়োজন অনুযায়ী প্যাকেজ বানাতে আমাদের সেলস টিমের সাথে কথা বলুন।</p>
            <Button
              variant="outline"
              className="bg-transparent border-purple-500/50 text-purple-300 hover:bg-purple-500/10 transition-all duration-300"
              onClick={() => window.location.href = "mailto:<EMAIL>"}
            >
              সেলস টিমের সাথে যোগাযোগ করুন
            </Button>
          </div>
        </div>
      </section>

      {/* Payment Banner */}
      <section className="py-10 px-4">
        <div className="container mx-auto text-center">
          <img
            src="https://eiijjilntzgicgyhfbge.supabase.co/storage/v1/object/public/images/01/Payment_Banner_5.png"
            alt="Payment Methods"
            className="max-w-full h-auto mx-auto rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
          />
        </div>
      </section>

      {/* Moneyback Guarantee */}
      <section className="py-20 px-4 bg-gradient-to-b from-gray-800 via-gray-900 to-gray-800 relative overflow-hidden">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center text-white mb-8">
            <span className="relative">
            আমাদের <span className="bg-gradient-to-r from-green-400 via-emerald-500 to-teal-500 bg-clip-text text-transparent relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-full after:bg-gradient-to-r after:from-green-400 after:via-emerald-500 after:to-teal-500 after:scale-x-0 after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left">৩০ দিনের মানিব্যাক</span> <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-full after:bg-gradient-to-r after:from-purple-400 after:via-pink-500 after:to-red-500 after:scale-x-0 after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left">গ্যারান্টি</span>
            </span>
          </h2>
          <p className="text-gray-300 text-center mb-12 text-xl max-w-3xl mx-auto">
          আমাদের কোনো প্রশ্ন ছাড়াই রিফান্ড নীতির সাথে eComEasy AI ঝুঁকিমুক্তভাবে ব্যবহার করে দেখুন।
          </p>

          <div className="max-w-5xl mx-auto bg-gradient-to-r from-green-500/10 to-purple-500/10 p-8 rounded-xl border border-green-500/20 mb-8 transform hover:scale-[1.01] transition-all duration-300">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="text-2xl font-semibold text-white mb-4">১০০% সন্তুষ্টি নিশ্চিত</h3>
                <p className="text-gray-300 mb-6">
                আমরা জানি, আমাদের এআই দিয়ে তৈরি পণ্যের বর্ণনা আপনার খুব ভালো লাগবে। তাই নতুন সাবস্ক্রিপশনে ৩০ দিনের টাকা ফেরত গ্যারান্টি দিচ্ছি।
                </p>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="h-6 w-6 bg-green-500/20 rounded-full flex items-center justify-center mr-3 mt-1">
                      <div className="h-3 w-3 bg-green-500 rounded-full"></div>
                    </div>
                    <p className="text-gray-300">৩০ দিনের মধ্যে কোনো প্রশ্ন ছাড়াই রিফান্ড</p>
                  </div>
                  <div className="flex items-start">
                    <div className="h-6 w-6 bg-green-500/20 rounded-full flex items-center justify-center mr-3 mt-1">
                      <div className="h-3 w-3 bg-green-500 rounded-full"></div>
                    </div>
                    <p className="text-gray-300">৫-১০ কার্যদিবসের মধ্যে দ্রুত প্রক্রিয়াকরণ</p>
                  </div>
                  <div className="flex items-start">
                    <div className="h-6 w-6 bg-green-500/20 rounded-full flex items-center justify-center mr-3 mt-1">
                      <div className="h-3 w-3 bg-green-500 rounded-full"></div>
                    </div>
                    <p className="text-gray-300">টাকা রিফান্ডে সহায়তা করার জন্য আমাদের টিম সবসময় প্রস্তুত</p>
                  </div>
                </div>
                <div className="mt-8">
                  <Link to="/footer/refund" className="text-green-400 hover:text-green-300 inline-flex items-center">
                  সম্পূর্ণ রিফান্ড নীতি দেখুন
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>
              <div className="relative">
                <div className="absolute inset-0 opacity-20 bg-gradient-to-r from-green-500 to-purple-500 blur-xl rounded-full"></div>
                <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative z-10">
                  <div className="flex justify-center mb-6">
                    <div className="h-20 w-20 bg-gradient-to-r from-green-500 to-purple-500 rounded-full flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                  <h4 className="text-xl font-semibold text-white text-center mb-4">আমাদের রিফান্ড প্রক্রিয়া যেভাবে কাজ করে</h4>
                  <ol className="space-y-4">
                    <li className="flex">
                      <span className="bg-gray-700 h-6 w-6 rounded-full flex items-center justify-center text-sm text-white font-medium mr-3">1</span>
                      <p className="text-gray-300"><EMAIL>-এ আমাদের সাপোর্ট টিমের সাথে যোগাযোগ করুন।</p>
                    </li>
                    <li className="flex">
                      <span className="bg-gray-700 h-6 w-6 rounded-full flex items-center justify-center text-sm text-white font-medium mr-3">2</span>
                      <p className="text-gray-300">আপনার অ্যাকাউন্টের বিবরণ এবং রিফান্ডের কারণ দিন।
                     </p>
                    </li>
                    <li className="flex">
                      <span className="bg-gray-700 h-6 w-6 rounded-full flex items-center justify-center text-sm text-white font-medium mr-3">3</span>
                      <p className="text-gray-300">২ কার্যদিবসের মধ্যে নিশ্চিতকরণ।
                    </p>
                    </li>
                    <li className="flex">
                      <span className="bg-gray-700 h-6 w-6 rounded-full flex items-center justify-center text-sm text-white font-medium mr-3">4</span>
                      <p className="text-gray-300">যেভাবে টাকা দিয়েছিলেন, সেভাবেই টাকা ফেরত পাবেন।</p>
                    </li>
                  </ol>
                </div>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-6 max-w-5xl mx-auto">
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(34,197,94,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-green-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-green-500/30 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-400 group-hover:text-green-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-green-300 transition-colors">ঝুঁকিহীন বিনিয়োগ</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors">
              সম্পূর্ণ আত্মবিশ্বাসের সাথে আমাদের প্ল্যাটফর্ম ব্যবহার করে দেখুন। যদি দেখেন যে বিক্রি বাড়ছে না আর সময়ও বাঁচছে না, তাহলে আপনার টাকা ফেরত পাবেন।
              </p>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(139,92,246,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-purple-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-purple-500/30 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-400 group-hover:text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-purple-300 transition-colors">সিদ্ধান্ত নেওয়ার জন্য ৩০ দিন</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors">
              সব ফিচার ভালো করে দেখে, আসল ফলাফল বোঝার জন্য পুরো এক মাস সময় নিন।আমাদের গ্যারান্টি আপনাকে প্ল্যাটফর্ম ভালোভাবে পরীক্ষা করার যথেষ্ট সময় দেবে।
              </p>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] hover:shadow-[0_0_30px_rgba(236,72,153,0.3)] transition-all duration-300 ease-out overflow-hidden">
              <div className="h-12 w-12 bg-pink-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:bg-pink-500/30 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-pink-400 group-hover:text-pink-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-pink-300 transition-colors">ঝামেলা-মুক্ত প্রক্রিয়া</h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors">
              কোনো কঠিন ফর্ম বা লম্বা করে কিছু বলার দরকার নেই। আমরা কাস্টমারদের সুবিধার কথা আগে ভাবি, তাই টাকা ফেরত নেওয়ার নিয়ম একদম সহজ।
              </p>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <Link to="/auth?mode=signup">
              <Button
                size="lg"
                className="bg-gradient-to-r from-green-500 to-purple-500 hover:from-green-600 hover:to-purple-600 text-white font-semibold shadow-lg hover:shadow-green-500/25 transition-all duration-300 transform hover:scale-105"
              >
                Start Your Risk-Free Trial Today
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* FAQs */}
      <section className="py-20 px-4 bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center text-white mb-8">
            <span className="relative">
            সাধারণ কিছু <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-full after:bg-gradient-to-r after:from-purple-400 after:via-pink-500 after:to-red-500 after:scale-x-0 after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left">প্রশ্ন ও</span> <span className="bg-gradient-to-r from-blue-400 via-cyan-500 to-teal-500 bg-clip-text text-transparent relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-full after:bg-gradient-to-r after:from-blue-400 after:via-cyan-500 after:to-teal-500 after:scale-x-0 after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left">উত্তর</span>
            </span>
          </h2>
          <p className="text-gray-300 text-center mb-12 text-xl max-w-3xl mx-auto">
          আমাদের এআই প্ল্যাটফর্ম সম্পর্কে আপনার মনে যেসব প্রশ্ন থাকে, সেগুলোর উত্তর এখানে পাবেন।
          </p>

          <div className="max-w-4xl mx-auto space-y-6 mb-12">
            {/* FAQ Accordion Items */}
            {[
              {
                question: "eComEasyAI কীভাবে পণ্যের বর্ণনা লিখে?",
                answer: "আমাদের এআই প্রযুক্তি আপনার পণ্যের ছবি বিশ্লেষণ করে মূল বৈশিষ্ট্য, উপকরণ, রঙ এবং ডিজাইন উপাদান সনাক্ত করে। তারপর এই ভিজ্যুয়াল তথ্যকে আমাদের বিশেষ ই-কমার্স ভাষা মডেলের সাথে একত্রিত করে, যা বিক্রি বাড়ানোর জন্য আকর্ষণীয় এবং নির্ভুল পণ্যের বর্ণনা তৈরি করে।"
              },
              {
                question: "eComEasyAI কোন কোন ভাষা সমর্থন করে?",
                answer: "আমরা বর্তমানে ইংরেজি, স্প্যানিশ, ফ্রেঞ্চ, জার্মান, ইতালীয়, পর্তুগিজ, ডাচ, রাশিয়ান, চীনা (সরলীকৃত এবং ঐতিহ্যবাহী), জাপানি, কোরিয়ান, আরবি এবং আরও অনেক কিছু সহ ৩০ টিরও বেশি ভাষা সমর্থন করি। গ্রাহকের চাহিদার উপর ভিত্তি করে আমরা ক্রমাগত নতুন ভাষা যোগ করছি।"
              },
              {
                question: "এআই-তৈরি বর্ণনাগুলো কতটা নির্ভুল?",
                answer: "আমাদের এআই অনেক অনেক পণ্যের ছবি আর বর্ণনা দেখে শিখেছে, যাতে লেখাগুলো একদম ঠিক হয়। তারপরও, পাবলিশ করার আগে একবার দেখে নেওয়া ভালো। আপনি যখন লেখা এডিট করেন, তখন আমাদের সিস্টেম আরও শেখে, শেখার সাথে সাথে আমাদের সিস্টেম সময়ের সাথে সাথে উন্নত হয়।"
              },
              {
                question: "আমি কি বর্ণনার লেখার ধরণ আর স্টাইল নিজের মতো করে ঠিক করতে পারব?",
                answer: "অবশ্যই পারবেন! আপনি আপনার ব্র্যান্ডের মতো করে লেখার ধরণ ঠিক করতে পারবেন, টোন ঠিক করতে পারবেন (যেমন, প্রফেশনাল, ক্যাজুয়াল, লাক্সারি ইত্যাদি), আর এমনকি আপনার পছন্দের লেখার স্টাইল এর উদাহরণও দিতে পারবেন। আমাদের এআই আপনার ব্র্যান্ডের বিশেষ স্টাইল অনুযায়ী লিখবে।"
              },
              {
                question: "আমি প্রতি মাসে কয়টি ছবি প্রক্রিয়া করতে পারি?",
                answer: "এটি আপনার সাবস্ক্রিপশন পরিকল্পনার উপর নির্ভর করে। আপনার দরকার বাড়লে, আপনি প্ল্যান আপগ্রেড করে নিতে পারবেন।"
              },
              {
                question: "আমার ডেটা কি সুরক্ষিত?",
                answer: "হ্যাঁ, আমরা ডেটা নিরাপত্তাকে খুব গুরুত্ব দিই। আপনার সব ছবি আর লেখা পাঠানোর সময় আর সেভ করে রাখার সময় এনক্রিপ্ট করা থাকে। আমরা আপনার ডেটা অন্য কারো সাথে শেয়ার করি না, আর আমাদের প্ল্যাটফর্মে তৈরি করা সব লেখার মালিক আপনিই।"
              }
            ].map((faq, index) => (
              <div key={index} className="bg-gray-800 rounded-xl border border-gray-700 overflow-hidden group hover:border-purple-500/30 transition-all duration-300 hover:shadow-[0_0_20px_rgba(139,92,246,0.2)]">
                <button
                  className="w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none group"
                  onClick={() => {
                    // This would be handled by state in a real implementation
                    // For demo purposes, we're using CSS for the open/close effect
                  }}
                >
                  <span className="text-lg font-medium text-white group-hover:text-purple-300 transition-colors">{faq.question}</span>
                  <div className="relative">
                    <div className="h-6 w-6 rounded-full bg-gray-700 group-hover:bg-purple-500/20 flex items-center justify-center transition-all duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-400 transform group-hover:rotate-180 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </button>
                <div className="px-6 py-0 max-h-0 overflow-hidden group-hover:max-h-96 group-hover:py-4 group-hover:border-t group-hover:border-gray-700 transition-all duration-500">
                  <p className="text-gray-300">{faq.answer}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center">
            <Link to="/footer/faq" className="inline-block">
              <Button
                size="lg"
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold shadow-lg hover:shadow-purple-500/25 transition-all duration-300 transform hover:scale-105"
              >
                সব প্রশ্নোত্তর দেখুন
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Affiliate */}
      <section
        id="affiliate"
        className="py-20 px-4 bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 scroll-mt-20"
        ref={affiliateSection.ref}
      >
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold text-white mb-8">অ্যাফিলিয়েট হয়ে যান</h2>
          <p className="text-gray-400 text-xl mb-8 max-w-2xl mx-auto">
            আমাদের অ্যাফিলিয়েট প্রোগ্রামে যোগ দিন এবং প্রতিটি রেফারেলে ৩০% পর্যন্ত কমিশন আয় করুন।
          </p>
          <Button size="lg">অ্যাফিলিয়েট প্রোগ্রামে যোগ দিন</Button>
        </div>
      </section>

      {/* Contact */}
      <section
        id="contact"
        className="py-20 px-4 scroll-mt-20"
        ref={contactSection.ref}
      >
        <div className="container mx-auto max-w-2xl">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">যোগাযোগ করুন</h2>
          <div className="bg-gray-800 p-8 rounded-xl border border-gray-700">
            <form className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  নাম
                </label>
                <input
                  type="text"
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  ইমেইল
                </label>
                <input
                  type="email"
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  মেসেজ
                </label>
                <textarea
                  rows={4}
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white"
                ></textarea>
              </div>
              <Button type="submit" className="w-full">মেসেজ পাঠান</Button>
            </form>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 p-12 rounded-2xl border border-gray-800 transform hover:scale-[1.02] transition-all duration-300 hover:shadow-[0_0_50px_rgba(168,85,247,0.2)]">
            <h2 className="text-4xl font-bold text-white mb-6 bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent">
            এআই দিয়ে লেখা পণ্যের বর্ণনার দারুণ সুবিধা আজই নিন!
            </h2>
            <p className="text-gray-300 mb-8 max-w-2xl mx-auto text-lg leading-relaxed">
              <span className="text-purple-400 font-semibold">১০,০০০</span> এরও বেশি সফল ব্যবসায়ী প্রতি সপ্তাহে <span className="text-pink-400 font-semibold">১৫ ঘণ্টারও</span> বেশি সময় বাঁচাচ্ছে, আপনিও তাদের সাথে যোগ দিন। আপনার পণ্যের লিস্টিংগুলো এমনভাবে তৈরি করুন, যাতে বিক্রি অনেক বেড়ে যায়!
            </p>
            <div className="flex flex-col items-center space-y-4">
              <Link to="/auth?mode=signup">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700
                  transform hover:scale-105 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/25
                  animate-pulse hover:animate-none group px-8 py-4"
                >
                  আপনার ফ্রি ট্রায়াল শুরু করুন
                  <Sparkles className="ml-2 h-5 w-5 group-hover:animate-spin" />
                </Button>
              </Link>
              <p className="text-gray-400 text-sm">
                🔥 <span className="text-purple-400 font-medium">সীমিত সময়ের অফার</span> - প্রথম মাসে ৫০% ছাড় পান
              </p>
              <div className="flex items-center space-x-4 mt-4">
                <div className="flex items-center text-gray-300">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  <span>ক্রেডিট কার্ড লাগবে না</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  <span>৩০ দিনের মানিব্যাক</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  <span>২৪/৭ সাপোর্ট</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <canvas
        className="pointer-events-none absolute inset-0 mx-auto z-0"
        id="canvas"
      ></canvas>

      <Footer />

      {/* Country Selection Popup */}
      <CountrySelectionPopup
        isOpen={showCountryPopup}
        onClose={() => setShowCountryPopup(false)}
      />

      {/* Content Modal */}
      <ContentModal
        item={selectedItem}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
}