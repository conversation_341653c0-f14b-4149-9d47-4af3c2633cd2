import React, { useState, Suspense, lazy } from 'react';
import { LucideProps } from 'lucide-react';

// Lazy load the glowing effect only when needed
const GlowingEffect = lazy(() => import('./glowing-effect').then(module => ({ default: module.GlowingEffect })));

interface OptimizedFeatureCardProps {
  icon: React.ComponentType<LucideProps>;
  title: string;
  description: string;
  iconColor: string;
  hoverColor: string;
  shadowColor: string;
}

export const OptimizedFeatureCard: React.FC<OptimizedFeatureCardProps> = ({
  icon: Icon,
  title,
  description,
  iconColor,
  hoverColor,
  shadowColor
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div 
      className={`bg-gray-800 p-6 rounded-xl border border-gray-700 relative group hover:scale-[1.02] transition-all duration-300 ease-out overflow-hidden ${shadowColor}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className={`h-12 w-12 ${iconColor} rounded-lg flex items-center justify-center mb-4 group-hover:${iconColor.replace('/20', '/30')} transition-colors`}>
        <Icon className={`h-6 w-6 ${hoverColor.replace('group-hover:', '')} group-hover:${hoverColor.replace('group-hover:', '')}`} />
      </div>
      
      <h3 className={`text-xl font-semibold text-white mb-2 group-hover:${hoverColor.replace('group-hover:', '')} transition-colors`}>
        {title}
      </h3>
      
      <p className="text-gray-400 group-hover:text-gray-300 transition-colors">
        {description}
      </p>
      
      {/* Only load glowing effect when hovered */}
      {isHovered && (
        <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
          <Suspense fallback={null}>
            <GlowingEffect 
              spread={15} 
              glow={true} 
              disabled={false} 
              proximity={30} 
              inactiveZone={0.01} 
              borderWidth={1} 
            />
          </Suspense>
        </div>
      )}
    </div>
  );
};
