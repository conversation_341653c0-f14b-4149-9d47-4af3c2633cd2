// Debug script to check user data in Firestore
const admin = require('firebase-admin');

// Initialize Firebase Admin
const serviceAccount = require('./functions/service-account-key.json');
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: 'ecomeasyai-v1'
});

const db = admin.firestore();

async function checkUserData() {
  try {
    console.log('🔍 Checking user data in Firestore...');
    
    // The user ID from webhook logs
    const webhookUserId = 'W7OUeQyNURXxe4vbE49qVlWW1lC2';
    
    console.log('📋 Searching for user ID:', webhookUserId);
    
    // Method 1: Check if there's a document with this ID
    console.log('\n1️⃣ Checking document with ID:', webhookUserId);
    const docRef = db.collection('users').doc(webhookUserId);
    const docSnap = await docRef.get();
    
    if (docSnap.exists) {
      console.log('✅ Found document with ID:', webhookUserId);
      console.log('📄 Document data:', docSnap.data());
    } else {
      console.log('❌ No document found with ID:', webhookUserId);
    }
    
    // Method 2: Query by uid field
    console.log('\n2️⃣ Querying by uid field:', webhookUserId);
    const usersRef = db.collection('users');
    const query = usersRef.where('uid', '==', webhookUserId);
    const querySnapshot = await query.get();
    
    if (!querySnapshot.empty) {
      console.log('✅ Found document(s) with uid field:', webhookUserId);
      querySnapshot.forEach(doc => {
        console.log('📄 Document ID:', doc.id);
        console.log('📄 Document data:', doc.data());
      });
    } else {
      console.log('❌ No documents found with uid field:', webhookUserId);
    }
    
    // Method 3: List all users to see what's there
    console.log('\n3️⃣ Listing all users in collection:');
    const allUsersSnapshot = await usersRef.limit(10).get();
    
    if (!allUsersSnapshot.empty) {
      console.log('📋 Found', allUsersSnapshot.size, 'user documents:');
      allUsersSnapshot.forEach(doc => {
        const data = doc.data();
        console.log(`- Document ID: ${doc.id}`);
        console.log(`  uid: ${data.uid}`);
        console.log(`  email: ${data.email}`);
        console.log(`  packageName: ${data.packageName}`);
        console.log(`  subscriptionStatus: ${data.subscriptionStatus}`);
        console.log(`  lastPaymentDate: ${data.lastPaymentDate}`);
        console.log('---');
      });
    } else {
      console.log('❌ No users found in collection');
    }
    
  } catch (error) {
    console.error('❌ Error checking user data:', error);
  }
}

checkUserData().then(() => {
  console.log('✅ Debug complete');
  process.exit(0);
}).catch(error => {
  console.error('❌ Debug failed:', error);
  process.exit(1);
});
