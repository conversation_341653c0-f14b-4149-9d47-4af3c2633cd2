Dear Concern

Thank you for choosing us as your online payment solutions. We have created a merchant account in SSLCOMMERZ. A live account has been provided. Please check below for more information.


Your information at our system
Merchant Id	httpsecomeasyai0live
Merchant Name	Computer Zone
Name	Krishna Kanta karmakar
Mailing Address	College Road
Email	<EMAIL>
Mobile	***********

Technical Support
Email	<EMAIL>
Mobile	+******** 26969
We highly appreciate if you first connect with the testbox and then move to the live system. Please inform us if you need to change Registered Store URL for live.

Briefly, there are only three steps to establish connectivity between Merchant Site and SSLCOMMERZ:

STEP 1: Create a transaction Session with SSLCommerz Session API

Request by HTTP POST Method
POST parameters mentioned in Ready the parameters in developer page.
It will return a JSON response
Customer's Redirection URL mentioned in JSON parameter: GatewayPageURL
STEP 2: Create a IPN (Instant Payment Notification) Listener to receive the successful payment notification

HTTP POST Method
POST Parameters mentioned in Validate Payment with IPN in developer page
Configure the IPN listener in SSLCommerz Merchant Panel (Menu > My Store > IPN Settings )
After receiving the IPN message, please call the SSLCommerz Validation API to check whether the IPN message is valid
Transaction status will be VALID / FAILED / CANCELLED
if risk_level = 1 and transaction status = VALID, then please hold the transaction and verify the customer
Once the Validation API is called, then the status will be returned as VALIDATED
STEP 3: SSLCommerz will redirect the customer to your callback url (Success/failed/cancel URL mention at the time of creating the transaction Session in step 1)



ACCESS CREDENTIALS
Merchant URL	https://merchant.sslcommerz.com/
(User Credentials given in different email)
Merchant Name	Computer Zone
 	
Store Information	
Store Name	httpsecomeasyai
Transaction Session API	https://securepay.sslcommerz.com/gwprocess/v4/api.php
Validation URL (By SOAP)	https://securepay.sslcommerz.com/validator/api/validationserverAPI.php?wsdl
Validation URL (By REST)	https://securepay.sslcommerz.com/validator/api/validationserverAPI.php
 	
Store ID	httpsecomeasyai0live (It is required at redirection and payment validation)
Store Password	6808DA135CE7539998 (It is required at payment validation.Note: No need to change the store password.)
Registered Store URL	Https://ecomeasy.ai
 	
Developer Page	https://developer.sslcommerz.com/