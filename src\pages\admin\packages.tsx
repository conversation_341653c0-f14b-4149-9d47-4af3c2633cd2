import { useState, useEffect } from 'react';
import { db } from '@/lib/firebase';
import { collection, getDocs, doc, addDoc, updateDoc, deleteDoc, query, where, orderBy } from 'firebase/firestore';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Package, Plus, Edit, Trash2, Search, Filter, Tag, Image as ImageIcon, Check, X, ArrowUpDown, Copy, Eye, EyeOff } from 'lucide-react';
import toast from 'react-hot-toast';

// Package feature interface
interface PackageFeature {
  id: string;
  name: string;
  included: boolean;
}

// Package interface
interface Package {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  billingPeriod: 'monthly' | 'yearly' | 'lifetime';
  features: PackageFeature[];
  isPopular: boolean;
  isActive: boolean;
  lemonSqueezyProductId?: string;
  lemonSqueezyVariantId?: string;
  createdAt: string;
  updatedAt: string;
  maxImages?: number;
  maxCaptions?: number;
  trialDays?: number;
  category?: string;
  tags?: string[];
  imageUrl?: string;
}

// Package form interface
interface PackageForm extends Omit<Package, 'id' | 'createdAt' | 'updatedAt'> {}

export function PackageManagement() {
  // State variables
  const [packages, setPackages] = useState<Package[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingPackage, setEditingPackage] = useState<Package | null>(null);
  const [formData, setFormData] = useState<PackageForm>({
    name: '',
    description: '',
    price: 0,
    currency: 'USD',
    billingPeriod: 'monthly',
    features: [],
    isPopular: false,
    isActive: true,
    maxImages: 100,
    maxCaptions: 100,
    trialDays: 0,
    category: 'standard',
    tags: [],
    imageUrl: ''
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBy, setFilterBy] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [newFeature, setNewFeature] = useState('');
  const [newTag, setNewTag] = useState('');
  const [selectedPackages, setSelectedPackages] = useState<string[]>([]);
  const [compareMode, setCompareMode] = useState(false);

  // Categories for packages
  const categories = ['standard', 'premium', 'enterprise', 'custom'];

  // Fetch packages from Firestore
  useEffect(() => {
    const fetchPackages = async () => {
      try {
        const packagesCollection = collection(db, 'packages');
        const packagesSnapshot = await getDocs(packagesCollection);
        const packagesList = packagesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Package[];
        
        setPackages(packagesList);
      } catch (error) {
        console.error('Error fetching packages:', error);
        toast.error('Failed to load packages');
      } finally {
        setLoading(false);
      }
    };

    fetchPackages();
  }, []);

  // Filter and sort packages
  const filteredPackages = packages
    .filter(pkg => {
      // Search filter
      const matchesSearch = 
        pkg.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pkg.description.toLowerCase().includes(searchTerm.toLowerCase());
      
      // Category filter
      const matchesFilter = 
        filterBy === 'all' ||
        (filterBy === 'active' && pkg.isActive) ||
        (filterBy === 'inactive' && !pkg.isActive) ||
        (filterBy === 'popular' && pkg.isPopular) ||
        pkg.category === filterBy;
      
      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      // Sort logic
      let comparison = 0;
      
      if (sortBy === 'name') {
        comparison = a.name.localeCompare(b.name);
      } else if (sortBy === 'price') {
        comparison = a.price - b.price;
      } else if (sortBy === 'createdAt') {
        comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      }
      
      return sortDirection === 'asc' ? comparison : -comparison;
    });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (name === 'price' || name === 'maxImages' || name === 'maxCaptions' || name === 'trialDays') {
      setFormData(prev => ({ ...prev, [name]: parseFloat(value) || 0 }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Add new feature to the package
  const handleAddFeature = () => {
    if (!newFeature.trim()) return;
    
    const feature: PackageFeature = {
      id: Date.now().toString(),
      name: newFeature,
      included: true
    };
    
    setFormData(prev => ({
      ...prev,
      features: [...prev.features, feature]
    }));
    
    setNewFeature('');
  };

  // Remove feature from the package
  const handleRemoveFeature = (id: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter(feature => feature.id !== id)
    }));
  };

  // Toggle feature inclusion
  const handleToggleFeature = (id: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.map(feature => 
        feature.id === id ? { ...feature, included: !feature.included } : feature
      )
    }));
  };

  // Add new tag to the package
  const handleAddTag = () => {
    if (!newTag.trim()) return;
    
    setFormData(prev => ({
      ...prev,
      tags: [...(prev.tags || []), newTag]
    }));
    
    setNewTag('');
  };

  // Remove tag from the package
  const handleRemoveTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: (prev.tags || []).filter(t => t !== tag)
    }));
  };

  // Reset form data
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      price: 0,
      currency: 'USD',
      billingPeriod: 'monthly',
      features: [],
      isPopular: false,
      isActive: true,
      maxImages: 100,
      maxCaptions: 100,
      trialDays: 0,
      category: 'standard',
      tags: [],
      imageUrl: ''
    });
    setEditingPackage(null);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const timestamp = new Date().toISOString();
      
      if (editingPackage) {
        // Update existing package
        const packageRef = doc(db, 'packages', editingPackage.id);
        await updateDoc(packageRef, {
          ...formData,
          updatedAt: timestamp
        });
        
        toast.success('Package updated successfully');
        
        // Update local state
        setPackages(prev => 
          prev.map(pkg => 
            pkg.id === editingPackage.id 
              ? { ...pkg, ...formData, updatedAt: timestamp } 
              : pkg
          )
        );
      } else {
        // Create new package
        const newPackage = {
          ...formData,
          createdAt: timestamp,
          updatedAt: timestamp
        };
        
        const docRef = await addDoc(collection(db, 'packages'), newPackage);
        
        toast.success('Package created successfully');
        
        // Update local state
        setPackages(prev => [...prev, { id: docRef.id, ...newPackage }]);
      }
      
      // Reset form and hide it
      resetForm();
      setShowForm(false);
    } catch (error) {
      console.error('Error saving package:', error);
      toast.error('Failed to save package');
    } finally {
      setLoading(false);
    }
  };

  // Handle package deletion
  const handleDeletePackage = async (id: string) => {
    if (!confirm('Are you sure you want to delete this package?')) return;
    
    setLoading(true);
    
    try {
      await deleteDoc(doc(db, 'packages', id));
      
      toast.success('Package deleted successfully');
      
      // Update local state
      setPackages(prev => prev.filter(pkg => pkg.id !== id));
    } catch (error) {
      console.error('Error deleting package:', error);
      toast.error('Failed to delete package');
    } finally {
      setLoading(false);
    }
  };

  // Handle package editing
  const handleEditPackage = (pkg: Package) => {
    setEditingPackage(pkg);
    setFormData({
      name: pkg.name,
      description: pkg.description,
      price: pkg.price,
      currency: pkg.currency,
      billingPeriod: pkg.billingPeriod,
      features: pkg.features,
      isPopular: pkg.isPopular,
      isActive: pkg.isActive,
      maxImages: pkg.maxImages || 100,
      maxCaptions: pkg.maxCaptions || 100,
      trialDays: pkg.trialDays || 0,
      category: pkg.category || 'standard',
      tags: pkg.tags || [],
      imageUrl: pkg.imageUrl || ''
    });
    setShowForm(true);
  };

  // Handle package duplication
  const handleDuplicatePackage = (pkg: Package) => {
    const duplicatedPackage = {
      ...pkg,
      name: `${pkg.name} (Copy)`,
      isPopular: false
    };
    
    setEditingPackage(null);
    setFormData({
      name: duplicatedPackage.name,
      description: duplicatedPackage.description,
      price: duplicatedPackage.price,
      currency: duplicatedPackage.currency,
      billingPeriod: duplicatedPackage.billingPeriod,
      features: duplicatedPackage.features,
      isPopular: duplicatedPackage.isPopular,
      isActive: duplicatedPackage.isActive,
      maxImages: duplicatedPackage.maxImages || 100,
      maxCaptions: duplicatedPackage.maxCaptions || 100,
      trialDays: duplicatedPackage.trialDays || 0,
      category: duplicatedPackage.category || 'standard',
      tags: duplicatedPackage.tags || [],
      imageUrl: duplicatedPackage.imageUrl || ''
    });
    setShowForm(true);
  };

  // Handle package selection for bulk operations
  const handleSelectPackage = (id: string) => {
    setSelectedPackages(prev => 
      prev.includes(id) 
        ? prev.filter(pkgId => pkgId !== id) 
        : [...prev, id]
    );
  };

  // Handle bulk deletion
  const handleBulkDelete = async () => {
    if (!selectedPackages.length) return;
    if (!confirm(`Are you sure you want to delete ${selectedPackages.length} packages?`)) return;
    
    setLoading(true);
    
    try {
      // Delete each selected package
      for (const id of selectedPackages) {
        await deleteDoc(doc(db, 'packages', id));
      }
      
      toast.success(`${selectedPackages.length} packages deleted successfully`);
      
      // Update local state
      setPackages(prev => prev.filter(pkg => !selectedPackages.includes(pkg.id)));
      setSelectedPackages([]);
    } catch (error) {
      console.error('Error deleting packages:', error);
      toast.error('Failed to delete packages');
    } finally {
      setLoading(false);
    }
  };

  // Handle bulk activation/deactivation
  const handleBulkToggleActive = async (activate: boolean) => {
    if (!selectedPackages.length) return;
    
    setLoading(true);
    
    try {
      const timestamp = new Date().toISOString();
      
      // Update each selected package
      for (const id of selectedPackages) {
        await updateDoc(doc(db, 'packages', id), {
          isActive: activate,
          updatedAt: timestamp
        });
      }
      
      toast.success(`${selectedPackages.length} packages ${activate ? 'activated' : 'deactivated'} successfully`);
      
      // Update local state
      setPackages(prev => 
        prev.map(pkg => 
          selectedPackages.includes(pkg.id) 
            ? { ...pkg, isActive: activate, updatedAt: timestamp } 
            : pkg
        )
      );
      
      setSelectedPackages([]);
    } catch (error) {
      console.error('Error updating packages:', error);
      toast.error('Failed to update packages');
    } finally {
      setLoading(false);
    }
  };

  // Toggle compare mode
  const handleToggleCompareMode = () => {
    setCompareMode(prev => !prev);
    if (!compareMode) {
      setSelectedPackages([]);
    }
  };

  // Handle image upload for package
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // In a real app, you would upload this to storage
    // For now, we'll just use a data URL
    const reader = new FileReader();
    reader.onload = () => {
      setFormData(prev => ({
        ...prev,
        imageUrl: reader.result as string
      }));
    };
    reader.readAsDataURL(file);
  };

  // Get package usage statistics
  const getPackageUsageStats = (packageId: string) => {
    // In a real app, you would fetch this from your database
    // For now, we'll return mock data
    return {
      activeUsers: Math.floor(Math.random() * 100),
      totalRevenue: Math.floor(Math.random() * 10000),
      conversionRate: (Math.random() * 10).toFixed(2) + '%'
    };
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-white">Package Management</h2>
          <p className="text-gray-400 mt-1">Manage your subscription packages</p>
        </div>
        
        <div className="flex space-x-2">
          {selectedPackages.length > 0 && (
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                className="flex items-center space-x-1 bg-red-900/20 border-red-800 text-red-400 hover:bg-red-900/40"
                onClick={handleBulkDelete}
              >
                <Trash2 size={16} />
                <span>Delete ({selectedPackages.length})</span>
              </Button>
              
              <Button 
                variant="outline" 
                size="sm"
                className="flex items-center space-x-1 bg-green-900/20 border-green-800 text-green-400 hover:bg-green-900/40"
                onClick={() => handleBulkToggleActive(true)}
              >
                <Eye size={16} />
                <span>Activate</span>
              </Button>
              
              <Button 
                variant="outline" 
                size="sm"
                className="flex items-center space-x-1 bg-gray-800 border-gray-700 text-gray-400 hover:bg-gray-700"
                onClick={() => handleBulkToggleActive(false)}
              >
                <EyeOff size={16} />
                <span>Deactivate</span>
              </Button>
            </div>
          )}
          
          <Button 
            variant="outline" 
            size="sm"
            className={`flex items-center space-x-1 ${compareMode 
              ? 'bg-purple-900/20 border-purple-800 text-purple-400 hover:bg-purple-900/40' 
              : 'bg-gray-800 border-gray-700 text-gray-400 hover:bg-gray-700'}`}
            onClick={handleToggleCompareMode}
          >
            {compareMode ? <X size={16} /> : <ArrowUpDown size={16} />}
            <span>{compareMode ? 'Exit Compare' : 'Compare'}</span>
          </Button>
          
          <Button 
            variant="default" 
            size="sm"
            className="flex items-center space-x-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
            onClick={() => {
              resetForm();
              setShowForm(true);
            }}
          >
            <Plus size={16} />
            <span>Add Package</span>
          </Button>
        </div>
      </div>
      
      <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" size={18} />
          <Input
            type="text"
            placeholder="Search packages..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-gray-800 border-gray-700 text-white"
          />
        </div>
        
        <div className="flex space-x-2">
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" size={18} />
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value)}
              className="pl-10 pr-8 py-2 bg-gray-800 border border-gray-700 rounded-md text-white appearance-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="all">All Packages</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="popular">Popular</option>
              {categories.map(category => (
                <option key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>
          </div>
          
          <div className="relative">
            <ArrowUpDown className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" size={18} />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="pl-10 pr-8 py-2 bg-gray-800 border border-gray-700 rounded-md text-white appearance-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="name">Name</option>
              <option value="price">Price</option>
              <option value="createdAt">Date Created</option>
            </select>
          </div>
          
          <Button
            variant="outline"
            size="icon"
            className="bg-gray-800 border-gray-700 text-white hover:bg-gray-700"
            onClick={() => setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')}
          >
            {sortDirection === 'asc' ? '↑' : '↓'}
          </Button>
        </div>
      </div>
      
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-white">Loading packages...</div>
        </div>
      ) : compareMode ? (
        // Package comparison view
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPackages
            .filter(pkg => selectedPackages.includes(pkg.id))
            .map(pkg => (
              <Card key={pkg.id} className="bg-gray-800 border-gray-700 text-white overflow-hidden">
                {pkg.imageUrl && (
                  <div className="h-40 overflow-hidden">
                    <img src={pkg.imageUrl} alt={pkg.name} className="w-full h-full object-cover" />
                  </div>
                )}
                
                <CardHeader className="relative pb-2">
                  {pkg.isPopular && (
                    <div className="absolute top-0 right-0 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-xs px-2 py-1 rounded-bl-lg">
                      Popular
                    </div>
                  )}
                  
                  <CardTitle className="text-xl font-bold">{pkg.name}</CardTitle>
                  <div className="flex items-baseline space-x-1">
                    <span className="text-2xl font-bold">${pkg.price}</span>
                    <span className="text-gray-400 text-sm">/{pkg.billingPeriod}</span>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <p className="text-gray-400">{pkg.description}</p>
                  
                  <div className="space-y-2">
                    <h4 className="font-medium">Features:</h4>
                    <ul className="space-y-1">
                      {pkg.features.map(feature => (
                        <li key={feature.id} className="flex items-start space-x-2">
                          {feature.included ? (
                            <Check size={18} className="text-green-500 shrink-0 mt-0.5" />
                          ) : (
                            <X size={18} className="text-red-500 shrink-0 mt-0.5" />
                          )}
                          <span className={feature.included ? 'text-white' : 'text-gray-500 line-through'}>
                            {feature.name}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  {pkg.tags && pkg.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {pkg.tags.map(tag => (
                        <span 
                          key={tag} 
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300"
                        >
                          <Tag size={12} className="mr-1" />
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                  
                  <div className="pt-2 flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
                      onClick={() => handleSelectPackage(pkg.id)}
                    >
                      Remove
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
            
          {selectedPackages.length < 3 && (
            <Card className="bg-gray-800 border-gray-700 border-dashed text-white h-full flex flex-col justify-center items-center p-6">
              <div className="text-center space-y-4">
                <div className="mx-auto w-12 h-12 rounded-full bg-gray-700 flex items-center justify-center">
                  <Plus size={24} className="text-gray-400" />
                </div>
                <div>
                  <h3 className="text-lg font-medium">Add Package to Compare</h3>
                  <p className="text-gray-400 text-sm mt-1">Select packages to compare features and pricing</p>
                </div>
              </div>
            </Card>
          )}
        </div>
      ) : (
        // Regular package list view
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPackages.map(pkg => (
            <Card 
              key={pkg.id} 
              className={`bg-gray-800 border-gray-700 text-white overflow-hidden ${!pkg.isActive ? 'opacity-60' : ''}`}
            >
              {pkg.imageUrl && (
                <div className="h-40 overflow-hidden">
                  <img src={pkg.imageUrl} alt={pkg.name} className="w-full h-full object-cover" />
                </div>
              )}
              
              <CardHeader className="relative pb-2">
                {!pkg.isActive && (
                  <div className="absolute top-0 right-0 bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-bl-lg">
                    Inactive
                  </div>
                )}
                
                {pkg.isActive && pkg.isPopular && (
                  <div className="absolute top-0 right-0 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-xs px-2 py-1 rounded-bl-lg">
                    Popular
                  </div>
                )}
                
                <div className="flex items-center space-x-2">
                  <input 
                    type="checkbox" 
                    checked={selectedPackages.includes(pkg.id)}
                    onChange={() => handleSelectPackage(pkg.id)}
                    className="w-4 h-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500"
                  />
                  <CardTitle className="text-xl font-bold">{pkg.name}</CardTitle>
                </div>
                
                <div className="flex items-baseline space-x-1">
                  <span className="text-2xl font-bold">${pkg.price}</span>
                  <span className="text-gray-400 text-sm">/{pkg.billingPeriod}</span>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <p className="text-gray-400">{pkg.description}</p>
                
                <div className="space-y-2">
                  <h4 className="font-medium">Features:</h4>
                  <ul className="space-y-1">
                    {pkg.features.slice(0, 5).map(feature => (
                      <li key={feature.id} className="flex items-start space-x-2">
                        {feature.included ? (
                          <Check size={18} className="text-green-500 shrink-0 mt-0.5" />
                        ) : (
                          <X size={18} className="text-red-500 shrink-0 mt-0.5" />
                        )}
                        <span className={feature.included ? 'text-white' : 'text-gray-500 line-through'}>
                          {feature.name}
                        </span>
                      </li>
                    ))}
                    {pkg.features.length > 5 && (
                      <li className="text-gray-400 text-sm italic">
                        +{pkg.features.length - 5} more features
                      </li>
                    )}
                  </ul>
                </div>
                
                {pkg.tags && pkg.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {pkg.tags.map(tag => (
                      <span 
                        key={tag} 
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300"
                      >
                        <Tag size={12} className="mr-1" />
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
                
                <div className="pt-2 flex justify-between items-center">
                  <div className="text-xs text-gray-400">
                    {pkg.category && (
                      <span className="capitalize">{pkg.category}</span>
                    )}
                    {pkg.trialDays > 0 && (
                      <span className="ml-2">{pkg.trialDays} days trial</span>
                    )}
                  </div>
                  
                  <div className="flex space-x-1">
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600 h-8 w-8 p-0"
                      onClick={() => handleEditPackage(pkg)}
                    >
                      <Edit size={14} />
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600 h-8 w-8 p-0"
                      onClick={() => handleDuplicatePackage(pkg)}
                    >
                      <Copy size={14} />
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-red-900/20 border-red-800 text-red-400 hover:bg-red-900/40 h-8 w-8 p-0"
                      onClick={() => handleDeletePackage(pkg.id)}
                    >
                      <Trash2 size={14} />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          
          {filteredPackages.length === 0 && (
            <div className="col-span-full text-center py-12">
              <div className="mx-auto w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mb-4">
                <Package size={32} className="text-gray-400" />
              </div>
              <h3 className="text-xl font-medium text-white">No packages found</h3>
              <p className="text-gray-400 mt-2">Try adjusting your search or filter criteria</p>
            </div>
          )}
        </div>
      )}
      
      {/* Package form dialog */}
      {showForm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div 
            className="fixed inset-0 bg-black/50" 
            onClick={() => setShowForm(false)}
          />
          
          <div className="z-50 w-full max-w-3xl overflow-auto rounded-lg bg-gray-800 border border-gray-700 p-6 shadow-xl max-h-[90vh]">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-white">
                {editingPackage ? 'Edit Package' : 'Create New Package'}
              </h2>
              
              <Button
                variant="outline"
                size="sm"
                className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600 h-8 w-8 p-0"
                onClick={() => setShowForm(false)}
              >
                <X size={16} />
              </Button>
            </div>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
                      Package Name *
                    </label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="bg-gray-700 border-gray-600 text-white"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-300 mb-1">
                      Description *
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      required
                      rows={3}
                      className="w-full rounded-md bg-gray-700 border-gray-600 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="price" className="block text-sm font-medium text-gray-300 mb-1">
                        Price *
                      </label>
                      <Input
                        id="price"
                        name="price"
                        type="number"
                        min="0"
                        step="0.01"
                        value={formData.price}
                        onChange={handleInputChange}
                        required
                        className="bg-gray-700 border-gray-600 text-white"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="currency" className="block text-sm font-medium text-gray-300 mb-1">
                        Currency
                      </label>
                      <select
                        id="currency"
                        name="currency"
                        value={formData.currency}
                        onChange={handleInputChange}
                        className="w-full rounded-md bg-gray-700 border-gray-600 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      >
                        <option value="USD">USD</option>
                        <option value="EUR">EUR</option>
                        <option value="GBP">GBP</option>
                        <option value="CAD">CAD</option>
                        <option value="AUD">AUD</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="billingPeriod" className="block text-sm font-medium text-gray-300 mb-1">
                        Billing Period
                      </label>
                      <select
                        id="billingPeriod"
                        name="billingPeriod"
                        value={formData.billingPeriod}
                        onChange={handleInputChange}
                        className="w-full rounded-md bg-gray-700 border-gray-600 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      >
                        <option value="monthly">Monthly</option>
                        <option value="yearly">Yearly</option>
                        <option value="lifetime">Lifetime</option>
                      </select>
                    </div>
                    
                    <div>
                      <label htmlFor="category" className="block text-sm font-medium text-gray-300 mb-1">
                        Category
                      </label>
                      <select
                        id="category"
                        name="category"
                        value={formData.category}
                        onChange={handleInputChange}
                        className="w-full rounded-md bg-gray-700 border-gray-600 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      >
                        {categories.map(category => (
                          <option key={category} value={category}>
                            {category.charAt(0).toUpperCase() + category.slice(1)}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="maxImages" className="block text-sm font-medium text-gray-300 mb-1">
                        Max Images
                      </label>
                      <Input
                        id="maxImages"
                        name="maxImages"
                        type="number"
                        min="0"
                        value={formData.maxImages}
                        onChange={handleInputChange}
                        className="bg-gray-700 border-gray-600 text-white"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="maxCaptions" className="block text-sm font-medium text-gray-300 mb-1">
                        Max Captions
                      </label>
                      <Input
                        id="maxCaptions"
                        name="maxCaptions"
                        type="number"
                        min="0"
                        value={formData.maxCaptions}
                        onChange={handleInputChange}
                        className="bg-gray-700 border-gray-600 text-white"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="trialDays" className="block text-sm font-medium text-gray-300 mb-1">
                      Trial Days
                    </label>
                    <Input
                      id="trialDays"
                      name="trialDays"
                      type="number"
                      min="0"
                      value={formData.trialDays}
                      onChange={handleInputChange}
                      className="bg-gray-700 border-gray-600 text-white"
                    />
                  </div>
                  
                  <div className="flex space-x-4">
                    <div className="flex items-center">
                      <input
                        id="isActive"
                        name="isActive"
                        type="checkbox"
                        checked={formData.isActive}
                        onChange={handleInputChange}
                        className="w-4 h-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500"
                      />
                      <label htmlFor="isActive" className="ml-2 text-sm text-gray-300">
                        Active
                      </label>
                    </div>
                    
                    <div className="flex items-center">
                      <input
                        id="isPopular"
                        name="isPopular"
                        type="checkbox"
                        checked={formData.isPopular}
                        onChange={handleInputChange}
                        className="w-4 h-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500"
                      />
                      <label htmlFor="isPopular" className="ml-2 text-sm text-gray-300">
                        Mark as Popular
                      </label>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">
                      Package Image
                    </label>
                    
                    {formData.imageUrl ? (
                      <div className="relative w-full h-40 rounded-md overflow-hidden mb-2">
                        <img 
                          src={formData.imageUrl} 
                          alt="Package preview"
                          className="w-full h-full object-cover"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          className="absolute top-2 right-2 bg-gray-800/80 border-gray-700 text-white hover:bg-gray-700 h-8 w-8 p-0"
                          onClick={() => setFormData(prev => ({ ...prev, imageUrl: '' }))}
                        >
                          <X size={14} />
                        </Button>
                      </div>
                    ) : (
                      <div className="border-2 border-dashed border-gray-600 rounded-md p-6 text-center">
                        <ImageIcon size={36} className="mx-auto text-gray-500 mb-2" />
                        <p className="text-sm text-gray-400 mb-2">Upload package image</p>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleImageUpload}
                          className="hidden"
                          id="image-upload"
                        />
                        <label htmlFor="image-upload">
                          <Button
                            variant="outline"
                            size="sm"
                            className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
                            type="button"
                          >
                            Browse
                          </Button>
                        </label>
                      </div>
                    )}
                    <p className="text-xs text-gray-300 mt-1">Recommended: 800x400px, max 2MB</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">
                      Features
                    </label>
                    
                    <div className="space-y-3">
                      <div className="flex space-x-2">
                        <Input
                          value={newFeature}
                          onChange={(e) => setNewFeature(e.target.value)}
                          placeholder="Add a feature"
                          className="bg-gray-700 border-gray-600 text-white"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
                          onClick={handleAddFeature}
                        >
                          Add
                        </Button>
                      </div>
                      
                      <div className="max-h-60 overflow-y-auto space-y-2 pr-2">
                        {formData.features.map(feature => (
                          <div key={feature.id} className="flex items-center justify-between bg-gray-700 rounded-md p-2">
                            <div className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                checked={feature.included}
                                onChange={() => handleToggleFeature(feature.id)}
                                className="w-4 h-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500"
                              />
                              <span className={feature.included ? 'text-white' : 'text-gray-400'}>
                                {feature.name}
                              </span>
                            </div>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              className="bg-gray-600 border-gray-500 text-white hover:bg-gray-500 h-7 w-7 p-0"
                              onClick={() => handleRemoveFeature(feature.id)}
                            >
                              <X size={14} />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">
                      Tags
                    </label>
                    
                    <div className="space-y-3">
                      <div className="flex space-x-2">
                        <Input
                          value={newTag}
                          onChange={(e) => setNewTag(e.target.value)}
                          placeholder="Add a tag"
                          className="bg-gray-700 border-gray-600 text-white"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
                          onClick={handleAddTag}
                        >
                          Add
                        </Button>
                      </div>
                      
                      <div className="flex flex-wrap gap-2">
                        {formData.tags?.map(tag => (
                          <span 
                            key={tag} 
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300 group"
                          >
                            <Tag size={12} className="mr-1" />
                            {tag}
                            <button
                              type="button"
                              className="ml-1 text-gray-400 hover:text-gray-200"
                              onClick={() => handleRemoveTag(tag)}
                            >
                              <X size={12} />
                            </button>
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-700">
                <Button
                  type="button"
                  variant="outline"
                  className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
                  onClick={() => setShowForm(false)}
                >
                  Cancel
                </Button>
                
                <Button
                  type="submit"
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
                  disabled={loading}
                >
                  {loading ? 'Saving...' : editingPackage ? 'Update Package' : 'Create Package'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default PackageManagement;