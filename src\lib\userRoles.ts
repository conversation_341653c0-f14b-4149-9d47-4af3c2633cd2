import { db } from './firebase';
import { doc, getDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { getUserPackage, isTeamMember, hasEnterpriseAccess } from './userLimits';

/**
 * Check if a user is an Enterprise Admin Owner (Enterprise-level user who is not a team member)
 * This includes both regular Enterprise users and Enterprise-based custom package users
 */
export const isEnterpriseAdminOwner = async (userId: string): Promise<boolean> => {
  if (!userId) {
    console.error('Invalid userId provided to isEnterpriseAdminOwner');
    return false;
  }

  try {
    // First check if the user has Enterprise-level access (regular Enterprise or Enterprise-based custom package)
    let hasEnterpriseLevel;
    try {
      hasEnterpriseLevel = await hasEnterpriseAccess(userId);
    } catch (enterpriseError) {
      console.error('Error checking Enterprise access:', enterpriseError);
      return false;
    }

    if (!hasEnterpriseLevel) {
      return false;
    }

    // Then check if the user is NOT a team member
    let isUserTeamMember;
    try {
      isUserTeamMember = await isTeamMember(userId);
    } catch (teamMemberError) {
      console.error('Error checking if user is a team member:', teamMemberError);
      return false;
    }

    // User is an Enterprise Admin Owner if they have Enterprise-level access and are not a team member
    const isAdminOwner = !isUserTeamMember;
    console.log('🔍 isEnterpriseAdminOwner result:', {
      userId,
      hasEnterpriseLevel,
      isUserTeamMember,
      isAdminOwner
    });

    return isAdminOwner;
  } catch (error) {
    console.error('Error checking if user is an Enterprise Admin Owner:', error);
    return false;
  }
};
