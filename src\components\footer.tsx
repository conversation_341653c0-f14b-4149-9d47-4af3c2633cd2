import { Link } from 'react-router-dom';
import { Facebook, Twitter, Instagram, Linkedin, Github } from 'lucide-react';

export function Footer() {
  const footerLinks = [
    {
      title: 'Company',
      links: [
        { name: 'About Us', href: '/footer/about' },
        { name: 'Contact Us', href: '/footer/contact' },
        { name: 'Meet the Team', href: '/footer/team' },
        { name: 'We Are Hiring', href: '/footer/careers' },
        { name: 'Mission & Vision', href: '/footer/mission' },
      ],
    },
    {
      title: 'Resources',
      links: [
        { name: 'Why Choose Us', href: '/footer/why-choose-us' },
        { name: 'FAQs', href: '/footer/faq' },
      ],
    },
    {
      title: 'Legal',
      links: [
        { name: 'Terms of Service', href: '/footer/terms' },
        { name: 'Privacy Policy', href: '/footer/privacy' },
        { name: 'Refund Policy', href: '/footer/refund' },
        { name: 'Public Offer Agreement', href: '/footer/offer' },
      ],
    },
  ];

  return (
    <footer className="bg-gray-900 border-t border-gray-800">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div>
            <div className="text-xl font-bold bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent mb-4">
              eComEasyAI
            </div>
            <p className="text-gray-400 mb-4">
              Transform your product images into compelling descriptions with our AI-powered platform.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Facebook size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Twitter size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Instagram size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Linkedin size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Github size={20} />
              </a>
            </div>
          </div>

          {/* Links */}
          {footerLinks.map((column) => (
            <div key={column.title}>
              <h3 className="text-white font-semibold mb-4">{column.title}</h3>
              <ul className="space-y-2">
                {column.links.map((link) => (
                  <li key={link.name}>
                    <Link to={link.href} className="text-gray-400 hover:text-white transition-colors">
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-300 text-sm mb-4 md:mb-0">
            © 2024 - {new Date().getFullYear()} eComEasyAI. All rights reserved. Developed & RND by{' '}
            <a
              href="https://ooffoo.tech"
              target="_blank"
              rel="noopener noreferrer"
              className="font-medium bg-gradient-to-r from-pink-400 to-orange-500 bg-clip-text text-transparent hover:from-pink-500 hover:to-orange-600 transition-all duration-300 transform hover:scale-105"
            >
              OOFFOO TECH
            </a>
            {' '}and{' '}
            <a
              href="https://hkmarketings.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="font-medium bg-gradient-to-r from-pink-400 to-orange-500 bg-clip-text text-transparent hover:from-pink-500 hover:to-orange-600 transition-all duration-300 transform hover:scale-105"
            >
              HKMarketings
            </a>.
          </p>
          <div className="flex space-x-6">
            <Link to="/footer/contact" className="text-gray-300 hover:text-white text-sm transition-colors">
              Contact
            </Link>
            <Link to="/footer/terms" className="text-gray-300 hover:text-white text-sm transition-colors">
              Terms
            </Link>
            <Link to="/footer/privacy" className="text-gray-300 hover:text-white text-sm transition-colors">
              Privacy
            </Link>
            <Link to="/footer/refund" className="text-gray-300 hover:text-white text-sm transition-colors">
              Refunds
            </Link>
            <Link to="/footer/offer" className="text-gray-300 hover:text-white text-sm transition-colors">
              Offer
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}