import { useState, useRef, useEffect } from 'react';
import { Search, ChevronDown, ChevronUp, Check, Globe, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { countries } from '@/lib/countries';
import { useCountry } from '@/lib/countryContext';
import { useOnClickOutside } from '@/hooks/use-on-click-outside';
import { useLanguage } from '@/lib/languageContext';
import { useNavigate, useLocation } from 'react-router-dom';

interface CountrySelectionPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CountrySelectionPopup({ isOpen, onClose }: CountrySelectionPopupProps) {
  const { country, setCountry } = useCountry();
  const { language, toggleLanguage } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedCountry, setSelectedCountry] = useState(country);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Filter countries based on search query
  const filteredCountries = countries.filter(c =>
    c.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Close dropdown when clicking outside
  useOnClickOutside(dropdownRef, () => setIsDropdownOpen(false));

  // Handle save button click
  const handleSave = () => {
    setCountry(selectedCountry);

    // Handle language switching based on country selection
    // Let the language context handle navigation automatically
    if (selectedCountry === 'Bangladesh') {
      // If Bangladesh is selected, switch to Bangla
      // The language context will automatically navigate to /bn
      if (language === 'en') {
        toggleLanguage(); // Switch to Bangla
      }
    } else {
      // For any other country, switch to English
      // The language context will automatically navigate to / if currently on /bn
      if (language === 'bn') {
        toggleLanguage(); // Switch to English
      }
    }

    onClose();
  };

  // Handle cancel button click
  const handleCancel = () => {
    // Default to USD if canceled
    setCountry('United States');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm p-4">
      <div className="relative w-full max-w-md p-6 bg-gray-800 rounded-xl shadow-2xl border border-gray-700 mx-auto my-auto max-h-[90vh] overflow-y-auto">
        {/* Close button */}
        <button
          onClick={handleCancel}
          className="absolute top-4 right-4 p-2 text-gray-400 hover:text-white rounded-full hover:bg-gray-700 transition-colors"
        >
          <X size={20} />
        </button>

        {/* Header */}
        <div className="flex items-center space-x-4 mb-6">
          <div className="p-3 bg-purple-500/20 rounded-full">
            <Globe className="h-6 w-6 text-purple-400" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-white">Select Your Country</h2>
            <p className="text-yellow-300 text-sm font-medium">
              We'll personalize your experience based on your location
            </p>
          </div>
        </div>

        {/* Country Dropdown */}
        <div className="mb-6">
          <label className="text-sm text-gray-400 mb-1 block">
            Country
          </label>
          <div className="relative" ref={dropdownRef}>
            <div
              className="flex items-center justify-between bg-gray-700 border border-gray-600 rounded-md p-2 cursor-pointer"
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            >
              <span className="text-sm text-white">
                {selectedCountry}
              </span>
              {isDropdownOpen ? (
                <ChevronUp className="h-4 w-4 text-gray-400" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-400" />
              )}
            </div>

            {isDropdownOpen && (
              <div className="absolute z-10 mt-1 w-full bg-gray-800 border border-gray-700 rounded-md shadow-lg max-h-60 overflow-y-auto">
                <div className="sticky top-0 bg-gray-800 p-2 border-b border-gray-700">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                    <Input
                      type="text"
                      placeholder="Search countries..."
                      className="pl-8 bg-gray-700 border-gray-600 text-white"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>
                </div>

                <div className="py-1">
                  {filteredCountries.length > 0 ? (
                    filteredCountries.map((countryName) => (
                      <div
                        key={countryName}
                        className={`px-4 py-2 text-sm cursor-pointer flex items-center justify-between ${
                          selectedCountry === countryName
                            ? 'bg-purple-600 text-white'
                            : 'text-gray-200 hover:bg-gray-700'
                        }`}
                        onClick={() => {
                          setSelectedCountry(countryName);
                          setIsDropdownOpen(false);
                          setSearchQuery('');
                        }}
                      >
                        {countryName}
                        {selectedCountry === countryName && (
                          <Check className="h-4 w-4" />
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="px-4 py-2 text-sm text-gray-400">No countries found</div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Information text */}
        <p className="text-gray-400 text-sm mb-6">
          Your selection will be used to display prices in your local currency and improve your checkout experience.
          You can change this setting anytime from your profile.
        </p>

        {/* Buttons */}
        <div className="flex space-x-3">
          <Button
            variant="outline"
            className="flex-1 border-gray-600"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
            onClick={handleSave}
          >
            Save
          </Button>
        </div>
      </div>
    </div>
  );
}
