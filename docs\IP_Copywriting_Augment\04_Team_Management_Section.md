# Team Management Section - Detailed IP Documentation

## Section Overview

The Team Management Section represents a sophisticated enterprise-grade collaboration system that combines advanced role-based access control, innovative invitation mechanisms, and comprehensive team administration capabilities. This section demonstrates significant innovation in enterprise software architecture, security systems, and collaborative workflow design.

## Core Innovations and Proprietary Features

### 1. Advanced Role-Based Access Control System

#### 1.1 Three-Tier User Hierarchy
The application implements a sophisticated role-based access control system:

```typescript
// Comprehensive user role definitions
interface UserRoles {
  'Free User': {
    teamManagement: false;
    inviteGeneration: false;
    memberLimits: false;
  };
  'Team Member': {
    teamManagement: false;
    inviteGeneration: false;
    memberLimits: false;
    accessToOwnerPrompts: true;
  };
  'Enterprise Admin Owner': {
    teamManagement: true;
    inviteGeneration: true;
    memberLimits: true;
    promptSharing: true;
    unlimitedTeamSize: true;
  };
}
```

**Architectural Innovations:**
- Hierarchical permission system with granular control
- Dynamic permission checking throughout the application
- Enterprise-level administrative capabilities
- Secure role verification and enforcement

#### 1.2 Dynamic Permission Enforcement
Real-time permission validation system:

```typescript
// Advanced permission checking with multiple validation layers
const fetchEnterpriseLimits = async () => {
  try {
    // Check for Enterprise-based custom packages
    const packageName = await getUserPackage(currentUser.uid);
    
    if (packageName !== 'Free' && packageName !== 'Pay-As-You-Go' && 
        packageName !== 'Pro' && packageName !== 'Enterprise') {
      const packageType = await getCustomPackageType(packageName);
      
      if (packageType === 'enterprise-based') {
        const customLimits = await getCustomPackageLimits(packageName);
        if (customLimits && 'maxTeamMembers' in customLimits) {
          setMaxTeamMembers(customLimits.maxTeamMembers || 5);
          return;
        }
      }
    }
    
    // Fallback to standard enterprise limits
    const limits = await getEnterpriseLimitSettings();
    setMaxTeamMembers(limits.maxTeamMembers || 5);
  } catch (error) {
    console.error('Error fetching enterprise limits:', error);
    setMaxTeamMembers(5); // Secure default
  }
};
```

### 2. Revolutionary Invitation System

#### 2.1 Token-Based Security Architecture
Advanced secure invitation mechanism:

```typescript
// Sophisticated invitation token generation and management
interface TeamInvite {
  id: string;
  token: string; // UUID-generated secure token
  createdAt: Date;
  expiresAt: Date;
  used: boolean;
  usedBy?: string | string[]; // Support for multiple registrations
  usedAt?: Date | Date[]; // Timestamp tracking
  limits?: TeamMemberLimits; // Custom limit assignment
}

// Secure token-based invitation creation
const generateInviteLink = async () => {
  // Company name validation
  const usersRef = collection(db, 'users');
  const q = query(usersRef, where('uid', '==', user.id));
  const querySnapshot = await getDocs(q);
  
  if (!querySnapshot.empty) {
    const userData = querySnapshot.docs[0].data();
    if (!userData.companyName) {
      toast.error('Please set your Company/Organization name before generating invite links');
      return;
    }
  }
  
  // Advanced limit checking before invitation generation
  const activeInvites = teamInvites.filter(invite =>
    invite.limits?.isMultipleRegistrations || 
    (invite.used !== true && !invite.limits?.isMultipleRegistrations)
  );
  
  if (teamMembers.length + activeInvites.length >= maxTeamMembers) {
    toast.error(`You can only have up to ${maxTeamMembers} team members`);
    return;
  }
  
  setShowLimitsForm(true); // Show custom limits configuration
};
```

**Security Features:**
- UUID-generated secure tokens preventing brute force attacks
- Expiration management with automatic cleanup
- Company validation requirements
- Usage tracking and audit trails
- Multiple registration support with individual tracking

#### 2.2 Custom Limit Assignment System
Granular control over team member permissions:

```typescript
// Advanced team member limit configuration
interface TeamMemberLimits {
  maxImages: number;
  deleteDelayHours: number;
  maxSavedPrompts: number;
  maxCustomPrompts: number;
  monthlyGenerationLimit: number;
  isMultipleRegistrations: boolean; // One-time vs multiple use invitations
}

// Custom limit assignment during invitation generation
const handleGenerateInviteWithLimits = async (limits: TeamMemberLimits) => {
  try {
    const token = await createTeamInvite(
      user.id, 
      user.email, 
      7, // 7-day expiration
      limits, 
      companyName
    );
    
    toast.success('Invite link with custom limits generated successfully');
    setActiveTab('invites'); // Auto-navigate to invites tab
  } catch (error) {
    console.error('Error generating invite link:', error);
    toast.error('Failed to generate invite link');
  }
};
```

### 3. Real-Time Team Management System

#### 3.1 Live Data Synchronization
Advanced real-time team member tracking:

```typescript
// Real-time listener for team invitations with error handling
const fetchTeamInvites = async () => {
  try {
    const invitesRef = collection(db, 'teamInvites');
    const q = query(invitesRef, where('ownerId', '==', user.id));
    
    // Create real-time listener with comprehensive error handling
    const unsubscribe = onSnapshot(q, (snapshot) => {
      try {
        const invites: TeamInvite[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          if (!data || !data.token) {
            console.warn(`Skipping invalid invite document: ${doc.id}`);
            return;
          }
          
          // Strict boolean comparison for 'used' field
          const isUsed = data.used === true;
          
          // Safe date conversion handling
          let createdAt, expiresAt, usedAt;
          try {
            createdAt = data.createdAt ? data.createdAt.toDate() : new Date();
            expiresAt = data.expiresAt ? data.expiresAt.toDate() : 
                       new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
            
            if (data.usedAt) {
              if (Array.isArray(data.usedAt)) {
                usedAt = data.usedAt.map(timestamp => timestamp.toDate());
              } else {
                usedAt = data.usedAt.toDate();
              }
            }
          } catch (dateError) {
            console.error('Error processing dates:', dateError);
            // Fallback to safe defaults
          }
          
          invites.push({
            id: doc.id,
            token: data.token,
            createdAt,
            expiresAt,
            used: isUsed,
            usedBy: data.usedBy,
            usedAt,
            limits: data.limits
          });
        });
        
        setTeamInvites(invites);
        setLoadingInvites(false);
      } catch (snapshotError) {
        console.error('Error processing snapshot:', snapshotError);
        setTeamInvites([]);
      }
    }, (error) => {
      console.error('Error in team invites listener:', error);
      toast.error('Failed to listen for team invite updates');
    });
    
    setUnsubscribeInvites(() => unsubscribe);
  } catch (error) {
    console.error('Error setting up team invites listener:', error);
    setTeamInvites([]);
  }
};
```

#### 3.2 Advanced Team Member Administration
Comprehensive member lifecycle management:

```typescript
// Sophisticated team member removal with data cleanup
const removeTeamMember = async (memberId: string, email: string) => {
  if (!confirm(`Are you sure you want to remove ${email} from your team?`)) {
    return;
  }
  
  try {
    // Remove team member document
    await deleteDoc(doc(db, 'teamMembers', memberId));
    
    // Update user record to revert to Free tier
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('email', '==', email));
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      const userDoc = querySnapshot.docs[0];
      const updateData = {
        packageName: 'Free',
        isTeamMember: false,
        teamOwnerId: null
      };
      await updateDoc(doc(db, 'users', userDoc.id), updateData);
    }
    
    toast.success(`${email} has been removed from your team`);
    fetchTeamMembers(); // Refresh team member list
  } catch (error) {
    console.error('Error removing team member:', error);
    toast.error('Failed to remove team member');
  }
};
```

### 4. Advanced User Interface Design

#### 4.1 Tabbed Interface System
Professional tabbed navigation with state management:

```typescript
// Sophisticated tab system with data refresh on navigation
<Tabs value={activeTab} className="w-full" onValueChange={setActiveTab}>
  <TabsList className="grid w-full grid-cols-2 mb-6">
    <TabsTrigger
      value="members"
      className="text-base"
      onClick={() => {
        fetchEnterpriseLimits();
        fetchTeamMembers();
      }}
    >
      <Users className="mr-2 h-4 w-4" />
      Team Members
    </TabsTrigger>
    <TabsTrigger
      value="invites"
      className="text-base"
      onClick={() => {
        fetchEnterpriseLimits();
        fetchTeamInvites();
      }}
    >
      <LinkIcon className="mr-2 h-4 w-4" />
      Invite Links
    </TabsTrigger>
  </TabsList>
  
  {/* Tab content with advanced features */}
</Tabs>
```

#### 4.2 Comprehensive Invitation Display System
Advanced invitation card system with detailed information:

```typescript
// Sophisticated invitation card with comprehensive metadata
<Card className={`bg-gray-700 border-gray-600 ${
  (isUsed && isOneTimeInvite) || isExpired ? 'opacity-70' : ''
}`}>
  <CardHeader className="pb-2">
    <div className="flex justify-between items-start">
      <div>
        <CardTitle className="text-white text-lg">
          Invite Link {
            invite.limits?.isMultipleRegistrations
              ? (isExpired ? '(Expired)' : '(Active)')
              : (isUsed ? '(Used)' : isExpired ? '(Expired)' : '(Active)')
          }
        </CardTitle>
        <CardDescription>
          Created on {invite.createdAt.toLocaleDateString()}
        </CardDescription>
      </div>
      {/* Delete button */}
    </div>
  </CardHeader>
  
  <CardContent>
    {/* Invitation URL display */}
    <div className="bg-gray-800 p-2 rounded-md text-sm font-mono text-gray-300 mb-2 truncate">
      {inviteUrl}
    </div>
    
    {/* Expiration and usage information */}
    <div className="flex justify-between items-center text-sm text-gray-400">
      <span>
        {invite.limits?.isMultipleRegistrations
          ? <span className="text-green-400">Never expires</span>
          : `Expires: ${invite.expiresAt.toLocaleDateString()}`
        }
      </span>
      {/* Usage tracking display */}
    </div>
    
    {/* Team member limits display */}
    {invite.limits && (
      <div className="mt-2 pt-2 border-t border-gray-600">
        <div className="grid grid-cols-3 gap-x-3 gap-y-1 mt-1">
          <div className="text-xs text-gray-400">
            Max Images: <span className="text-white">{invite.limits.maxImages}</span>
          </div>
          <div className="text-xs text-gray-400">
            Max Saved: <span className="text-white">{invite.limits.maxSavedPrompts}</span>
          </div>
          <div className="text-xs text-gray-400">
            Max Custom: <span className="text-white">{invite.limits.maxCustomPrompts}</span>
          </div>
          {/* Additional limit displays */}
        </div>
      </div>
    )}
  </CardContent>
  
  <CardFooter className="pt-0">
    <div className="flex space-x-2 w-full">
      <Button onClick={() => copyInviteLink(invite.token)}>
        <Copy className="h-4 w-4 mr-2" />
        Copy Link
      </Button>
      <Button onClick={() => window.open(inviteUrl, '_blank')}>
        <LinkIcon className="h-4 w-4 mr-2" />
        Open Link
      </Button>
    </div>
  </CardFooter>
</Card>
```

### 5. Enterprise Features Integration

#### 5.1 Custom Package Support
Advanced support for enterprise-based custom packages:

```typescript
// Enterprise package detection and limit assignment
if (packageName !== 'Free' && packageName !== 'Pay-As-You-Go' && 
    packageName !== 'Pro' && packageName !== 'Enterprise') {
  try {
    const packageType = await getCustomPackageType(packageName);
    
    if (packageType === 'enterprise-based') {
      const customLimits = await getCustomPackageLimits(packageName);
      if (customLimits && 'maxTeamMembers' in customLimits) {
        setMaxTeamMembers(customLimits.maxTeamMembers || 5);
        return;
      }
    }
  } catch (customPackageError) {
    console.error('Error fetching custom package limits:', customPackageError);
  }
}
```

#### 5.2 Prompt Sharing System
Enterprise team prompt collaboration:

```typescript
// Team owner prompt sharing for Enterprise users
if (userData.isTeamMember && userData.teamOwnerId) {
  const ownerPromptsQuery = query(
    collection(db, 'customPrompts'), 
    where('userId', '==', userData.teamOwnerId)
  );
  const ownerSnapshot = await getDocs(ownerPromptsQuery);
  
  // Add owner's prompts with special indicators
  ownerSnapshot.forEach((doc) => {
    const promptData = doc.data();
    prompts.push({
      id: doc.id,
      ...promptData,
      name: `👑 ${promptData.name}`, // Crown indicator
      isOwnerPrompt: true // Prevent editing by team members
    });
  });
}
```

### 6. Security and Data Protection

#### 6.1 Comprehensive Access Control
Multi-layer security validation:

- **Authentication Verification**: User login status checking
- **Role-Based Permissions**: Dynamic permission enforcement
- **Company Validation**: Required company information for invite generation
- **Usage Limit Enforcement**: Real-time limit checking and enforcement
- **Data Isolation**: User-specific data access controls

#### 6.2 Audit Trail and Monitoring
Advanced tracking and monitoring:

- **Invitation Usage Tracking**: Detailed usage logs with timestamps
- **Team Member Activity**: Comprehensive activity monitoring
- **Permission Changes**: Audit trail for permission modifications
- **Error Logging**: Detailed error tracking and reporting

## Technical Implementation Details

### Frontend Architecture
- **React 18.3.1**: Modern React with concurrent features and hooks
- **TypeScript**: Full type safety for enterprise-grade reliability
- **Real-Time Updates**: Live data synchronization with Firestore listeners
- **Professional UI**: Card-based layout with comprehensive information display

### Backend Integration
- **Firebase Firestore**: Scalable NoSQL database with real-time capabilities
- **Security Rules**: Comprehensive data access controls
- **Cloud Functions**: Server-side business logic for invitation processing
- **Authentication**: Secure user verification and role management

### State Management
- **Complex State Logic**: Multi-level state management for team operations
- **Real-Time Synchronization**: Live updates for team changes
- **Error Handling**: Comprehensive error recovery and user feedback
- **Performance Optimization**: Efficient data loading and caching

## Intellectual Property Claims

The Team Management Section represents substantial intellectual property in:

1. **Advanced Role-Based Access Control**: Three-tier hierarchical permission system with dynamic enforcement
2. **Token-Based Invitation System**: Secure UUID-generated invitation mechanism with custom limit assignment
3. **Real-Time Team Synchronization**: Live data updates with comprehensive error handling and recovery
4. **Custom Limit Assignment**: Granular control over team member permissions and usage limits
5. **Enterprise Package Integration**: Advanced support for custom enterprise packages with dynamic limit detection
6. **Comprehensive Audit System**: Detailed tracking and monitoring of team activities and permissions
7. **Professional UI Design**: Card-based interface with comprehensive metadata display and management

This section demonstrates exceptional innovation in enterprise software architecture, security systems, and collaborative workflow design, representing valuable intellectual property suitable for comprehensive copyright protection.
