import { Navigate, Outlet } from 'react-router-dom';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { AdminDashboard } from '@/pages/admin/dashboard';
import { UsersManagement } from '@/pages/admin/users';
import { AdminLogin } from '@/pages/admin/login';
import { PackageManagement } from '@/pages/admin/packages';
import { UserLimitsManagement } from '@/pages/admin/user-limits';
import { PayAsYouGoUserLimitsManagement } from '@/pages/admin/pay-as-you-go-user-limits';
import { ProUserLimitsManagement } from '@/pages/admin/pro-user-limits';
import { EnterpriseAdminOwnerLimitsManagement } from '@/pages/admin/enterprise-admin-owner-limits';
import { CouponCodeManagement } from '@/pages/admin/coupon-codes';
import { CustomLimitSettingsManagement } from '@/pages/admin/custom-limit-settings';

// Protected route component that checks for admin authentication
export const AdminProtectedRoute = () => {
  const isAdminAuthenticated = localStorage.getItem('adminAuthenticated') === 'true';

  if (!isAdminAuthenticated) {
    return <Navigate to="/admin/login" replace />;
  }

  return <Outlet />;
};

// Admin routes configuration
export const adminRoutes = [
  {
    path: '/admin',
    element: <Navigate to="/admin/dashboard" replace />
  },
  {
    path: '/admin/login',
    element: <AdminLogin />
  },
  {
    path: '/admin',
    element: <AdminProtectedRoute />,
    children: [
      {
        path: '',
        element: <AdminLayout />,
        children: [
          {
            path: 'dashboard',
            element: <AdminDashboard />
          },
          {
            path: 'users',
            element: <UsersManagement />
          },
          {
            path: 'pro-user-limits',
            element: <ProUserLimitsManagement />
          },
          {
            path: 'enterprise-admin-owner-limits',
            element: <EnterpriseAdminOwnerLimitsManagement />
          },
          {
            path: 'custom-limit-settings',
            element: <CustomLimitSettingsManagement />
          },
          // Other admin routes can be added here
          {
            path: 'packages',
            element: <PackageManagement />
          },
          {
            path: 'settings',
            element: <div className="p-4 text-white">Payment Settings (Coming Soon)</div>
          },
          {
            path: 'user-limits',
            element: <UserLimitsManagement />
          },
          {
            path: 'pay-as-you-go-user-limits',
            element: <PayAsYouGoUserLimitsManagement />
          },
          {
            path: 'coupon-codes',
            element: <CouponCodeManagement />
          }
        ]
      }
    ]
  }
];