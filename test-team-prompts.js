// Test script to verify Enterprise Admin Owner's custom prompts are shared with team members
const { db } = require('./src/lib/firebase');
const { collection, query, where, getDocs, doc, getDoc } = require('firebase/firestore');

async function testTeamPromptSharing() {
  try {
    // 1. Get a team member user ID
    console.log('Finding a team member...');
    const usersRef = collection(db, 'users');
    const teamMemberQuery = query(usersRef, where('isTeamMember', '==', true));
    const teamMemberSnapshot = await getDocs(teamMemberQuery);
    
    if (teamMemberSnapshot.empty) {
      console.log('No team members found. Please create a team member first.');
      return;
    }
    
    const teamMember = teamMemberSnapshot.docs[0].data();
    const teamMemberId = teamMember.uid;
    const teamOwnerId = teamMember.teamOwnerId;
    
    console.log(`Found team member: ${teamMember.email} (ID: ${teamMemberId})`);
    console.log(`Team owner ID: ${teamOwnerId}`);
    
    // 2. Get the owner's custom prompts
    console.log('\nFetching owner prompts...');
    const ownerPromptsQuery = query(collection(db, 'customPrompts'), where('userId', '==', teamOwnerId));
    const ownerPromptsSnapshot = await getDocs(ownerPromptsQuery);
    
    if (ownerPromptsSnapshot.empty) {
      console.log('No custom prompts found for the team owner. Please create some prompts first.');
      return;
    }
    
    console.log(`Found ${ownerPromptsSnapshot.size} custom prompts from the team owner:`);
    ownerPromptsSnapshot.forEach(doc => {
      const prompt = doc.data();
      console.log(`- ${prompt.name}: ${prompt.text.substring(0, 50)}...`);
    });
    
    // 3. Simulate the loadSavedPrompts function to verify it works
    console.log('\nSimulating loadSavedPrompts function for team member...');
    
    // First, load the team member's own custom prompts
    const memberPromptsQuery = query(collection(db, 'customPrompts'), where('userId', '==', teamMemberId));
    const memberPromptsSnapshot = await getDocs(memberPromptsQuery);
    
    const prompts = [];
    memberPromptsSnapshot.forEach((doc) => {
      prompts.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    console.log(`Found ${prompts.length} custom prompts belonging to the team member`);
    
    // Now, load the owner's custom prompts
    ownerPromptsSnapshot.forEach((doc) => {
      const promptData = doc.data();
      prompts.push({
        id: doc.id,
        ...promptData,
        name: `👑 ${promptData.name}`,
        isOwnerPrompt: true
      });
    });
    
    console.log(`Total prompts after adding owner's prompts: ${prompts.length}`);
    console.log('\nAll prompts that would be displayed to the team member:');
    prompts.forEach(prompt => {
      console.log(`- ${prompt.name} (${prompt.isOwnerPrompt ? 'Owner Prompt' : 'Own Prompt'})`);
    });
    
    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Error during test:', error);
  }
}

testTeamPromptSharing();
