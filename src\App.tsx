import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { Toaster } from '@/components/ui/toaster';
import { AuthForm } from './components/auth/auth-form';
import { Layout } from './components/layout';
import { Upload } from './pages/upload';
import { ImageToCaption } from './pages/image-to-caption';
import { SavedData } from './pages/saved-data';
import { ProfileSettings } from './pages/profile-settings';
import { PaymentHistoryPage } from './pages/payment-history';
import { TeamManagement } from './pages/team-management';
import { TeamInviteRegister } from './pages/team-invite-register';
import { EmailVerificationPage } from './pages/email-verification';
import { LandingPage } from './pages/landing';
import { LandingPage as BanglaLandingPage } from './pages/bn';
import Dashboard from './pages/dashboard';
import { PaddleTestPage } from './pages/paddle-test';
import PaymentSuccessPage from './pages/payment/success';
import PaymentFailedPage from './pages/payment/failed';
import PaymentCanceledPage from './pages/payment/canceled';
import { adminRoutes } from './routes/admin-routes';
import { ProtectedRoute, EnterpriseAdminOwnerRoute } from './routes/protected-routes';
import { PageTransition } from './components/ui/page-transition';
import { PageLoadingAnimation } from './components/ui/page-loading-animation';
import { LanguageProvider } from './lib/languageContext';
import { SubscriptionProvider } from './lib/subscriptionContext';
import { CountryProvider } from './lib/countryContext';
import { AuthProvider } from './lib/authProvider';

// Footer Pages
import { AboutPage } from './pages/footer/about';
import { TeamPage } from './pages/footer/team';
import { CareersPage } from './pages/footer/careers';
import { MissionPage } from './pages/footer/mission';
import { WhyChooseUsPage } from './pages/footer/why-choose-us';
import { FAQPage } from './pages/footer/faq';
import { TermsPage } from './pages/footer/terms';
import { PrivacyPage } from './pages/footer/privacy';
import { RefundPage } from './pages/footer/refund';
import { OfferPage } from './pages/footer/offer';
import { ContactPage } from './pages/footer/contact';

function App() {
  return (
    <HelmetProvider>
      <PageLoadingAnimation>
        <Router>
          <AuthProvider>
            <LanguageProvider>
              <SubscriptionProvider>
                <CountryProvider>                  <div className="min-h-screen bg-gray-900 overflow-y-auto">
                    <Toaster />
                <Routes>
                  <Route path="/" element={<LandingPage />} />
                  <Route path="/bn" element={<BanglaLandingPage />} />
                  <Route path="/auth" element={<AuthForm />} />
                  <Route path="/email-verification" element={<EmailVerificationPage />} />
                  <Route path="/test/paddle" element={<PaddleTestPage />} />
                  <Route path="/team-invite/:token" element={<TeamInviteRegister />} />
                  {/* Add a fallback route for team invites without the leading slash */}
                  <Route path="team-invite/:token" element={<TeamInviteRegister />} />
                  {/* Protected App Routes - Require Authentication */}
                  <Route path="/app" element={<ProtectedRoute />}>
                    <Route element={<Layout />}>
                      <Route index element={<Upload />} />
                      <Route path="dashboard" element={<Dashboard />} />
                      <Route path="caption" element={<ImageToCaption />} />
                      <Route path="saved" element={<SavedData />} />
                      <Route path="profile" element={<ProfileSettings />} />
                    </Route>
                  </Route>

                  {/* Payment History Route - Protected */}
                  <Route path="/payment-history" element={<ProtectedRoute />}>
                    <Route index element={<PageTransition><PaymentHistoryPage /></PageTransition>} />
                  </Route>

                  {/* Enterprise Admin Owner Routes */}
                  <Route path="/app" element={<ProtectedRoute />}>
                    <Route element={<EnterpriseAdminOwnerRoute />}>
                      <Route element={<Layout />}>
                        <Route path="team" element={<TeamManagement />} />
                      </Route>
                    </Route>
                  </Route>

                  {/* Footer Pages */}
                  <Route path="/footer/about" element={<PageTransition><AboutPage /></PageTransition>} />
                  <Route path="/footer/contact" element={<PageTransition><ContactPage /></PageTransition>} />
                  <Route path="/footer/team" element={<PageTransition><TeamPage /></PageTransition>} />
                  <Route path="/footer/careers" element={<PageTransition><CareersPage /></PageTransition>} />
                  <Route path="/footer/mission" element={<PageTransition><MissionPage /></PageTransition>} />
                  <Route path="/footer/why-choose-us" element={<PageTransition><WhyChooseUsPage /></PageTransition>} />
                  <Route path="/footer/faq" element={<PageTransition><FAQPage /></PageTransition>} />
                  <Route path="/footer/terms" element={<PageTransition><TermsPage /></PageTransition>} />
                  <Route path="/footer/privacy" element={<PageTransition><PrivacyPage /></PageTransition>} />
                  <Route path="/footer/refund" element={<PageTransition><RefundPage /></PageTransition>} />
                  <Route path="/footer/offer" element={<PageTransition><OfferPage /></PageTransition>} />

                  {/* Payment Status Routes - Protected */}
                  <Route path="/payment" element={<ProtectedRoute />}>
                    <Route path="success" element={<PageTransition><PaymentSuccessPage /></PageTransition>} />
                    <Route path="failed" element={<PageTransition><PaymentFailedPage /></PageTransition>} />
                    <Route path="canceled" element={<PageTransition><PaymentCanceledPage /></PageTransition>} />
                  </Route>

                  {/* Admin Routes */}
                  {adminRoutes.map((route, index) => (
                    <Route key={index} path={route.path} element={route.element}>
                      {route.children && route.children.map((childRoute, childIndex) => (
                        <Route key={childIndex} path={childRoute.path} element={childRoute.element}>
                          {childRoute.children && childRoute.children.map((grandChildRoute, grandChildIndex) => (
                            <Route key={grandChildIndex} path={grandChildRoute.path} element={grandChildRoute.element} />
                          ))}
                        </Route>
                      ))}
                    </Route>
                  ))}
                  </Routes>
                  </div>
                </CountryProvider>
              </SubscriptionProvider>
            </LanguageProvider>
          </AuthProvider>
        </Router>
      </PageLoadingAnimation>
    </HelmetProvider>
  );
}

export default App;