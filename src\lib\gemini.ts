import { GoogleGenerative<PERSON><PERSON> } from '@google/generative-ai';
import { DEFAULT_PRODUCT_PROMPT } from './promptUtils';

const genAI = new GoogleGenerativeAI(import.meta.env.VITE_GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

// Interface for default prompts
export interface DefaultPrompt {
  id: string;
  name: string;
  text: string;
}

// Interface for categorized prompts
export interface PromptCategory {
  id: string;
  name: string;
  prompts: DefaultPrompt[];
}

// Export the default prompts from promptUtils
export { DEFAULT_PRODUCT_PROMPT };

// Default prompts organized by category
export const DEFAULT_PROMPTS_CATEGORIES: PromptCategory[] = [
  {
    id: 'ecommerce',
    name: 'eCommerce (Feature Based)',
    prompts: [
      {
        id: 'default-1',
        name: '🛍️ Standard Product Description',
        text: DEFAULT_PRODUCT_PROMPT
      },
      {
        id: 'social-caption',
        name: '📱 Social Media Caption',
        text: `Create engaging social media captions for this product. The captions should:

>* Start with a hook to grab attention
>* Include relevant details about the product
>* Use appropriate emojis to enhance engagement
>* Include relevant hashtags
>* End with a clear call to action

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'article-blog',
        name: '📝 Article/Blog Post',
        text: `Write a comprehensive article or blog post about this product. The content should:

>* Start with an engaging introduction
>* Include detailed sections on features, benefits, and specifications
>* Provide honest pros and cons
>* Include user experience and personal insights
>* End with a verdict and recommendation
>* Be well-structured and informative

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'landing-page',
        name: '🌐 Landing Page Description',
        text: `Write content for a product landing page. Ensure the content:

>* Includes a captivating headline and subheadline
>* Highlights the product's unique selling points
>* Uses storytelling to engage the audience
>* Incorporates testimonials or social proof
>* Ends with a clear and persuasive call to action
>* Is structured for easy scanning and readability

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'product-launch',
        name: '📧 Product Launch Email',
        text: `Create a compelling product launch email. The email should:

>* Have an attention-grabbing subject line
>* Start with an exciting announcement
>* Highlight key features and benefits
>* Include pricing and availability information
>* End with a clear call to action
>* Be formatted for easy scanning

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'voice-over',
        name: '🎤 Voice Over Script',
        text: `Write a dynamic and persuasive voiceover script for a product commercial. Ensure the script is:

>* Clear, concise, and engaging
>* Uses storytelling techniques to capture attention
>* Highlights key features and benefits
>* Includes a strong call to action
>* Formatted for easy reading and voiceover delivery
>* Between 30-60 seconds in length

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'press-release',
        name: '📰 Press Release',
        text: `Create a professional press release for this product launch. The press release should:

>* Include a compelling headline and subheadline
>* Start with a strong lead paragraph (who, what, when, where, why)
>* Provide detailed information about the product
>* Include quotes from company leadership
>* Highlight the product's unique features and benefits
>* End with company information and contact details

Output result in [Language] with blending [Tone] tone.`
      }
    ]
  },
  {
    id: 'ecommerce-marketplace',
    name: '🛒 eCommerce (Marketplace Specific)',
    prompts: [
      {
        id: 'marketplace-amazon',
        name: '🛒 Amazon',
        text: `Create a perfect product description for Amazon marketplace. The description should:

>* Follow Amazon's best practices and writing style
>* Include a compelling product title with relevant keywords (max 200 characters)
>* Provide 5-7 bullet points highlighting key features and benefits
>* Write a detailed product description with proper formatting
>* Include relevant search terms and keywords naturally
>* Address potential customer questions and concerns
>* Comply with Amazon's content guidelines
>* Focus on benefits while accurately describing features

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'marketplace-woocommerce',
        name: '🛍️ WooCommerce',
        text: `Create a perfect product description for a WooCommerce store. The description should:

>* Include an SEO-friendly product title
>* Provide a compelling short description (1-2 paragraphs)
>* Write a detailed long description with proper HTML formatting
>* Highlight key features and benefits with bullet points
>* Include technical specifications and product details
>* Add appropriate categories and tags suggestions
>* Incorporate trust signals and social proof elements
>* End with a clear call-to-action

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'marketplace-shopify',
        name: '🏪 Shopify',
        text: `Create a perfect product description for a Shopify store. The description should:

>* Include an attention-grabbing product title
>* Write a compelling product overview (1-2 paragraphs)
>* Highlight 5-7 key features and benefits with bullet points
>* Include technical specifications and product details
>* Use storytelling to create emotional connection
>* Incorporate social proof elements
>* Suggest meta description and SEO elements
>* End with a persuasive call-to-action
>* Follow Shopify's best practices for product pages

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'marketplace-etsy',
        name: '🧶 Etsy',
        text: `Create a perfect product description for Etsy marketplace. The description should:

>* Include a descriptive, keyword-rich title (max 140 characters)
>* Start with a personal, authentic introduction about the product
>* Highlight handmade/unique aspects and creation process
>* Detail materials, dimensions, and specifications
>* Explain the inspiration or story behind the product
>* Include care instructions and shipping information
>* Address potential customer questions
>* Use Etsy's preferred style of personal, conversational tone
>* Suggest relevant tags and categories

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'marketplace-ebay',
        name: '📦 eBay',
        text: `Create a perfect product description for eBay marketplace. The description should:

>* Include a keyword-rich, specific title (max 80 characters)
>* Start with a concise product overview
>* List all specifications, dimensions, and technical details
>* Highlight condition information clearly (new, used, refurbished)
>* Include bullet points of key features and benefits
>* Address shipping, returns, and warranty information
>* Use HTML formatting for better readability
>* Include relevant item specifics for eBay's search algorithm
>* End with seller guarantees and policies

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'marketplace-aliexpress',
        name: '🌐 AliExpress',
        text: `Create a perfect product description for AliExpress marketplace. The description should:

>* Include a keyword-rich product title with specifications
>* Start with a brief product overview
>* Provide detailed specifications and parameters in table format
>* Include high-quality image placement suggestions
>* Highlight 5-7 key features and benefits
>* Address shipping, packaging, and warranty information
>* Include size charts and measurement guides if applicable
>* Use simple, clear language optimized for international buyers
>* End with FAQ section addressing common concerns

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'marketplace-walmart',
        name: '🛒 Walmart Marketplace',
        text: `Create a perfect product description for Walmart Marketplace. The description should:

>* Include a clear, benefit-focused product title (max 200 characters)
>* Start with a concise product overview
>* Provide 3-5 bullet points highlighting key features and benefits
>* Write a detailed product description with proper formatting
>* Include all specifications and technical details
>* Address potential customer questions and concerns
>* Comply with Walmart's content guidelines
>* Focus on value proposition and competitive pricing
>* Include relevant keywords for Walmart's search algorithm

Output result in [Language] with blending [Tone] tone.`
      }
    ]
  },
  {
    id: 'ecommerce-advance',
    name: '🌐 eCommerce Advance',
    prompts: [
      {
        id: 'blank-1',
        name: '🛍️🗣️ Product Description in Local Accent',
        text: `Write a perfect product description according to following Criteria:

>* Detailed Information: Provide comprehensive information about the product, including features, specifications, materials, dimensions, etc.

>* Benefits and Value Proposition: Focus on how the product benefits the customer and solves their problems.

>* Target Audience: Tailor the language and tone to the target audience.

>* Storytelling and Engagement: Use storytelling techniques to engage the reader and create an emotional connection.

>* Hashtags: Incorporate relevant hashtags naturally throughout the description for social media search optimization.

>* Formatting and Readability: Use formatting (bullet points, headings, bold text) to improve readability and scannability.

>* Emotional Appeal: Use emojis and emoticons to add a touch of personality and engagement.

>* Call to Action: Consider including a subtle call to action to encourage purchase.

>* Addressing Customer Concerns: Anticipate and address potential customer questions or concerns.

>* Uniqueness and Originality: Write original descriptions and avoid simply copying manufacturer descriptions.

output result in [Language] using local accent with blending [Tone] tone.`
      },
      {
        id: 'blank-2',
        name: '🎯📢 Product Description for Gen Z',
        text: `Write a perfect product description according to following Criteria:

>* Detailed Information: Provide comprehensive information about the product, including features, specifications, materials, dimensions, etc.

>* Benefits and Value Proposition: Focus on how the product benefits the customer and solves their problems.

>* Target Audience: Tailor the language and tone to the target audience.

>* Storytelling and Engagement: Use storytelling techniques to engage the reader and create an emotional connection.

>* Hashtags: Incorporate relevant hashtags naturally throughout the description for social media search optimization.

>* Formatting and Readability: Use formatting (bullet points, headings, bold text) to improve readability and scannability.

>* Emotional Appeal: Use emojis and emoticons to add a touch of personality and engagement.

>* Call to Action: Consider including a subtle call to action to encourage purchase.

>* Addressing Customer Concerns: Anticipate and address potential customer questions or concerns.

>* Uniqueness and Originality: Write original descriptions and avoid simply copying manufacturer descriptions.

output result in [Language] using Generation Z accent with blending [Tone] tone.`
      },
      {
        id: 'default-2',
        name: '🔍 Pain Point Focus with 5 FAQs',
        text: `Write a product description that focuses on the customer's pain points and provides 5 clear and actionable FAQs. Ensure the description:

>* Identifies the key pain points the product solves
>* Explains how the product addresses these pain points
>* Includes 5 distinct FAQs (Frequently Asked Questions)
>* Uses persuasive and empathetic language
>* Maintains a professional yet engaging tone

Output result in [Language] with blending [Tone].`
      },
      {
        id: 'default-3',
        name: '📋 Product Description with AIDA',
        text: `Write a product description using the AIDA (Attention, Interest, Desire, Action) framework. Ensure the description:

>* Captures attention with a compelling opening
>* Builds interest by highlighting unique features and benefits
>* Creates desire by emphasizing the value and emotional appeal
>* Ends with a strong call to action

Output result in [Language] with blending [Tone].`
      },
      {
        id: 'default-4',
        name: '🌐 Product Landing/Sales Page Content',
        text: `Write content for a product landing or sales page. Ensure the content:

>* Includes a captivating headline and subheadline
>* Highlights the product's unique selling points
>* Uses storytelling to engage the audience
>* Incorporates testimonials or social proof
>* Ends with a clear and persuasive call to action

Output result in [Language] with blending [Tone].`
      },
      {
        id: 'default-5',
        name: '🛒 eCommerce Page Content',
        text: `Write content for an eCommerce product page. Ensure the content:

>* Includes a concise and engaging product title
>* Provides a detailed product description with key features and benefits
>* Highlights any discounts, offers, or promotions
>* Incorporates customer reviews or ratings
>* Ends with a clear call to action (e.g., "Add to Cart," "Buy Now")

Output result in [Language] with blending [Tone].`
      },
      {
        id: 'blank-6',
        name: '📦 Amazon Product Page Content',
        text: `Write content for an Amazon product page. Ensure the content:

>* Includes a compelling product title with relevant keywords
>* Provides a detailed bullet-point list of key features and benefits
>* Highlights the product's unique selling points
>* Incorporates customer testimonials or reviews
>* Ends with a clear call to action to encourage purchase

Output result in [Language] with blending [Tone].`
      }
    ]
  },
  {
    id: 'social-media',
    name: 'Social Media Writing',
    prompts: [
      {
        id: 'facebook-caption',
        name: '👥 Facebook Caption',
        text: `Create an engaging and conversion-focused Facebook caption for this product. The caption should:

>* Begin with a powerful attention-grabbing hook or question
>* Tell a compelling micro-story about the product that resonates with everyday users
>* Include 2-3 paragraphs with proper spacing for readability
>* Highlight 3-5 key benefits or features that solve specific customer problems
>* Incorporate social proof elements (reviews, testimonials, or usage statistics)
>* Use strategic emojis to break up text and enhance emotional connection
>* Include a time-sensitive element or limited availability mention
>* End with a strong, clear call-to-action with sense of urgency
>* Add 3-5 relevant hashtags that balance popularity and specificity
>* Keep overall length between 100-250 words for optimal engagement

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'facebook-marketplace',
        name: '🛒 Facebook Marketplace Listing',
        text: `Create a comprehensive and effective Facebook Marketplace listing for this product. Include:

>* Title: Clear, searchable product name with key specifications (max 100 characters)
>* Price Section: Suggested price point with justification based on market value
>* Condition Section: Detailed description of product condition with specific details
>* Description with storytelling:
  - Opening hook that addresses buyer pain points
  - Comprehensive item details including dimensions, materials, and technical specifications
  - 5+ unique selling points formatted as bullet points
  - Origin and authenticity verification information
  - Usage instructions or versatility highlights
  - Maintenance/care information
  - Warranty or guarantee details if applicable
  - 5 anticipated FAQs with clear answers
  - Shipping/delivery/pickup options
>* Category Recommendations: 3 most relevant marketplace categories for maximum visibility
>* Product Tags: 10-15 trending, comma-separated search tags in English
>* Closing statement with urgency element and contact information

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'instagram-caption',
        name: '📸 Instagram Caption',
        text: `Create a visually-descriptive and engaging Instagram caption for this product. The caption should:

>* Start with a scroll-stopping first line (under 125 characters before the "more" cutoff)
>* Use a storytelling approach that complements visual content
>* Include sensory language that helps viewers imagine using the product
>* Incorporate line breaks strategically for improved readability
>* Balance promotional content with authentic, relatable messaging
>* Use 2-3 relevant emojis placed strategically (not clustered)
>* Include a question or call for engagement to boost comments
>* End with a clear, non-pushy call to action
>* Add 8-10 strategic hashtags mixing branded, niche, and trending options
>* Keep overall length between 125-400 characters (excluding hashtags)

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'twitter-tweet',
        name: '🐦 X/Twitter Tweet',
        text: `Create a concise, impactful X/Twitter post for this product that maximizes engagement. The tweet should:

>* Start with a powerful hook using strong verbs or intriguing questions
>* Communicate a single clear message or benefit (focus on ONE thing only)
>* Use concise, punchy language with active voice
>* Include strategic spacing and line breaks for visual impact
>* Incorporate relevant emojis (1-2 maximum) to enhance message
>* Use 1-2 highly relevant hashtags (preferably trending or industry-specific)
>* Include a clear call to action that encourages specific engagement
>* Consider incorporating numbers, statistics, or percentages when relevant
>* Leave room for image/video attachment context (20-30 characters)
>* Stay under 280 characters total with optimal length between 70-100 characters

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'linkedin-post',
        name: '💼 LinkedIn Post',
        text: `Create a professional and engaging LinkedIn post for this product that resonates with a business audience. The post should:

>* Begin with a compelling professional hook (industry statistic, business challenge, or thought-provoking question)
>* Structure content with strategic line breaks and clear paragraphs (5-7 lines maximum per paragraph)
>* Include industry-specific insights that demonstrate expertise
>* Highlight business benefits focusing on ROI, efficiency, or professional development
>* Incorporate relevant data points or case study elements to build credibility
>* Use a professional yet conversational tone appropriate for LinkedIn's environment
>* Include 1-2 relevant hashtags that industry professionals would follow
>* End with a clear professional call-to-action or thought-provoking question
>* Consider adding a "P.S." with additional value or personal insight
>* Keep overall length between 1200-1500 characters for optimal engagement

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'linkedin-article',
        name: '📄 LinkedIn Article',
        text: `Create a comprehensive LinkedIn article outline for this product that positions it within a broader industry context. The article should:

>* Title: Create an attention-grabbing, benefit-focused headline (60-100 characters)
>* Introduction (150-200 words):
  - Open with an industry challenge or trend relevant to the product
  - Establish your authority/expertise on the subject
  - Present a clear thesis statement about the product's place in solving industry challenges
  - Include a brief overview of what readers will learn
>* Main Body (4-5 sections, 200-300 words each):
  - Section 1: Industry context and problem definition
  - Section 2: Current approaches and their limitations
  - Section 3: Product introduction as a solution (features and specifications)
  - Section 4: Benefits and ROI analysis with specific examples
  - Section 5: Implementation considerations or best practices
>* Conclusion (100-150 words):
  - Summarize key points and reinforce main thesis
  - Provide forward-looking statement about industry impact
  - Include subtle call-to-action for further engagement
>* Additional Elements:
  - 3-5 suggested subheadings for each section
  - 2-3 places to incorporate relevant statistics or data points
  - 3-5 industry-specific hashtags to include at article end

Output result in [Language] with blending [Tone] tone.`
      }
    ]
  },
  {
    id: 'ad-copywriting',
    name: 'SMM Ad Copywriting',
    prompts: [
      {
        id: 'ad-google',
        name: '🔍 Google Ads',
        text: `Create compelling Google Ads copy for this product that follows Google's best practices. Include:

>* Headline 1 (30 characters max) - Include primary keyword and value proposition
>* Headline 2 (30 characters max) - Highlight unique selling point or benefit
>* Headline 3 (30 characters max) - Include call-to-action or secondary benefit
>* Description 1 (90 characters max) - Expand on features and benefits with keywords
>* Description 2 (90 characters max) - Include social proof, urgency, and final CTA
>* URL Path (15 characters each, 2 fields) - Relevant keywords for display URL
>* 3 Responsive Ad Headlines (30 characters each) - For A/B testing
>* 2 Responsive Ad Descriptions (90 characters each) - Alternative messaging

Ensure copy follows these guidelines:
- Include primary and secondary keywords naturally
- Focus on specific benefits rather than generic claims
- Use power words that trigger emotional responses
- Include numbers and statistics when relevant
- Maintain consistent messaging across all elements
- Follow Google's editorial policies (no excessive capitalization, exclamation marks, etc.)

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'ad-facebook',
        name: '📱 Facebook Ads',
        text: `Create engaging Facebook Ad copy for this product that drives conversions. Include:

>* Primary headline (40 characters max) - Attention-grabbing with main benefit
>* Secondary headline (40 characters max) - Supporting information or secondary benefit
>* Ad body text (125-500 characters) - Compelling story with features and benefits
>* News Feed link description (30 characters) - Additional context for the link
>* Call to action button text - Select from Facebook's options (Shop Now, Learn More, etc.)

Ensure the copy follows these Facebook-specific guidelines:
- Start with a hook that stops scrolling
- Use conversational, personalized language that addresses the audience directly
- Include social proof or testimonial elements
- Create a sense of urgency or FOMO (Fear of Missing Out)
- Address pain points and provide clear solutions
- Use emojis strategically to increase engagement
- Ensure copy aligns with visuals for maximum impact
- Follow Facebook's advertising policies (no personal attributes, appropriate language, etc.)

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'ad-instagram',
        name: '� Instagram Ads',
        text: `Create visually-oriented Instagram Ad copy for this product that drives engagement. Include:

>* Caption (2,200 characters max, but first 125 characters most visible)
>* Primary headline (40 characters max) - For Instagram feed ads
>* Secondary headline (40 characters max) - Supporting information
>* Call to action button text - Select from Instagram's options
>* 5-10 relevant hashtags - Mix of popular and niche

Ensure the copy follows these Instagram-specific guidelines:
- Create a visually descriptive narrative that complements images
- Use storytelling techniques that inspire and engage
- Include a strong first line that hooks viewers before "more" button
- Incorporate emojis strategically to enhance visual appeal
- Use line breaks effectively for readability
- Balance promotional content with authentic, relatable messaging
- Include a question or call for engagement (comments, shares)
- Consider carousel ad format with unique copy for each slide
- Follow Instagram's aesthetic best practices (aspirational, authentic, visually-driven)

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'ad-linkedin',
        name: '💼 LinkedIn Ads',
        text: `Create professional LinkedIn Ad copy for this product that resonates with a business audience. Include:

>* Headline (150 characters max) - Professional, benefit-focused
>* Introductory text (600 characters max) - Business context and value proposition
>* Description (100 characters max) - Supporting information
>* Call to action button text - Select from LinkedIn's options

Ensure the copy follows these LinkedIn-specific guidelines:
- Use professional, industry-specific language appropriate for B2B context
- Focus on ROI, efficiency, professional development, or business outcomes
- Include relevant statistics or data points to build credibility
- Address specific business pain points and solutions
- Incorporate industry trends or insights to demonstrate expertise
- Use a more formal tone while maintaining engagement
- Highlight professional benefits rather than emotional appeals
- Consider the decision-maker's position and priorities in the organization
- Follow LinkedIn's professional standards (no hyperbole, appropriate claims)

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'ad-pinterest',
        name: '� Pinterest Ads',
        text: `Create inspirational Pinterest Ad copy for this product that drives saves and clicks. Include:

>* Pin title (100 characters max) - Descriptive and keyword-rich
>* Pin description (500 characters max) - Detailed information with keywords
>* Destination URL headline (40 characters max) - Compelling click incentive
>* Call to action button text - Select from Pinterest's options
>* 3-5 relevant hashtags - Category and trend focused

Ensure the copy follows these Pinterest-specific guidelines:
- Use aspirational, idea-focused language that inspires action
- Include detailed how-to elements or usage scenarios
- Incorporate seasonal or trending themes when relevant
- Focus on lifestyle benefits and visual storytelling
- Use keywords naturally throughout for discoverability
- Create content that's saveable and shareable
- Address the planning/inspiration mindset of Pinterest users
- Consider the longer lifecycle of Pinterest content (evergreen approach)
- Follow Pinterest's creative best practices (positive, inspirational, solution-oriented)

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'ad-reddit',
        name: '🤖 Reddit Ads',
        text: `Create authentic Reddit Ad copy for this product that resonates with Reddit's community-focused audience. Include:

>* Headline (300 characters max) - Direct, authentic, and intriguing
>* Body text (3,000 characters max) - Detailed information in Reddit's conversational style
>* Call to action button text - Select from Reddit's options

Ensure the copy follows these Reddit-specific guidelines:
- Use authentic, transparent language that acknowledges the ad format
- Avoid overly promotional or marketing-speak language
- Include relevant details that pre-empt common questions or objections
- Consider an AMA (Ask Me Anything) style approach where appropriate
- Address the Reddit community directly and respectfully
- Use humor or references that resonate with Reddit culture when appropriate
- Anticipate skepticism and address it proactively
- Focus on genuine value proposition rather than hype
- Consider subreddit-specific language or interests if targeting specific communities
- Follow Reddit's advertising policies (transparent, authentic, non-manipulative)

Output result in [Language] with blending [Tone] tone.`
      }
    ]
  },
  {
    id: 'voice-over',
    name: 'Voice Over Script 2.0',
    prompts: [
      {
        id: 'general-vo',
        name: '🎤 General VO Script',
        text: `Create a professional and versatile voice over script for this product that can be used across multiple platforms. The script should:

>* Begin with a powerful hook that immediately captures attention
>* Use clear, concise language with a conversational flow
>* Highlight 3-5 key features and their direct benefits to the user
>* Incorporate strategic pauses and emphasis points for vocal delivery
>* Include sensory language that helps listeners visualize the product
>* Address common pain points and how this product solves them
>* Build credibility through subtle mentions of quality, research, or testimonials
>* End with a compelling call-to-action that creates urgency
>* Be adaptable for different time lengths (15, 30, and 60 seconds)
>* Include timing markers and delivery notes for the voice talent

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'product-review-vo',
        name: '🔍 Product Review VO Script',
        text: `Create an authentic and detailed product review voice over script. The script should:

>* Open with a personalized introduction establishing credibility
>* Present a balanced overview of the product's purpose and target audience
>* Structure the review in clear sections (unboxing, features, testing, comparison)
>* Describe the physical attributes and quality in vivid detail
>* Document a hands-on testing experience with specific examples
>* Highlight both strengths and limitations with honest assessment
>* Compare with 1-2 similar products in the market
>* Include technical specifications presented in an accessible way
>* Provide a value assessment (price-to-performance ratio)
>* Conclude with a clear recommendation and ideal user profile
>* Include natural transitions between sections
>* Maintain an authentic, unbiased tone throughout

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'ugc-vo',
        name: '📱 UGC VO Script',
        text: `Create an authentic user-generated content (UGC) voice over script that feels genuine and relatable. The script should:

>* Start with a casual, personal greeting that establishes a peer-to-peer connection
>* Use first-person perspective throughout with conversational language
>* Include a brief personal anecdote or pain point that led to discovering this product
>* Describe the initial expectations and first impressions
>* Detail the real-world usage experience with specific examples
>* Highlight unexpected benefits or pleasant surprises discovered through use
>* Address any initial concerns and how they were resolved
>* Include authentic reactions and emotional responses
>* Avoid overly polished marketing language or technical jargon
>* Incorporate natural verbal fillers and speech patterns ("um," "like," "you know")
>* End with a genuine recommendation based on personal experience
>* Include suggestions for camera angles, settings, and authentic filming scenarios

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'problem-solution-vo',
        name: '⚡ Problem-Solution Based Script',
        text: `Create a compelling problem-solution voice over script that resonates with audience pain points. The script should:

>* Open with a vivid description of a relatable problem or frustration
>* Use questions to engage the audience and validate their experience
>* Build emotional connection by acknowledging the impact of the problem
>* Introduce the product as the solution at a strategic moment
>* Explain how specific features directly address each aspect of the problem
>* Include a brief explanation of how the product works
>* Incorporate mini success stories or testimonial elements
>* Address potential objections or skepticism proactively
>* Use contrast between "before" and "after" scenarios
>* Build to an emotional peak highlighting transformation
>* End with a solution-focused call to action
>* Include timing suggestions for problem (30%) vs. solution (70%) portions

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'feature-benefit-vo',
        name: '🔎 Feature-Benefit Based Script',
        text: `Create a detailed feature-benefit voice over script that clearly connects product attributes to user advantages. The script should:

>* Begin with a concise product overview and primary value proposition
>* Structure content around 3-5 key feature-benefit pairs
>* For each feature:
  - Describe the technical or physical attribute clearly
  - Immediately connect to the practical benefit
  - Extend to the emotional or lifestyle benefit
  - Include a brief real-world application example
>* Use transitional phrases to move smoothly between features
>* Incorporate comparative elements that highlight advantages over alternatives
>* Include specific numbers, measurements, or statistics where relevant
>* Use power words that emphasize innovation, efficiency, or quality
>* Maintain a logical progression building toward the most impressive features
>* End with a comprehensive benefit summary and clear call to action
>* Include technical terminology with accessible explanations

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'emotional-lifestyle-vo',
        name: '💫 Emotional/Lifestyle Based Script',
        text: `Create an evocative emotional/lifestyle voice over script that connects the product to aspirational experiences. The script should:

>* Open with an immersive scenario that transports the audience
>* Use sensory-rich language appealing to multiple senses
>* Focus on how the product makes users feel rather than technical specifications
>* Create a narrative arc with emotional progression
>* Incorporate aspirational elements that connect to identity and self-image
>* Use metaphors and analogies to elevate the product experience
>* Include subtle product mentions that feel organic to the story
>* Emphasize transformational aspects and lifestyle enhancement
>* Use musical cue suggestions to enhance emotional impact
>* Incorporate strategic pauses for emotional resonance
>* Balance aspirational content with authentic relatability
>* End with an emotionally compelling call to action
>* Include notes on tone variation and emotional delivery

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'efficiency-accessibility-vo',
        name: '⚙️ Efficiency & Accessibility Based Script',
        text: `Create a practical voice over script focused on efficiency and accessibility benefits. The script should:

>* Begin with a direct acknowledgment of time, effort, or accessibility challenges
>* Use clear, straightforward language avoiding unnecessary jargon
>* Focus on specific metrics: time saved, steps eliminated, or barriers removed
>* Structure content around practical everyday scenarios
>* Include specific comparisons between conventional methods and this solution
>* Highlight universal design elements or adaptability features
>* Incorporate actual time measurements and efficiency statistics
>* Address different user capability levels and how the product accommodates them
>* Use a logical, methodical structure with clear transitions
>* Include brief testimonial elements from diverse user perspectives
>* Emphasize ease of learning, implementation, or integration
>* Conclude with a value-focused summary emphasizing return on investment (time, effort, money)
>* Include accessibility-focused terminology and concepts

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'educational-knowledge-vo',
        name: '🧠 Educational & Knowledge-Based Script',
        text: `Create an informative educational voice over script that positions the product within a broader knowledge context. The script should:

>* Open with an intriguing fact or statistic that establishes the topic's importance
>* Provide brief historical or industry context relevant to the product
>* Structure content in a logical learning progression (basic to advanced)
>* Explain key concepts or terminology before introducing complex features
>* Use analogies or simplified explanations for technical aspects
>* Include "did you know" elements that add depth and interest
>* Connect product features to broader principles or best practices
>* Incorporate expert perspectives or research findings
>* Balance educational content (70%) with product information (30%)
>* Use a teaching tone that empowers rather than talks down
>* Include suggestions for visual aids, diagrams, or demonstrations
>* Conclude with key learnings and how the product applies them
>* Provide resources for further learning or exploration

Output result in [Language] with blending [Tone] tone.`
      }
    ]
  },
  {
    id: 'blog-article',
    name: 'Blog/Article Writing',
    prompts: [
      {
        id: 'product-description-article',
        name: '📋 Product Description Article',
        text: `Create a comprehensive product description article that educates and persuades readers. The article should:

>* Begin with an attention-grabbing headline that includes the product name and primary benefit
>* Open with a compelling hook that identifies the reader's pain point or desire
>* Include an overview paragraph that summarizes the product's purpose and unique value proposition
>* Structure the main content with clear H2 and H3 subheadings for easy scanning
>* Provide detailed specifications and technical information in an easily digestible format (tables or bullet points)
>* Use descriptive, sensory-rich language that helps readers visualize using the product
>* Include 3-5 distinct sections highlighting different aspects (features, benefits, applications, etc.)
>* Incorporate relevant industry terminology to establish authority
>* Address potential objections or questions proactively
>* Use comparison elements to position against alternatives without directly naming competitors
>* Include social proof elements (testimonials, user statistics, awards, certifications)
>* End with a clear summary and subtle call-to-action
>* Optimize for SEO with strategic keyword placement and semantic relevance
>* Be between 1000-1500 words with proper formatting for online readability

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'product-review-article',
        name: '🔍 Product Review Article',
        text: `Create an authentic and comprehensive product review article. The article should:

>* Begin with an engaging headline that includes the product name and review angle
>* Start with a personal introduction explaining why this review matters to readers
>* Include a clear rating system (stars, points, or percentage) with visual elements
>* Provide transparent disclosure about how the product was obtained/tested
>* Structure the review with distinct sections:
  - First impressions and unboxing experience
  - Design and build quality assessment
  - Feature-by-feature detailed analysis
  - Performance testing methodology and results
  - User experience over time (immediate vs. extended use)
  - Comparison with 2-3 similar products or previous versions
>* Include a balanced pros and cons section with at least 3 points each
>* Use a combination of technical analysis and real-world usage scenarios
>* Incorporate high-value buying considerations (price-to-performance ratio, longevity)
>* Address different user personas and how the product meets their specific needs
>* Include a FAQ section addressing common reader questions
>* End with a clear verdict and specific recommendations (who should buy, who should avoid)
>* Be between 1500-2000 words with proper formatting for online readability

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'how-to-guide-article',
        name: '📚 How-To Guide Article',
        text: `Create a detailed, user-friendly how-to guide featuring this product. The article should:

>* Begin with a clear, benefit-focused headline ("How to [Achieve Specific Outcome] with [Product]")
>* Start with a brief introduction explaining what readers will accomplish and why it matters
>* Include a "What You'll Need" section listing all required materials, tools, or prerequisites
>* Provide an estimated time commitment and difficulty level
>* Structure the tutorial with numbered steps, each with its own descriptive subheading
>* Break complex processes into manageable phases or sections
>* Include clear, action-oriented instructions using command verbs
>* Anticipate common mistakes or challenges and provide troubleshooting tips
>* Use callout boxes for important warnings, tips, or alternative approaches
>* Incorporate visual instruction cues (suggested places for images, diagrams, or videos)
>* Highlight product-specific features that simplify or enhance the process
>* Include a "Pro Tips" section with advanced techniques or shortcuts
>* End with a summary of what was accomplished and suggestions for next steps or related projects
>* Be between 1200-1800 words with proper formatting for online readability

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'problem-solution-article',
        name: '⚡ Problem-Solution Article',
        text: `Create a compelling problem-solution article that positions this product as the answer to a specific challenge. The article should:

>* Begin with a headline that clearly identifies the problem and hints at the solution
>* Open with a vivid description of the problem that creates immediate reader recognition
>* Include statistics, research, or expert quotes that validate the problem's significance
>* Structure the article in a clear problem-to-solution narrative arc
>* Thoroughly explore the problem's causes, manifestations, and consequences
>* Discuss conventional or incomplete solutions and their limitations
>* Introduce the product naturally within the narrative as a superior solution
>* Explain specifically how the product's features address each aspect of the problem
>* Include real-world examples, case studies, or scenarios demonstrating the solution in action
>* Address potential objections or implementation challenges
>* Provide a step-by-step implementation guide for using the product to solve the problem
>* Include measurable outcomes or success indicators readers can expect
>* End with an empowering conclusion and clear next steps
>* Be between 1200-1800 words with proper formatting for online readability

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'behind-the-scenes-article',
        name: '🎬 Behind The Scene Article',
        text: `Create an engaging behind-the-scenes article that tells the story of this product's development. The article should:

>* Begin with an intriguing headline that promises exclusive insights
>* Open with a compelling narrative hook that draws readers into the story
>* Structure the article as a journey from concept to finished product
>* Include the origin story - the problem or inspiration that sparked the product idea
>* Introduce key team members with brief backgrounds and their roles in development
>* Detail the research and development process, including challenges and breakthroughs
>* Share interesting anecdotes or pivotal moments that shaped the product
>* Discuss unexpected discoveries or pivots during development
>* Include quotes or perspectives from different team members
>* Reveal proprietary processes or unique approaches (without disclosing trade secrets)
>* Discuss quality control, testing methodologies, and refinement iterations
>* Connect development decisions to customer benefits
>* Include a glimpse into future developments or the product roadmap
>* End with reflections on lessons learned and the team's pride in the final product
>* Be between 1200-1800 words with proper formatting for online readability

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'feature-benefit-article',
        name: '🔎 Feature & Benefit Highlight Article',
        text: `Create a detailed feature-benefit article that thoroughly explains how this product's attributes translate to user advantages. The article should:

>* Begin with a headline that promises specific, valuable benefits
>* Open with a concise overview of the product's primary purpose and value proposition
>* Structure the article around 5-7 key feature-benefit pairs, each with its own section
>* For each feature section:
  - Start with a clear subheading naming the feature
  - Explain the technical or physical attributes in accessible language
  - Connect directly to practical benefits using "which means that..." transitions
  - Extend to emotional or lifestyle benefits
  - Include real-world application examples or use cases
  - Add supporting evidence (data, testimonials, or comparisons)
>* Use a consistent format for each section to aid readability
>* Include comparison elements that highlight advantages over generic alternatives
>* Incorporate specific numbers, measurements, or statistics where relevant
>* Use visual callouts for key specifications or technical details
>* Progress logically from fundamental features to more advanced capabilities
>* End with a comprehensive benefit summary and value statement
>* Be between 1200-1800 words with proper formatting for online readability

Output result in [Language] with blending [Tone] tone.`
      }
    ]
  },
  {
    id: 'email-copywriting',
    name: 'Email Marketing',
    prompts: [
      {
        id: 'new-product-launch-email',
        name: '🚀 New Product Launch Email',
        text: `Create a compelling new product launch email that generates excitement and drives action. The email should:

>* Have an attention-grabbing subject line that creates curiosity or highlights key value proposition
>* Begin with a powerful headline announcing the new product with clear positioning
>* Open with a personalized greeting and an exciting announcement that conveys exclusivity
>* Include a captivating product image placeholder with specific direction on what to showcase
>* Clearly articulate the product's unique value proposition in the first paragraph
>* Structure the body with scannable sections highlighting 3-5 key features and their benefits
>* Use bullet points, bold text, and short paragraphs for easy mobile reading
>* Include specific product details (pricing, availability date, purchasing options)
>* Incorporate social proof elements (early testimonials, expert endorsements, or press mentions)
>* Create urgency with limited-time launch offers, early-bird pricing, or exclusive bonuses
>* Address potential objections or hesitations proactively
>* Include a primary call-to-action button that stands out visually (with specific button text)
>* Add a secondary CTA for those not ready to purchase (learn more, watch demo, etc.)
>* End with a personal sign-off from a real person (founder, product manager, etc.)
>* Include essential footer elements (unsubscribe option, contact information, social links)

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'discount-offer-email',
        name: '💰 Discount or Offer Email',
        text: `Create a persuasive discount or special offer email that drives immediate conversions. The email should:

>* Have a subject line that clearly communicates the value of the offer (specific discount/deal)
>* Begin with a bold, attention-grabbing headline stating the offer with urgency
>* Open with a personalized greeting that acknowledges the customer relationship
>* Clearly state the offer details in the first paragraph (discount percentage, dollar amount, or special bundle)
>* Explain the exact mechanics of how to redeem the offer (coupon code, automatic application, etc.)
>* Include visually prominent offer details with expiration date and any conditions
>* Showcase 2-3 recommended products that pair well with the discount
>* Incorporate product images with original and discounted prices clearly marked
>* Use benefit-focused copy that emphasizes value rather than just price reduction
>* Create genuine urgency with a specific deadline and countdown element
>* Address why you're extending this offer (customer appreciation, seasonal promotion, etc.)
>* Include trust signals (money-back guarantee, customer service availability, etc.)
>* Feature a high-contrast CTA button with action-oriented text ("Save 30% Now", "Claim Your Discount")
>* Add a secondary reminder of the expiration date near the final CTA
>* Include any necessary terms and conditions in smaller text below the main content

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'flash-sale-email',
        name: '⚡ Flash Sale or Limited Time Offer Email',
        text: `Create an urgent and compelling flash sale or limited-time offer email that drives immediate action. The email should:

>* Have a subject line that creates extreme urgency (specific time limit and value proposition)
>* Begin with a bold, time-sensitive headline ("24-Hour Flash Sale", "Weekend Special", etc.)
>* Open with direct, urgent language that emphasizes the limited window of opportunity
>* Include a prominent countdown timer or deadline display (specific hours/minutes remaining)
>* Clearly state the exact duration of the offer with both start and end times/dates
>* Highlight the exclusivity or rarity of the opportunity ("First time ever", "Once a year", etc.)
>* Showcase featured products with striking visuals and clear before/after pricing
>* Use urgent, action-oriented language throughout ("Hurry", "Don't miss out", "Last chance")
>* Incorporate scarcity elements if applicable (limited quantities, selling fast indicators)
>* Include social proof of people taking advantage of previous flash sales
>* Feature multiple high-contrast CTA buttons throughout the email for easy conversion
>* Address FOMO (Fear Of Missing Out) directly in the copy
>* Include a specific reason for the flash sale to increase legitimacy (inventory clearance, anniversary, etc.)
>* End with a final urgent reminder and countdown element
>* Add a P.S. section with an additional incentive or highest-value offer highlight

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'product-update-email',
        name: '🔄 Product Update or Enhancement Email',
        text: `Create an informative and engaging product update or enhancement email that builds value for existing customers. The email should:

>* Have a subject line that clearly communicates the nature of the update and its primary benefit
>* Begin with a personalized greeting that acknowledges the recipient as an existing customer
>* Open with a clear announcement of the update/enhancement and why it matters
>* Include a brief recap of customer feedback or market needs that inspired the changes
>* Structure the body with clear before/after comparisons for each significant change
>* Use visual elements to highlight the differences (comparison charts, screenshots, etc.)
>* Explain each new feature or improvement with specific benefits to the user
>* Include step-by-step instructions or a quick-start guide for accessing new features
>* Incorporate testimonials from beta testers or early access users if available
>* Address any potential learning curve with reassurances and support options
>* Include links to detailed resources (tutorial videos, knowledge base articles, etc.)
>* Acknowledge and thank customers for their continued support and feedback
>* Feature a clear CTA button for immediate exploration of the new features
>* End with an invitation for feedback on the updates
>* Include contact information for support or questions about the new features

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'pre-booking-email',
        name: '🔖 Pre-booking or Early Access Email',
        text: `Create an exclusive and compelling pre-booking or early access email that generates anticipation and secures advance commitments. The email should:

>* Have a subject line that emphasizes exclusivity and limited opportunity
>* Begin with a personalized greeting that makes the recipient feel specially selected
>* Open with language that creates a sense of insider access and privilege
>* Clearly explain why the recipient is receiving this exclusive opportunity (loyal customer, waitlist member, etc.)
>* Include a compelling product description focusing on its most innovative or desirable aspects
>* Highlight the specific benefits of pre-booking or early access (guaranteed availability, special pricing, exclusive bonuses)
>* Use aspirational language that helps recipients envision being the first to experience the product
>* Include high-quality teaser images or videos that reveal just enough to create desire
>* Clearly outline the pre-booking process with specific dates and requirements
>* Address potential hesitations with risk-reducers (refundable deposits, satisfaction guarantees, etc.)
>* Create urgency with limited slots or time-sensitive early bird bonuses
>* Include social proof from product testers, industry experts, or previous launch participants
>* Feature a prominent CTA button with specific action text ("Secure Your Spot", "Pre-Order Now")
>* Clearly state what happens after they pre-book (confirmation process, next communication, delivery timeline)
>* End with a personal note from the creator, founder, or product team

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'content-promotional-email',
        name: '📚 Content-Based Promotional Email',
        text: `Create a value-rich content-based promotional email that educates while subtly selling. The email should:

>* Have a benefit-focused subject line that emphasizes information value rather than selling
>* Begin with a personalized greeting that establishes relevance to the recipient's interests or challenges
>* Open with an intriguing hook, question, or statistic that creates interest in the content
>* Structure the email around a specific educational theme or problem-solution framework
>* Include valuable standalone content that provides immediate value (tips, insights, how-tos)
>* Use a conversational, helpful tone that positions your brand as a trusted advisor
>* Incorporate relevant industry data, expert quotes, or research findings to build credibility
>* Naturally transition from educational content to product relevance without hard selling
>* Include subtle product mentions that demonstrate how it relates to the educational content
>* Use visual elements that enhance understanding (diagrams, charts, or instructional images)
>* Feature content-focused CTAs ("Read the Full Guide", "Watch the Tutorial") alongside product CTAs
>* Include social sharing options for the content elements
>* Offer additional related resources to continue the educational journey
>* End with a thoughtful conclusion that reinforces the main educational takeaway
>* Include a P.S. section with a special offer related to the content theme

Output result in [Language] with blending [Tone] tone.`
      }
    ]
  },
  {
    id: 'sms-im-copywriting',
    name: '📱 SMS & IM Copywriting',
    prompts: [
      {
        id: 'discount-offer-sms',
        name: '💰 Discount/Offer Messages',
        text: `Create compelling and concise SMS/IM discount offer messages that drive immediate action. The messages should:

>* Begin with attention-grabbing opener that mentions the specific discount/offer
>* Use precise numbers and percentages to clearly communicate the value proposition
>* Include a clear timeframe with specific expiration date/time to create urgency
>* Incorporate personalization elements where appropriate (first name, past purchase)
>* Use concise, punchy language optimized for small screens and quick reading
>* Include a clear, tappable call-to-action link with UTM parameters
>* Mention any redemption instructions (coupon code, show message, etc.)
>* Comply with SMS/messaging regulations (opt-out instructions where required)
>* Stay within optimal character limits (160 chars for SMS, 70-100 for messaging apps)
>* Create multiple variations for A/B testing (3-5 versions with different approaches)
>* Include versions for different contexts (abandoned cart, loyalty reward, seasonal promotion)
>* Maintain brand voice while being direct and action-oriented

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'new-product-launch-sms',
        name: '🚀 New Product Launch Messages',
        text: `Create exciting and effective SMS/IM product launch messages that generate immediate interest. The messages should:

>* Start with a powerful announcement phrase that creates excitement
>* Clearly identify the new product with its most compelling unique selling point
>* Use vivid, sensory language to help recipients visualize the product
>* Include one standout feature or benefit that differentiates from competitors
>* Mention any limited-time launch offers or exclusive early-access benefits
>* Create a sense of exclusivity ("You're among the first to know")
>* Include a direct link to the product page with tracking parameters
>* Use emojis strategically to enhance visual appeal without overwhelming
>* Incorporate social proof elements if space allows ("Already loved by X customers")
>* Stay within optimal character limits while maintaining impact
>* Create variations for different customer segments (new vs. loyal customers)
>* End with a clear, compelling call-to-action that creates urgency

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'event-sale-announcement-sms',
        name: '📅 Event/Sale Announcement Messages',
        text: `Create high-converting SMS/IM event or sale announcement messages that drive attendance or participation. The messages should:

>* Begin with a clear event/sale name and primary benefit of attending
>* Include essential details (date, time, location) in the most concise format possible
>* Highlight the most compelling reason to attend or participate
>* Create a sense of exclusivity or limited availability where appropriate
>* Use action verbs that encourage immediate response
>* Include specific details about special offers available only during the event
>* Mention any notable participants, speakers, or special features
>* Incorporate a countdown element to create urgency ("Only 2 days left")
>* Provide a simple, direct way to RSVP, register, or learn more
>* Use appropriate event-specific emojis to enhance visual appeal
>* Stay within optimal character limits while conveying all critical information
>* Create variations for different stages (early announcement, reminder, last chance)

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'exclusive-vip-sms',
        name: '👑 Exclusive Access/VIP Messages',
        text: `Create premium-feeling SMS/IM messages that make recipients feel valued and special. The messages should:

>* Open with personalized recognition of the recipient's VIP or loyalty status
>* Use language that reinforces exclusivity and special treatment
>* Clearly articulate the exclusive benefit or offer not available to general public
>* Include specific details about the exclusive access (early shopping hours, private event, etc.)
>* Create a sense of membership and belonging to an elite group
>* Use sophisticated language and tone appropriate for premium positioning
>* Incorporate gratitude for their continued loyalty or valued relationship
>* Provide clear instructions for how to access the exclusive offer or event
>* Include any necessary identification or verification process
>* Use premium-associated emojis sparingly and strategically
>* Stay within optimal character limits while maintaining the premium feel
>* Create variations for different VIP tiers or loyalty levels

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'customer-engagement-sms',
        name: '💬 Customer Engagement/Feedback Messages',
        text: `Create conversational SMS/IM messages that encourage meaningful customer interaction and feedback. The messages should:

>* Begin with a personalized greeting that references specific customer history
>* Ask a clear, specific question that's easy to respond to
>* Use a conversational, friendly tone that invites honest dialogue
>* Clearly explain the purpose of seeking feedback and how it will be used
>* Keep the initial message short with the option to continue the conversation
>* Include an incentive for participation where appropriate
>* Offer multiple response options (reply, link to survey, etc.)
>* Express genuine appreciation for their time and input
>* Mention the approximate time commitment required (e.g., "2-minute survey")
>* Use appropriate conversational emojis to create a friendly atmosphere
>* Stay within optimal character limits while being engaging
>* Create variations for different feedback purposes (post-purchase, service experience, product development)

Output result in [Language] with blending [Tone] tone.`
      }
    ]
  },
  {
    id: 'print-media-writing',
    name: '🖨️ Print Media Writing',
    prompts: [
      {
        id: 'advertisements',
        name: '📢 Advertisements',
        text: `Create compelling print advertisements that capture attention and drive action. The advertisement should:

>* Include a powerful headline that stops readers in their tracks (max 10 words)
>* Feature a clear, concise subheadline that expands on the main benefit
>* Use persuasive body copy that follows the AIDA formula (Attention, Interest, Desire, Action)
>* Incorporate strategic use of white space for visual impact
>* Include a clear value proposition that differentiates from competitors
>* Feature concise bullet points highlighting 3-5 key benefits or features
>* Use persuasive language that appeals to both rational and emotional motivations
>* Include appropriate imagery suggestions that complement the message
>* Feature a prominent, action-oriented call-to-action
>* Include necessary contact information or website details
>* Maintain appropriate tone for the publication/context
>* Consider placement specifications (full-page, half-page, quarter-page versions)
>* Include any necessary legal disclaimers or fine print

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'brochure',
        name: '📑 Brochure',
        text: `Create comprehensive brochure content that effectively showcases products or services. The brochure should:

>* Include an attention-grabbing cover with compelling headline and visual direction
>* Feature a powerful introduction that establishes brand positioning and value
>* Organize content in a logical flow with clear section headings
>* Use a mix of informative paragraphs and scannable bullet points
>* Include detailed product/service descriptions focusing on benefits and features
>* Incorporate testimonials or case studies strategically throughout
>* Use persuasive language that builds credibility and trust
>* Include technical specifications or comparison charts where relevant
>* Feature pricing information and package options if applicable
>* Include company history and mission statement section
>* Provide clear next steps and multiple contact options
>* End with a compelling call-to-action that encourages immediate response
>* Include appropriate legal information and disclaimers
>* Consider both tri-fold and multi-page format options

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'flyer',
        name: '📃 Flyer',
        text: `Create attention-grabbing flyer content that delivers maximum impact in minimal space. The flyer should:

>* Feature a bold, benefit-focused headline that immediately communicates value (max 8 words)
>* Include a supporting subheadline that clarifies the offer or proposition
>* Use concise, high-impact body copy that emphasizes key selling points
>* Organize information in a clear visual hierarchy for quick scanning
>* Include 3-4 bullet points highlighting the most compelling benefits
>* Feature specific details about any promotions, discounts, or special offers
>* Include essential event information (date, time, location) if applicable
>* Use action-oriented language that creates urgency or excitement
>* Feature a prominent, clear call-to-action with specific direction
>* Include necessary contact information and social media handles
>* Consider both digital distribution and physical handout contexts
>* Optimize for single-page impact with minimal text (250-300 words maximum)
>* Include any time limitations or exclusivity factors to drive response

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'poster',
        name: '🖼️ Poster',
        text: `Create visually striking poster content that commands attention from a distance. The poster should:

>* Feature an ultra-concise headline that can be read and understood in 3 seconds
>* Use a clear typographic hierarchy with varying font sizes for scanning at different distances
>* Include minimal body copy focused only on essential information (50-75 words maximum)
>* Organize content for viewing from top to bottom in a logical sequence
>* Include critical details (date, time, location, price) in a prominent position
>* Feature a clear, specific call-to-action that stands out visually
>* Use powerful, evocative language that creates emotional impact
>* Include necessary contact information or website in an easily readable format
>* Consider viewing context and distance in content recommendations
>* Provide guidance on visual elements that would complement the text
>* Include any sponsor information or logos placement suggestions if applicable
>* Feature any special instructions for QR codes or interactive elements
>* Balance information density with visual impact for maximum effectiveness

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'banner',
        name: '🏷️ Banner',
        text: `Create ultra-concise banner content that delivers maximum impact with minimal text. The banner should:

>* Feature an extremely brief headline optimized for instant comprehension (3-5 words maximum)
>* Focus on a single, powerful message or value proposition
>* Use bold, action-oriented language that creates immediate interest
>* Include only the most essential supporting information
>* Feature a clear, concise call-to-action that can be read at a glance
>* Consider viewing distance and movement (static vs. moving audience) in content design
>* Include only critical contact information (website or simple call/text instruction)
>* Use powerful, evocative words that create emotional resonance
>* Consider both horizontal and vertical orientation options
>* Optimize for various sizes (small, medium, large) with appropriate text density
>* Include any necessary branding elements or taglines
>* Balance brevity with clarity for maximum effectiveness
>* Consider context-specific versions (trade show, retail, outdoor, event)

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'press-release',
        name: '📰 Press Release',
        text: `Create a professional, newsworthy press release that generates media coverage. The press release should:

>* Include a compelling headline that clearly communicates the news value (max 15 words)
>* Feature a concise subheadline that adds essential context or details
>* Begin with a dateline (city, state, date) in proper format
>* Open with a strong lead paragraph covering the 5Ws (who, what, when, where, why)
>* Structure information in inverted pyramid style (most to least important)
>* Include 1-2 relevant quotes from company leadership or subject matter experts
>* Provide necessary background information about the company or product
>* Use objective, journalistic language rather than marketing hype
>* Include relevant statistics, data points, or research findings
>* Maintain third-person perspective throughout
>* End with a standard company boilerplate (about section)
>* Include media contact information with name, phone, email
>* Feature any relevant links to additional resources or media kits
>* Adhere to standard press release formatting and length (400-500 words)

Output result in [Language] with blending [Tone] tone.`
      },
      {
        id: 'advertorials',
        name: '📋 Advertorials',
        text: `Create persuasive advertorial content that blends editorial value with marketing objectives. The advertorial should:

>* Feature a headline that promises valuable information rather than obvious selling
>* Begin with an engaging hook that addresses reader interests or pain points
>* Structure content in a journalistic format with subheadings and logical flow
>* Provide genuinely useful information, tips, or insights (70% educational content)
>* Naturally weave product mentions and benefits into the narrative
>* Include factual data, statistics, or expert opinions to build credibility
>* Use subtle persuasion techniques rather than overt sales language
>* Incorporate case studies or success stories as social proof
>* Maintain a consistent editorial tone that matches the publication
>* Include appropriate disclosure language to meet advertising regulations
>* Feature a soft call-to-action that feels like a natural next step
>* Balance promotional content with genuine reader value
>* Adhere to publication-specific guidelines for advertorial content
>* Optimize length for print context (typically 500-1000 words)

Output result in [Language] with blending [Tone] tone.`
      }
    ]
  }
];

// For backward compatibility, flatten the categories into a single array
export const DEFAULT_PROMPTS: DefaultPrompt[] = DEFAULT_PROMPTS_CATEGORIES.flatMap(
  category => category.prompts
);

// Function to analyze a single image
export const analyzeImage = async (imageData: string, prompt: string) => {
  try {
    const result = await model.generateContent([
      prompt,
      {
        inlineData: {
          mimeType: "image/jpeg",
          data: imageData
        }
      }
    ]);
    
    return result.response.text();
  } catch (error) {
    console.error('Error analyzing image:', error);
    throw error;
  }
};

// Function to analyze multiple images (for multi-single-product mode)
export const analyzeMultipleImages = async (imagesData: string[], prompt: string) => {
  try {
    const imageContents = imagesData.map(imageData => ({
      inlineData: {
        mimeType: "image/jpeg",
        data: imageData
      }
    }));
    
    const result = await model.generateContent([
      prompt,
      ...imageContents
    ]);
    
    return result.response.text();
  } catch (error) {
    console.error('Error analyzing multiple images:', error);
    throw error;
  }
};

// Function to format the prompt with language and tone
export const formatPrompt = (basePrompt: string, language: string, tone: string) => {
  return basePrompt.replace(/\[Language\]/g, language).replace(/\[Tone\]/g, tone);
};