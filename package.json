{"name": "image-analysis-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:analyze": "vite build --mode analyze", "build:production": "vite build --mode production", "lint": "eslint .", "preview": "vite preview", "test-storage": "node --experimental-modules scripts/test-storage.js", "analyze-bundle": "npx vite-bundle-analyzer dist/stats.json", "performance-test": "npm run build && npm run preview"}, "dependencies": {"@google/generative-ai": "^0.2.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@supabase/supabase-js": "^2.49.1", "@tailwindcss/line-clamp": "^0.4.4", "@tinymce/tinymce-react": "^6.0.0", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-helmet": "^6.1.11", "@types/uuid": "^10.0.0", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "dotenv": "^16.5.0", "firebase": "^11.3.1", "firebase-functions": "^6.3.2", "framer-motion": "^12.5.0", "lodash": "^4.17.21", "lucide-react": "^0.344.0", "marked": "^15.0.7", "matter-js": "^0.20.0", "motion": "^12.4.7", "nanoid": "^5.1.5", "node-fetch": "^2.7.0", "poly-decomp": "^0.3.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.22.2", "svg-path-commander": "^2.1.10", "tailwind-merge": "^2.2.1", "uuid": "^11.1.0", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "firebase-tools": "^13.32.0", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}