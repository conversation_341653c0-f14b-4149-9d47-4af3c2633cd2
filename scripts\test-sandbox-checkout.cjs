// Test script to verify sandbox checkout configuration
// This script tests the sandbox environment setup and validates checkout prerequisites

const https = require('https');
require('dotenv').config();

// Sandbox configuration
const SANDBOX_CONFIG = {
  apiKey: process.env.VITE_PADDLE_API_KEY_SANDBOX,
  clientToken: process.env.VITE_PADDLE_CLIENT_SIDE_TOKEN_SANDBOX,
  apiEndpoint: 'https://sandbox-api.paddle.com'
};

// Expected sandbox products and prices
const EXPECTED_PRODUCTS = {
  payAsYouGo: {
    productId: 'pro_01jxejs22b2bdtms6nx83p066j',
    priceId: 'pri_01jxejv3jrz3qpfynwg325zr91'
  },
  pro: {
    productId: 'pro_01jxejx7dwwftkhnkxh4axp4py',
    monthlyPriceId: 'pri_01jxejy2yd3zkpva6p7pre4bbx',
    yearlyPriceId: 'pri_01jxejzssvekxchyy0sk229xt0'
  },
  enterprise: {
    productId: 'pro_01jxek141ywebe45jr7ww4vdwz',
    monthlyPriceId: 'pri_01jxek1vgmzbbxhs3nc4w08rht',
    yearlyPriceId: 'pri_01jxek2y2rpmbq67qq660g3eg8'
  }
};

// Make API request to Paddle Sandbox
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, SANDBOX_CONFIG.apiEndpoint);
    
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Authorization': `Bearer ${SANDBOX_CONFIG.apiKey}`,
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(response);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${JSON.stringify(response)}`));
          }
        } catch (error) {
          reject(new Error(`Invalid JSON response: ${body}`));
        }
      });
    });

    req.on('error', (error) => reject(error));

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Test sandbox configuration
async function testSandboxConfiguration() {
  console.log('🧪 Testing Paddle Sandbox Configuration\n');
  
  // 1. Validate credentials
  console.log('1️⃣ Validating Credentials...');
  if (!SANDBOX_CONFIG.apiKey) {
    console.error('❌ Missing VITE_PADDLE_API_KEY_SANDBOX');
    return false;
  }
  if (!SANDBOX_CONFIG.clientToken) {
    console.error('❌ Missing VITE_PADDLE_CLIENT_SIDE_TOKEN_SANDBOX');
    return false;
  }
  
  console.log(`✅ API Key: ${SANDBOX_CONFIG.apiKey.substring(0, 20)}...`);
  console.log(`✅ Client Token: ${SANDBOX_CONFIG.clientToken.substring(0, 15)}...`);
  console.log(`✅ API Endpoint: ${SANDBOX_CONFIG.apiEndpoint}`);
  
  // 2. Test API connection
  console.log('\n2️⃣ Testing API Connection...');
  try {
    const response = await makeRequest('GET', '/products?per_page=10');
    console.log(`✅ API connection successful`);
    console.log(`📊 Found ${response.data?.length || 0} products`);
  } catch (error) {
    console.error(`❌ API connection failed: ${error.message}`);
    return false;
  }
  
  // 3. Validate products exist
  console.log('\n3️⃣ Validating Products...');
  try {
    const productsResponse = await makeRequest('GET', '/products?per_page=50');
    const products = productsResponse.data || [];
    
    const productMap = {};
    products.forEach(product => {
      productMap[product.id] = product;
    });
    
    // Check each expected product
    for (const [planName, planData] of Object.entries(EXPECTED_PRODUCTS)) {
      console.log(`\n📦 Checking ${planName}...`);
      
      const product = productMap[planData.productId];
      if (!product) {
        console.error(`❌ Product not found: ${planData.productId}`);
        return false;
      }
      
      console.log(`✅ Product found: ${product.name} (${product.id})`);
      console.log(`   Status: ${product.status}`);
      console.log(`   Tax Category: ${product.tax_category}`);
    }
  } catch (error) {
    console.error(`❌ Product validation failed: ${error.message}`);
    return false;
  }
  
  // 4. Validate prices exist
  console.log('\n4️⃣ Validating Prices...');
  try {
    const pricesResponse = await makeRequest('GET', '/prices?per_page=50');
    const prices = pricesResponse.data || [];
    
    const priceMap = {};
    prices.forEach(price => {
      priceMap[price.id] = price;
    });
    
    // Collect all expected price IDs
    const expectedPriceIds = [];
    for (const planData of Object.values(EXPECTED_PRODUCTS)) {
      if (planData.priceId) expectedPriceIds.push(planData.priceId);
      if (planData.monthlyPriceId) expectedPriceIds.push(planData.monthlyPriceId);
      if (planData.yearlyPriceId) expectedPriceIds.push(planData.yearlyPriceId);
    }
    
    for (const priceId of expectedPriceIds) {
      const price = priceMap[priceId];
      if (!price) {
        console.error(`❌ Price not found: ${priceId}`);
        return false;
      }
      
      console.log(`✅ Price found: ${price.description} (${price.id})`);
      console.log(`   Amount: ${price.unit_price.amount} ${price.unit_price.currency_code}`);
      if (price.billing_cycle) {
        console.log(`   Billing: ${price.billing_cycle.frequency} ${price.billing_cycle.interval}(s)`);
      }
    }
  } catch (error) {
    console.error(`❌ Price validation failed: ${error.message}`);
    return false;
  }
  
  // 5. Test token type validation
  console.log('\n5️⃣ Validating Token Type...');
  const isTestToken = SANDBOX_CONFIG.clientToken.startsWith('test_');
  if (!isTestToken) {
    console.error(`❌ Invalid token type for sandbox. Expected 'test_' prefix, got: ${SANDBOX_CONFIG.clientToken.substring(0, 10)}...`);
    return false;
  }
  console.log(`✅ Token type valid: TEST token for sandbox environment`);
  
  return true;
}

// Test specific price ID
async function testPriceId(priceId, description) {
  console.log(`\n🔍 Testing price: ${description} (${priceId})`);
  
  try {
    const response = await makeRequest('GET', `/prices/${priceId}`);
    const price = response.data;
    
    console.log(`✅ Price accessible: ${price.description}`);
    console.log(`   Product ID: ${price.product_id}`);
    console.log(`   Amount: ${price.unit_price.amount} ${price.unit_price.currency_code}`);
    console.log(`   Status: ${price.status}`);
    
    return true;
  } catch (error) {
    console.error(`❌ Price test failed: ${error.message}`);
    return false;
  }
}

// Main test function
async function main() {
  console.log('🚀 Paddle Sandbox Checkout Test\n');
  
  try {
    // Test basic configuration
    const configValid = await testSandboxConfiguration();
    if (!configValid) {
      console.error('\n❌ Sandbox configuration test failed');
      process.exit(1);
    }
    
    // Test specific price IDs that will be used in checkout
    console.log('\n6️⃣ Testing Checkout Price IDs...');
    
    const testPrices = [
      { id: EXPECTED_PRODUCTS.payAsYouGo.priceId, desc: 'Pay-As-You-Go' },
      { id: EXPECTED_PRODUCTS.pro.monthlyPriceId, desc: 'Pro Monthly' },
      { id: EXPECTED_PRODUCTS.pro.yearlyPriceId, desc: 'Pro Yearly' },
      { id: EXPECTED_PRODUCTS.enterprise.monthlyPriceId, desc: 'Enterprise Monthly' },
      { id: EXPECTED_PRODUCTS.enterprise.yearlyPriceId, desc: 'Enterprise Yearly' }
    ];
    
    for (const testPrice of testPrices) {
      const success = await testPriceId(testPrice.id, testPrice.desc);
      if (!success) {
        console.error(`\n❌ Price test failed for ${testPrice.desc}`);
        process.exit(1);
      }
    }
    
    console.log('\n🎉 All sandbox tests passed!');
    console.log('\n📋 Summary:');
    console.log('✅ Sandbox credentials configured');
    console.log('✅ API connection working');
    console.log('✅ All products available');
    console.log('✅ All prices available');
    console.log('✅ Token type valid');
    console.log('✅ Checkout price IDs accessible');
    
    console.log('\n🔧 Next Steps:');
    console.log('1. Open http://localhost:5173/test/paddle');
    console.log('2. Click "Sandbox" to switch environments');
    console.log('3. Test checkout with sandbox products');
    console.log('4. Use test payment methods only');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  main();
}
