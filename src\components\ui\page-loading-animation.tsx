import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import '../../styles/page-transitions.css';

interface PageLoadingAnimationProps {
  children: React.ReactNode;
}

export function PageLoadingAnimation({ children }: PageLoadingAnimationProps) {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time (can be removed in production)
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  // Animation variants for the loading screen
  const loadingScreenVariants = {
    initial: { y: 0 },
    exit: { y: '-100%', transition: { duration: 0.8, ease: [0.65, 0, 0.35, 1] } }
  };

  // Animation variants for the content
  const contentVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        delay: 0.2,
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  return (
    <>
      {/* Loading Screen Animation */}
      {isLoading && (
        <motion.div
          className="fixed inset-0 z-50 bg-gradient-to-br from-purple-900 via-gray-800 to-gray-900 flex flex-col items-center justify-center"
          initial="initial"
          animate={isLoading ? 'initial' : 'exit'}
          exit="exit"
          variants={loadingScreenVariants}
        >
          <div className="flex flex-col items-center">
            <div className="text-4xl font-bold bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent mb-6">
              eComEasyAI
            </div>
            <div className="flex space-x-2">
              {[0, 1, 2, 3, 4].map((index) => (
                <motion.div
                  key={index}
                  className="w-3 h-3 rounded-full bg-gradient-to-r from-purple-400 to-pink-500"
                  animate={{
                    y: [0, -10, 0],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 1,
                    repeat: Infinity,
                    delay: index * 0.1,
                    ease: "easeInOut"
                  }}
                />
              ))}
            </div>
          </div>
        </motion.div>
      )}

      {/* Content */}
      <motion.div
        initial="hidden"
        animate={!isLoading ? 'visible' : 'hidden'}
        variants={contentVariants}
      >
        {children}
      </motion.div>
    </>
  );
}