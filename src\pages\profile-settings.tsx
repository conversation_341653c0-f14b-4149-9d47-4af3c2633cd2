import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { User, Package, Calendar, Lock, Save, Building2 } from 'lucide-react';
import { db, auth } from '@/lib/firebase';
import { collection, query, where, getDocs, updateDoc, doc } from 'firebase/firestore';
import { useStore } from '@/lib/store';
import { getLimitSettings, getUserUsage, hasEnterpriseAccess } from '@/lib/userLimits';
import { isEnterpriseAdminOwner } from '@/lib/userRoles';
import { updatePassword, EmailAuthProvider, reauthenticateWithCredential } from 'firebase/auth';
import { toast } from 'react-hot-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { BillingDetails } from '@/components/billing-details';
import { PaymentHistory } from '@/components/payment-history';

interface UserProfile {
  displayName: string;
  email: string;
  packageName: string;
  subscriptionExpireDate?: string;
  registrationDate: string;
  promptCount: number;
  totalPromptsRemaining?: number;
  companyName?: string;
  isEnterpriseAdminOwner?: boolean;
  isTeamMember?: boolean;
}

export function ProfileSettings() {
  const { user } = useStore();
  const location = useLocation();
  const navigate = useNavigate();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [hasEnterpriseLevel, setHasEnterpriseLevel] = useState(false);

  // Company name state
  const [companyName, setCompanyName] = useState('');
  const [updatingCompanyName, setUpdatingCompanyName] = useState(false);

  // Password change state
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [changingPassword, setChangingPassword] = useState(false);

  useEffect(() => {
    if (user) {
      fetchUserProfile();
    }
  }, [user]);

  // Check for payment success and refresh data
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const paymentSuccess = urlParams.get('payment_success');
    const fromPayment = urlParams.get('from_payment');

    if (paymentSuccess === 'true' || fromPayment === 'true') {
      // Show success message
      toast.success('Payment successful! Your subscription has been activated.');

      // Trigger payment success event for subscription context
      window.dispatchEvent(new CustomEvent('paymentSuccess'));

      // Force refresh user data after a short delay to ensure Firebase has updated
      setTimeout(() => {
        if (user) {
          fetchUserProfile();
        }
      }, 2000);

      // Clean up URL parameters
      const newUrl = window.location.pathname;
      navigate(newUrl, { replace: true });
    }
  }, [location.search, user, navigate]);

  // Update company name state when profile is loaded
  useEffect(() => {
    if (profile && profile.companyName) {
      setCompanyName(profile.companyName);
    }
  }, [profile]);

  const fetchUserProfile = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Fetch user data from Firestore
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('uid', '==', user.id));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        // Get default limits for free users
        const limits = await getLimitSettings();

        setProfile({
          displayName: user.email.split('@')[0],
          email: user.email,
          packageName: 'Free',
          registrationDate: 'N/A',
          promptCount: 0,
          totalPromptsRemaining: limits.totalGenerationLimit,
          companyName: '',
          isEnterpriseAdminOwner: false,
          isTeamMember: false
        });
        return;
      }

      const userData = querySnapshot.docs[0].data();

      // Count total prompts generated
      const captionsRef = collection(db, 'savedCaptions');
      const captionsQuery = query(captionsRef, where('userId', '==', user.id));
      const captionsSnapshot = await getDocs(captionsQuery);
      const totalPrompts = captionsSnapshot.size;

      // Get user's package and usage information
      const packageName = userData.packageName || 'Free';

      // Check if user has Enterprise-level access (regular Enterprise or Enterprise-based custom package)
      const hasEnterpriseLevel = await hasEnterpriseAccess(user.id);
      setHasEnterpriseLevel(hasEnterpriseLevel);

      // Check if user is an Enterprise Admin Owner or a team member
      const isEnterpriseAdminOwnerUser = hasEnterpriseLevel ? await isEnterpriseAdminOwner(user.id) : false;
      const isTeamMember = userData.isTeamMember || false;
      console.log('🔍 Profile Settings: Enterprise access check:', {
        packageName,
        hasEnterpriseLevel,
        isEnterpriseAdminOwnerUser,
        isTeamMember,
        userId: user.id
      });

      // For free users, get their total usage and remaining limit
      let totalPromptsRemaining;

      if (packageName === 'Free') {
        // Get the limit settings and user usage
        const limits = await getLimitSettings();
        const usage = await getUserUsage(user.id);

        // Calculate remaining prompts based on total limit
        totalPromptsRemaining = Math.max(0, limits.totalGenerationLimit - usage.dailyGenerationCount);
      }

      // Format registration date
      const registrationDate = userData.createdAt ?
        new Date(userData.createdAt).toLocaleDateString() :
        'N/A';

      // Format subscription expiration date if it exists
      const subscriptionExpireDate = userData.subscriptionExpireDate ?
        (userData.subscriptionExpireDate === 'lifetime' ? 'Lifetime Access' : new Date(userData.subscriptionExpireDate).toLocaleDateString()) :
        undefined;

      setProfile({
        displayName: userData.displayName || user.email.split('@')[0],
        email: user.email,
        packageName: packageName,
        subscriptionExpireDate: subscriptionExpireDate,
        registrationDate: registrationDate,
        promptCount: totalPrompts,
        totalPromptsRemaining,
        companyName: userData.companyName || '',
        isEnterpriseAdminOwner: isEnterpriseAdminOwnerUser,
        isTeamMember
      });
    } catch (error) {
      console.error('Error fetching user profile:', error);
      toast.error('Failed to load profile information');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateCompanyName = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast.error('User not authenticated');
      return;
    }

    if (!companyName.trim()) {
      toast.error('Company name cannot be empty');
      return;
    }

    setUpdatingCompanyName(true);

    try {
      // Find the user document
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('uid', '==', user.id));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        toast.error('User record not found');
        return;
      }

      const userDoc = querySnapshot.docs[0];

      // Update the company name
      await updateDoc(doc(db, 'users', userDoc.id), {
        companyName: companyName.trim()
      });

      // Update the profile state
      setProfile(prev => prev ? { ...prev, companyName: companyName.trim() } : null);

      toast.success('Company name updated successfully');
    } catch (error) {
      console.error('Error updating company name:', error);
      toast.error('Failed to update company name');
    } finally {
      setUpdatingCompanyName(false);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate passwords
    if (newPassword !== confirmPassword) {
      toast.error('New passwords do not match');
      return;
    }

    if (newPassword.length < 6) {
      toast.error('New password must be at least 6 characters');
      return;
    }

    setChangingPassword(true);

    try {
      const currentUser = auth.currentUser;

      if (!currentUser || !currentUser.email) {
        toast.error('User not authenticated');
        return;
      }

      // Re-authenticate user before changing password
      const credential = EmailAuthProvider.credential(
        currentUser.email,
        currentPassword
      );

      await reauthenticateWithCredential(currentUser, credential);

      // Change password
      await updatePassword(currentUser, newPassword);

      // Clear form
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');

      toast.success('Password updated successfully');
    } catch (error: any) {
      console.error('Error changing password:', error);

      if (error.code === 'auth/wrong-password') {
        toast.error('Current password is incorrect');
      } else if (error.code === 'auth/too-many-requests') {
        toast.error('Too many attempts. Please try again later');
      } else {
        toast.error('Failed to update password');
      }
    } finally {
      setChangingPassword(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-white mb-8">Profile and Settings</h1>

      {loading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      ) : profile ? (
        <div className="space-y-8">
          {/* Account Information Section */}
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <User className="mr-2 h-5 w-5 text-purple-400" />
              Account Information
            </h2>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <p className="text-sm text-gray-400 mb-1">Display Name</p>
                <p className="font-medium text-white">{profile.displayName}</p>
              </div>

              <div>
                <p className="text-sm text-gray-400 mb-1">Email Address</p>
                <p className="font-medium text-white">{profile.email}</p>
              </div>

              <div>
                <p className="text-sm text-gray-400 mb-1">Package</p>
                <p className="font-medium text-white">{profile.packageName}</p>
              </div>

              {profile.subscriptionExpireDate && (
                <div>
                  <p className="text-sm text-gray-400 mb-1">Subscription</p>
                  {profile.subscriptionExpireDate === 'Lifetime Access' ? (
                    <p className="font-medium bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                      ✨ {profile.subscriptionExpireDate}
                    </p>
                  ) : (
                    <p className="font-medium text-white">Expires: {profile.subscriptionExpireDate}</p>
                  )}
                </div>
              )}

              <div>
                <p className="text-sm text-gray-400 mb-1">Registration Date</p>
                <p className="font-medium text-white">{profile.registrationDate}</p>
              </div>

              <div>
                <p className="text-sm text-gray-400 mb-1">Total Prompts Generated</p>
                <p className="font-medium text-white">{profile.promptCount}</p>
              </div>

              {profile.totalPromptsRemaining !== undefined && (
                <div>
                  <p className="text-sm text-gray-400 mb-1">Prompts Remaining</p>
                  <p className="font-medium text-white">{profile.totalPromptsRemaining}</p>
                </div>
              )}
            </div>
          </div>

          {/* Billing Details Section */}
          {user && <BillingDetails userId={user.id} />}

          {/* Payment History Section */}
          {user && <PaymentHistory userId={user.id} />}

          {/* Company Name Section - Visible only for Enterprise-level Admin Owners (regular Enterprise or Enterprise-based custom packages) */}
          {hasEnterpriseLevel && profile.isEnterpriseAdminOwner && !profile.isTeamMember && (
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                <Building2 className="mr-2 h-5 w-5 text-purple-400" />
                Company/Organization Name
              </h2>

              <div className="mb-4">
                <p className="text-sm text-gray-400 mb-2">
                  This name will be displayed to your team members and in team invitations.
                </p>
                {profile.companyName ? (
                  <div className="p-3 bg-gray-700 rounded-lg mb-4">
                    <p className="text-sm text-gray-400">Current Company Name</p>
                    <p className="font-medium text-white">{profile.companyName}</p>
                  </div>
                ) : (
                  <div className="p-3 bg-yellow-900/30 border border-yellow-700 rounded-lg mb-4">
                    <p className="text-sm text-yellow-300">
                      You haven't set a company name yet. This is required to generate team invite links.
                    </p>
                  </div>
                )}

                <form onSubmit={handleUpdateCompanyName} className="space-y-4">
                  <div>
                    <label htmlFor="company-name" className="text-sm text-gray-400 mb-1 block">
                      Company/Organization Name
                    </label>
                    <Input
                      id="company-name"
                      type="text"
                      value={companyName}
                      onChange={(e) => setCompanyName(e.target.value)}
                      required
                      placeholder="Enter your company or organization name"
                      className="bg-gray-700 border-gray-600 text-white"
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={updatingCompanyName || !companyName.trim()}
                    className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white"
                  >
                    {updatingCompanyName ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                        Updating...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Update Company Name
                      </>
                    )}
                  </Button>
                </form>
              </div>
            </div>
          )}

          {/* Password Change Section */}
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Lock className="mr-2 h-5 w-5 text-purple-400" />
              Change Password
            </h2>

            <form onSubmit={handlePasswordChange} className="space-y-4">
              <div>
                <label htmlFor="current-password" className="text-sm text-gray-400 mb-1 block">
                  Current Password
                </label>
                <Input
                  id="current-password"
                  type="password"
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                  required
                  className="bg-gray-700 border-gray-600 text-white"
                />
              </div>

              <div>
                <label htmlFor="new-password" className="text-sm text-gray-400 mb-1 block">
                  New Password
                </label>
                <Input
                  id="new-password"
                  type="password"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  required
                  className="bg-gray-700 border-gray-600 text-white"
                />
              </div>

              <div>
                <label htmlFor="confirm-password" className="text-sm text-gray-400 mb-1 block">
                  Confirm New Password
                </label>
                <Input
                  id="confirm-password"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  className="bg-gray-700 border-gray-600 text-white"
                />
              </div>

              <Button
                type="submit"
                disabled={changingPassword || !currentPassword || !newPassword || !confirmPassword}
                className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white"
              >
                {changingPassword ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Update Password
                  </>
                )}
              </Button>
            </form>
          </div>
        </div>
      ) : (
        <div className="text-center py-8 text-gray-400">
          Unable to load profile information
        </div>
      )}
    </div>
  );
}
