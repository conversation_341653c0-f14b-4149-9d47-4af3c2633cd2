import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Footer } from '../../components/footer';

export function PrivacyPage() {
  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-12">
        <Link to="/" className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Home
        </Link>
        
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-white mb-8">Privacy Policy</h1>
          
          <div className="prose prose-lg prose-invert max-w-none">
            <p className="text-gray-300 mb-6">
              Last Updated: June 1, 2024
            </p>
            
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 mb-8">
              <p className="text-gray-300">
                At eComEasyAI, we take your privacy seriously. This Privacy Policy explains how we collect, use, disclose, 
                and safeguard your information when you use our platform. Please read this privacy policy carefully. 
                If you do not agree with the terms of this privacy policy, please do not access the site.
              </p>
            </div>
            
            <h2 className="text-2xl font-semibold text-white mb-4">1. Information We Collect</h2>
            <p className="text-gray-300 mb-6">
              We collect information that you provide directly to us when you register for an account, use our services, 
              or communicate with us. This may include:
            </p>
            <ul className="list-disc pl-6 text-gray-300 mb-6">
              <li>Personal identifiers such as your name, email address, and billing information</li>
              <li>Account credentials such as your username and password</li>
              <li>Product images you upload to our platform</li>
              <li>User-generated content such as edited descriptions and preferences</li>
              <li>Usage data and analytics information</li>
            </ul>
            
            <h2 className="text-2xl font-semibold text-white mb-4">2. How We Use Your Information</h2>
            <p className="text-gray-300 mb-6">
              We use the information we collect for various purposes, including to:
            </p>
            <ul className="list-disc pl-6 text-gray-300 mb-6">
              <li>Provide, maintain, and improve our services</li>
              <li>Process transactions and send related information</li>
              <li>Send you technical notices, updates, security alerts, and support messages</li>
              <li>Respond to your comments, questions, and customer service requests</li>
              <li>Train and improve our AI models (using anonymized data only)</li>
              <li>Monitor and analyze trends, usage, and activities in connection with our services</li>
            </ul>
            
            <h2 className="text-2xl font-semibold text-white mb-4">3. Data Security</h2>
            <p className="text-gray-300 mb-6">
              We have implemented appropriate technical and organizational security measures designed to protect the security of any personal 
              information we process. However, despite our safeguards and efforts to secure your information, no electronic transmission over 
              the Internet or information storage technology can be guaranteed to be 100% secure.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">4. Data Retention</h2>
            <p className="text-gray-300 mb-6">
              We will only keep your personal information for as long as it is necessary for the purposes set out in this privacy policy, 
              unless a longer retention period is required or permitted by law. When we have no ongoing legitimate business need to process 
              your personal information, we will either delete or anonymize such information.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">5. Your Rights</h2>
            <p className="text-gray-300 mb-6">
              Depending on your location, you may have certain rights regarding your personal information, such as:
            </p>
            <ul className="list-disc pl-6 text-gray-300 mb-6">
              <li>The right to access personal information we hold about you</li>
              <li>The right to request correction of inaccurate personal information</li>
              <li>The right to request deletion of your personal information</li>
              <li>The right to object to processing of your personal information</li>
              <li>The right to data portability</li>
            </ul>
            
            <h2 className="text-2xl font-semibold text-white mb-4">6. Third-Party Services</h2>
            <p className="text-gray-300 mb-6">
              Our service may contain links to third-party websites and services that are not owned or controlled by eComEasyAI. 
              We have no control over, and assume no responsibility for, the content, privacy policies, or practices of any third-party 
              websites or services. We strongly advise you to read the terms and privacy policy of each site you visit.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">7. Changes to This Privacy Policy</h2>
            <p className="text-gray-300 mb-6">
              We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page 
              and updating the "Last Updated" date. You are advised to review this Privacy Policy periodically for any changes.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">8. Contact Us</h2>
            <p className="text-gray-300 mb-6">
              If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.
            </p>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

export default PrivacyPage;