@tailwind base;
@tailwind components;
@tailwind utilities;

/* Masonry Grid Animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Masonry grid animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

/* Responsive masonry grid improvements */
@media (max-width: 767px) {
  /* Ensure 2 columns on mobile with proper spacing */
  .masonry-grid-mobile {
    column-count: 2 !important;
    column-gap: 0.75rem !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  /* Ensure 2 columns on tablet with proper spacing */
  .masonry-grid-tablet {
    column-count: 2 !important;
    column-gap: 1rem !important;
  }
}

/* Global scrolling behavior */
html, body {
  height: 100%;
  overflow-y: auto;
}

/* Custom Scrollbar Styling */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(75, 85, 99, 0.2);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #8b5cf6, #ec4899);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #7c3aed, #db2777);
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #8b5cf6 rgba(75, 85, 99, 0.2);
}

/* Firefox scrollbar styling */
* {
  scrollbar-width: thin;
  scrollbar-color: #8b5cf6 #1f2937;
}

/* Device Preview Styles */
.preview-container {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 1rem 0;
}

.device-frame {
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.device-frame.laptop {
  width: 90%;
  max-width: 900px;
  height: 400px;
  border: 12px solid #333;
  border-radius: 12px;
  border-bottom-width: 20px;
}

.device-frame.tablet {
  width: 70%;
  max-width: 600px;
  height: 400px;
  border: 16px solid #333;
  border-radius: 12px;
}

.device-frame.smartphone {
  width: 40%;
  max-width: 320px;
  height: 500px;
  border: 12px solid #333;
  border-radius: 24px;
  border-top-width: 40px;
}

/* Testimonial Carousel Styles */
.testimonial-scroll {
  -ms-overflow-style: none;  /* Hide scrollbar for IE and Edge */
  scrollbar-width: none;  /* Hide scrollbar for Firefox */
}

.testimonial-scroll::-webkit-scrollbar {
  display: none;  /* Hide scrollbar for Chrome, Safari and Opera */
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-320px * 6.5));
  }
}

.animate-scroll {
  animation: scroll 60s linear infinite;
  animation-play-state: running;
}

.animate-scroll:hover {
  animation-play-state: paused;
}

.device-frame.smartphone {
  border-bottom-width: 40px;
  position: relative;
}

.device-frame.smartphone:before {
  content: '';
  position: absolute;
  width: 50px;
  height: 4px;
  background: #444;
  top: -22px;
  left: 50%;
  transform: translateX(-50%);
}

.device-content {
  height: 100%;
  overflow-y: auto;
  padding: 1rem;
  background-color: white;
}

.caption-content {
  font-family: system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  color: #333;
  padding: 0.5rem;
}

.device-content.laptop {
  font-size: 1rem;
}

.device-content.tablet {
  font-size: 0.95rem;
}

.device-content.smartphone {
  font-size: 0.85rem;
}

/* Toast Notification Styles */
.toast-notification {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.go2072408551 {
  background: linear-gradient(to right, #10B981 50%, transparent 50%);
  background-size: 200% 100%;
  animation: progress 5s linear forwards;
}

@keyframes progress {
  from {
    background-position: 0% 0;
  }
  to {
    background-position: -100% 0;
  }
}
