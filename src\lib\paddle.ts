// Paddle Integration for eComEasyAI
// This file handles Paddle product creation and checkout integration

import { storePaddlePaymentData } from './paddle-payment-processor';

// Environment configuration
export type PaddleEnvironment = 'production' | 'sandbox';

// Get current environment from localStorage (for runtime switching) or environment variables
export const getCurrentEnvironment = (): PaddleEnvironment => {
  // Check localStorage first for runtime environment switching
  if (typeof window !== 'undefined') {
    const storedEnv = localStorage.getItem('paddle_environment');
    if (storedEnv === 'sandbox' || storedEnv === 'production') {
      return storedEnv as PaddleEnvironment;
    }
  }

  // Fallback to environment variable
  const env = import.meta.env.VITE_PADDLE_ENVIRONMENT?.toLowerCase();
  return env === 'sandbox' ? 'sandbox' : 'production';
};

// Set current environment (for runtime switching)
export const setCurrentEnvironment = (environment: PaddleEnvironment) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('paddle_environment', environment);
    console.log(`🔄 Environment switched to: ${environment}`);
  }
};

// Get environment-specific credentials
export const getEnvironmentCredentials = (environment?: PaddleEnvironment) => {
  const currentEnv = environment || getCurrentEnvironment();

  if (currentEnv === 'sandbox') {
    return {
      apiKey: import.meta.env.VITE_PADDLE_API_KEY_SANDBOX,
      clientSideToken: import.meta.env.VITE_PADDLE_CLIENT_SIDE_TOKEN_SANDBOX,
      apiEndpoint: 'https://sandbox-api.paddle.com',
    };
  } else {
    return {
      apiKey: import.meta.env.VITE_PADDLE_API_KEY_PRODUCTION || import.meta.env.VITE_PADDLE_API_KEY,
      clientSideToken: import.meta.env.VITE_PADDLE_CLIENT_SIDE_TOKEN_PRODUCTION || import.meta.env.VITE_PADDLE_CLIENT_SIDE_TOKEN,
      apiEndpoint: 'https://api.paddle.com',
    };
  }
};

// Paddle configuration from environment variables
export const paddleConfig = {
  ...getEnvironmentCredentials(),
  environment: getCurrentEnvironment(),
};

// Product and Price IDs mapping for different environments
export const paddleProductsConfig = {
  production: {
    payAsYouGo: {
      productId: 'pro_01jxbvansyepyq95kskd02bx9z',
      priceId: 'pri_01jxbve4a77pt0545ntrvaea5t',
    },
    pro: {
      productId: 'pro_01jxbvap4t1dg04b9wb9kg6kzh',
      monthlyPriceId: 'pri_01jxbve4kcgjvmq2jk65bcjw1w',
      yearlyPriceId: 'pri_01jxbve4wp9g66xjxcbhjjw8c2',
    },
    enterprise: {
      productId: 'pro_01jxbvapfjr0gt6yjjdvare06j',
      monthlyPriceId: 'pri_01jxbve5q9se3877q6vs3sdmzs',
      yearlyPriceId: 'pri_01jxbve61ky5hvyt93ytq7rnzp',
    },
  },
  sandbox: {
    payAsYouGo: {
      productId: 'pro_01jxejs22b2bdtms6nx83p066j',
      priceId: 'pri_01jxejv3jrz3qpfynwg325zr91',
    },
    pro: {
      productId: 'pro_01jxejx7dwwftkhnkxh4axp4py',
      monthlyPriceId: 'pri_01jxejy2yd3zkpva6p7pre4bbx',
      yearlyPriceId: 'pri_01jxejzssvekxchyy0sk229xt0',
    },
    enterprise: {
      productId: 'pro_01jxek141ywebe45jr7ww4vdwz',
      monthlyPriceId: 'pri_01jxek1vgmzbbxhs3nc4w08rht',
      yearlyPriceId: 'pri_01jxek2y2rpmbq67qq660g3eg8',
    },
  },
};

// Get environment-specific product configuration
export const getPaddleProducts = (environment?: PaddleEnvironment) => {
  const currentEnv = environment || getCurrentEnvironment();
  return paddleProductsConfig[currentEnv];
};

// Legacy export for backward compatibility
export const paddleProducts = getPaddleProducts();

// Helper function to get Paddle price ID based on plan and billing period
export const getPaddlePriceId = (
  plan: string,
  billingPeriod?: 'monthly' | 'yearly',
  environment?: PaddleEnvironment
): string => {
  const products = getPaddleProducts(environment);

  switch (plan) {
    case 'Pay-As-You-Go':
      return products.payAsYouGo.priceId;
    case 'Pro':
      return billingPeriod === 'yearly'
        ? products.pro.yearlyPriceId
        : products.pro.monthlyPriceId;
    case 'Enterprise':
      return billingPeriod === 'yearly'
        ? products.enterprise.yearlyPriceId
        : products.enterprise.monthlyPriceId;
    default:
      throw new Error(`Unknown plan: ${plan}`);
  }
};

// Helper function to get Paddle product ID based on plan
export const getPaddleProductId = (plan: string, environment?: PaddleEnvironment): string => {
  const products = getPaddleProducts(environment);

  switch (plan) {
    case 'Pay-As-You-Go':
      return products.payAsYouGo.productId;
    case 'Pro':
      return products.pro.productId;
    case 'Enterprise':
      return products.enterprise.productId;
    default:
      throw new Error(`Unknown plan: ${plan}`);
  }
};

// Function to initialize Paddle checkout with environment support
export const initializePaddleCheckout = (
  priceId: string,
  customerEmail?: string,
  customData?: any,
  discountCode?: string,
  environment?: PaddleEnvironment,
  planInfo?: { plan: string; billingPeriod: string; amount: number; currency: string }
) => {
  if (typeof window === 'undefined' || !window.Paddle) {
    throw new Error('Paddle not available');
  }

  const currentEnv = environment || getCurrentEnvironment();
  const credentials = getEnvironmentCredentials(currentEnv);

  // Validate that Paddle is set up for the correct environment
  const currentPaddleEnv = (window as any).__PADDLE_ENVIRONMENT__;
  const currentTokenType = (window as any).__PADDLE_TOKEN_TYPE__;

  console.log(`🚀 Initializing Paddle checkout for ${currentEnv} environment`);
  console.log(`🔍 Current Paddle environment: ${currentPaddleEnv}`);
  console.log(`🎫 Current token type: ${currentTokenType}`);
  console.log(`💰 Price ID: ${priceId}`);

  if (currentPaddleEnv !== currentEnv) {
    console.warn(`⚠️ Environment mismatch! Expected: ${currentEnv}, Current: ${currentPaddleEnv}`);
    console.warn('🔄 Re-initializing Paddle for correct environment...');
    setupPaddle(currentEnv);
  }

  // Add environment information to custom data
  const enhancedCustomData = {
    ...customData,
    environment: currentEnv,
    timestamp: new Date().toISOString(),
    tokenType: credentials.clientSideToken?.startsWith('live_') ? 'live' : 'test',
  };

  // Generate a temporary session ID for storing payment data
  const tempSessionId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Store payment data for later retrieval
  if (planInfo && customerEmail && enhancedCustomData.userId) {
    storePaddlePaymentData(tempSessionId, {
      plan: planInfo.plan,
      billingPeriod: planInfo.billingPeriod,
      amount: planInfo.amount,
      currency: planInfo.currency,
      userId: enhancedCustomData.userId,
      userEmail: customerEmail,
      priceId: priceId
    });
  }

  const checkoutData: PaddleCheckoutData = {
    items: [{ priceId: priceId, quantity: 1 }],
    customer: customerEmail ? { email: customerEmail } : undefined,
    customData: enhancedCustomData,
    successUrl: `${window.location.origin}/payment/success?session_id={checkout.id}&temp_id=${tempSessionId}`,
    discountCode: discountCode,
  };

  // Remove undefined properties to keep the payload clean
  if (!checkoutData.discountCode) {
    delete checkoutData.discountCode;
  }

  console.log(`🛒 Opening Paddle checkout:`, {
    environment: currentEnv,
    priceId,
    customerEmail,
    hasDiscountCode: !!discountCode,
    discountCode: discountCode,
    checkoutData
  });

  try {
    // Additional validation before opening checkout
    if (!window.Paddle.Checkout) {
      throw new Error('Paddle Checkout not available');
    }

    // Log detailed debugging information
    console.log('🔍 Pre-checkout validation:');
    console.log('  - Paddle SDK loaded:', !!window.Paddle);
    console.log('  - Checkout available:', !!window.Paddle.Checkout);
    console.log('  - Environment:', currentEnv);
    console.log('  - Token type:', currentTokenType);
    console.log('  - Price ID:', priceId);
    console.log('  - Customer email:', customerEmail);

    // Check if Paddle SDK has environment info
    if ((window.Paddle as any).Environment) {
      console.log('  - Paddle SDK Environment:', (window.Paddle as any).Environment);
    }

    window.Paddle.Checkout.open(checkoutData);
    console.log('✅ Paddle checkout opened successfully');

  } catch (error) {
    console.error('❌ Paddle checkout error:', error);
    console.error('🔧 Checkout data:', checkoutData);
    console.error('🌍 Environment:', currentEnv);
    console.error('🎫 Token type:', currentTokenType);
    console.error('⚙️ Paddle config:', (window as any).__PADDLE_CONFIG__);
    console.error('🌐 Window Paddle object:', window.Paddle);

    // Additional debugging for 403 errors
    if (error.message && error.message.includes('403')) {
      console.error('🚨 403 Forbidden Error Detected:');
      console.error('  - This usually indicates a permissions issue');
      console.error('  - Check if the client-side token has checkout permissions');
      console.error('  - Verify the token is correctly configured for the environment');
      console.error('  - Ensure the price ID exists in the correct environment');
    }

    throw error;
  }
};

// Function to setup Paddle with environment support
export const setupPaddle = (environment?: PaddleEnvironment) => {
  if (typeof window === 'undefined') {
    throw new Error('Paddle setup can only be called in browser environment');
  }

  if (!window.Paddle) {
    throw new Error('Paddle script not loaded. Make sure the Paddle script is included in your HTML.');
  }

  const credentials = getEnvironmentCredentials(environment);
  const currentEnv = environment || getCurrentEnvironment();

  if (!credentials.clientSideToken) {
    throw new Error(`Paddle client side token is not configured for ${currentEnv} environment`);
  }

  // Validate token type matches environment
  const isLiveToken = credentials.clientSideToken.startsWith('live_');
  const isTestToken = credentials.clientSideToken.startsWith('test_');

  if (currentEnv === 'production' && !isLiveToken) {
    throw new Error('Production environment requires live token (starting with "live_")');
  }

  if (currentEnv === 'sandbox' && !isTestToken) {
    throw new Error('Sandbox environment requires test token (starting with "test_")');
  }

  try {
    // For Paddle v2, we need to explicitly set the environment for sandbox
    // IMPORTANT: Paddle.Environment.set() must be called BEFORE Paddle.Initialize()

    if (currentEnv === 'sandbox') {
      console.log('🧪 Setting Paddle environment to sandbox');
      window.Paddle.Environment.set('sandbox');
    }
    // Note: For production, we don't need to call Paddle.Environment.set()
    // as production is the default environment

    const paddleConfig = {
      token: credentials.clientSideToken,
    };

    console.log(`🎯 Initializing Paddle for ${currentEnv} environment`);
    console.log(`🎫 Using ${isLiveToken ? 'LIVE' : 'TEST'} token`);

    window.Paddle.Initialize(paddleConfig);

    console.log(`🎯 Paddle setup successful for ${currentEnv} environment`);
    console.log('📡 Token:', credentials.clientSideToken?.substring(0, 15) + '...');
    console.log('🌐 API Endpoint:', credentials.apiEndpoint);
    console.log('🔧 Token Type:', isLiveToken ? 'LIVE (Production)' : 'TEST (Sandbox)');
    console.log('⚙️ Paddle Config:', paddleConfig);

    // Store current environment in window for debugging
    (window as any).__PADDLE_ENVIRONMENT__ = currentEnv;
    (window as any).__PADDLE_TOKEN_TYPE__ = isLiveToken ? 'live' : 'test';
    (window as any).__PADDLE_CONFIG__ = paddleConfig;

    // Test Paddle SDK state after setup
    if (window.Paddle && (window.Paddle as any).Environment) {
      console.log('🌍 Paddle SDK Environment:', (window.Paddle as any).Environment);
    }

    return true;
  } catch (error) {
    console.error('❌ Error setting up Paddle:', error);
    console.error('🔧 Attempted config:', { token: credentials.clientSideToken?.substring(0, 15) + '...', environment: currentEnv });
    throw error;
  }
};

// Types for Paddle integration
export interface PaddleCheckoutData {
  items: Array<{
    priceId: string;
    quantity: number;
  }>;
  customer?: {
    email: string;
    address?: {
      countryCode: string;
      postalCode?: string;
    };
  };
  customData?: Record<string, any>;
  successUrl?: string;
  discountCode?: string;
}

// Utility functions for environment management
export const switchEnvironment = (environment: PaddleEnvironment) => {
  setCurrentEnvironment(environment);

  // Re-initialize Paddle with the new environment
  if (typeof window !== 'undefined' && window.Paddle) {
    try {
      setupPaddle(environment);
      console.log(`✅ Successfully switched to ${environment} environment`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to switch to ${environment} environment:`, error);
      return false;
    }
  } else {
    console.warn('⚠️ Paddle not available for immediate re-initialization');
    return false;
  }
};

export const getEnvironmentInfo = (environment?: PaddleEnvironment) => {
  const currentEnv = environment || getCurrentEnvironment();
  const credentials = getEnvironmentCredentials(currentEnv);

  return {
    environment: currentEnv,
    apiEndpoint: credentials.apiEndpoint,
    hasApiKey: !!credentials.apiKey,
    hasClientToken: !!credentials.clientSideToken,
    tokenType: credentials.clientSideToken?.startsWith('live_') ? 'production' : 'sandbox',
  };
};

export const validateEnvironmentSetup = (environment?: PaddleEnvironment) => {
  const credentials = getEnvironmentCredentials(environment);
  const currentEnv = environment || getCurrentEnvironment();

  const errors: string[] = [];

  if (!credentials.apiKey) {
    errors.push(`Missing API key for ${currentEnv} environment`);
  }

  if (!credentials.clientSideToken) {
    errors.push(`Missing client side token for ${currentEnv} environment`);
  }

  // Validate token type matches environment
  if (credentials.clientSideToken) {
    const isLiveToken = credentials.clientSideToken.startsWith('live_');
    const isTestToken = credentials.clientSideToken.startsWith('test_');

    if (currentEnv === 'production' && !isLiveToken) {
      errors.push('Production environment requires live token (starting with "live_")');
    }

    if (currentEnv === 'sandbox' && !isTestToken) {
      errors.push('Sandbox environment requires test token (starting with "test_")');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    environment: currentEnv,
    credentials: {
      hasApiKey: !!credentials.apiKey,
      hasClientToken: !!credentials.clientSideToken,
      apiEndpoint: credentials.apiEndpoint,
    },
  };
};

// Extended Paddle interface for TypeScript
declare global {
  interface Window {
    Paddle: {
      Initialize: (config: { token: string }) => void;
      Setup?: (config: { token: string }) => void; // Deprecated, kept for backward compatibility
      Environment: {
        set: (environment: 'sandbox' | 'production') => void;
      };
      Checkout: {
        open: (data: PaddleCheckoutData) => void;
      };
    };
  }
}
