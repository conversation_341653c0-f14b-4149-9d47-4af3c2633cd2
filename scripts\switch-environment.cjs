// Environment Switcher for Paddle Integration
// Usage: node scripts/switch-environment.cjs [production|sandbox]

const fs = require('fs');
const path = require('path');

const ENV_FILE = path.join(__dirname, '..', '.env');

function updateEnvironment(targetEnv) {
  if (!['production', 'sandbox'].includes(targetEnv)) {
    console.error('❌ Invalid environment. Use "production" or "sandbox"');
    process.exit(1);
  }

  try {
    // Read current .env file
    let envContent = fs.readFileSync(ENV_FILE, 'utf8');
    
    // Update the VITE_PADDLE_ENVIRONMENT line
    const envRegex = /^VITE_PADDLE_ENVIRONMENT=.*/m;
    const newEnvLine = `VITE_PADDLE_ENVIRONMENT=${targetEnv}`;
    
    if (envRegex.test(envContent)) {
      envContent = envContent.replace(envRegex, newEnvLine);
    } else {
      // Add the line if it doesn't exist
      envContent += `\n${newEnvLine}\n`;
    }
    
    // Write back to file
    fs.writeFileSync(ENV_FILE, envContent);
    
    console.log(`✅ Environment switched to: ${targetEnv}`);
    console.log(`📝 Updated .env file: VITE_PADDLE_ENVIRONMENT=${targetEnv}`);
    
    if (targetEnv === 'production') {
      console.log('🚀 You are now in PRODUCTION mode');
      console.log('⚠️  Real payments will be processed!');
    } else {
      console.log('🧪 You are now in SANDBOX mode');
      console.log('💡 Test payments only - no real charges');
    }
    
    console.log('\n📋 Next steps:');
    console.log('1. Restart your development server (npm run dev)');
    console.log('2. Test the integration');
    console.log('3. Verify the environment in the test page');
    
  } catch (error) {
    console.error('❌ Failed to update environment:', error.message);
    process.exit(1);
  }
}

function getCurrentEnvironment() {
  try {
    const envContent = fs.readFileSync(ENV_FILE, 'utf8');
    const match = envContent.match(/^VITE_PADDLE_ENVIRONMENT=(.*)$/m);
    return match ? match[1].trim() : 'production';
  } catch (error) {
    return 'production';
  }
}

function showStatus() {
  const currentEnv = getCurrentEnvironment();
  
  console.log('🌍 Current Paddle Environment Status');
  console.log('=====================================');
  console.log(`Current Environment: ${currentEnv}`);
  
  if (currentEnv === 'production') {
    console.log('🚀 Mode: PRODUCTION (Live payments)');
    console.log('💳 Real credit cards will be charged');
    console.log('🔴 Use with caution!');
  } else {
    console.log('🧪 Mode: SANDBOX (Test payments)');
    console.log('💡 Test mode - no real charges');
    console.log('🟢 Safe for testing');
  }
  
  console.log('\n📋 Available commands:');
  console.log('  node scripts/switch-environment.cjs production');
  console.log('  node scripts/switch-environment.cjs sandbox');
  console.log('  node scripts/switch-environment.cjs status');
}

// Main function
function main() {
  const [,, command] = process.argv;
  
  if (!command) {
    showStatus();
    return;
  }
  
  switch (command.toLowerCase()) {
    case 'production':
    case 'prod':
    case 'live':
      updateEnvironment('production');
      break;
      
    case 'sandbox':
    case 'test':
    case 'dev':
      updateEnvironment('sandbox');
      break;
      
    case 'status':
    case 'current':
      showStatus();
      break;
      
    default:
      console.log('🔄 Paddle Environment Switcher');
      console.log('\nUsage: node scripts/switch-environment.cjs [command]');
      console.log('\nCommands:');
      console.log('  production        - Switch to production environment (live payments)');
      console.log('  sandbox           - Switch to sandbox environment (test payments)');
      console.log('  status            - Show current environment status');
      console.log('\nAliases:');
      console.log('  prod, live        - Same as production');
      console.log('  test, dev         - Same as sandbox');
      console.log('\nExamples:');
      console.log('  node scripts/switch-environment.cjs production');
      console.log('  node scripts/switch-environment.cjs sandbox');
      console.log('  node scripts/switch-environment.cjs status');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { updateEnvironment, getCurrentEnvironment, showStatus };
