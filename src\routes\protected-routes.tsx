import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useStore } from '@/lib/store';
import { useEffect, useState } from 'react';
import { isEnterpriseAdminOwner } from '@/lib/userRoles';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { ExpiredPackageModal } from '@/components/ui/expired-package-modal';
import { TeamManagementRouteSkeleton } from '@/components/ui/team-management-skeleton';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/lib/authProvider';

// Basic protected route that checks if user is authenticated
export const ProtectedRoute = () => {
  const { user } = useStore();
  const { currentUser } = useAuth();
  const location = useLocation();
  const [isExpired, setIsExpired] = useState(false);
  const [packageName, setPackageName] = useState<string | null>(null);
  const [expireDate, setExpireDate] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkSubscriptionStatus = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        // Fetch user data from Firestore
        const usersRef = collection(db, 'users');
        const q = query(usersRef, where('uid', '==', user.id));
        const querySnapshot = await getDocs(q);

        if (querySnapshot.empty) {
          setLoading(false);
          return;
        }

        const userData = querySnapshot.docs[0].data();
        const userPackageName = userData.packageName || 'Free';
        setPackageName(userPackageName);

        // Only Pro and Enterprise packages can expire
        if (userPackageName !== 'Pro' && userPackageName !== 'Enterprise') {
          setIsExpired(false);
          setLoading(false);
          return;
        }

        // Check if there's an expiration date
        if (userData.subscriptionExpireDate) {
          setExpireDate(userData.subscriptionExpireDate);
          const expireDate = new Date(userData.subscriptionExpireDate);
          const now = new Date();
          setIsExpired(expireDate < now);
        } else {
          setIsExpired(false);
        }
      } catch (error) {
        console.error('Error checking subscription status:', error);
        setIsExpired(false);
      } finally {
        setLoading(false);
      }
    };

    checkSubscriptionStatus();
  }, [user]);

  if (!user) {
    // Redirect to auth page with a return URL
    return <Navigate to={`/auth?redirect=${encodeURIComponent(location.pathname)}`} replace />;
  }

  // Check email verification status
  if (currentUser && !currentUser.emailVerified) {
    // Allow access to email verification page itself
    if (location.pathname === '/email-verification') {
      return <Outlet />;
    }
    // Redirect unverified users to email verification page
    return <Navigate to="/email-verification" replace />;
  }

  if (loading) {
    return null; // Show nothing while checking
  }

  return (
    <>
      {/* Non-dismissible modal that blocks access until payment is completed */}
      {isExpired && packageName && expireDate && (
        <ExpiredPackageModal
          packageName={packageName}
          expireDate={expireDate}
        />
      )}
      <Outlet />
    </>
  );
};

// Special protected route for Enterprise Admin Owner features
export const EnterpriseAdminOwnerRoute = () => {
  const { user } = useStore();
  const location = useLocation();
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAuthorization = async () => {
      if (!user) {
        setIsAuthorized(false);
        setLoading(false);
        return;
      }

      try {
        const isAdminOwner = await isEnterpriseAdminOwner(user.id);
        setIsAuthorized(isAdminOwner);

        // If not authorized, show immediate feedback
        if (!isAdminOwner) {
          toast.error('Team Management is only available for Enterprise Admin Owners');
        }
      } catch (error) {
        console.error('Error checking Enterprise Admin Owner status:', error);
        setIsAuthorized(false);
        toast.error('Error verifying access permissions');
      } finally {
        setLoading(false);
      }
    };

    checkAuthorization();
  }, [user]);

  // Show skeleton screen while checking authorization
  if (loading) {
    return <TeamManagementRouteSkeleton />;
  }

  // If not authorized, redirect to app dashboard
  if (!isAuthorized) {
    return <Navigate to="/app/dashboard" replace />;
  }

  return <Outlet />;
};
