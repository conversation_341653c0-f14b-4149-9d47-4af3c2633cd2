const functions = require('firebase-functions');
const admin = require('firebase-admin');
const SSLCommerzPayment = require('sslcommerz-lts');
const { v4: uuidv4 } = require('uuid');
const cors = require('cors')({ origin: true });
require('dotenv').config();

// Get Firestore instance
const db = admin.firestore();

// Get environment variables
const storeId = process.env.SSLCOMMERZ_STORE_ID || process.env.VITE_SSLCOMMERZ_STORE_ID || "httpsecomeasyai0live";
const storePassword = process.env.SSLCOMMERZ_STORE_PASSWORD || process.env.VITE_SSLCOMMERZ_STORE_PASSWORD || "6808DA135CE7539998";
const isLive = (process.env.SSLCOMMERZ_IS_LIVE || process.env.VITE_SSLCOMMERZ_IS_LIVE) === 'true';
const appUrl = process.env.APP_URL || process.env.VITE_APP_URL || 'https://ecomeasy.ai';
const localAppUrl = process.env.LOCAL_APP_URL || process.env.VITE_LOCAL_APP_URL || 'http://localhost:5173';

// Log environment variables for debugging
console.log('SSLCOMMERZ Configuration:', {
  storeId,
  isLive,
  appUrl,
  localAppUrl,
  // Don't log the full password for security
  passwordProvided: !!storePassword,
  // Additional debugging
  expectedEnvironment: isLive ? 'PRODUCTION' : 'SANDBOX',
  storeIdType: storeId === 'httpsecomeasyai0live' ? 'PRODUCTION_STORE' : 'SANDBOX_STORE'
});

// Initialize SSLCommerz with explicit debugging
console.log('🔧 Initializing SSLCOMMERZ with parameters:', {
  storeId: storeId,
  isLive: isLive,
  expectedEndpoint: isLive ? 'securepay.sslcommerz.com' : 'sandbox.sslcommerz.com'
});

// CRITICAL FIX: Force production environment for production store ID
// The sslcommerz-lts library might have issues with environment detection
let finalIsLive = isLive;
if (storeId === 'httpsecomeasyai0live') {
  // This is definitely a production store ID, force live mode
  finalIsLive = true;
  console.log('🔧 FORCING PRODUCTION MODE: Detected production store ID');
} else if (storeId === 'testbox') {
  // This is definitely a sandbox store ID, force sandbox mode
  finalIsLive = false;
  console.log('🔧 FORCING SANDBOX MODE: Detected sandbox store ID');
}

console.log('🎯 Final environment settings:', {
  originalIsLive: isLive,
  finalIsLive: finalIsLive,
  storeId: storeId,
  environment: finalIsLive ? 'PRODUCTION' : 'SANDBOX'
});

const sslcommerz = new SSLCommerzPayment(storeId, storePassword, finalIsLive);

// Log the initialized instance properties if available
console.log('🔍 SSLCOMMERZ instance created. Checking internal properties...');
if (sslcommerz.is_live !== undefined) {
  console.log('   Internal is_live flag:', sslcommerz.is_live);
}
if (sslcommerz.store_id !== undefined) {
  console.log('   Internal store_id:', sslcommerz.store_id);
}

// Create a function to get the correct SSLCOMMERZ instance
function getSSLCommerzInstance() {
  const effectiveIsLive = storeId === 'httpsecomeasyai0live' ? true : (storeId === 'testbox' ? false : isLive);
  console.log('🔧 Creating SSLCOMMERZ instance with effectiveIsLive:', effectiveIsLive);
  return new SSLCommerzPayment(storeId, storePassword, effectiveIsLive);
}

/**
 * Initializes a payment session with SSLCOMMERZ
 * This function is called from the client-side when a user proceeds to checkout
 * Updated: Now properly handles coupon discounts before currency conversion
 */
exports.initSSLCommerzPayment = functions.https.onCall(async (data, context) => {
  try {
    console.log('initSSLCommerzPayment called with data:', JSON.stringify(data));

    // Ensure the user is authenticated
    if (!context.auth) {
      console.error('Authentication failed: No auth context');
      throw new functions.https.HttpsError(
        'unauthenticated',
        'The function must be called while authenticated.'
      );
    }

    console.log('User authenticated:', context.auth.uid);

    // Get request data
    const {
      userEmail,
      userName,
      userPhone,
      amount,
      plan,
      billingPeriod,
      billingDetails,
      couponCode,
      discountAmount,
      environment
    } = data;

    const userId = context.auth.uid;

    // Validate required fields
    if (!userId || !userEmail || !amount || !plan || !billingPeriod || !billingDetails) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Missing required fields'
      );
    }

    // Generate a unique transaction ID
    const transactionId = `ECOM_${uuidv4().replace(/-/g, '').substring(0, 10)}_${Date.now()}`;

    // Determine the base URL based on the environment
    const baseUrl = environment === 'development' ? localAppUrl : appUrl;

    // Get the region from the environment or default to us-central1
    const region = process.env.FUNCTION_REGION || 'us-central1';

    // Get the project ID from the environment
    const projectId = process.env.GCLOUD_PROJECT || 'product-img-2-ecom';

    // Construct the Firebase Functions URLs
    const functionsBaseUrl = `https://${region}-${projectId}.cloudfunctions.net`;

    // Apply coupon discount to USD amount before currency conversion
    let finalUsdAmount = amount;
    let appliedDiscountUsd = 0;

    if (couponCode && discountAmount) {
      console.log('Applying coupon discount:', {
        couponCode,
        originalAmount: amount,
        discountAmount
      });

      // Query the coupon to get the discount type
      try {
        const couponsRef = db.collection('coupons');
        const couponQuery = await couponsRef.where('code', '==', couponCode).limit(1).get();

        if (!couponQuery.empty) {
          const couponData = couponQuery.docs[0].data();

          if (couponData.discountType === 'percentage') {
            // Apply percentage discount
            appliedDiscountUsd = amount * (couponData.discountValue / 100);
            finalUsdAmount = amount * (1 - couponData.discountValue / 100);
          } else {
            // Apply fixed amount discount
            appliedDiscountUsd = Math.min(couponData.discountValue, amount);
            finalUsdAmount = Math.max(0, amount - couponData.discountValue);
          }

          console.log('Coupon discount applied:', {
            discountType: couponData.discountType,
            discountValue: couponData.discountValue,
            appliedDiscountUsd,
            finalUsdAmount
          });
        } else {
          console.warn('Coupon not found in database:', couponCode);
        }
      } catch (error) {
        console.error('Error fetching coupon data:', error);
        // Continue without discount if coupon lookup fails
      }
    }

    // Convert USD amount to BDT for SSLCOMMERZ
    // SSLCOMMERZ expects amounts in BDT, but our client sends USD amounts
    // Exchange rate: 1 USD = 100 BDT (as defined in currencies.ts)
    const usdToBdtRate = 100;
    const bdtAmount = finalUsdAmount * usdToBdtRate;
    const bdtDiscountAmount = appliedDiscountUsd * usdToBdtRate;

    console.log('Amount conversion:', {
      originalAmountUsd: amount,
      discountAmountUsd: appliedDiscountUsd,
      finalAmountUsd: finalUsdAmount,
      currency: 'USD to BDT',
      exchangeRate: usdToBdtRate,
      finalAmountBdt: bdtAmount,
      discountAmountBdt: bdtDiscountAmount
    });

    // Prepare data for SSLCOMMERZ
    const sslData = {
      total_amount: bdtAmount,
      currency: 'BDT',
      tran_id: transactionId,
      success_url: `${functionsBaseUrl}/handleSSLCommerzSuccess`,
      fail_url: `${functionsBaseUrl}/handleSSLCommerzFail`,
      cancel_url: `${functionsBaseUrl}/handleSSLCommerzCancel`,
      ipn_url: `${functionsBaseUrl}/handleSSLCommerzIPN`,
      shipping_method: 'No',
      product_name: `eComEasy.AI ${plan} Plan (${billingPeriod})`,
      product_category: 'Software',
      product_profile: 'general',
      cus_name: userName || billingDetails.fullName,
      cus_email: userEmail,
      cus_add1: billingDetails.address,
      cus_city: billingDetails.city,
      cus_state: billingDetails.state || 'N/A',
      cus_postcode: billingDetails.zipCode,
      cus_country: billingDetails.country,
      cus_phone: userPhone || '0000000000',
      value_a: userId,
      value_b: plan,
      value_c: billingPeriod,
      value_d: couponCode || ''
    };

    // Store transaction details in Firestore
    await db.collection('sslcommerzTransactions').doc(transactionId).set({
      userId,
      userEmail,
      amount: bdtAmount, // Store the final BDT amount (after discount)
      originalAmount: amount, // Store the original USD amount (before discount)
      originalAmountBdt: amount * usdToBdtRate, // Store the original BDT amount (before discount)
      discountAmountUsd: appliedDiscountUsd, // Store the USD discount amount
      discountAmountBdt: bdtDiscountAmount, // Store the BDT discount amount
      finalAmountUsd: finalUsdAmount, // Store the final USD amount (after discount)
      currency: 'BDT',
      plan,
      billingPeriod,
      transactionId,
      status: 'INITIATED',
      couponCode: couponCode || null,
      discountAmount: discountAmount || 0, // Keep for backward compatibility
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      billingDetails
    });

    // Log the data being sent to SSLCOMMERZ
    console.log('Sending data to SSLCOMMERZ:', {
      ...sslData,
      // Don't log sensitive information
      cus_name: '[REDACTED]',
      cus_email: '[REDACTED]',
      cus_phone: '[REDACTED]'
    });

    try {
      // Get a fresh SSLCOMMERZ instance with correct environment
      const sslcommerzInstance = getSSLCommerzInstance();
      const effectiveIsLive = storeId === 'httpsecomeasyai0live' ? true : (storeId === 'testbox' ? false : isLive);

      // Initialize payment with SSLCOMMERZ
      console.log('🚀 Calling SSLCOMMERZ init with environment settings:', {
        originalIsLive: isLive,
        effectiveIsLive: effectiveIsLive,
        storeId: storeId
      });
      console.log('🚀 Expected redirect domain:', effectiveIsLive ? 'securepay.sslcommerz.com' : 'sandbox.sslcommerz.com');

      const sslcz = await sslcommerzInstance.init(sslData);

      console.log('SSLCOMMERZ initialization response:', sslcz);

      // Check the redirect URL domain
      if (sslcz.GatewayPageURL) {
        const url = new URL(sslcz.GatewayPageURL);
        console.log('🔍 Redirect URL analysis:');
        console.log('   Full URL:', sslcz.GatewayPageURL);
        console.log('   Domain:', url.hostname);
        console.log('   Expected domain:', effectiveIsLive ? 'securepay.sslcommerz.com' : 'sandbox.sslcommerz.com');
        console.log('   Domain match:', url.hostname.includes(effectiveIsLive ? 'securepay' : 'sandbox'));

        if (effectiveIsLive && url.hostname.includes('sandbox')) {
          console.error('🚨 CRITICAL: Expected production but got sandbox URL!');
          console.error('   Store ID:', storeId);
          console.error('   Original isLive:', isLive);
          console.error('   Effective isLive:', effectiveIsLive);
          console.error('   This indicates the SSLCOMMERZ library is not respecting the environment settings');
        } else if (!effectiveIsLive && url.hostname.includes('securepay')) {
          console.error('🚨 CRITICAL: Expected sandbox but got production URL!');
          console.error('   Store ID:', storeId);
          console.error('   Original isLive:', isLive);
          console.error('   Effective isLive:', effectiveIsLive);
        } else {
          console.log('✅ URL domain matches expected environment');
        }
      }

      // Check if the initialization was successful
      if (sslcz.status !== 'SUCCESS') {
        console.error('SSLCOMMERZ initialization failed:', sslcz);
        throw new functions.https.HttpsError(
          'aborted',
          'Payment initialization failed',
          sslcz
        );
      }

      return {
        status: 'SUCCESS',
        message: 'Payment initialization successful',
        data: {
          transactionId,
          redirectUrl: sslcz.GatewayPageURL
        }
      };
    } catch (sslError) {
      console.error('SSLCOMMERZ API error:', sslError);
      throw new functions.https.HttpsError(
        'aborted',
        'SSLCOMMERZ API error',
        sslError.message || sslError
      );
    }

    // The return statement is now inside the try block
  } catch (error) {
    console.error('Error initializing SSLCOMMERZ payment:', error);
    throw new functions.https.HttpsError(
      'internal',
      'An error occurred while initializing the payment',
      error
    );
  }
});

/**
 * Handles successful payments from SSLCOMMERZ
 * Updated with enhanced logging and validation
 */
exports.handleSSLCommerzSuccess = functions.https.onRequest((req, res) => {
  cors(req, res, async () => {
    // Declare tran_id outside try block for error handling
    let tran_id;
    let userDocId;

    try {
      // Get transaction ID from the request
      tran_id = req.body.tran_id;

      if (!tran_id) {
        console.error('Missing transaction ID in success callback');
        return res.redirect(`${appUrl}/payment/failed?error=missing_transaction_id`);
      }

      console.log('Success callback received for transaction:', tran_id);
      console.log('Request body:', req.body);

      // Validate the transaction with SSLCOMMERZ
      let validationResponse;
      try {
        console.log('🔍 Starting validation process...');
        console.log('Validating transaction with val_id:', req.body.val_id);
        console.log('Request body keys:', Object.keys(req.body));

        validationResponse = await sslcommerz.validate({ val_id: req.body.val_id });
        console.log('SSLCOMMERZ validation response received');
        console.log('Response type:', typeof validationResponse);
        console.log('Response keys:', Object.keys(validationResponse || {}));
        console.log('SSLCOMMERZ validation response:', JSON.stringify(validationResponse, null, 2));

        // Check if the validation was successful
        // SSLCOMMERZ can return different status values, let's check for common success statuses
        const successStatuses = ['VALID', 'VALIDATED', 'SUCCESS', 'SUCCESSFUL'];
        const isValidPayment = successStatuses.includes(validationResponse.status);

        console.log('🔍 Validation Status Check:', {
          receivedStatus: validationResponse.status,
          expectedStatuses: successStatuses,
          isValid: isValidPayment
        });

        if (!isValidPayment) {
          console.error('SSLCOMMERZ validation failed. Expected one of:', successStatuses, 'Got:', validationResponse.status);
          console.error('Full validation response:', JSON.stringify(validationResponse, null, 2));

          // TEMPORARY: For debugging, let's check if this is a sandbox/test environment issue
          // In sandbox, sometimes the validation might work differently
          const isSandboxTest = !isLive && (
            validationResponse.status === 'VALIDATED' ||
            validationResponse.status === 'VALID' ||
            (validationResponse.APIConnect === 'DONE' && validationResponse.validated_on)
          );

          if (isSandboxTest) {
            console.log('🧪 SANDBOX MODE: Treating as valid payment despite status mismatch');
            console.log('APIConnect:', validationResponse.APIConnect);
            console.log('validated_on:', validationResponse.validated_on);
          } else {
            // Update transaction status in Firestore
            await db.collection('sslcommerzTransactions').doc(tran_id).update({
              status: 'FAILED',
              validationResponse: validationResponse,
              updatedAt: admin.firestore.FieldValue.serverTimestamp()
            });

            return res.redirect(`${appUrl}/payment/failed?transaction_id=${tran_id}&reason=validation_failed&status=${validationResponse.status}`);
          }
        }

        console.log('✅ Transaction validation successful! Status:', validationResponse.status);
      } catch (validationError) {
        console.error('❌ Error validating transaction:', validationError);
        console.error('Validation error details:', {
          message: validationError.message,
          stack: validationError.stack,
          val_id: req.body.val_id
        });

        // Update transaction status in Firestore
        await db.collection('sslcommerzTransactions').doc(tran_id).update({
          status: 'FAILED',
          validationError: validationError.message || 'Validation error',
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        return res.redirect(`${appUrl}/payment/failed?transaction_id=${tran_id}&error=validation_error`);
      }

      // Get transaction details from Firestore
      console.log('🔍 Fetching transaction details from Firestore for:', tran_id);
      const transactionDoc = await db.collection('sslcommerzTransactions').doc(tran_id).get();

      if (!transactionDoc.exists) {
        console.error('❌ Transaction not found in Firestore:', tran_id);
        return res.redirect(`${appUrl}/payment/failed?error=transaction_not_found`);
      }

      const transactionData = transactionDoc.data();
      const { userId, plan, billingPeriod } = transactionData;
      console.log('📄 Transaction data retrieved:', { userId, plan, billingPeriod, status: transactionData.status });

      // Update transaction status in Firestore
      console.log('💾 Updating transaction status to COMPLETED...');
      await db.collection('sslcommerzTransactions').doc(tran_id).update({
        status: 'COMPLETED',
        validationResponse: validationResponse,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      console.log('✅ Transaction status updated to COMPLETED');

      // Create payment log entry
      console.log('📝 Creating payment log entry...');
      const paymentLogData = {
        transactionId: tran_id,
        userId: userId,
        status: 'SUCCESS',
        packageName: plan,
        amount: transactionData.amount || 0,
        currency: transactionData.currency || 'BDT',
        paymentMethod: 'SSLCOMMERZ',
        billingPeriod: billingPeriod,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        validationResponse: validationResponse || {},
        userEmail: transactionData.userEmail || '',
        userName: transactionData.userName || ''
      };

      await db.collection('paymentLogs').add(paymentLogData);
      console.log('✅ Payment log entry created successfully');

      // Update user subscription in Firestore
      console.log('👤 Fetching user details for UID:', userId);

      // Query users collection by uid field (not document ID)
      const usersQuery = db.collection('users').where('uid', '==', userId);
      const userQuerySnapshot = await usersQuery.get();

      if (userQuerySnapshot.empty) {
        console.error('❌ User not found in Firestore with UID:', userId);
        console.log('🔍 Attempting to create user record...');

        // Try to create a basic user record if it doesn't exist
        try {
          const newUserDoc = await db.collection('users').add({
            uid: userId,
            email: transactionData.userEmail || '<EMAIL>',
            displayName: transactionData.userName || 'User',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            role: 'Free User',
            packageName: 'Free',
            status: 'active',
            createdViaPayment: true
          });
          console.log('✅ User record created:', newUserDoc.id);

          // Get the newly created user document
          const userDoc = await newUserDoc.get();
          var userRef = newUserDoc;
          userDocId = newUserDoc.id;
        } catch (createError) {
          console.error('❌ Failed to create user record:', createError);
          return res.redirect(`${appUrl}/payment/failed?error=user_creation_failed`);
        }
      } else {
        // User found, get the first document
        const userDoc = userQuerySnapshot.docs[0];
        var userRef = userDoc.ref;
        userDocId = userDoc.id;
        console.log('✅ User found in Firestore:', userDoc.id);
      }

      // Calculate subscription end date
      const now = new Date();
      let endDate = new Date();

      if (billingPeriod === 'monthly') {
        endDate.setMonth(now.getMonth() + 1);
      } else if (billingPeriod === 'yearly') {
        endDate.setFullYear(now.getFullYear() + 1);
      }

      // Determine role based on package for Admin Panel compatibility
      let role = 'Free User';
      if (plan === 'Pro') {
        role = 'Pro User';
      } else if (plan === 'Enterprise') {
        role = 'Enterprise User';
      } else if (plan === 'Pay-As-You-Go') {
        role = 'Pay-As-You-Go User';
      }

      // Update user subscription - FIXED: Write to fields that dashboard reads
      await userRef.update({
        // Direct fields that dashboard reads from
        packageName: plan,
        role: role, // Also update role for Admin Panel compatibility
        subscriptionExpireDate: endDate.toISOString(),
        subscriptionStatus: 'active',
        lastPaymentDate: admin.firestore.FieldValue.serverTimestamp(),
        paymentMethod: 'SSLCOMMERZ',

        // Keep nested subscription object for backward compatibility
        subscription: {
          plan,
          status: 'active',
          startDate: admin.firestore.FieldValue.serverTimestamp(),
          endDate: admin.firestore.Timestamp.fromDate(endDate),
          billingPeriod,
          paymentMethod: 'SSLCOMMERZ',
          transactionId: tran_id
        },
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      // If there's a coupon code, mark it as used
      if (transactionData.couponCode) {
        try {
          // Find the coupon
          const couponQuery = await db.collection('coupons')
            .where('code', '==', transactionData.couponCode)
            .limit(1)
            .get();

          if (!couponQuery.empty) {
            const couponDoc = couponQuery.docs[0];
            const couponData = couponDoc.data();

            // Increment the current uses
            await couponDoc.ref.update({
              currentUses: admin.firestore.FieldValue.increment(1)
            });

            // Update coupon usage status
            const couponUsageQuery = await db.collection('couponUsage')
              .where('couponCode', '==', transactionData.couponCode)
              .where('userId', '==', userId)
              .where('completed', '==', false)
              .limit(1)
              .get();

            if (!couponUsageQuery.empty) {
              await couponUsageQuery.docs[0].ref.update({
                completed: true,
                transactionId: tran_id,
                completedAt: admin.firestore.FieldValue.serverTimestamp()
              });
            }
          }
        } catch (error) {
          console.error('Error updating coupon usage:', error);
          // Don't block the success flow if coupon update fails
        }
      }

      // Redirect to success page
      const successUrl = `${appUrl}/payment/success?transaction_id=${tran_id}`;
      console.log('🎉 Payment processing completed successfully!');
      console.log('🔗 Redirecting to success page:', successUrl);
      console.log('📊 Final transaction summary:', {
        transactionId: tran_id,
        userId,
        plan,
        billingPeriod,
        amount: transactionData.amount,
        status: 'COMPLETED'
      });
      console.log('✅ User subscription updated successfully!');
      console.log('📋 Updated fields:', {
        packageName: plan,
        subscriptionExpireDate: endDate.toISOString(),
        subscriptionStatus: 'active',
        lastPaymentDate: 'serverTimestamp',
        paymentMethod: 'SSLCOMMERZ'
      });
      console.log('🔍 User document ID:', userDocId);
      console.log('🎯 Dashboard should now show package:', plan);
      return res.redirect(successUrl);
    } catch (error) {
      console.error('❌ Error handling SSLCOMMERZ success:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        transactionId: tran_id
      });
      return res.redirect(`${appUrl}/payment/failed?error=server_error`);
    }
  });
});

/**
 * Handles failed payments from SSLCOMMERZ
 */
exports.handleSSLCommerzFail = functions.https.onRequest((req, res) => {
  cors(req, res, async () => {
    try {
      console.log('Fail callback received with body:', req.body);

      // Get transaction ID from the request
      const { tran_id } = req.body;

      if (!tran_id) {
        console.error('Missing transaction ID in fail callback');
        return res.redirect(`${appUrl}/payment/failed?error=missing_transaction_id`);
      }

      console.log('Fail callback received for transaction:', tran_id);

      // Get transaction details for logging
      const transactionDoc = await db.collection('sslcommerzTransactions').doc(tran_id).get();
      let transactionData = {};
      if (transactionDoc.exists) {
        transactionData = transactionDoc.data();
      }

      // Update transaction status in Firestore
      await db.collection('sslcommerzTransactions').doc(tran_id).update({
        status: 'FAILED',
        failureDetails: req.body,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      console.log('Transaction status updated to FAILED');

      // Create payment log entry for failed payment
      if (transactionData.userId) {
        console.log('📝 Creating failed payment log entry...');
        const paymentLogData = {
          transactionId: tran_id,
          userId: transactionData.userId,
          status: 'FAILED',
          packageName: transactionData.plan || 'Unknown',
          amount: transactionData.amount || 0,
          currency: transactionData.currency || 'BDT',
          paymentMethod: 'SSLCOMMERZ',
          billingPeriod: transactionData.billingPeriod || 'monthly',
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          failureDetails: req.body || {},
          userEmail: transactionData.userEmail || '',
          userName: transactionData.userName || ''
        };

        await db.collection('paymentLogs').add(paymentLogData);
        console.log('✅ Failed payment log entry created successfully');
      }

      // Redirect to failed page
      return res.redirect(`${appUrl}/payment/failed?transaction_id=${tran_id}`);
    } catch (error) {
      console.error('Error handling SSLCOMMERZ failure:', error);
      return res.redirect(`${appUrl}/payment/failed?error=server_error`);
    }
  });
});

/**
 * Handles canceled payments from SSLCOMMERZ
 */
exports.handleSSLCommerzCancel = functions.https.onRequest((req, res) => {
  cors(req, res, async () => {
    try {
      console.log('Cancel callback received with body:', req.body);

      // Get transaction ID from the request
      const { tran_id } = req.body;

      if (!tran_id) {
        console.error('Missing transaction ID in cancel callback');
        return res.redirect(`${appUrl}/payment/canceled?error=missing_transaction_id`);
      }

      console.log('Cancel callback received for transaction:', tran_id);

      // Get transaction details for logging
      const transactionDoc = await db.collection('sslcommerzTransactions').doc(tran_id).get();
      let transactionData = {};
      if (transactionDoc.exists) {
        transactionData = transactionDoc.data();
      }

      // Update transaction status in Firestore
      await db.collection('sslcommerzTransactions').doc(tran_id).update({
        status: 'CANCELED',
        cancelDetails: req.body,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      console.log('Transaction status updated to CANCELED');

      // Note: We no longer create payment log entries for canceled payments
      // to keep user-facing payment history clean. Transaction details are
      // still preserved in sslcommerzTransactions collection for audit purposes.
      console.log('🚫 Skipping payment log creation for canceled transaction (user-facing history kept clean)');

      // Redirect to canceled page
      return res.redirect(`${appUrl}/payment/canceled?transaction_id=${tran_id}`);
    } catch (error) {
      console.error('Error handling SSLCOMMERZ cancellation:', error);
      return res.redirect(`${appUrl}/payment/canceled?error=server_error`);
    }
  });
});

/**
 * Handles Instant Payment Notifications (IPN) from SSLCOMMERZ
 */
exports.handleSSLCommerzIPN = functions.https.onRequest((req, res) => {
  cors(req, res, async () => {
    try {
      console.log('IPN callback received with body:', req.body);

      // Get transaction ID and validation ID from the request
      const { tran_id, val_id, status } = req.body;

      if (!tran_id || !val_id) {
        console.error('Missing transaction ID or validation ID in IPN callback');
        return res.status(400).json({ error: 'Missing transaction ID or validation ID' });
      }

      console.log('IPN callback received for transaction:', tran_id, 'with status:', status);

      try {
        // Validate the transaction with SSLCOMMERZ
        const response = await sslcommerz.validate({ val_id });
        console.log('SSLCOMMERZ validation response for IPN:', response);

        // Update transaction status in Firestore
        await db.collection('sslcommerzTransactions').doc(tran_id).update({
          status: response.status,
          ipnDetails: req.body,
          validationResponse: response,
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        console.log('Transaction status updated to', response.status);
      } catch (validationError) {
        console.error('Error validating transaction in IPN:', validationError);

        // Update transaction with error
        await db.collection('sslcommerzTransactions').doc(tran_id).update({
          ipnDetails: req.body,
          validationError: validationError.message || 'Validation error',
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        return res.status(500).json({ error: 'Validation error' });
      }

      // If the transaction is valid, update the user's subscription
      if (status === 'VALID') {
        // Get transaction details from Firestore
        const transactionDoc = await db.collection('sslcommerzTransactions').doc(tran_id).get();

        if (transactionDoc.exists) {
          const transactionData = transactionDoc.data();
          const { userId, plan, billingPeriod } = transactionData;

          // Update user subscription in Firestore
          const usersQuery = db.collection('users').where('uid', '==', userId);
          const userQuerySnapshot = await usersQuery.get();

          if (!userQuerySnapshot.empty) {
            const userDoc = userQuerySnapshot.docs[0];
            const userRef = userDoc.ref;
            // Calculate subscription end date
            const now = new Date();
            let endDate = new Date();

            if (billingPeriod === 'monthly') {
              endDate.setMonth(now.getMonth() + 1);
            } else if (billingPeriod === 'yearly') {
              endDate.setFullYear(now.getFullYear() + 1);
            }

            // Determine role based on package for Admin Panel compatibility
            let role = 'Free User';
            if (plan === 'Pro') {
              role = 'Pro User';
            } else if (plan === 'Enterprise') {
              role = 'Enterprise User';
            } else if (plan === 'Pay-As-You-Go') {
              role = 'Pay-As-You-Go User';
            }

            // Update user subscription - FIXED: Write to fields that dashboard reads
            await userRef.update({
              // Direct fields that dashboard reads from
              packageName: plan,
              role: role, // Also update role for Admin Panel compatibility
              subscriptionExpireDate: endDate.toISOString(),
              subscriptionStatus: 'active',
              lastPaymentDate: admin.firestore.FieldValue.serverTimestamp(),
              paymentMethod: 'SSLCOMMERZ',

              // Keep nested subscription object for backward compatibility
              subscription: {
                plan,
                status: 'active',
                startDate: admin.firestore.FieldValue.serverTimestamp(),
                endDate: admin.firestore.Timestamp.fromDate(endDate),
                billingPeriod,
                paymentMethod: 'SSLCOMMERZ',
                transactionId: tran_id
              },
              updatedAt: admin.firestore.FieldValue.serverTimestamp()
            });
          }
        }
      }

      // Return success response
      return res.status(200).json({ status: 'SUCCESS' });
    } catch (error) {
      console.error('Error handling SSLCOMMERZ IPN:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  });
});
