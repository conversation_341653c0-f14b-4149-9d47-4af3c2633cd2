// Test script to verify Supabase storage access
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';
import fs from 'fs';

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from the .env file
dotenv.config({ path: resolve(__dirname, '../.env') });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
const STORAGE_BUCKET = 'images';

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testStorageAccess() {
  console.log('Testing Supabase storage access...');
  
  try {
    // Test 1: List files in the bucket
    console.log(`\nTest 1: Listing files in '${STORAGE_BUCKET}' bucket...`);
    const { data: listData, error: listError } = await supabase.storage
      .from(STORAGE_BUCKET)
      .list();
    
    if (listError) {
      console.error('❌ Error listing files:', listError);
    } else {
      console.log('✅ Successfully listed files:', listData.length > 0 ? `${listData.length} files found` : 'No files found');
    }
    
    // Test 2: Upload a test file
    console.log(`\nTest 2: Uploading test file to '${STORAGE_BUCKET}' bucket...`);
    
    // Create a small test file
    const testFilePath = resolve(__dirname, 'test-file.txt');
    fs.writeFileSync(testFilePath, 'This is a test file for Supabase storage access');
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from(STORAGE_BUCKET)
      .upload('test-folder/test-file.txt', fs.readFileSync(testFilePath), {
        contentType: 'text/plain',
        upsert: true
      });
    
    // Clean up the test file
    fs.unlinkSync(testFilePath);
    
    if (uploadError) {
      console.error('❌ Error uploading test file:', uploadError);
    } else {
      console.log('✅ Successfully uploaded test file');
      
      // Test 3: Get the public URL of the uploaded file
      console.log(`\nTest 3: Getting public URL of the uploaded file...`);
      const { data: publicUrlData } = supabase.storage
        .from(STORAGE_BUCKET)
        .getPublicUrl('test-folder/test-file.txt');
      
      console.log('✅ Public URL:', publicUrlData.publicUrl);
      
      // Test 4: Delete the test file
      console.log(`\nTest 4: Deleting test file...`);
      const { data: deleteData, error: deleteError } = await supabase.storage
        .from(STORAGE_BUCKET)
        .remove(['test-folder/test-file.txt']);
      
      if (deleteError) {
        console.error('❌ Error deleting test file:', deleteError);
      } else {
        console.log('✅ Successfully deleted test file');
      }
    }
    
    console.log('\nStorage access test completed');
    
  } catch (error) {
    console.error('Error during storage access test:', error);
  }
}

testStorageAccess();
