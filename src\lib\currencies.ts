// Define currency information for different countries
export interface Currency {
  code: string;        // Currency code (e.g., USD, EUR)
  symbol: string;      // Currency symbol (e.g., $, €)
  name: string;        // Currency name (e.g., US Dollar, Euro)
  exchangeRate: number; // Exchange rate relative to USD (1 USD = X of this currency)
  symbolPosition: 'before' | 'after'; // Whether the symbol appears before or after the amount
}

// Define subscription pricing in USD
export interface SubscriptionPricing {
  starter: {
    price: number; // Always 0 for Starter (Free)
    features: string[];
  };
  payAsYouGo: {
    price: number; // Price per 15 generations
    features: string[];
  };
  pro: {
    monthly: number;
    yearly: number;
    features: string[];
  };
  enterprise: {
    monthly: number;
    yearly: number;
    features: string[];
  };
}

// Base pricing in USD
export const basePricing: SubscriptionPricing = {
  starter: {
    price: 0,
    features: [
      '10 stored images',
      '10 total generations (lifetime)',
      '10 saved data',
      'No custom prompts',
      '6h image deletion delay'
    ]
  },
  payAsYouGo: {
    price: 2, // $1 for 15 generations
    features: [
      '20 stored images',
      '5 custom prompts',
      '24h image deletion delay',
      'Credits never expire'
    ]
  },
  pro: {
    monthly: 10,
    yearly: 96, // $96/year (20% discount)
    features: [
      '50 stored images',
      '150 generations per month',
      '10 custom prompts',
      'Instant image deletion',
      'Priority support'
    ]
  },
  enterprise: {
    monthly: 100,
    yearly: 960, // $960/year (20% discount)
    features: [
      'Unlimited stored images',
      'Unlimited generations',
      'Unlimited custom prompts',
      'Instant image deletion',
      'Priority support',
      'Team management',
      'Dedicated account manager'
    ]
  }
};

// Map of countries to their currencies
export const countryCurrencies: Record<string, Currency> = {
  // North America
  "United States": {
    code: "USD",
    symbol: "$",
    name: "US Dollar",
    exchangeRate: 1,
    symbolPosition: 'before'
  },
  "Canada": {
    code: "CAD",
    symbol: "CA$",
    name: "Canadian Dollar",
    exchangeRate: 1.35,
    symbolPosition: 'before'
  },
  "Mexico": {
    code: "MXN",
    symbol: "MX$",
    name: "Mexican Peso",
    exchangeRate: 16.75,
    symbolPosition: 'before'
  },

  // Europe
  "United Kingdom": {
    code: "GBP",
    symbol: "£",
    name: "British Pound",
    exchangeRate: 0.78,
    symbolPosition: 'before'
  },
  "Germany": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "France": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Italy": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Spain": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Poland": {
    code: "PLN",
    symbol: "zł",
    name: "Polish Złoty",
    exchangeRate: 3.95,
    symbolPosition: 'after'
  },
  "Romania": {
    code: "RON",
    symbol: "lei",
    name: "Romanian Leu",
    exchangeRate: 4.57,
    symbolPosition: 'after'
  },
  "Russia": {
    code: "RUB",
    symbol: "₽",
    name: "Russian Ruble",
    exchangeRate: 92.5,
    symbolPosition: 'after'
  },
  "Switzerland": {
    code: "CHF",
    symbol: "CHF",
    name: "Swiss Franc",
    exchangeRate: 0.89,
    symbolPosition: 'before'
  },
  "Austria": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Belgium": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Bulgaria": {
    code: "BGN",
    symbol: "лв",
    name: "Bulgarian Lev",
    exchangeRate: 1.80,
    symbolPosition: 'after'
  },
  "Croatia": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Cyprus": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Czech Republic": {
    code: "CZK",
    symbol: "Kč",
    name: "Czech Koruna",
    exchangeRate: 22.5,
    symbolPosition: 'after'
  },
  "Denmark": {
    code: "DKK",
    symbol: "kr",
    name: "Danish Krone",
    exchangeRate: 6.85,
    symbolPosition: 'after'
  },
  "Estonia": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Finland": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Greece": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Hungary": {
    code: "HUF",
    symbol: "Ft",
    name: "Hungarian Forint",
    exchangeRate: 350.5,
    symbolPosition: 'after'
  },
  "Ireland": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Latvia": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Lithuania": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Luxembourg": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Malta": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Netherlands": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Portugal": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Slovakia": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Slovenia": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Sweden": {
    code: "SEK",
    symbol: "kr",
    name: "Swedish Krona",
    exchangeRate: 10.45,
    symbolPosition: 'after'
  },
  "Norway": {
    code: "NOK",
    symbol: "kr",
    name: "Norwegian Krone",
    exchangeRate: 10.65,
    symbolPosition: 'after'
  },
  "Iceland": {
    code: "ISK",
    symbol: "kr",
    name: "Icelandic Króna",
    exchangeRate: 138.5,
    symbolPosition: 'after'
  },
  "Albania": {
    code: "ALL",
    symbol: "L",
    name: "Albanian Lek",
    exchangeRate: 95.5,
    symbolPosition: 'before'
  },
  "Serbia": {
    code: "RSD",
    symbol: "дин.",
    name: "Serbian Dinar",
    exchangeRate: 108.5,
    symbolPosition: 'after'
  },
  "Ukraine": {
    code: "UAH",
    symbol: "₴",
    name: "Ukrainian Hryvnia",
    exchangeRate: 38.5,
    symbolPosition: 'before'
  },
  "Moldova": {
    code: "MDL",
    symbol: "L",
    name: "Moldovan Leu",
    exchangeRate: 17.8,
    symbolPosition: 'after'
  },
  "North Macedonia": {
    code: "MKD",
    symbol: "ден",
    name: "Macedonian Denar",
    exchangeRate: 56.5,
    symbolPosition: 'after'
  },
  "Bosnia and Herzegovina": {
    code: "BAM",
    symbol: "KM",
    name: "Bosnia and Herzegovina Convertible Mark",
    exchangeRate: 1.8,
    symbolPosition: 'before'
  },
  "Montenegro": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },

  // Asia
  "Japan": {
    code: "JPY",
    symbol: "¥",
    name: "Japanese Yen",
    exchangeRate: 149.5,
    symbolPosition: 'before'
  },
  "China": {
    code: "CNY",
    symbol: "¥",
    name: "Chinese Yuan",
    exchangeRate: 7.2,
    symbolPosition: 'before'
  },
  "India": {
    code: "INR",
    symbol: "₹",
    name: "Indian Rupee",
    exchangeRate: 83.5,
    symbolPosition: 'before'
  },
  "Bangladesh": {
    code: "BDT",
    symbol: "৳",
    name: "Bangladeshi Taka",
    exchangeRate: 100,
    symbolPosition: 'before'
  },
  "Singapore": {
    code: "SGD",
    symbol: "S$",
    name: "Singapore Dollar",
    exchangeRate: 1.34,
    symbolPosition: 'before'
  },
  "Afghanistan": {
    code: "AFN",
    symbol: "؋",
    name: "Afghan Afghani",
    exchangeRate: 73.5,
    symbolPosition: 'before'
  },
  "Malaysia": {
    code: "MYR",
    symbol: "RM",
    name: "Malaysian Ringgit",
    exchangeRate: 4.65,
    symbolPosition: 'before'
  },
  "Pakistan": {
    code: "PKR",
    symbol: "₨",
    name: "Pakistani Rupee",
    exchangeRate: 278.5,
    symbolPosition: 'before'
  },
  "Indonesia": {
    code: "IDR",
    symbol: "Rp",
    name: "Indonesian Rupiah",
    exchangeRate: 15650,
    symbolPosition: 'before'
  },
  "Thailand": {
    code: "THB",
    symbol: "฿",
    name: "Thai Baht",
    exchangeRate: 35.5,
    symbolPosition: 'before'
  },
  "Vietnam": {
    code: "VND",
    symbol: "₫",
    name: "Vietnamese Dong",
    exchangeRate: 24850,
    symbolPosition: 'after'
  },
  "South Korea": {
    code: "KRW",
    symbol: "₩",
    name: "South Korean Won",
    exchangeRate: 1350,
    symbolPosition: 'before'
  },
  "Philippines": {
    code: "PHP",
    symbol: "₱",
    name: "Philippine Peso",
    exchangeRate: 56.5,
    symbolPosition: 'before'
  },
  "Taiwan": {
    code: "TWD",
    symbol: "NT$",
    name: "New Taiwan Dollar",
    exchangeRate: 31.8,
    symbolPosition: 'before'
  },
  "Hong Kong": {
    code: "HKD",
    symbol: "HK$",
    name: "Hong Kong Dollar",
    exchangeRate: 7.8,
    symbolPosition: 'before'
  },
  "United Arab Emirates": {
    code: "AED",
    symbol: "د.إ",
    name: "UAE Dirham",
    exchangeRate: 3.67,
    symbolPosition: 'before'
  },
  "Saudi Arabia": {
    code: "SAR",
    symbol: "﷼",
    name: "Saudi Riyal",
    exchangeRate: 3.75,
    symbolPosition: 'before'
  },
  "Qatar": {
    code: "QAR",
    symbol: "﷼",
    name: "Qatari Riyal",
    exchangeRate: 3.64,
    symbolPosition: 'before'
  },
  "Kuwait": {
    code: "KWD",
    symbol: "د.ك",
    name: "Kuwaiti Dinar",
    exchangeRate: 0.31,
    symbolPosition: 'before'
  },
  "Bahrain": {
    code: "BHD",
    symbol: ".د.ب",
    name: "Bahraini Dinar",
    exchangeRate: 0.38,
    symbolPosition: 'before'
  },
  "Oman": {
    code: "OMR",
    symbol: "﷼",
    name: "Omani Rial",
    exchangeRate: 0.38,
    symbolPosition: 'before'
  },
  "Israel": {
    code: "ILS",
    symbol: "₪",
    name: "Israeli New Shekel",
    exchangeRate: 3.65,
    symbolPosition: 'before'
  },
  "Turkey": {
    code: "TRY",
    symbol: "₺",
    name: "Turkish Lira",
    exchangeRate: 32.5,
    symbolPosition: 'before'
  },
  "Iran": {
    code: "IRR",
    symbol: "﷼",
    name: "Iranian Rial",
    exchangeRate: 42000,
    symbolPosition: 'after'
  },
  "Iraq": {
    code: "IQD",
    symbol: "ع.د",
    name: "Iraqi Dinar",
    exchangeRate: 1310,
    symbolPosition: 'before'
  },
  "Jordan": {
    code: "JOD",
    symbol: "د.ا",
    name: "Jordanian Dinar",
    exchangeRate: 0.71,
    symbolPosition: 'before'
  },
  "Lebanon": {
    code: "LBP",
    symbol: "ل.ل",
    name: "Lebanese Pound",
    exchangeRate: 15000,
    symbolPosition: 'before'
  },
  "Syria": {
    code: "SYP",
    symbol: "£",
    name: "Syrian Pound",
    exchangeRate: 2512,
    symbolPosition: 'before'
  },
  "Kazakhstan": {
    code: "KZT",
    symbol: "₸",
    name: "Kazakhstani Tenge",
    exchangeRate: 450,
    symbolPosition: 'before'
  },
  "Uzbekistan": {
    code: "UZS",
    symbol: "лв",
    name: "Uzbekistani Som",
    exchangeRate: 12500,
    symbolPosition: 'after'
  },
  "Sri Lanka": {
    code: "LKR",
    symbol: "₨",
    name: "Sri Lankan Rupee",
    exchangeRate: 310,
    symbolPosition: 'before'
  },
  "Nepal": {
    code: "NPR",
    symbol: "₨",
    name: "Nepalese Rupee",
    exchangeRate: 133,
    symbolPosition: 'before'
  },
  "Myanmar": {
    code: "MMK",
    symbol: "K",
    name: "Myanmar Kyat",
    exchangeRate: 2100,
    symbolPosition: 'before'
  },
  "Cambodia": {
    code: "KHR",
    symbol: "៛",
    name: "Cambodian Riel",
    exchangeRate: 4100,
    symbolPosition: 'after'
  },
  "Laos": {
    code: "LAK",
    symbol: "₭",
    name: "Lao Kip",
    exchangeRate: 20500,
    symbolPosition: 'before'
  },
  "Mongolia": {
    code: "MNT",
    symbol: "₮",
    name: "Mongolian Tugrik",
    exchangeRate: 3450,
    symbolPosition: 'before'
  },

  // Oceania
  "Australia": {
    code: "AUD",
    symbol: "A$",
    name: "Australian Dollar",
    exchangeRate: 1.52,
    symbolPosition: 'before'
  },
  "New Zealand": {
    code: "NZD",
    symbol: "NZ$",
    name: "New Zealand Dollar",
    exchangeRate: 1.65,
    symbolPosition: 'before'
  },
  "Fiji": {
    code: "FJD",
    symbol: "FJ$",
    name: "Fijian Dollar",
    exchangeRate: 2.25,
    symbolPosition: 'before'
  },
  "Papua New Guinea": {
    code: "PGK",
    symbol: "K",
    name: "Papua New Guinean Kina",
    exchangeRate: 3.75,
    symbolPosition: 'before'
  },

  // Africa
  "Morocco": {
    code: "MAD",
    symbol: "د.م.",
    name: "Moroccan Dirham",
    exchangeRate: 10.05,
    symbolPosition: 'before'
  },
  "Algeria": {
    code: "DZD",
    symbol: "د.ج",
    name: "Algerian Dinar",
    exchangeRate: 134.5,
    symbolPosition: 'before'
  },
  "Tunisia": {
    code: "TND",
    symbol: "د.ت",
    name: "Tunisian Dinar",
    exchangeRate: 3.12,
    symbolPosition: 'before'
  },
  "Libya": {
    code: "LYD",
    symbol: "ل.د",
    name: "Libyan Dinar",
    exchangeRate: 4.85,
    symbolPosition: 'before'
  },
  "Egypt": {
    code: "EGP",
    symbol: "ج.م",
    name: "Egyptian Pound",
    exchangeRate: 48.5,
    symbolPosition: 'before'
  },
  "South Africa": {
    code: "ZAR",
    symbol: "R",
    name: "South African Rand",
    exchangeRate: 18.5,
    symbolPosition: 'before'
  },
  "Nigeria": {
    code: "NGN",
    symbol: "₦",
    name: "Nigerian Naira",
    exchangeRate: 1550,
    symbolPosition: 'before'
  },
  "Kenya": {
    code: "KES",
    symbol: "KSh",
    name: "Kenyan Shilling",
    exchangeRate: 129,
    symbolPosition: 'before'
  },
  "Ghana": {
    code: "GHS",
    symbol: "₵",
    name: "Ghanaian Cedi",
    exchangeRate: 14.5,
    symbolPosition: 'before'
  },
  "Ethiopia": {
    code: "ETB",
    symbol: "Br",
    name: "Ethiopian Birr",
    exchangeRate: 56.5,
    symbolPosition: 'before'
  },
  "Tanzania": {
    code: "TZS",
    symbol: "TSh",
    name: "Tanzanian Shilling",
    exchangeRate: 2550,
    symbolPosition: 'before'
  },
  "Uganda": {
    code: "UGX",
    symbol: "USh",
    name: "Ugandan Shilling",
    exchangeRate: 3750,
    symbolPosition: 'before'
  },
  "Zambia": {
    code: "ZMW",
    symbol: "ZK",
    name: "Zambian Kwacha",
    exchangeRate: 26.5,
    symbolPosition: 'before'
  },
  "Zimbabwe": {
    code: "ZWL",
    symbol: "Z$",
    name: "Zimbabwean Dollar",
    exchangeRate: 322.5,
    symbolPosition: 'before'
  },
  "Angola": {
    code: "AOA",
    symbol: "Kz",
    name: "Angolan Kwanza",
    exchangeRate: 850,
    symbolPosition: 'before'
  },
  "Mozambique": {
    code: "MZN",
    symbol: "MT",
    name: "Mozambican Metical",
    exchangeRate: 63.5,
    symbolPosition: 'before'
  },
  "Ivory Coast": {
    code: "XOF",
    symbol: "CFA",
    name: "West African CFA Franc",
    exchangeRate: 605,
    symbolPosition: 'before'
  },
  "Senegal": {
    code: "XOF",
    symbol: "CFA",
    name: "West African CFA Franc",
    exchangeRate: 605,
    symbolPosition: 'before'
  },
  "Cameroon": {
    code: "XAF",
    symbol: "FCFA",
    name: "Central African CFA Franc",
    exchangeRate: 605,
    symbolPosition: 'before'
  },

  // South America
  "Brazil": {
    code: "BRL",
    symbol: "R$",
    name: "Brazilian Real",
    exchangeRate: 5.15,
    symbolPosition: 'before'
  },
  "Argentina": {
    code: "ARS",
    symbol: "$",
    name: "Argentine Peso",
    exchangeRate: 870,
    symbolPosition: 'before'
  },
  "Colombia": {
    code: "COP",
    symbol: "$",
    name: "Colombian Peso",
    exchangeRate: 3950,
    symbolPosition: 'before'
  },
  "Chile": {
    code: "CLP",
    symbol: "$",
    name: "Chilean Peso",
    exchangeRate: 925,
    symbolPosition: 'before'
  },
  "Peru": {
    code: "PEN",
    symbol: "S/",
    name: "Sol",
    exchangeRate: 3.72,
    symbolPosition: 'before'
  },
  "Ecuador": {
    code: "USD",
    symbol: "$",
    name: "US Dollar",
    exchangeRate: 1,
    symbolPosition: 'before'
  },
  "Venezuela": {
    code: "VES",
    symbol: "Bs.",
    name: "Venezuelan Bolívar",
    exchangeRate: 36.5,
    symbolPosition: 'before'
  },
  "Bolivia": {
    code: "BOB",
    symbol: "Bs.",
    name: "Bolivian Boliviano",
    exchangeRate: 6.91,
    symbolPosition: 'before'
  },
  "Uruguay": {
    code: "UYU",
    symbol: "$U",
    name: "Uruguayan Peso",
    exchangeRate: 39.5,
    symbolPosition: 'before'
  },
  "Paraguay": {
    code: "PYG",
    symbol: "₲",
    name: "Paraguayan Guaraní",
    exchangeRate: 7450,
    symbolPosition: 'before'
  },

  // Caribbean
  "Jamaica": {
    code: "JMD",
    symbol: "J$",
    name: "Jamaican Dollar",
    exchangeRate: 155.5,
    symbolPosition: 'before'
  },
  "Trinidad and Tobago": {
    code: "TTD",
    symbol: "TT$",
    name: "Trinidad and Tobago Dollar",
    exchangeRate: 6.8,
    symbolPosition: 'before'
  },
  "Bahamas": {
    code: "BSD",
    symbol: "B$",
    name: "Bahamian Dollar",
    exchangeRate: 1,
    symbolPosition: 'before'
  },
  "Barbados": {
    code: "BBD",
    symbol: "Bds$",
    name: "Barbadian Dollar",
    exchangeRate: 2,
    symbolPosition: 'before'
  },
  "Dominican Republic": {
    code: "DOP",
    symbol: "RD$",
    name: "Dominican Peso",
    exchangeRate: 58.5,
    symbolPosition: 'before'
  },
  "Haiti": {
    code: "HTG",
    symbol: "G",
    name: "Haitian Gourde",
    exchangeRate: 132.5,
    symbolPosition: 'before'
  },
  "Cuba": {
    code: "CUP",
    symbol: "₱",
    name: "Cuban Peso",
    exchangeRate: 24,
    symbolPosition: 'before'
  },

  // Central America
  "Costa Rica": {
    code: "CRC",
    symbol: "₡",
    name: "Costa Rican Colón",
    exchangeRate: 535,
    symbolPosition: 'before'
  },
  "Panama": {
    code: "PAB",
    symbol: "B/.",
    name: "Panamanian Balboa",
    exchangeRate: 1,
    symbolPosition: 'before'
  },
  "Guatemala": {
    code: "GTQ",
    symbol: "Q",
    name: "Guatemalan Quetzal",
    exchangeRate: 7.8,
    symbolPosition: 'before'
  },
  "El Salvador": {
    code: "USD",
    symbol: "$",
    name: "US Dollar",
    exchangeRate: 1,
    symbolPosition: 'before'
  },
  "Honduras": {
    code: "HNL",
    symbol: "L",
    name: "Honduran Lempira",
    exchangeRate: 24.7,
    symbolPosition: 'before'
  },
  "Nicaragua": {
    code: "NIO",
    symbol: "C$",
    name: "Nicaraguan Córdoba",
    exchangeRate: 36.5,
    symbolPosition: 'before'
  },
  "Belize": {
    code: "BZD",
    symbol: "BZ$",
    name: "Belize Dollar",
    exchangeRate: 2,
    symbolPosition: 'before'
  },

  // Middle East & Central Asia
  "Armenia": {
    code: "AMD",
    symbol: "֏",
    name: "Armenian Dram",
    exchangeRate: 390,
    symbolPosition: 'before'
  },
  "Azerbaijan": {
    code: "AZN",
    symbol: "₼",
    name: "Azerbaijani Manat",
    exchangeRate: 1.7,
    symbolPosition: 'before'
  },
  "Georgia": {
    code: "GEL",
    symbol: "₾",
    name: "Georgian Lari",
    exchangeRate: 2.65,
    symbolPosition: 'before'
  },
  "Kyrgyzstan": {
    code: "KGS",
    symbol: "с",
    name: "Kyrgyzstani Som",
    exchangeRate: 89.5,
    symbolPosition: 'before'
  },
  "Tajikistan": {
    code: "TJS",
    symbol: "ЅМ",
    name: "Tajikistani Somoni",
    exchangeRate: 11.2,
    symbolPosition: 'before'
  },
  "Turkmenistan": {
    code: "TMT",
    symbol: "m",
    name: "Turkmenistani Manat",
    exchangeRate: 3.5,
    symbolPosition: 'before'
  },
  "Yemen": {
    code: "YER",
    symbol: "﷼",
    name: "Yemeni Rial",
    exchangeRate: 250,
    symbolPosition: 'before'
  },
  "Palestine": {
    code: "ILS",
    symbol: "₪",
    name: "Israeli New Shekel",
    exchangeRate: 3.65,
    symbolPosition: 'before'
  },

  // Pacific Islands
  "Solomon Islands": {
    code: "SBD",
    symbol: "SI$",
    name: "Solomon Islands Dollar",
    exchangeRate: 8.4,
    symbolPosition: 'before'
  },
  "Vanuatu": {
    code: "VUV",
    symbol: "VT",
    name: "Vanuatu Vatu",
    exchangeRate: 119,
    symbolPosition: 'before'
  },
  "Samoa": {
    code: "WST",
    symbol: "WS$",
    name: "Samoan Tala",
    exchangeRate: 2.75,
    symbolPosition: 'before'
  },
  "Tonga": {
    code: "TOP",
    symbol: "T$",
    name: "Tongan Paʻanga",
    exchangeRate: 2.4,
    symbolPosition: 'before'
  },
  "Kiribati": {
    code: "AUD",
    symbol: "A$",
    name: "Australian Dollar",
    exchangeRate: 1.52,
    symbolPosition: 'before'
  },
  "Micronesia": {
    code: "USD",
    symbol: "$",
    name: "US Dollar",
    exchangeRate: 1,
    symbolPosition: 'before'
  },
  "Marshall Islands": {
    code: "USD",
    symbol: "$",
    name: "US Dollar",
    exchangeRate: 1,
    symbolPosition: 'before'
  },
  "Palau": {
    code: "USD",
    symbol: "$",
    name: "US Dollar",
    exchangeRate: 1,
    symbolPosition: 'before'
  },
  "Nauru": {
    code: "AUD",
    symbol: "A$",
    name: "Australian Dollar",
    exchangeRate: 1.52,
    symbolPosition: 'before'
  },
  "Tuvalu": {
    code: "AUD",
    symbol: "A$",
    name: "Australian Dollar",
    exchangeRate: 1.52,
    symbolPosition: 'before'
  },

  // Other African Countries
  "Benin": {
    code: "XOF",
    symbol: "CFA",
    name: "West African CFA Franc",
    exchangeRate: 605,
    symbolPosition: 'before'
  },
  "Burkina Faso": {
    code: "XOF",
    symbol: "CFA",
    name: "West African CFA Franc",
    exchangeRate: 605,
    symbolPosition: 'before'
  },
  "Burundi": {
    code: "BIF",
    symbol: "FBu",
    name: "Burundian Franc",
    exchangeRate: 2850,
    symbolPosition: 'before'
  },
  "Cabo Verde": {
    code: "CVE",
    symbol: "CVE",
    name: "Cape Verdean Escudo",
    exchangeRate: 101.5,
    symbolPosition: 'before'
  },
  "Central African Republic": {
    code: "XAF",
    symbol: "FCFA",
    name: "Central African CFA Franc",
    exchangeRate: 605,
    symbolPosition: 'before'
  },
  "Chad": {
    code: "XAF",
    symbol: "FCFA",
    name: "Central African CFA Franc",
    exchangeRate: 605,
    symbolPosition: 'before'
  },
  "Comoros": {
    code: "KMF",
    symbol: "CF",
    name: "Comorian Franc",
    exchangeRate: 455,
    symbolPosition: 'before'
  },
  "Congo": {
    code: "XAF",
    symbol: "FCFA",
    name: "Central African CFA Franc",
    exchangeRate: 605,
    symbolPosition: 'before'
  },
  "Djibouti": {
    code: "DJF",
    symbol: "Fdj",
    name: "Djiboutian Franc",
    exchangeRate: 178,
    symbolPosition: 'before'
  },
  "Equatorial Guinea": {
    code: "XAF",
    symbol: "FCFA",
    name: "Central African CFA Franc",
    exchangeRate: 605,
    symbolPosition: 'before'
  },
  "Eritrea": {
    code: "ERN",
    symbol: "Nfk",
    name: "Eritrean Nakfa",
    exchangeRate: 15,
    symbolPosition: 'before'
  },
  "Eswatini": {
    code: "SZL",
    symbol: "E",
    name: "Swazi Lilangeni",
    exchangeRate: 18.5,
    symbolPosition: 'before'
  },
  "Gabon": {
    code: "XAF",
    symbol: "FCFA",
    name: "Central African CFA Franc",
    exchangeRate: 605,
    symbolPosition: 'before'
  },
  "Gambia": {
    code: "GMD",
    symbol: "D",
    name: "Gambian Dalasi",
    exchangeRate: 67.5,
    symbolPosition: 'before'
  },
  "Guinea": {
    code: "GNF",
    symbol: "FG",
    name: "Guinean Franc",
    exchangeRate: 8650,
    symbolPosition: 'before'
  },
  "Guinea-Bissau": {
    code: "XOF",
    symbol: "CFA",
    name: "West African CFA Franc",
    exchangeRate: 605,
    symbolPosition: 'before'
  },
  "Lesotho": {
    code: "LSL",
    symbol: "L",
    name: "Lesotho Loti",
    exchangeRate: 18.5,
    symbolPosition: 'before'
  },
  "Liberia": {
    code: "LRD",
    symbol: "L$",
    name: "Liberian Dollar",
    exchangeRate: 190,
    symbolPosition: 'before'
  },
  "Madagascar": {
    code: "MGA",
    symbol: "Ar",
    name: "Malagasy Ariary",
    exchangeRate: 4450,
    symbolPosition: 'before'
  },
  "Malawi": {
    code: "MWK",
    symbol: "MK",
    name: "Malawian Kwacha",
    exchangeRate: 1650,
    symbolPosition: 'before'
  },
  "Mali": {
    code: "XOF",
    symbol: "CFA",
    name: "West African CFA Franc",
    exchangeRate: 605,
    symbolPosition: 'before'
  },
  "Mauritania": {
    code: "MRU",
    symbol: "UM",
    name: "Mauritanian Ouguiya",
    exchangeRate: 38.5,
    symbolPosition: 'before'
  },
  "Mauritius": {
    code: "MUR",
    symbol: "₨",
    name: "Mauritian Rupee",
    exchangeRate: 45.5,
    symbolPosition: 'before'
  },
  "Namibia": {
    code: "NAD",
    symbol: "N$",
    name: "Namibian Dollar",
    exchangeRate: 18.5,
    symbolPosition: 'before'
  },
  "Niger": {
    code: "XOF",
    symbol: "CFA",
    name: "West African CFA Franc",
    exchangeRate: 605,
    symbolPosition: 'before'
  },
  "Rwanda": {
    code: "RWF",
    symbol: "RF",
    name: "Rwandan Franc",
    exchangeRate: 1250,
    symbolPosition: 'before'
  },
  "Sao Tome and Principe": {
    code: "STN",
    symbol: "Db",
    name: "São Tomé and Príncipe Dobra",
    exchangeRate: 22.5,
    symbolPosition: 'before'
  },
  "Seychelles": {
    code: "SCR",
    symbol: "SR",
    name: "Seychellois Rupee",
    exchangeRate: 13.5,
    symbolPosition: 'before'
  },
  "Sierra Leone": {
    code: "SLL",
    symbol: "Le",
    name: "Sierra Leonean Leone",
    exchangeRate: 19750,
    symbolPosition: 'before'
  },
  "Somalia": {
    code: "SOS",
    symbol: "Sh.So.",
    name: "Somali Shilling",
    exchangeRate: 570,
    symbolPosition: 'before'
  },
  "South Sudan": {
    code: "SSP",
    symbol: "£",
    name: "South Sudanese Pound",
    exchangeRate: 985,
    symbolPosition: 'before'
  },
  "Sudan": {
    code: "SDG",
    symbol: "ج.س.",
    name: "Sudanese Pound",
    exchangeRate: 600,
    symbolPosition: 'before'
  },
  "Togo": {
    code: "XOF",
    symbol: "CFA",
    name: "West African CFA Franc",
    exchangeRate: 605,
    symbolPosition: 'before'
  },

  // Other European Countries
  "Andorra": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Belarus": {
    code: "BYN",
    symbol: "Br",
    name: "Belarusian Ruble",
    exchangeRate: 3.25,
    symbolPosition: 'before'
  },
  "Liechtenstein": {
    code: "CHF",
    symbol: "CHF",
    name: "Swiss Franc",
    exchangeRate: 0.89,
    symbolPosition: 'before'
  },
  "Monaco": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "San Marino": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },
  "Vatican City": {
    code: "EUR",
    symbol: "€",
    name: "Euro",
    exchangeRate: 0.92,
    symbolPosition: 'before'
  },

  // Other Asian Countries
  "Bhutan": {
    code: "BTN",
    symbol: "Nu.",
    name: "Bhutanese Ngultrum",
    exchangeRate: 83.5,
    symbolPosition: 'before'
  },
  "Brunei": {
    code: "BND",
    symbol: "B$",
    name: "Brunei Dollar",
    exchangeRate: 1.34,
    symbolPosition: 'before'
  },
  "Maldives": {
    code: "MVR",
    symbol: "Rf",
    name: "Maldivian Rufiyaa",
    exchangeRate: 15.4,
    symbolPosition: 'before'
  },
  "North Korea": {
    code: "KPW",
    symbol: "₩",
    name: "North Korean Won",
    exchangeRate: 900,
    symbolPosition: 'before'
  },
  "Timor-Leste": {
    code: "USD",
    symbol: "$",
    name: "US Dollar",
    exchangeRate: 1,
    symbolPosition: 'before'
  },

  // Small Island Nations & Territories
  "Antigua and Barbuda": {
    code: "XCD",
    symbol: "EC$",
    name: "East Caribbean Dollar",
    exchangeRate: 2.7,
    symbolPosition: 'before'
  },
  "Dominica": {
    code: "XCD",
    symbol: "EC$",
    name: "East Caribbean Dollar",
    exchangeRate: 2.7,
    symbolPosition: 'before'
  },
  "Grenada": {
    code: "XCD",
    symbol: "EC$",
    name: "East Caribbean Dollar",
    exchangeRate: 2.7,
    symbolPosition: 'before'
  },
  "Saint Kitts and Nevis": {
    code: "XCD",
    symbol: "EC$",
    name: "East Caribbean Dollar",
    exchangeRate: 2.7,
    symbolPosition: 'before'
  },
  "Saint Lucia": {
    code: "XCD",
    symbol: "EC$",
    name: "East Caribbean Dollar",
    exchangeRate: 2.7,
    symbolPosition: 'before'
  },
  "Saint Vincent and the Grenadines": {
    code: "XCD",
    symbol: "EC$",
    name: "East Caribbean Dollar",
    exchangeRate: 2.7,
    symbolPosition: 'before'
  },
  "Guyana": {
    code: "GYD",
    symbol: "G$",
    name: "Guyanese Dollar",
    exchangeRate: 210,
    symbolPosition: 'before'
  },
  "Suriname": {
    code: "SRD",
    symbol: "$",
    name: "Surinamese Dollar",
    exchangeRate: 38.5,
    symbolPosition: 'before'
  },

  // Default currency for countries not in the list
  "default": {
    code: "USD",
    symbol: "$",
    name: "US Dollar",
    exchangeRate: 1,
    symbolPosition: 'before'
  }
};

// Function to get currency for a country
export function getCurrencyForCountry(country: string): Currency {
  return countryCurrencies[country] || countryCurrencies.default;
}

// Function to format price in the given currency
export function formatPrice(amount: number, currency: Currency, showUsdReference = false): string {
  // Calculate the price in the target currency
  const convertedAmount = amount * currency.exchangeRate;

  // Format the number with 2 decimal places, unless it's JPY or other currencies that don't typically use decimals
  const formattedAmount = ['JPY', 'KRW', 'VND', 'IDR'].includes(currency.code)
    ? Math.round(convertedAmount).toString()
    : convertedAmount.toFixed(2);

  // Format with the currency symbol in the correct position
  const formattedPrice = currency.symbolPosition === 'before'
    ? `${currency.symbol}${formattedAmount}`
    : `${formattedAmount} ${currency.symbol}`;

  // Add USD reference if requested and not already in USD
  if (showUsdReference && currency.code !== 'USD') {
    return `${formattedPrice} ($${amount.toFixed(2)} USD)`;
  }

  return formattedPrice;
}
