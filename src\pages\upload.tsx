import { useState, useEffect, useRef } from 'react';
import { Upload as UploadIcon, Trash2, Camera, ZoomIn, ZoomOut, AlertCircle, RefreshCw, Check, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import imageCompression from 'browser-image-compression';
import { toast } from 'react-hot-toast';
import { supabase, STORAGE_BUCKET } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';
import { useStore } from '@/lib/store';
import { useNavigate } from 'react-router-dom';
import { canUploadMoreImages, incrementUploadedImagesCount, canDeleteImage, decrementUploadedImagesCount } from '@/lib/userLimits';
import { Timestamp } from 'firebase/firestore';

interface UploadedImage {
  id: string;
  url: string;
  name: string;
  path: string;
  selected?: boolean;
  uploadTime?: Timestamp;
}

export function Upload() {
  const [images, setImages] = useState<UploadedImage[]>([]);
  const [uploading, setUploading] = useState(false);
  const [savingImage, setSavingImage] = useState(false); // Dedicated state for Save Image button
  const [loading, setLoading] = useState(true);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleting, setDeleting] = useState(false); // State for delete operation loading
  const [showCameraModal, setShowCameraModal] = useState(false);
  const [cameraStream, setCameraStream] = useState<MediaStream | null>(null);
  const [zoomLevel, setZoomLevel] = useState(2); // Default zoom level (1=small, 2=medium, 3=large)
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [capturedBlob, setCapturedBlob] = useState<Blob | null>(null);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const cameraClickSoundRef = useRef<HTMLAudioElement>(null);
  const savingInProgressRef = useRef<boolean>(false); // Additional safeguard for save operations
  const { user } = useStore();
  const navigate = useNavigate();

  // Define grid column classes based on zoom level
  const gridColumnClasses = {
    1: "grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6", // Small (more columns)
    2: "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4", // Medium (default)
    3: "grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3"  // Large (fewer columns)
  };

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!user) {
      toast.error('Please log in to upload images');
      navigate('/auth?redirect=/upload');
      return;
    }

    const initializeStorage = async () => {
      try {
        console.log('Initializing storage...');
        await loadExistingImages();
        console.log('Existing images loaded successfully');
      } catch (error) {
        console.error('Error initializing Supabase storage:', error);
        toast.error('Error initializing storage. Please check console for details.');
      } finally {
        setLoading(false);
      }
    };

    initializeStorage();
  }, [user, navigate]);

  // Clean up camera stream when component unmounts or modal closes
  useEffect(() => {
    return () => {
      if (cameraStream) {
        cameraStream.getTracks().forEach(track => track.stop());
      }
    };
  }, [cameraStream]);

  const loadExistingImages = async () => {
    if (!user) return;

    try {
      // Use user ID in the folder path to separate user images
      const userImagesPath = `product-images/${user.id}`;
      console.log(`Attempting to list images from ${userImagesPath} folder...`);

      const { data, error } = await supabase.storage
        .from(STORAGE_BUCKET)
        .list(userImagesPath, {
          limit: 100,
          offset: 0,
        });

      if (error) {
        console.error('Error listing images:', error);
        // If the folder doesn't exist yet, it's not an error
        if (error.message.includes('The resource was not found')) {
          console.log(`${userImagesPath} folder not found, it will be created on first upload`);
          return;
        }
        throw error;
      }

      console.log('Images data retrieved:', data);
      if (data && data.length > 0) {
        // Sort the data array by created_at timestamp (newest first)
        // If created_at is available in the file metadata
        const sortedData = [...data].sort((a, b) => {
          // If created_at is available, use it for sorting
          if (a.created_at && b.created_at) {
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          }
          // If no timestamps available, keep original order (or sort by name as a fallback)
          return a.name.localeCompare(b.name);
        });

        const loadedImages = await Promise.all(
          sortedData.map(async (file) => {
            const { data: publicUrlData } = supabase.storage
              .from(STORAGE_BUCKET)
              .getPublicUrl(`${userImagesPath}/${file.name}`);

            // Convert created_at to Firestore Timestamp if available
            const uploadTime = file.created_at
              ? Timestamp.fromDate(new Date(file.created_at))
              : Timestamp.now();

            return {
              id: uuidv4(),
              url: publicUrlData.publicUrl,
              name: file.name,
              path: `${userImagesPath}/${file.name}`,
              uploadTime
            };
          })
        );
        setImages(loadedImages);
        console.log('Loaded images:', loadedImages);

        // Update the user's uploaded images count in Firestore
        if (loadedImages.length > 0) {
          try {
            await incrementUploadedImagesCount(user.id, 0); // Just ensure the record exists
          } catch (err) {
            console.error('Error updating image count:', err);
          }
        }
      } else {
        console.log('No existing images found');
      }
    } catch (error) {
      console.error('Error loading images:', error);
      toast.error('Error loading existing images');
    } finally {
      setLoading(false);
    }
  };

  // Function to open camera
  const openCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment',
          width: { ideal: 1920 },
          height: { ideal: 1080 }
        }
      });

      setCameraStream(stream);
      setShowCameraModal(true);
      setIsPreviewMode(false);

      // Set the stream to the video element when modal is shown
      setTimeout(() => {
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }
      }, 100);
    } catch (error) {
      console.error('Error accessing camera:', error);
      toast.error('Could not access camera. Please check permissions.');
    }
  };

  // Function to capture image from camera
  const captureImage = async () => {
    if (!videoRef.current || !canvasRef.current || !user) return;

    // Play camera click sound
    if (cameraClickSoundRef.current) {
      cameraClickSoundRef.current.currentTime = 0; // Reset sound to beginning
      cameraClickSoundRef.current.play().catch(error => {
        // Silently handle any autoplay restrictions
        console.warn('Could not play camera click sound:', error);
      });
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw the current video frame to the canvas
    const context = canvas.getContext('2d');
    if (!context) return;

    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert canvas to blob
    canvas.toBlob((blob) => {
      if (!blob) {
        toast.error('Failed to capture image');
        return;
      }

      // Store the blob for later use
      setCapturedBlob(blob);

      // Create a preview URL
      const imageUrl = URL.createObjectURL(blob);
      setPreviewImage(imageUrl);

      // Switch to preview mode
      setIsPreviewMode(true);

    }, 'image/jpeg', 0.9);
  };

  // Function to reset preview state
  const resetPreview = () => {
    if (previewImage) {
      URL.revokeObjectURL(previewImage);
    }
    setPreviewImage(null);
    setCapturedBlob(null);
    setIsPreviewMode(false);
  };

  // Function to recapture the image
  const recaptureImage = () => {
    // First reset preview state
    resetPreview();

    // Ensure video element gets the stream again
    setTimeout(() => {
      // Check if we have a valid and active stream
      if (videoRef.current && cameraStream && cameraStream.active) {
        videoRef.current.srcObject = cameraStream;
      } else {
        // If stream was lost or inactive, get a new one
        // First ensure any existing tracks are stopped
        if (cameraStream) {
          cameraStream.getTracks().forEach(track => track.stop());
        }

        navigator.mediaDevices.getUserMedia({
          video: {
            facingMode: 'environment',
            width: { ideal: 1920 },
            height: { ideal: 1080 }
          }
        })
        .then(stream => {
          setCameraStream(stream);
          if (videoRef.current) {
            videoRef.current.srcObject = stream;
          }
        })
        .catch(error => {
          console.error('Error accessing camera during recapture:', error);
          toast.error('Could not access camera. Please check permissions.');
        });
      }
    }, 100);
  };

  // Function to save the captured image
  const saveImage = async () => {
    if (!capturedBlob || !user) return;

    // Prevent multiple simultaneous save operations using both state and ref
    if (savingImage || savingInProgressRef.current) {
      return;
    }

    // Set saving state immediately for instant feedback
    savingInProgressRef.current = true;
    setSavingImage(true);

    try {
      // Check if user has reached their upload limit
      const canUpload = await canUploadMoreImages(user.id);
      if (!canUpload) {
        toast.error('You have reached your maximum image upload limit. Please delete some images or upgrade your plan.');
        // Reset both state and ref on early return
        savingInProgressRef.current = false;
        setSavingImage(false);
        return;
      }

      // Compress the image
      const options = {
        maxSizeMB: 1,
        maxWidthOrHeight: 1920,
      };
      const compressedFile = await imageCompression(new File([capturedBlob], 'camera-capture.jpg', { type: 'image/jpeg' }), options);

      // Generate a unique file name
      const fileName = `${uuidv4()}.jpg`;
      const filePath = `product-images/${user.id}/${fileName}`;

      // Upload to Supabase Storage
      const { error } = await supabase.storage
        .from(STORAGE_BUCKET)
        .upload(filePath, compressedFile, {
          cacheControl: '3600',
          upsert: false,
        });

      if (error) {
        throw error;
      }

      // Get the public URL
      const { data: publicUrlData } = supabase.storage
        .from(STORAGE_BUCKET)
        .getPublicUrl(filePath);

      const newImage = {
        id: uuidv4(),
        url: publicUrlData.publicUrl,
        name: 'Camera Capture',
        path: filePath,
        uploadTime: Timestamp.now()
      };

      // Add new image to the beginning of the array so it appears first
      setImages((prev) => [newImage, ...prev]);

      // Update the user's uploaded images count in Firestore
      await incrementUploadedImagesCount(user.id, 1);

      toast.success('Image captured and uploaded successfully!');

      // Reset preview state
      resetPreview();

      // Reinitialize camera stream after saving
      setTimeout(() => {
        // If we still have a valid stream, reconnect it to the video element
        if (cameraStream && cameraStream.active) {
          if (videoRef.current) {
            videoRef.current.srcObject = cameraStream;
          }
        } else {
          // If stream was lost or inactive, get a new one
          navigator.mediaDevices.getUserMedia({
            video: {
              facingMode: 'environment',
              width: { ideal: 1920 },
              height: { ideal: 1080 }
            }
          })
          .then(stream => {
            setCameraStream(stream);
            if (videoRef.current) {
              videoRef.current.srcObject = stream;
            }
          })
          .catch(error => {
            console.error('Error reinitializing camera after save:', error);
            toast.error('Could not reinitialize camera. Please try again.');
          });
        }
      }, 100);
    } catch (error) {
      console.error('Error uploading captured image:', error);
      toast.error('Error uploading captured image');
    } finally {
      // Always reset saving state
      savingInProgressRef.current = false;
      setSavingImage(false);
    }
  };

  // Function to close camera
  const closeCamera = () => {
    if (cameraStream) {
      cameraStream.getTracks().forEach(track => track.stop());
      setCameraStream(null);
    }
    // Reset preview state when closing camera
    resetPreview();
    setShowCameraModal(false);
  };

  // Function to trigger file input click
  const handleUploadButtonClick = () => {
    console.log('Upload button clicked');
    if (fileInputRef.current) {
      fileInputRef.current.click();
    } else {
      console.error('File input ref is null');
    }
  };

  const handleUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!user) {
      toast.error('Please log in to upload images');
      navigate('/auth?redirect=/upload');
      return;
    }

    const files = e.target.files;
    console.log('File input change event triggered', files);

    if (!files?.length) {
      console.log('No files selected');
      return;
    }

    // Check if user has reached their upload limit
    const canUpload = await canUploadMoreImages(user.id);
    if (!canUpload) {
      toast.error('You have reached your maximum image upload limit. Please delete some images or upgrade your plan.');
      return;
    }

    console.log(`${files.length} files selected for upload`);
    setUploading(true);
    try {
      const uploadPromises = Array.from(files).map(async (file) => {
        // Compress the image first
        const options = {
          maxSizeMB: 1,
          maxWidthOrHeight: 1920,
        };
        console.log(`Compressing file: ${file.name}`);
        const compressedFile = await imageCompression(file, options);

        // Generate a unique file name with user ID in the path
        const fileExt = file.name.split('.').pop();
        const fileName = `${uuidv4()}.${fileExt}`;
        const filePath = `product-images/${user.id}/${fileName}`;

        console.log(`Uploading file to ${filePath}`);
        // Upload to Supabase Storage
        const { error } = await supabase.storage
          .from(STORAGE_BUCKET)
          .upload(filePath, compressedFile, {
            cacheControl: '3600',
            upsert: false,
          });

        if (error) {
          console.error(`Error uploading file ${file.name}:`, error);
          throw error;
        }

        console.log(`File uploaded successfully, getting public URL`);
        // Get the public URL
        const { data: publicUrlData } = supabase.storage
          .from(STORAGE_BUCKET)
          .getPublicUrl(filePath);

        return {
          id: uuidv4(),
          url: publicUrlData.publicUrl,
          name: file.name,
          path: filePath,
          uploadTime: Timestamp.now()
        };
      });

      const uploadedImages = await Promise.all(uploadPromises);
      console.log('All images uploaded successfully:', uploadedImages);
      // Add new images to the beginning of the array so they appear first
      setImages((prev) => [...uploadedImages, ...prev]);

      // Update the user's uploaded images count in Firestore
      await incrementUploadedImagesCount(user.id, uploadedImages.length);

      toast.success('Images uploaded to Supabase successfully!');
    } catch (error) {
      console.error('Error uploading images:', error);
      toast.error('Error uploading images to Supabase');
    } finally {
      setUploading(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const toggleImageSelection = (id: string) => {
    setImages(prev => prev.map(img =>
      img.id === id ? { ...img, selected: !img.selected } : img
    ));
  };

  // Function to handle navigation to the caption page
  const handleNavigateToCaption = () => {
    // Get selected images (if any)
    const selectedImages = images.filter(img => img.selected);

    // Navigate to the caption page regardless of image selection
    navigate('/app/caption', {
      state: {
        // Only pass selected image data if there are selected images
        selectedImageIds: selectedImages.length > 0 ? selectedImages.map(img => img.id) : [],
        selectedImageUrls: selectedImages.length > 0 ? selectedImages.map(img => img.url) : []
      }
    });
  };

  // Function to handle zoom level change
  const handleZoomChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setZoomLevel(Number(e.target.value));
  };

  const handleBulkDelete = async () => {
    const selectedImages = images.filter(img => img.selected);
    if (selectedImages.length === 0) return;

    setDeleting(true); // Start loading state
    try {
      // Check if any of the selected images are within the deletion delay period
      const imagesWithinDelayPeriod = [];

      for (const image of selectedImages) {
        if (!image.uploadTime) {
          // If no upload time is recorded, assume it's an old image and allow deletion
          continue;
        }

        const canDelete = await canDeleteImage(user?.id || '', image.uploadTime);
        if (!canDelete) {
          imagesWithinDelayPeriod.push(image);
        }
      }

      if (imagesWithinDelayPeriod.length > 0) {
        toast.error(
          `${imagesWithinDelayPeriod.length} image(s) cannot be deleted yet due to the deletion delay period. ` +
          `Free users must wait before deleting uploaded images.`
        );
        setDeleting(false); // Clear loading state
        return;
      }

      const deletePromises = selectedImages.map(async (image) => {
        const { error } = await supabase.storage
          .from(STORAGE_BUCKET)
          .remove([image.path]);

        if (error) throw error;
      });

      await Promise.all(deletePromises);

      // Update the user's uploaded images count in Firestore
      if (user) {
        await decrementUploadedImagesCount(user.id, selectedImages.length);
      }

      setImages(prev => prev.filter(img => !img.selected));
      toast.success(`${selectedImages.length} images deleted successfully`);
    } catch (error) {
      console.error('Error deleting images:', error);
      toast.error('Error deleting selected images');
    } finally {
      setDeleting(false); // Clear loading state
    }
    setShowDeleteDialog(false);
  };


  return (
    <div className="space-y-6 p-4 md:p-6"> {/* Add padding for smaller screens */}
      {/* Hidden audio element for camera click sound */}
      <audio ref={cameraClickSoundRef} src="/sounds/camera-13695.mp3" preload="auto" className="hidden"></audio>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4"> {/* Stack on mobile */}
        <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent">
          Upload Your Product Images
        </h1>
        <div className="flex flex-wrap gap-2 w-full md:w-auto justify-end"> {/* Allow wrapping and full width on mobile */}
          {images.some(img => img.selected) && (
            <Button
              variant="destructive"
              onClick={() => setShowDeleteDialog(true)}
              className="bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white"
            >
              <Trash2 className="w-4 h-4 mr-1 md:mr-2" /> {/* Adjust margin */}
              Delete Selected
            </Button>
          )}
          <Button
            onClick={openCamera}
            variant="outline"
            className="border border-gray-700 bg-gray-800 text-gray-200 hover:bg-gray-700 hover:text-white flex-grow md:flex-grow-0" // Grow on mobile
            disabled={uploading || savingImage}
            size="sm" // Smaller button on mobile
          >
            <Camera className="w-4 h-4 mr-1 md:mr-2" />
            Camera
          </Button>
          <input
            type="file"
            multiple
            accept="image/*"
            className="hidden"
            onChange={handleUpload}
            disabled={uploading || savingImage}
            ref={fileInputRef}
          />
          <Button
            disabled={uploading || savingImage}
            onClick={handleUploadButtonClick}
            className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white border-0 flex-grow md:flex-grow-0" // Grow on mobile
            size="sm" // Smaller button on mobile
          >
            <UploadIcon className="w-4 h-4 mr-1 md:mr-2" />
            {uploading ? 'Uploading...' : 'Upload'} {/* Shorter text */}
          </Button>
        </div>
      </div>

      {/* Zoom Control - Hide on mobile (md breakpoint) */}
      {!loading && images.length > 0 && (
        <div className="hidden md:flex flex-col sm:flex-row items-start sm:items-center gap-2 bg-gray-800 p-2 rounded-lg w-full max-w-md border border-gray-700"> {/* Added hidden md:flex */}
          <div className="flex items-center gap-2 w-full sm:w-auto">
            <ZoomOut className="w-4 h-4 text-gray-300" />
            <input
              type="range"
              min="1"
              max="3"
              value={zoomLevel}
              onChange={handleZoomChange} // Correct placement of onChange
              className="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
            />
            <ZoomIn className="w-4 h-4 text-gray-300" />
          </div> {/* Closing tag for inner flex div */}
          <span className="text-xs text-gray-300 w-full sm:w-24 text-center sm:text-left mt-1 sm:mt-0"> {/* Adjust width and alignment */}
            Grid Size: {zoomLevel === 1 ? 'Small' : zoomLevel === 3 ? 'Large' : 'Medium'}
          </span>
        </div>
      )}

      {/* Camera Modal */}
      {showCameraModal && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"> {/* Add padding */}
          <div className="bg-gray-800 p-4 md:p-6 rounded-lg max-w-xl w-full mx-auto border border-gray-700 shadow-xl"> {/* Adjust padding and max-width for mobile */}
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">{isPreviewMode ? 'Preview Photo' : 'Take a Photo'}</h3>
              {/* Use outline variant if ghost is invalid */}
              <Button variant="outline" onClick={closeCamera} className="h-8 w-8 p-0 text-gray-300 hover:text-white hover:bg-gray-700">
                ✕
              </Button>
            </div>
            <div className="relative rounded-lg overflow-hidden border border-gray-700">
              {isPreviewMode ? (
                <img
                  src={previewImage || ''}
                  alt="Preview"
                  className="w-full rounded-lg"
                />
              ) : (
                <>
                  <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    className="w-full rounded-lg"
                  />
                  <canvas ref={canvasRef} className="hidden" />
                </>
              )}
            </div>
            <div className="flex justify-center gap-4 mt-4">
              {isPreviewMode ? (
                <>
                  <Button
                    onClick={recaptureImage}
                    variant="outline"
                    className="border border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white"
                    disabled={savingImage}
                    size="sm"
                  >
                    <RefreshCw className="w-4 h-4 mr-1 md:mr-2" />
                    Recapture
                  </Button>
                  <Button
                    onClick={saveImage}
                    className="bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white border-0"
                    disabled={savingImage}
                    size="sm"
                  >
                    {savingImage ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-1 md:mr-2"></div>
                        AI Analyzing and Saving The Image... Please Wait...
                      </>
                    ) : (
                      <>
                        <Check className="w-4 h-4 mr-1 md:mr-2" />
                        ...Analyze and Save The Image...
                      </>
                    )}
                  </Button>
                </>
              ) : (
                <Button
                  onClick={captureImage}
                  className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white border-0"
                  disabled={uploading || savingImage}
                  size="sm"
                >
                  Capture Product Photo
                </Button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"> {/* Add padding */}
          <div className="bg-gray-800 p-4 md:p-6 rounded-lg max-w-sm w-full mx-auto border border-gray-700 shadow-xl"> {/* Adjust padding and max-width */}
            <h3 className="text-lg font-semibold mb-4 text-white">Confirm Deletion</h3>
            <p className="text-sm md:text-base text-gray-300 mb-6"> {/* Adjust text size */}
              Delete {images.filter(img => img.selected).length} selected image(s)?

            </p>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowDeleteDialog(false)}
                className="border border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white"
                size="sm" // Smaller button
                disabled={deleting} // Disable cancel button during deletion
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleBulkDelete}
                className="bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white border-0"
                size="sm" // Smaller button
                disabled={deleting} // Disable button during deletion
              >
                {deleting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Deleting...
                  </>
                ) : (
                  'Delete'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      {uploading && (
        <div className="flex flex-col sm:flex-row justify-center items-center py-8 text-center sm:text-left"> {/* Stack on mobile */}
          <div className="animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-t-2 border-b-2 border-purple-500 mb-2 sm:mb-0 sm:mr-3"></div> {/* Adjust size/margin */}
          <p className="text-purple-400">Uploading images...</p> {/* Shorter text */}
        </div>
      )}

      {loading && (
        <div className="flex flex-col sm:flex-row justify-center items-center py-8 text-center sm:text-left"> {/* Stack on mobile */}
          <div className="animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-t-2 border-b-2 border-purple-500 mb-2 sm:mb-0 sm:mr-3"></div> {/* Adjust size/margin */}
          <p className="text-purple-400">Loading images...</p> {/* Shorter text */}
        </div>
      )}

      {!loading && (
        <>
          {/* Adjust grid columns for mobile */}
          <div className={`grid grid-cols-2 sm:grid-cols-${zoomLevel === 1 ? 3 : zoomLevel === 3 ? 2 : 2} md:grid-cols-${zoomLevel === 1 ? 4 : zoomLevel === 3 ? 2 : 3} lg:grid-cols-${zoomLevel === 1 ? 6 : zoomLevel === 3 ? 3 : 4} gap-2 md:gap-4`}>
            {images.map((image) => (
              <div
                key={image.id}
                className="relative group aspect-square bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-700 hover:border-purple-500 transition-all duration-200 cursor-pointer" // Add cursor-pointer
                onClick={() => toggleImageSelection(image.id)}
              >
                <div className={`absolute top-1 left-1 md:top-2 md:left-2 z-10 w-4 h-4 md:w-5 md:h-5 border rounded ${image.selected ? 'bg-purple-500 border-purple-500' : 'border-white bg-transparent opacity-70 group-hover:opacity-100'}`} /> {/* Adjust size/position/style */}
                <img
                  src={image.url}
                  alt={image.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200" // Add hover effect
                />
                {/* Hide name overlay on small screens or make it smaller */}
                <div className="absolute bottom-0 left-0 right-0 p-1 md:p-2 bg-gradient-to-t from-black/70 to-transparent">
                  <p className="text-xs md:text-sm text-white truncate">{image.name}</p>
                </div>
              </div>
            ))}
          </div>

          {images.length === 0 && !uploading && ( // Ensure not shown during upload
            <div className="text-center py-12 bg-gray-800 rounded-lg border border-dashed border-gray-700 p-4 md:p-8"> {/* Add dashed border */}
              <UploadIcon className="w-10 h-10 md:w-12 md:h-12 mx-auto text-gray-400" />
              <p className="mt-4 text-gray-300">No images uploaded yet</p>
              <p className="text-sm text-gray-400">Click "Upload" or use the Camera</p> {/* Updated text */}
            </div>
          )}
        </>
      )}

      {/* Sticky Next Step Button - Only visible on mobile/tablet */}
      <div className="md:hidden fixed bottom-6 left-0 right-0 flex justify-center z-40">
        <Button
          onClick={handleNavigateToCaption}
          className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white border-0 px-8 py-4 rounded-full shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95 animate-pulse hover:animate-none"
          size="lg"
          aria-label="Navigate to Generate Description page"
        >
          <span className="mr-2 font-semibold">Next Step</span>
          <ArrowRight className="w-5 h-5" />
        </Button>
      </div>
    </div>
  );
}
