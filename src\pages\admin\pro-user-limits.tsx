import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { db } from '@/lib/firebase';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { toast } from 'react-hot-toast';
import { Settings, Save, RefreshCw } from 'lucide-react';

interface ProLimitSettings {
  maxImages: number;
  deleteDelayHours: number;
  maxSavedPrompts: number;
  maxCustomPrompts: number;
  monthlyGenerationLimit: number;
}

export function ProUserLimitsManagement() {
  const [limits, setLimits] = useState<ProLimitSettings>({
    maxImages: 100,
    deleteDelayHours: 0,
    maxSavedPrompts: 100,
    maxCustomPrompts: 20,
    monthlyGenerationLimit: 300
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchLimits();
  }, []);

  const fetchLimits = async () => {
    setLoading(true);
    try {
      // Import auth to check if user is authenticated
      const { getAuth } = await import('firebase/auth');
      const auth = getAuth();
      const currentUser = auth.currentUser;
      
      if (!currentUser) {
        toast.error('You must be logged in to access this page');
        setLoading(false);
        return;
      }
      
      // Check if limits document exists
      const limitsDocRef = doc(db, 'settings', 'proUserLimits');
      const limitsDoc = await getDoc(limitsDocRef);
      
      if (limitsDoc.exists()) {
        const data = limitsDoc.data() as ProLimitSettings;
        setLimits(data);
      } else {
        // If no document exists, create one with default values
        await setDoc(limitsDocRef, limits);
        toast.success('Created default pro user limits settings');
      }
    } catch (error: any) {
      console.error('Error fetching pro user limits:', error);
      if (error.code === 'permission-denied') {
        toast.error('You do not have permission to access pro user limits settings');
      } else {
        toast.error(`Error loading pro user limits: ${error.message || 'Unknown error'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSaveLimits = async () => {
    setSaving(true);
    try {
      const limitsDocRef = doc(db, 'settings', 'proUserLimits');
      await setDoc(limitsDocRef, limits);
      toast.success('Pro user limits saved successfully');
    } catch (error) {
      console.error('Error saving pro user limits:', error);
      toast.error('Error saving pro user limits');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof ProLimitSettings, value: number) => {
    setLimits(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const resetToDefaults = () => {
    setLimits({
      maxImages: 100,
      deleteDelayHours: 0,
      maxSavedPrompts: 100,
      maxCustomPrompts: 20,
      monthlyGenerationLimit: 300
    });
    toast.success('Reset to default values');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-white">Pro User Limit Settings</h2>
          <p className="text-gray-400 mt-1">Configure limits for pro users</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={resetToDefaults}
            className="bg-gray-800 border-gray-700 text-gray-200 hover:bg-gray-700 hover:text-white"
            disabled={loading || saving}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Reset to Defaults
          </Button>
          <Button
            onClick={handleSaveLimits}
            className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white border-0"
            disabled={loading || saving}
          >
            {saving ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-t-transparent"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
          <span className="ml-3 text-gray-400">Loading settings...</span>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Max Images Upload Limit */}
          <Card className="bg-gray-800 border-gray-700 text-white">
            <CardHeader>
              <CardTitle className="text-lg font-medium flex items-center">
                <Settings className="h-5 w-5 mr-2 text-purple-400" />
                Max Images Upload Limit
              </CardTitle>
              <CardDescription className="text-gray-400">
                Maximum number of images a pro user can upload
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">Current value: {limits.maxImages}</span>
                  <Input
                    type="number"
                    value={limits.maxImages}
                    onChange={(e) => handleInputChange('maxImages', parseInt(e.target.value) || 0)}
                    min="1"
                    max="1000"
                    className="w-20 bg-gray-700 border-gray-600 text-white"
                  />
                </div>
                <Slider
                  value={[limits.maxImages]}
                  min={1}
                  max={1000}
                  step={10}
                  onValueChange={(value) => handleInputChange('maxImages', value[0])}
                  className="py-4"
                />
              </div>
            </CardContent>
          </Card>

          {/* Delete Delay Hours */}
          <Card className="bg-gray-800 border-gray-700 text-white">
            <CardHeader>
              <CardTitle className="text-lg font-medium flex items-center">
                <Settings className="h-5 w-5 mr-2 text-purple-400" />
                Image Delete Delay (Hours)
              </CardTitle>
              <CardDescription className="text-gray-400">
                Hours before a pro user can delete uploaded images (0 for immediate deletion)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">Current value: {limits.deleteDelayHours} hours</span>
                  <Input
                    type="number"
                    value={limits.deleteDelayHours}
                    onChange={(e) => handleInputChange('deleteDelayHours', parseInt(e.target.value) || 0)}
                    min="0"
                    max="72"
                    className="w-20 bg-gray-700 border-gray-600 text-white"
                  />
                </div>
                <Slider
                  value={[limits.deleteDelayHours]}
                  min={0}
                  max={72}
                  step={1}
                  onValueChange={(value) => handleInputChange('deleteDelayHours', value[0])}
                  className="py-4"
                />
              </div>
            </CardContent>
          </Card>

          {/* Max Saved Prompts Limit */}
          <Card className="bg-gray-800 border-gray-700 text-white">
            <CardHeader>
              <CardTitle className="text-lg font-medium flex items-center">
                <Settings className="h-5 w-5 mr-2 text-purple-400" />
                Max Saved Prompts Limit
              </CardTitle>
              <CardDescription className="text-gray-400">
                Maximum number of prompts a pro user can save
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">Current value: {limits.maxSavedPrompts}</span>
                  <Input
                    type="number"
                    value={limits.maxSavedPrompts}
                    onChange={(e) => handleInputChange('maxSavedPrompts', parseInt(e.target.value) || 0)}
                    min="1"
                    max="500"
                    className="w-20 bg-gray-700 border-gray-600 text-white"
                  />
                </div>
                <Slider
                  value={[limits.maxSavedPrompts]}
                  min={1}
                  max={500}
                  step={10}
                  onValueChange={(value) => handleInputChange('maxSavedPrompts', value[0])}
                  className="py-4"
                />
              </div>
            </CardContent>
          </Card>

          {/* Max Custom Prompts Limit */}
          <Card className="bg-gray-800 border-gray-700 text-white">
            <CardHeader>
              <CardTitle className="text-lg font-medium flex items-center">
                <Settings className="h-5 w-5 mr-2 text-purple-400" />
                Max Custom Prompts Limit
              </CardTitle>
              <CardDescription className="text-gray-400">
                Maximum number of custom prompts a pro user can create
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">Current value: {limits.maxCustomPrompts}</span>
                  <Input
                    type="number"
                    value={limits.maxCustomPrompts}
                    onChange={(e) => handleInputChange('maxCustomPrompts', parseInt(e.target.value) || 0)}
                    min="1"
                    max="100"
                    className="w-20 bg-gray-700 border-gray-600 text-white"
                  />
                </div>
                <Slider
                  value={[limits.maxCustomPrompts]}
                  min={1}
                  max={100}
                  step={1}
                  onValueChange={(value) => handleInputChange('maxCustomPrompts', value[0])}
                  className="py-4"
                />
              </div>
            </CardContent>
          </Card>

          {/* Monthly Generation Limit */}
          <Card className="bg-gray-800 border-gray-700 text-white">
            <CardHeader>
              <CardTitle className="text-lg font-medium flex items-center">
                <Settings className="h-5 w-5 mr-2 text-purple-400" />
                Monthly Generation Limit
              </CardTitle>
              <CardDescription className="text-gray-400">
                Maximum number of prompts a pro user can generate per month
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">Current value: {limits.monthlyGenerationLimit}</span>
                  <Input
                    type="number"
                    value={limits.monthlyGenerationLimit}
                    onChange={(e) => handleInputChange('monthlyGenerationLimit', parseInt(e.target.value) || 0)}
                    min="1"
                    max="1000"
                    className="w-20 bg-gray-700 border-gray-600 text-white"
                  />
                </div>
                <Slider
                  value={[limits.monthlyGenerationLimit]}
                  min={1}
                  max={1000}
                  step={10}
                  onValueChange={(value) => handleInputChange('monthlyGenerationLimit', value[0])}
                  className="py-4"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

export default ProUserLimitsManagement;