import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Sparkles } from 'lucide-react';
import { EmailVerificationStatus } from '../components/auth/email-verification-status';
import { useAuth } from '../lib/authProvider';
import { Button } from '../components/ui/button';

export function EmailVerificationPage() {
  const navigate = useNavigate();
  const { currentUser } = useAuth();

  useEffect(() => {
    // If user is not authenticated, redirect to auth page
    if (!currentUser) {
      navigate('/auth');
      return;
    }

    // If user is already verified, redirect to app
    if (currentUser.emailVerified) {
      navigate('/app');
      return;
    }
  }, [currentUser, navigate]);

  const handleVerificationComplete = () => {
    // Redirect to app after successful verification
    navigate('/app');
  };

  const handleBackToAuth = () => {
    navigate('/auth');
  };

  if (!currentUser) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-r from-purple-500 to-pink-600 p-3 rounded-full">
              <Sparkles className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">
            Almost There!
          </h1>
          <p className="text-gray-400">
            Please verify your email address to continue
          </p>
        </div>

        {/* Email Verification Status */}
        <EmailVerificationStatus 
          onVerificationComplete={handleVerificationComplete}
          showTitle={false}
          className="mb-6"
        />

        {/* Back to Auth Button */}
        <div className="text-center">
          <Button
            onClick={handleBackToAuth}
            variant="ghost"
            className="text-gray-400 hover:text-white"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Sign In
          </Button>
        </div>

        {/* Help Text */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>
            Having trouble? Contact our support team at{' '}
            <a 
              href="mailto:<EMAIL>" 
              className="text-purple-400 hover:text-purple-300"
            >
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
