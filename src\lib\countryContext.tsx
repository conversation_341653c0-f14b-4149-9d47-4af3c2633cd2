import React, { createContext, useContext, useState, useEffect } from 'react';
import { countries } from './countries';
import { Currency, getCurrencyForCountry, formatPrice } from './currencies';

// Define the context type
interface CountryContextType {
  country: string;
  setCountry: (country: string) => void;
  currency: Currency;
  formatCurrency: (amount: number, showUsdReference?: boolean) => string;
  isFirstVisit: boolean;
  setIsFirstVisit: (value: boolean) => void;
}

// Create the context with a default value
const CountryContext = createContext<CountryContextType | undefined>(undefined);

// Cookie utility functions
const setCookie = (name: string, value: string, days: number) => {
  const date = new Date();
  date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
  const expires = `expires=${date.toUTCString()}`;
  document.cookie = `${name}=${value};${expires};path=/`;
};

const getCookie = (name: string): string | null => {
  const cookieName = `${name}=`;
  const cookies = document.cookie.split(';');
  
  for (let i = 0; i < cookies.length; i++) {
    let cookie = cookies[i].trim();
    if (cookie.indexOf(cookieName) === 0) {
      return cookie.substring(cookieName.length, cookie.length);
    }
  }
  
  return null;
};

// Provider props type
interface CountryProviderProps {
  children: React.ReactNode;
}

// Create the provider component
export const CountryProvider: React.FC<CountryProviderProps> = ({ children }) => {
  // Check if this is the first visit (no country cookie)
  const initialIsFirstVisit = !getCookie('userCountry');
  
  // Initialize country from cookie or default to United States
  const [country, setCountryState] = useState<string>(() => {
    const savedCountry = getCookie('userCountry');
    return savedCountry || 'United States';
  });
  
  const [isFirstVisit, setIsFirstVisit] = useState<boolean>(initialIsFirstVisit);
  
  // Get the currency for the selected country
  const currency = getCurrencyForCountry(country);
  
  // Update cookie when country changes
  const setCountry = (newCountry: string) => {
    if (countries.includes(newCountry)) {
      setCountryState(newCountry);
      setCookie('userCountry', newCountry, 365); // Store for 1 year
      setIsFirstVisit(false);
    } else {
      console.error(`Invalid country: ${newCountry}`);
    }
  };
  
  // Format currency with the current country's currency
  const formatCurrency = (amount: number, showUsdReference = false): string => {
    return formatPrice(amount, currency, showUsdReference);
  };
  
  // Context value
  const contextValue: CountryContextType = {
    country,
    setCountry,
    currency,
    formatCurrency,
    isFirstVisit,
    setIsFirstVisit
  };
  
  return (
    <CountryContext.Provider value={contextValue}>
      {children}
    </CountryContext.Provider>
  );
};

// Custom hook to use the country context
export const useCountry = (): CountryContextType => {
  const context = useContext(CountryContext);
  
  if (context === undefined) {
    throw new Error('useCountry must be used within a CountryProvider');
  }
  
  return context;
};

// Country selection component (to be used in other components)
export const CountrySelector: React.FC<{
  selectedCountry: string;
  onCountryChange: (country: string) => void;
  className?: string;
}> = ({ selectedCountry, onCountryChange, className }) => {
  return (
    <select 
      value={selectedCountry}
      onChange={(e) => onCountryChange(e.target.value)}
      className={className || "bg-gray-700 border border-gray-600 text-white rounded-md p-2"}
    >
      {countries.map((country) => (
        <option key={country} value={country}>
          {country}
        </option>
      ))}
    </select>
  );
};
