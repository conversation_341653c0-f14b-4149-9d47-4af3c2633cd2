# SSLCOMMERZ Integration Guide

This guide provides detailed information about the SSLCOMMERZ payment gateway integration in the eComEasy.AI application.

## Overview

SSLCOMMERZ is a popular payment gateway in Bangladesh that allows users to pay using various local payment methods. This integration enables users from Bangladesh to make payments using SSLCOMMERZ, while users from other countries will continue to use Paddle.

## Implementation Details

### Environment Variables

The following environment variables are used for the SSLCOMMERZ integration:

#### Frontend (.env)
```
# SSLCOMMERZ Configuration
VITE_SSLCOMMERZ_STORE_ID="your_store_id"
VITE_SSLCOMMERZ_STORE_PASSWORD="your_store_password"
VITE_SSLCOMMERZ_IS_LIVE=false  # Set to true for production
VITE_APP_URL="https://ecomeasy.ai"
VITE_LOCAL_APP_URL="http://localhost:5173"
```

#### Backend (functions/.env)
```
# SSLCOMMERZ Configuration
SSLCOMMERZ_STORE_ID="your_store_id"
SSLCOMMERZ_STORE_PASSWORD="your_store_password"
SSLCOMMERZ_IS_LIVE=false  # Set to true for production

# Application URLs
APP_URL="https://ecomeasy.ai"
LOCAL_APP_URL="http://localhost:5173"
```

#### Firebase Functions Config
You can also set these environment variables directly in Firebase Functions using the Firebase CLI:

```bash
firebase functions:config:set sslcommerz.store_id="your_store_id" sslcommerz.store_password="your_store_password" sslcommerz.is_live="false"
```

After setting the config, deploy the functions:

```bash
firebase deploy --only functions
```

### Dependencies

The following dependencies are required for the SSLCOMMERZ integration:

- `sslcommerz-lts`: The official SSLCOMMERZ Node.js library
- `dotenv`: For loading environment variables
- `uuid`: For generating unique transaction IDs

### Firebase Cloud Functions

The following Firebase Cloud Functions have been implemented for the SSLCOMMERZ integration:

1. `initSSLCommerzPayment`: Initializes a payment session with SSLCOMMERZ
2. `handleSSLCommerzSuccess`: Handles successful payments
3. `handleSSLCommerzFail`: Handles failed payments
4. `handleSSLCommerzCancel`: Handles canceled payments
5. `handleSSLCommerzIPN`: Handles Instant Payment Notifications (IPN)

### Frontend Components

The frontend integration consists of the following components:

1. `src/lib/sslcommerz.ts`: Contains the functions for initializing and processing SSLCOMMERZ payments
2. `src/components/billing-details-step.tsx`: Displays the billing details form and payment gateway selection
3. `src/components/payment-status.tsx`: Displays the payment status (success, failure, or cancellation)
4. `src/pages/payment/success.tsx`, `src/pages/payment/failed.tsx`, `src/pages/payment/canceled.tsx`: Payment status pages

### Firebase Function URLs

The following Firebase Function URLs are used for the SSLCOMMERZ integration:

1. `https://us-central1-product-img-2-ecom.cloudfunctions.net/initSSLCommerzPayment`: Initializes a payment session with SSLCOMMERZ
2. `https://us-central1-product-img-2-ecom.cloudfunctions.net/handleSSLCommerzSuccess`: Handles successful payments
3. `https://us-central1-product-img-2-ecom.cloudfunctions.net/handleSSLCommerzFail`: Handles failed payments
4. `https://us-central1-product-img-2-ecom.cloudfunctions.net/handleSSLCommerzCancel`: Handles canceled payments
5. `https://us-central1-product-img-2-ecom.cloudfunctions.net/handleSSLCommerzIPN`: Handles Instant Payment Notifications (IPN)

These URLs are automatically configured in the Firebase Functions code.

## Payment Flow

1. User selects a plan and clicks "Upgrade" or "Get Started"
2. User fills in or confirms billing details
3. If the user's country is Bangladesh, SSLCOMMERZ is selected as the default payment gateway
4. User clicks "Proceed to Checkout"
5. The application initializes a payment session with SSLCOMMERZ
6. User is redirected to the SSLCOMMERZ payment page
7. User completes the payment on the SSLCOMMERZ payment page
8. SSLCOMMERZ redirects the user back to the application
9. The application verifies the payment and updates the user's subscription

## Database Schema

### Firestore Collections

#### sslcommerzTransactions
- `userId`: The ID of the user making the payment
- `userEmail`: The email of the user making the payment
- `amount`: The payment amount
- `plan`: The selected plan (Pay-As-You-Go, Pro, Enterprise)
- `billingPeriod`: The billing period (monthly, yearly)
- `transactionId`: The unique transaction ID
- `status`: The transaction status (INITIATED, COMPLETED, FAILED, CANCELED)
- `couponCode`: The coupon code used (if any)
- `discountAmount`: The discount amount (if any)
- `createdAt`: The timestamp when the transaction was created
- `updatedAt`: The timestamp when the transaction was last updated
- `billingDetails`: The billing details of the user
- `validationResponse`: The validation response from SSLCOMMERZ (for completed transactions)
- `failureDetails`: The failure details (for failed transactions)
- `cancelDetails`: The cancellation details (for canceled transactions)
- `ipnDetails`: The IPN details (for transactions with IPN)

## Testing

### Sandbox Testing

For testing purposes, you can use the SSLCOMMERZ sandbox environment. Set `VITE_SSLCOMMERZ_IS_LIVE=false` in the `.env` file and `SSLCOMMERZ_IS_LIVE=false` in the `functions/.env` file.

In sandbox mode, you can use the following test cards:

- Visa: ****************
- Mastercard: ****************
- American Express: ***************

For all test cards, use any future expiry date and any 3-digit CVV.

### IPN Testing

To test IPN (Instant Payment Notification), you need to configure the IPN URL in the SSLCOMMERZ merchant panel:

1. Log in to the SSLCOMMERZ merchant panel
2. Go to "My Stores" > "IPN Settings"
3. Set the IPN URL to `https://us-central1-product-img-2-ecom.cloudfunctions.net/handleSSLCommerzIPN`

This ensures that SSLCOMMERZ will send payment notifications directly to your Firebase Function.

## Troubleshooting

### Common Issues

1. **Store Credential Error**: This error occurs when the SSLCOMMERZ store ID or password is incorrect, or if the store is not active. Check your credentials and make sure they are correctly set in the environment variables.

2. **Payment Initialization Failed**: Check if the SSLCOMMERZ credentials are correct and if the required parameters are provided. Make sure the user is authenticated and all required fields are filled in.

3. **Validation Failed**: Check if the transaction ID is valid and if the payment was actually completed. This can happen if the payment was interrupted or if there was an issue with the payment gateway.

4. **Redirect URLs Not Working**: Make sure the success, fail, cancel, and IPN URLs are correctly configured and accessible. These should be the Firebase Function URLs.

5. **CORS Issues**: If you're experiencing CORS issues, make sure the CORS middleware is correctly configured in the Firebase Functions.

### Debugging

For debugging purposes, you can check the following:

1. **Firebase Functions Logs**: Use the Firebase CLI to view the logs:
   ```bash
   firebase functions:log
   ```

2. **Transaction Records**: Check the transaction records in the `sslcommerzTransactions` collection in Firestore to see the status and details of each transaction.

3. **Network Requests**: Use the browser's developer tools to inspect the network requests and responses when initializing a payment.

4. **Client-side Debugging**: Enable debug logging in the client-side code by setting `DEBUG = true` in `src/lib/sslcommerz.ts`.

## Security Considerations

1. All sensitive operations (payment initialization, validation, etc.) are performed on the server-side using Firebase Cloud Functions.
2. SSLCOMMERZ credentials are stored in environment variables and not exposed to the client.
3. All transactions are validated with SSLCOMMERZ before updating the user's subscription.

## References

- [SSLCOMMERZ Official Documentation](https://developer.sslcommerz.com/doc/v4/)
- [SSLCOMMERZ Node.js Library](https://github.com/sslcommerz/SSLCommerz-NodeJS)
- [Firebase Cloud Functions Documentation](https://firebase.google.com/docs/functions)
