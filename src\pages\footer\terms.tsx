import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Footer } from '../../components/footer';

export function TermsPage() {
  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-12">
        <Link to="/" className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Home
        </Link>
        
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-white mb-8">Terms of Service</h1>
          
          <div className="prose prose-lg prose-invert max-w-none">
            <p className="text-gray-300 mb-6">
              Last Updated: June 1, 2024
            </p>
            
            <div className="bg-gray-700 p-4 rounded-lg mb-6">
              <p className="text-white font-medium">
                eComEasyAI is owned and operated by <PERSON><PERSON>, a registered business entity.
              </p>
            </div>
            
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 mb-8">
              <p className="text-gray-300">
                Please read these Terms of Service ("Terms") carefully before using the eComEasyAI platform. 
                By accessing or using our service, you agree to be bound by these Terms. If you disagree with any part of the Terms, 
                you may not access the service.
              </p>
              <p className="text-gray-300 mt-4">
                eComEasyAI is a fully automated AI-powered platform. Our service does not require human experts from our team to generate content. 
                All product descriptions and marketing content are generated automatically by our AI technology based on the images and preferences you provide.
              </p>
            </div>
            
            <h2 className="text-2xl font-semibold text-white mb-4">1. Accounts</h2>
            <p className="text-gray-300 mb-6">
              When you create an account with us, you must provide accurate, complete, and current information. 
              Failure to do so constitutes a breach of the Terms, which may result in immediate termination of your account on our service.
            </p>
            <p className="text-gray-300 mb-6">
              You are responsible for safeguarding the password that you use to access the service and for any activities or actions under your password. 
              You agree not to disclose your password to any third party. You must notify us immediately upon becoming aware of any breach of security or unauthorized use of your account.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">2. Subscription and Billing</h2>
            <p className="text-gray-300 mb-6">
              Some parts of the service are billed on a subscription basis. You will be billed in advance on a recurring and periodic basis, depending on the type of subscription plan you select.
            </p>
            <p className="text-gray-300 mb-6">
              At the end of each period, your subscription will automatically renew under the same conditions unless you cancel it or eComEasyAI cancels it. 
              You may cancel your subscription either through your online account management page or by contacting our customer support team.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">3. Content Ownership</h2>
            <p className="text-gray-300 mb-6">
              You retain all rights to your content. By uploading images or other content to our service, you grant eComEasyAI a worldwide, non-exclusive, royalty-free license to use, reproduce, and display such content solely for the purpose of providing the service to you.
            </p>
            <p className="text-gray-300 mb-6">
              You own all rights to the AI-generated descriptions created using our service. However, you grant eComEasyAI the right to use anonymized data derived from your usage to improve our AI models and service quality.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">4. Acceptable Use</h2>
            <p className="text-gray-300 mb-6">
              You agree not to use the service for any purpose that is illegal or prohibited by these Terms. You may not use the service in any manner that could damage, disable, overburden, or impair the service.
            </p>
            <p className="text-gray-300 mb-6">
              You may not attempt to gain unauthorized access to any portion of the service, other accounts, computer systems, or networks connected to the service through hacking, password mining, or any other means.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">5. Limitation of Liability</h2>
            <p className="text-gray-300 mb-6">
              In no event shall eComEasyAI, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your access to or use of or inability to access or use the service.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">6. Changes to Terms</h2>
            <p className="text-gray-300 mb-6">
              We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days' notice prior to any new terms taking effect. What constitutes a material change will be determined at our sole discretion.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">7. Contact Us</h2>
            <p className="text-gray-300 mb-6">
              If you have any questions about these Terms, please contact <NAME_EMAIL>.
            </p>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

export default TermsPage;