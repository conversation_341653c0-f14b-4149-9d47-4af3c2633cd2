import { Toaster as HotToaster } from 'react-hot-toast';

export function Toaster() {
  return (
    <HotToaster
      position="bottom-right"
      toastOptions={{
        duration: 5000,
        style: {
          background: '#1f2937',
          color: '#fff',
          borderRadius: '8px',
          padding: '16px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        },
        success: {
          iconTheme: {
            primary: '#10B981',
            secondary: '#fff',
          },
        },
        error: {
          iconTheme: {
            primary: '#EF4444',
            secondary: '#fff',
          },
        },
        className: 'toast-notification',
      }}
    />
  );
}

// Add this to your global CSS or create a new CSS module
export const toastStyles = `
  .toast-notification {
    animation: slideIn 0.5s ease-out;
  }

  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .go2072408551 {
    background: linear-gradient(to right, #10B981 50%, transparent 50%);
    background-size: 200% 100%;
    animation: progress 5s linear forwards;
  }

  @keyframes progress {
    from {
      background-position: 0% 0;
    }
    to {
      background-position: -100% 0;
    }
  }
`;