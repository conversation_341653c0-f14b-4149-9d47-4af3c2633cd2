import { initializeApp } from 'firebase/app';
import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword, sendPasswordResetEmail, GoogleAuthProvider, signInWithPopup, sendEmailVerification, reload } from 'firebase/auth';
import { getFirestore, collection, addDoc, getDocs, updateDoc, deleteDoc, doc, query, where } from 'firebase/firestore';

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID
};

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const db = getFirestore(app);

export const signUp = async (email: string, password: string) => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);

    // Send email verification
    try {
      await sendEmailVerification(userCredential.user);
      console.log('Email verification sent successfully');
    } catch (verificationError) {
      console.error('Error sending email verification:', verificationError);
      // Don't fail the signup if email verification fails
    }

    // Add the user to Firestore database
    try {
      // Check if user already exists in Firestore
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('uid', '==', userCredential.user.uid));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        // User doesn't exist in Firestore, add them
        await addDoc(collection(db, 'users'), {
          uid: userCredential.user.uid,
          email: userCredential.user.email,
          displayName: userCredential.user.displayName || email.split('@')[0] || 'User',
          createdAt: new Date().toISOString(),
          role: 'Free User',
          packageName: 'Free',
          status: 'active',
          emailVerified: false
        });
        console.log('User added to Firestore');
      }
    } catch (firestoreError) {
      console.error('Error adding user to Firestore:', firestoreError);
      // We don't return an error here as the authentication was successful
    }

    return { user: userCredential.user, error: null, emailVerificationSent: true };
  } catch (error: any) {
    let message = 'An error occurred during sign up';
    if (error.code === 'auth/email-already-in-use') {
      message = 'This email is already registered';
    } else if (error.code === 'auth/weak-password') {
      message = 'Password should be at least 6 characters';
    } else if (error.code === 'auth/invalid-email') {
      message = 'Invalid email address';
    }
    return { user: null, error: message, emailVerificationSent: false };
  }
};

export const signIn = async (email: string, password: string) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return { user: userCredential.user, error: null };
  } catch (error: any) {
    let message = 'An error occurred during sign in';
    if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
      message = 'Invalid email or password';
    } else if (error.code === 'auth/too-many-requests') {
      message = 'Too many failed attempts. Please try again later';
    }
    return { user: null, error: message };
  }
};

export const resetPassword = async (email: string) => {
  try {
    await sendPasswordResetEmail(auth, email);
    return { error: null };
  } catch (error: any) {
    let message = 'An error occurred while sending reset email';
    if (error.code === 'auth/user-not-found') {
      message = 'No account found with this email';
    } else if (error.code === 'auth/invalid-email') {
      message = 'Invalid email address';
    }
    return { error: message };
  }
};

export const signInWithGoogle = async () => {
  try {
    const provider = new GoogleAuthProvider();
    provider.setCustomParameters({
      prompt: 'select_account'
    });

    const userCredential = await signInWithPopup(auth, provider);

    // Add the user to Firestore database if they don't exist
    try {
      // Check if user already exists in Firestore
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('uid', '==', userCredential.user.uid));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        // User doesn't exist in Firestore, add them
        await addDoc(collection(db, 'users'), {
          uid: userCredential.user.uid,
          email: userCredential.user.email,
          displayName: userCredential.user.displayName || userCredential.user.email?.split('@')[0] || 'User',
          photoURL: userCredential.user.photoURL,
          createdAt: new Date().toISOString(),
          role: 'Free User',
          packageName: 'Free',
          status: 'active',
          provider: 'google'
        });
        console.log('Google user added to Firestore');
      }
    } catch (firestoreError) {
      console.error('Error adding Google user to Firestore:', firestoreError);
      // We don't return an error here as the authentication was successful
    }

    return { user: userCredential.user, error: null };
  } catch (error: any) {
    let message = 'An error occurred during Google sign in';
    if (error.code === 'auth/popup-closed-by-user') {
      message = 'Sign in was cancelled';
    } else if (error.code === 'auth/account-exists-with-different-credential') {
      message = 'An account already exists with the same email address but different sign-in credentials';
    }
    return { user: null, error: message };
  }
};

// Email verification functions
export const sendVerificationEmail = async () => {
  try {
    const user = auth.currentUser;
    if (!user) {
      return { error: 'No user is currently signed in' };
    }

    if (user.emailVerified) {
      return { error: 'Email is already verified' };
    }

    await sendEmailVerification(user);
    return { error: null, message: 'Verification email sent successfully' };
  } catch (error: any) {
    let message = 'An error occurred while sending verification email';
    if (error.code === 'auth/too-many-requests') {
      message = 'Too many requests. Please wait before requesting another verification email';
    }
    return { error: message };
  }
};

export const checkEmailVerification = async () => {
  try {
    const user = auth.currentUser;
    if (!user) {
      return { isVerified: false, error: 'No user is currently signed in' };
    }

    // Reload user to get the latest verification status
    await reload(user);

    // Update Firestore if verification status changed
    if (user.emailVerified) {
      try {
        const usersRef = collection(db, 'users');
        const q = query(usersRef, where('uid', '==', user.uid));
        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          const userDoc = querySnapshot.docs[0];
          const userData = userDoc.data();

          if (!userData.emailVerified) {
            await updateDoc(userDoc.ref, {
              emailVerified: true,
              emailVerifiedAt: new Date().toISOString()
            });
            console.log('Email verification status updated in Firestore');
          }
        }
      } catch (firestoreError) {
        console.error('Error updating email verification status in Firestore:', firestoreError);
      }
    }

    return { isVerified: user.emailVerified, error: null };
  } catch (error: any) {
    console.error('Error checking email verification:', error);
    return { isVerified: false, error: 'An error occurred while checking verification status' };
  }
};