import { useState, useEffect } from 'react';
import { Users, Trash2, AlertCircle, CheckCircle, Mail, User, Link as LinkI<PERSON>, Co<PERSON>, Refresh<PERSON>w, Settings } from 'lucide-react';
import { db, auth } from '@/lib/firebase';
import { collection, query, where, getDocs, addDoc, deleteDoc, doc, updateDoc, getDoc, setDoc, onSnapshot } from 'firebase/firestore';
import { useStore } from '@/lib/store';
import { getEnterpriseLimitSettings, getUserPackage, isTeamMember, hasEnterpriseAccess } from '@/lib/userLimits';
import { createTeamInvite, getTeamInvites, deleteInvite } from '@/lib/inviteUtils';
import { toast } from 'react-hot-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { TeamMemberLimits, DEFAULT_TEAM_MEMBER_LIMITS } from '@/lib/teamMemberLimits';
import { TeamMemberLimitsForm } from '@/components/team-member-limits-form';
import { isEnterpriseAdminOwner } from '@/lib/userRoles';
import { useNavigate } from 'react-router-dom';
import { TeamManagementSkeleton } from '@/components/ui/team-management-skeleton';

interface TeamMember {
  id: string;
  email: string;
  name?: string;
  phone?: string;
  status: 'active' | 'pending';
  addedAt: Date;
}

interface TeamInvite {
  id: string;
  token: string;
  createdAt: Date;
  expiresAt: Date;
  used: boolean;
  usedBy?: string | string[]; // Can be a single string or an array of strings for multiple registrations
  usedAt?: Date | Date[]; // Can be a single date or an array of dates for multiple registrations
  limits?: TeamMemberLimits;
}

export function TeamManagement() {
  const { user } = useStore();
  const navigate = useNavigate();
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [teamInvites, setTeamInvites] = useState<TeamInvite[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingInvites, setLoadingInvites] = useState(true);
  const [isEnterpriseUser, setIsEnterpriseUser] = useState(false);
  const [isEnterpriseAdminOwnerUser, setIsEnterpriseAdminOwnerUser] = useState(false);
  const [isTeamMemberUser, setIsTeamMemberUser] = useState(false);
  const [generatingInvite, setGeneratingInvite] = useState(false);
  const [maxTeamMembers, setMaxTeamMembers] = useState(5); // Default value
  const [activeTab, setActiveTab] = useState('members'); // 'members' or 'invites'
  const [showLimitsForm, setShowLimitsForm] = useState(false);

  useEffect(() => {
    // If no user is logged in, redirect to login page
    if (!user) {
      toast.error('Please log in to access this page');
      navigate('/auth?redirect=/app/team');
      return;
    }

    // Load data - permissions are already checked by the route guard
    const loadData = async () => {
      try {
        setLoading(true);
        // Set user as Enterprise Admin Owner since route guard already verified this
        setIsEnterpriseUser(true);
        setIsEnterpriseAdminOwnerUser(true);

        // Check if the user is a team member for display purposes
        const teamMemberStatus = await isTeamMember(user.id);
        setIsTeamMemberUser(teamMemberStatus);

        // Fetch enterprise limits and team data
        await fetchEnterpriseLimits();
        await fetchTeamMembers();
        await fetchTeamInvites();
      } catch (error) {
        console.error('Error loading team management data:', error);
        toast.error('An error occurred while loading data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [user, navigate]);



  const fetchEnterpriseLimits = async () => {
    try {
      // First try to get the limits from the enterpriseAdminOwnerLimits document
      const { getAuth } = await import('firebase/auth');
      const auth = getAuth();
      const currentUser = auth.currentUser;

      if (!currentUser) {
        console.error('User not authenticated when fetching enterprise admin owner limits');
        setMaxTeamMembers(5); // Default fallback
        return;
      }

      // Check if user has Enterprise-based custom package first
      const { getUserPackage } = await import('@/lib/userLimits');
      const packageName = await getUserPackage(currentUser.uid);

      // Check if it's an Enterprise-based custom package
      if (packageName !== 'Free' && packageName !== 'Pay-As-You-Go' && packageName !== 'Pro' && packageName !== 'Enterprise') {
        try {
          const { getCustomPackageType, getCustomPackageLimits } = await import('@/lib/customPackages');
          const packageType = await getCustomPackageType(packageName);

          if (packageType === 'enterprise-based') {
            const customLimits = await getCustomPackageLimits(packageName);
            if (customLimits && 'maxTeamMembers' in customLimits) {
              console.log('Found Enterprise-based custom package limits:', customLimits);
              setMaxTeamMembers(customLimits.maxTeamMembers || 5);
              return;
            }
          }
        } catch (customPackageError) {
          console.error('Error fetching custom package limits:', customPackageError);
        }
      }

      try {
        // Check if the admin owner limits document exists (for regular Enterprise users)
        const limitsDocRef = doc(db, 'settings', 'enterpriseAdminOwnerLimits');
        const limitsDoc = await getDoc(limitsDocRef);

        if (limitsDoc.exists()) {
          const adminOwnerLimits = limitsDoc.data();
          console.log('Found enterprise admin owner limits:', adminOwnerLimits);
          setMaxTeamMembers(adminOwnerLimits.maxTeamMembers || 5);
        } else {
          // If not found, fall back to the enterprise limit settings
          try {
            const limits = await getEnterpriseLimitSettings();
            setMaxTeamMembers(limits.maxTeamMembers || 5);
          } catch (enterpriseLimitsError) {
            console.error('Error fetching enterprise limit settings:', enterpriseLimitsError);
            setMaxTeamMembers(5); // Default fallback
          }
        }
      } catch (limitsDocError) {
        console.error('Error fetching admin owner limits document:', limitsDocError);
        setMaxTeamMembers(5); // Default fallback
      }
    } catch (error) {
      console.error('Error fetching enterprise limits:', error);
      setMaxTeamMembers(5); // Default fallback
    }
  };

  const fetchTeamMembers = async () => {
    if (!user) return;

    try {
      // Fetch team members from Firestore
      const teamMembersRef = collection(db, 'teamMembers');
      const q = query(teamMembersRef, where('ownerId', '==', user.id));

      try {
        const querySnapshot = await getDocs(q);

        const members: TeamMember[] = [];
        querySnapshot.forEach((doc) => {
          try {
            const data = doc.data();
            if (data && data.email) {
              members.push({
                id: doc.id,
                email: data.email,
                name: data.name || '',
                phone: data.phone || '',
                status: data.status || 'active',
                addedAt: data.addedAt ? data.addedAt.toDate() : new Date()
              });
            }
          } catch (docError) {
            console.error('Error processing team member document:', docError);
            // Continue processing other documents
          }
        });

        setTeamMembers(members);
      } catch (queryError) {
        console.error('Error executing team members query:', queryError);
        toast.error('Failed to load team members');
        setTeamMembers([]);
      }
    } catch (error) {
      console.error('Error fetching team members:', error);
      toast.error('Failed to load team members');
      setTeamMembers([]);
    }
  };

  // Store the unsubscribe function for the real-time listener
  const [unsubscribeInvites, setUnsubscribeInvites] = useState<(() => void) | null>(null);

  // Clean up the listener when component unmounts
  useEffect(() => {
    return () => {
      if (unsubscribeInvites) {
        console.log('Unsubscribing from team invites listener');
        unsubscribeInvites();
      }
    };
  }, [unsubscribeInvites]);

  const fetchTeamInvites = async () => {
    if (!user) return;

    // Unsubscribe from any existing listener
    if (unsubscribeInvites) {
      try {
        unsubscribeInvites();
        setUnsubscribeInvites(null);
      } catch (unsubError) {
        console.error('Error unsubscribing from previous listener:', unsubError);
      }
    }

    setLoadingInvites(true);
    try {
      // Set up a real-time listener for team invites
      const invitesRef = collection(db, 'teamInvites');
      const q = query(invitesRef, where('ownerId', '==', user.id));

      // Create a real-time listener with error handling
      try {
        const unsubscribe = onSnapshot(q, (snapshot) => {
          try {
            const invites: TeamInvite[] = [];
            snapshot.forEach((doc) => {
              try {
                const data = doc.data();
                if (!data || !data.token) {
                  console.warn(`Skipping invalid invite document: ${doc.id}`);
                  return;
                }

                // Make sure to explicitly handle the 'used' field with strict boolean comparison
                const isUsed = data.used === true;

                console.log(`Processing invite ${doc.id} (token: ${data.token}):`, {
                  isOneTime: !data.limits?.isMultipleRegistrations,
                  used: isUsed,
                  rawUsedValue: data.used,
                  usedBy: data.usedBy
                });

                // Safely handle date conversions
                let createdAt, expiresAt, usedAt;
                try {
                  createdAt = data.createdAt ? data.createdAt.toDate() : new Date();
                  expiresAt = data.expiresAt ? data.expiresAt.toDate() : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

                  if (data.usedAt) {
                    if (Array.isArray(data.usedAt)) {
                      usedAt = data.usedAt.map((timestamp: any) => {
                        try {
                          return timestamp.toDate();
                        } catch (dateError) {
                          console.error('Error converting usedAt timestamp:', dateError);
                          return new Date();
                        }
                      });
                    } else {
                      try {
                        usedAt = data.usedAt.toDate();
                      } catch (dateError) {
                        console.error('Error converting usedAt timestamp:', dateError);
                        usedAt = new Date();
                      }
                    }
                  }
                } catch (dateError) {
                  console.error('Error processing dates for invite:', dateError);
                  createdAt = new Date();
                  expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
                }

                invites.push({
                  id: doc.id,
                  token: data.token,
                  createdAt,
                  expiresAt,
                  used: isUsed, // Use the strictly checked boolean value
                  usedBy: data.usedBy,
                  usedAt,
                  limits: data.limits
                });
              } catch (docError) {
                console.error('Error processing invite document:', docError);
                // Continue processing other documents
              }
            });

            console.log('Real-time update: Team invites updated', invites);
            setTeamInvites(invites);
            setLoadingInvites(false);
          } catch (snapshotError) {
            console.error('Error processing snapshot data:', snapshotError);
            setLoadingInvites(false);
            setTeamInvites([]);
          }
        }, (error) => {
          console.error('Error in team invites listener:', error);
          toast.error('Failed to listen for team invite updates');
          setLoadingInvites(false);
          setTeamInvites([]);
        });

        // Store the unsubscribe function
        setUnsubscribeInvites(() => unsubscribe);
      } catch (listenerError) {
        console.error('Error creating snapshot listener:', listenerError);
        toast.error('Failed to set up team invites listener');
        setLoadingInvites(false);

        // Fall back to one-time fetch
        try {
          const invites = await getTeamInvites(user.id);
          setTeamInvites(invites);
        } catch (fallbackError) {
          console.error('Error in fallback team invites fetch:', fallbackError);
          setTeamInvites([]);
        }
      }
    } catch (error) {
      console.error('Error setting up team invites listener:', error);
      toast.error('Failed to load team invites');
      setLoadingInvites(false);
      setTeamInvites([]);
    }
  };

  // Team member adding functionality by email address has been removed

  const generateInviteLink = async () => {
    // Prevent multiple simultaneous requests
    if (generatingInvite) {
      return;
    }

    if (!user) {
      toast.error('You must be logged in to generate invite links');
      return;
    }

    if (!isEnterpriseUser) {
      toast.error('Only Enterprise users can generate invite links');
      return;
    }

    // For now, we'll allow all Enterprise users to generate invite links
    // if (!isEnterpriseAdminOwnerUser) {
    //   toast.error('Only Enterprise Admin Owners can generate invite links');
    //   return;
    // }

    // Set loading state to prevent multiple clicks
    setGeneratingInvite(true);

    try {
      // Check if company name is set
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('uid', '==', user.id));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const userData = querySnapshot.docs[0].data();

        if (!userData.companyName) {
          toast.error('Please set your Company/Organization name in Profile and Settings before generating invite links');
          return;
        }
      }

      // Refresh the team member limit before checking
      await fetchEnterpriseLimits();

      // Count active invites (not used for one-time invites, or multiple registration invites)
      const activeInvites = teamInvites.filter(invite =>
        invite.limits?.isMultipleRegistrations || (invite.used !== true && !invite.limits?.isMultipleRegistrations)
      );
      console.log('Active invites count:', activeInvites.length);

      if (teamMembers.length + activeInvites.length >= maxTeamMembers) {
        toast.error(`You can only have up to ${maxTeamMembers} team members with your Enterprise plan`);
        return;
      }

      // Show the limits form instead of immediately generating the invite
      setShowLimitsForm(true);
    } catch (error) {
      console.error('Error checking company name:', error);
      toast.error('Failed to check company name');
    } finally {
      // Reset loading state
      setGeneratingInvite(false);
    }
  };

  const handleGenerateInviteWithLimits = async (limits: TeamMemberLimits) => {
    if (!user) return;

    setGeneratingInvite(true);

    try {
      // Get the company name from the user's profile
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('uid', '==', user.id));
      const querySnapshot = await getDocs(q);

      let companyName = '';
      if (!querySnapshot.empty) {
        const userData = querySnapshot.docs[0].data();
        companyName = userData.companyName || '';
      }

      if (!companyName) {
        toast.error('Please set your Company/Organization name in Profile and Settings before generating invite links');
        setShowLimitsForm(false);
        return;
      }

      const token = await createTeamInvite(user.id, user.email, 7, limits, companyName);
      toast.success('Invite link with custom limits generated successfully');

      // Close the limits form modal
      setShowLimitsForm(false);

      // Refresh invite links data
      await fetchTeamInvites();

      // Auto-navigate to Invite Links tab after successful generation
      console.log('Navigating to invites tab after successful invite generation');
      setActiveTab('invites');
    } catch (error) {
      console.error('Error generating invite link:', error);
      toast.error('Failed to generate invite link');
    } finally {
      setGeneratingInvite(false);
    }
  };

  const copyInviteLink = (token: string) => {
    // Make sure the URL is correctly formatted
    const inviteUrl = `${window.location.origin}/team-invite/${token}`;
    console.log('Generated invite URL:', inviteUrl);
    navigator.clipboard.writeText(inviteUrl)
      .then(() => toast.success('Invite link copied to clipboard'))
      .catch(() => toast.error('Failed to copy invite link'));
  };

  const removeInvite = async (inviteId: string) => {
    if (!user) {
      toast.error('You must be logged in to remove invites');
      return;
    }

    if (!isEnterpriseUser) {
      toast.error('Only Enterprise users can remove invites');
      return;
    }

    if (!isEnterpriseAdminOwnerUser) {
      toast.error('Only Enterprise Admin Owners can remove invite links');
      return;
    }

    if (!confirm('Are you sure you want to delete this invite link?')) {
      return;
    }

    try {
      await deleteInvite(inviteId);
      toast.success('Invite link deleted successfully');
      fetchTeamInvites();
    } catch (error) {
      console.error('Error removing invite:', error);
      toast.error('Failed to delete invite link');
    }
  };

  const removeTeamMember = async (memberId: string, email: string) => {
    if (!user) {
      toast.error('You must be logged in to remove team members');
      return;
    }

    if (!isEnterpriseUser) {
      toast.error('Only Enterprise users can remove team members');
      return;
    }

    if (!isEnterpriseAdminOwnerUser) {
      toast.error('Only Enterprise Admin Owners can remove team members');
      return;
    }

    if (!confirm(`Are you sure you want to remove ${email} from your team?`)) {
      return;
    }

    try {
      console.log('Removing team member with ID:', memberId, 'and email:', email);

      // Remove team member from Firestore
      await deleteDoc(doc(db, 'teamMembers', memberId));
      console.log('Team member document deleted successfully');

      try {
        // Update user record for the team member
        const usersRef = collection(db, 'users');
        const q = query(usersRef, where('email', '==', email));
        console.log('Querying users collection for email:', email);
        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          console.log('Existing user found, updating record');
          const userDoc = querySnapshot.docs[0];
          const updateData = {
            packageName: 'Free',
            isTeamMember: false,
            teamOwnerId: null
          };
          console.log('Update data:', updateData);
          await updateDoc(doc(db, 'users', userDoc.id), updateData);
          console.log('User record updated successfully');
        } else {
          console.log('No user record found with email:', email);
        }
      } catch (userError) {
        console.error('Error updating user record:', userError);
        // Continue with the team member removal even if user update fails
        // We'll just log the error but not throw it
      }

      toast.success(`${email} has been removed from your team`);
      fetchTeamMembers();
    } catch (error) {
      console.error('Error removing team member:', error);
      if (error instanceof Error) {
        toast.error(`Failed to remove team member: ${error.message}`);
      } else {
        toast.error('Failed to remove team member');
      }
    }
  };

  // Show skeleton while loading data
  if (loading) {
    return <TeamManagementSkeleton />;
  }

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-white mb-8">Team Management</h1>

      {/* Team Member Limits Form */}
      {user && (
        <TeamMemberLimitsForm
          open={showLimitsForm}
          onClose={() => setShowLimitsForm(false)}
          onSave={handleGenerateInviteWithLimits}
          ownerId={user.id}
        />
      )}

      <div className="space-y-8">
          <Tabs value={activeTab} className="w-full" onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger
                value="members"
                className="text-base"
                onClick={() => {
                  fetchEnterpriseLimits();
                  fetchTeamMembers();
                }}
              >
                <Users className="mr-2 h-4 w-4" />
                Team Members
              </TabsTrigger>
              <TabsTrigger
                value="invites"
                className="text-base"
                onClick={() => {
                  fetchEnterpriseLimits();
                  fetchTeamInvites();
                }}
              >
                <LinkIcon className="mr-2 h-4 w-4" />
                Invite Links
              </TabsTrigger>
            </TabsList>

            <TabsContent value="members" className="space-y-6">
              {/* Team Members Section */}
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-white flex items-center">
                    <Users className="mr-2 h-5 w-5 text-purple-400" />
                    Team Members ({teamMembers.length}/{maxTeamMembers})
                  </h2>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs bg-gray-700 border-gray-600 text-gray-200 hover:bg-gray-600 hover:text-white"
                    onClick={() => {
                      fetchEnterpriseLimits();
                      fetchTeamMembers();
                      toast.success('Team member limits refreshed');
                    }}
                  >
                    <RefreshCw className="h-3.5 w-3.5 mr-1.5" />
                    Refresh
                  </Button>
                </div>

                {teamMembers.length === 0 ? (
                  <div className="text-center py-8 text-gray-400">
                    <Mail className="h-12 w-12 mx-auto mb-3 text-gray-500" />
                    <p>You haven't added any team members yet.</p>
                    <p className="text-sm">Add team members directly or generate invite links for them to join.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {teamMembers.map((member) => (
                      <div key={member.id} className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                        <div className="flex items-center">
                          <div className="bg-gray-600 rounded-full p-2 mr-3">
                            <User className="h-5 w-5 text-gray-300" />
                          </div>
                          <div>
                            <p className="font-medium text-white">{member.name || member.email}</p>
                            <p className="text-sm text-gray-400">{member.email}</p>
                            {member.phone && <p className="text-sm text-gray-400">{member.phone}</p>}
                            <div className="flex items-center mt-1">
                              {member.status === 'active' ? (
                                <>
                                  <CheckCircle className="h-3.5 w-3.5 text-green-500 mr-1.5" />
                                  <span className="text-xs text-green-500">Active</span>
                                </>
                              ) : (
                                <>
                                  <AlertCircle className="h-3.5 w-3.5 text-yellow-500 mr-1.5" />
                                  <span className="text-xs text-yellow-500">Pending</span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-red-700 text-red-500 hover:bg-red-900/20"
                          onClick={() => removeTeamMember(member.id, member.email)}
                          disabled={!isEnterpriseAdminOwnerUser}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}

                {/* Team member adding by email has been removed */}
                <div className="mt-6 text-center">
                  <p className="text-gray-400 mb-4">Use the "Invite Links" tab to generate invite links for new team members.</p>
                  <Button
                    onClick={() => setActiveTab('invites')}
                    className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white"
                  >
                    <LinkIcon className="h-4 w-4 mr-2" />
                    Generate Invite Link
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="invites" className="space-y-6">
              {/* Invite Links Section */}
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-white flex items-center">
                    <LinkIcon className="mr-2 h-5 w-5 text-purple-400" />
                    Invite Links
                  </h2>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs bg-gray-700 border-gray-600 text-gray-200 hover:bg-gray-600 hover:text-white"
                    onClick={() => {
                      fetchEnterpriseLimits();
                      fetchTeamInvites();
                      toast.success('Invite links refreshed');
                    }}
                  >
                    <RefreshCw className="h-3.5 w-3.5 mr-1.5" />
                    Refresh
                  </Button>
                </div>

                <p className="text-gray-300 mb-4">
                  Generate unique invite links to share with your team members. They will be able to register with their name, email, phone, and password.
                </p>

                {loadingInvites ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {teamInvites.length === 0 ? (
                      <div className="text-center py-8 text-gray-400">
                        <LinkIcon className="h-12 w-12 mx-auto mb-3 text-gray-500" />
                        <p>You haven't created any invite links yet.</p>
                        <p className="text-sm">Generate invite links to share with your team members.</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {teamInvites.map((invite) => {
                          const inviteUrl = `${window.location.origin}/team-invite/${invite.token}`;

                          // For multiple registrations invites, they never expire
                          const isExpired = invite.limits?.isMultipleRegistrations ? false : new Date() > invite.expiresAt;

                          // Determine if this is a one-time invite that has been used
                          const isOneTimeInvite = !invite.limits?.isMultipleRegistrations;
                          const isUsed = invite.used === true;

                          console.log(`Rendering invite ${invite.id} (token: ${invite.token}):`, {
                            isOneTimeInvite,
                            isUsed,
                            isExpired,
                            url: inviteUrl
                          });
                          return (
                            <Card key={invite.id} className={`bg-gray-700 border-gray-600 ${
                              (isUsed && isOneTimeInvite) || isExpired ? 'opacity-70' : ''
                            }`}>
                              <CardHeader className="pb-2">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <CardTitle className="text-white text-lg">
                                      Invite Link {
                                        invite.limits?.isMultipleRegistrations
                                          ? (isExpired ? '(Expired)' : '(Active)')
                                          : (isUsed ? '(Used)' : isExpired ? '(Expired)' : '(Active)')
                                      }
                                    </CardTitle>
                                    <CardDescription>
                                      Created on {invite.createdAt.toLocaleDateString()}
                                    </CardDescription>
                                  </div>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="border-red-700 text-red-500 hover:bg-red-900/20"
                                    onClick={() => removeInvite(invite.id)}
                                    disabled={!isEnterpriseAdminOwnerUser}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </CardHeader>
                              <CardContent>
                                <div className="bg-gray-800 p-2 rounded-md text-sm font-mono text-gray-300 mb-2 truncate">
                                  {inviteUrl}
                                </div>
                                <div className="flex justify-between items-center text-sm text-gray-400">
                                  <span>
                                    {invite.limits?.isMultipleRegistrations
                                      ? <span className="text-green-400">Never expires</span>
                                      : `Expires: ${invite.expiresAt.toLocaleDateString()}`
                                    }
                                  </span>
                                  {isUsed && isOneTimeInvite && (
                                    <span>Used by: {invite.usedBy}</span>
                                  )}
                                  {invite.limits?.isMultipleRegistrations && invite.usedBy && (
                                    <span>Used by: {Array.isArray(invite.usedBy) ? invite.usedBy.length : 1} user(s)</span>
                                  )}
                                </div>

                                {/* Display team member limits if they exist */}
                                {invite.limits && (
                                  <div className="mt-2 pt-2 border-t border-gray-600">
                                    <div className="text-xs text-gray-400 flex items-center">
                                      <Settings className="h-3 w-3 mr-1 text-purple-400" />
                                      <span>Team Member Limits:</span>
                                    </div>
                                    <div className="grid grid-cols-3 gap-x-3 gap-y-1 mt-1">
                                      <div className="text-xs text-gray-400">
                                        Max Images: <span className="text-white">{invite.limits.maxImages}</span>
                                      </div>
                                      <div className="text-xs text-gray-400">
                                        Max Saved: <span className="text-white">{invite.limits.maxSavedPrompts}</span>
                                      </div>
                                      <div className="text-xs text-gray-400">
                                        Max Custom: <span className="text-white">{invite.limits.maxCustomPrompts}</span>
                                      </div>
                                      <div className="text-xs text-gray-400">
                                        Delete Delay: <span className="text-white">{invite.limits.deleteDelayHours}h</span>
                                      </div>
                                      <div className="text-xs text-gray-400 col-span-2">
                                        Max Monthly Prompts: <span className="text-white">{invite.limits.monthlyGenerationLimit}</span>
                                      </div>
                                      <div className="text-xs text-gray-400 col-span-3 mt-1 pt-1 border-t border-gray-600">
                                        Registration Type:
                                        <span className={`ml-1 ${invite.limits.isMultipleRegistrations ? 'text-green-400' : 'text-white'}`}>
                                          {invite.limits.isMultipleRegistrations
                                            ? 'Multiple Registrations'
                                            : 'One-time Registration'}
                                        </span>
                                        {invite.limits.isMultipleRegistrations && (
                                          <div className="mt-1 text-yellow-400 flex items-start">
                                            <AlertCircle className="h-3 w-3 mr-1 mt-0.5 flex-shrink-0" />
                                            <span>This link can be used multiple times until deleted</span>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </CardContent>
                              <CardFooter className="pt-0">
                                <div className="flex space-x-2 w-full">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex-1 border-gray-600 text-white hover:bg-gray-600 hover:border-gray-500"
                                    onClick={() => copyInviteLink(invite.token)}
                                    disabled={(isUsed && isOneTimeInvite) || isExpired || !isEnterpriseAdminOwnerUser}
                                  >
                                    <Copy className="h-4 w-4 mr-2" />
                                    Copy Link
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex-1 bg-gradient-to-r from-purple-500/10 to-pink-500/10 border-purple-600/50 text-white hover:from-purple-500/20 hover:to-pink-500/20 hover:border-purple-500"
                                    onClick={() => {
                                      const inviteUrl = `${window.location.origin}/team-invite/${invite.token}`;
                                      window.open(inviteUrl, '_blank');
                                    }}
                                    disabled={(isUsed && isOneTimeInvite) || isExpired || !isEnterpriseAdminOwnerUser}
                                  >
                                    <LinkIcon className="h-4 w-4 mr-2" />
                                    Open Link
                                  </Button>
                                </div>
                              </CardFooter>
                            </Card>
                          );
                        })}
                      </div>
                    )}

                    <div className="mt-6">
                      <Button
                        onClick={async () => {
                          // Prevent multiple simultaneous requests
                          if (generatingInvite) return;

                          // Refresh limits before generating invite
                          await fetchEnterpriseLimits();
                          generateInviteLink();
                        }}
                        disabled={generatingInvite || !isEnterpriseAdminOwnerUser || teamMembers.length + teamInvites.filter(invite =>
                          invite.limits?.isMultipleRegistrations || (invite.used !== true && !invite.limits?.isMultipleRegistrations)
                        ).length >= maxTeamMembers}
                        className="w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {generatingInvite ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                            Generating...
                          </>
                        ) : (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Generate New Invite Link
                          </>
                        )}
                      </Button>
                      {teamMembers.length + teamInvites.filter(invite =>
                        invite.limits?.isMultipleRegistrations || (invite.used !== true && !invite.limits?.isMultipleRegistrations)
                      ).length >= maxTeamMembers && (
                        <p className="mt-2 text-sm text-yellow-500 text-center">
                          You've reached the maximum number of team members for your Enterprise plan.
                        </p>
                      )}
                      {!isEnterpriseAdminOwnerUser && isEnterpriseUser && (
                        <p className="mt-2 text-sm text-yellow-500 text-center">
                          Only Enterprise Admin Owners can generate invite links. Team members cannot invite other members.
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>

          {/* Enterprise Features Section */}
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h2 className="text-xl font-semibold text-white mb-4">Enterprise Admin Owner Features</h2>
            <ul className="space-y-3 text-gray-300">
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Team members get access to Enterprise features based on your custom limits</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Set custom limits for each team member when generating invite links</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Control how many images each team member can upload</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Set limits on saved prompts and custom prompts for each team member</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Control image deletion restrictions for each team member</span>
              </li>
            </ul>
          </div>
        </div>
    </div>
  );
}
