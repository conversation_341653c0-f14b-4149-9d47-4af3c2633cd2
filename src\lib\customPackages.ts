import { db } from './firebase';
import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy 
} from 'firebase/firestore';
import { toast } from 'react-hot-toast';
import { 
  CustomPackage, 
  CustomProLimitSettings, 
  CustomEnterpriseLimitSettings 
} from './userLimits';

// Collection name for custom packages
const CUSTOM_PACKAGES_COLLECTION = 'customPackages';

/**
 * Create a new custom package
 */
export const createCustomPackage = async (
  name: string,
  type: 'pro-based' | 'enterprise-based',
  limits: CustomProLimitSettings | CustomEnterpriseLimitSettings,
  createdBy: string
): Promise<string> => {
  try {
    const timestamp = new Date().toISOString();
    const customPackage: Omit<CustomPackage, 'id'> = {
      name,
      type,
      limits,
      createdAt: timestamp,
      updatedAt: timestamp,
      createdBy,
      isActive: true
    };

    const docRef = doc(collection(db, CUSTOM_PACKAGES_COLLECTION));
    await setDoc(docRef, customPackage);
    
    toast.success('Custom package created successfully');
    return docRef.id;
  } catch (error) {
    console.error('Error creating custom package:', error);
    toast.error('Failed to create custom package');
    throw error;
  }
};

/**
 * Get all custom packages
 */
export const getAllCustomPackages = async (): Promise<CustomPackage[]> => {
  try {
    const packagesRef = collection(db, CUSTOM_PACKAGES_COLLECTION);
    const q = query(packagesRef, orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as CustomPackage[];
  } catch (error) {
    console.error('Error fetching custom packages:', error);
    toast.error('Failed to load custom packages');
    throw error;
  }
};

/**
 * Get active custom packages only
 */
export const getActiveCustomPackages = async (): Promise<CustomPackage[]> => {
  try {
    const packagesRef = collection(db, CUSTOM_PACKAGES_COLLECTION);
    const q = query(
      packagesRef, 
      where('isActive', '==', true),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as CustomPackage[];
  } catch (error) {
    console.error('Error fetching active custom packages:', error);
    toast.error('Failed to load active custom packages');
    throw error;
  }
};

/**
 * Get a specific custom package by ID
 */
export const getCustomPackageById = async (packageId: string): Promise<CustomPackage | null> => {
  try {
    const packageRef = doc(db, CUSTOM_PACKAGES_COLLECTION, packageId);
    const packageDoc = await getDoc(packageRef);
    
    if (packageDoc.exists()) {
      return {
        id: packageDoc.id,
        ...packageDoc.data()
      } as CustomPackage;
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching custom package:', error);
    throw error;
  }
};

/**
 * Update an existing custom package
 */
export const updateCustomPackage = async (
  packageId: string,
  updates: Partial<Omit<CustomPackage, 'id' | 'createdAt' | 'createdBy'>>
): Promise<void> => {
  try {
    const packageRef = doc(db, CUSTOM_PACKAGES_COLLECTION, packageId);
    const updateData = {
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    await updateDoc(packageRef, updateData);
    toast.success('Custom package updated successfully');
  } catch (error) {
    console.error('Error updating custom package:', error);
    toast.error('Failed to update custom package');
    throw error;
  }
};

/**
 * Delete a custom package
 */
export const deleteCustomPackage = async (packageId: string): Promise<void> => {
  try {
    const packageRef = doc(db, CUSTOM_PACKAGES_COLLECTION, packageId);
    await deleteDoc(packageRef);
    toast.success('Custom package deleted successfully');
  } catch (error) {
    console.error('Error deleting custom package:', error);
    toast.error('Failed to delete custom package');
    throw error;
  }
};

/**
 * Check if a custom package name already exists
 */
export const isCustomPackageNameExists = async (name: string, excludeId?: string): Promise<boolean> => {
  try {
    const packagesRef = collection(db, CUSTOM_PACKAGES_COLLECTION);
    const q = query(packagesRef, where('name', '==', name));
    const querySnapshot = await getDocs(q);
    
    if (excludeId) {
      // When updating, exclude the current package from the check
      return querySnapshot.docs.some(doc => doc.id !== excludeId);
    }
    
    return !querySnapshot.empty;
  } catch (error) {
    console.error('Error checking custom package name:', error);
    return false;
  }
};

/**
 * Get custom package type (pro-based or enterprise-based)
 */
export const getCustomPackageType = async (
  packageName: string
): Promise<'pro-based' | 'enterprise-based' | null> => {
  try {
    const packagesRef = collection(db, CUSTOM_PACKAGES_COLLECTION);
    const q = query(
      packagesRef,
      where('name', '==', packageName),
      where('isActive', '==', true)
    );
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      const packageDoc = querySnapshot.docs[0];
      const packageData = packageDoc.data() as CustomPackage;
      return packageData.type;
    }

    return null;
  } catch (error) {
    console.error('Error fetching custom package type:', error);
    return null;
  }
};

/**
 * Get custom package limits for a user
 */
export const getCustomPackageLimits = async (
  packageName: string
): Promise<CustomProLimitSettings | CustomEnterpriseLimitSettings | null> => {
  try {
    const packagesRef = collection(db, CUSTOM_PACKAGES_COLLECTION);
    const q = query(
      packagesRef,
      where('name', '==', packageName),
      where('isActive', '==', true)
    );
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      const packageDoc = querySnapshot.docs[0];
      const packageData = packageDoc.data() as CustomPackage;
      return packageData.limits;
    }

    return null;
  } catch (error) {
    console.error('Error fetching custom package limits:', error);
    return null;
  }
};

/**
 * Default limits for new custom packages
 */
export const getDefaultCustomProLimits = (): CustomProLimitSettings => ({
  maxImages: 100,
  deleteDelayHours: 0,
  maxSavedPrompts: 100,
  maxCustomPrompts: 20,
  monthlyGenerationLimit: 300
});

export const getDefaultCustomEnterpriseLimits = (): CustomEnterpriseLimitSettings => ({
  maxTeamMembers: 5,
  maxImages: 200,
  deleteDelayHours: 0,
  maxSavedPrompts: 150,
  maxCustomPrompts: 20,
  monthlyGenerationLimit: 500
});
