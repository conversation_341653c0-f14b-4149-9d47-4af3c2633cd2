import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { db } from '@/lib/firebase';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { toast } from 'react-hot-toast';
import { Settings, Save, RefreshCw } from 'lucide-react';

interface PayAsYouGoLimitSettings {
  maxImages: number;
  deleteDelayHours: number;
  maxSavedPrompts: number;
  maxCustomPrompts: number;
  totalGenerationLimit: number;
}

export function PayAsYouGoUserLimitsManagement() {
  const [limits, setLimits] = useState<PayAsYouGoLimitSettings>({
    maxImages: 20,
    deleteDelayHours: 24,
    maxSavedPrompts: 30,
    maxCustomPrompts: 5,
    totalGenerationLimit: 15
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchLimits();
  }, []);

  const fetchLimits = async () => {
    setLoading(true);
    try {
      // Import auth to check if user is authenticated
      const { getAuth } = await import('firebase/auth');
      const auth = getAuth();
      const currentUser = auth.currentUser;
      
      if (!currentUser) {
        toast.error('You must be logged in to access this page');
        setLoading(false);
        return;
      }
      
      // Check if limits document exists
      const limitsDocRef = doc(db, 'settings', 'payAsYouGoUserLimits');
      const limitsDoc = await getDoc(limitsDocRef);
      
      if (limitsDoc.exists()) {
        const data = limitsDoc.data() as PayAsYouGoLimitSettings;
        setLimits(data);
      } else {
        // If no document exists, create one with default values
        await setDoc(limitsDocRef, limits);
        toast.success('Created default Pay-As-You-Go user limits settings');
      }
    } catch (error: any) {
      console.error('Error fetching Pay-As-You-Go user limits:', error);
      if (error.code === 'permission-denied') {
        toast.error('You do not have permission to access Pay-As-You-Go user limits settings');
      } else {
        toast.error(`Error loading Pay-As-You-Go user limits: ${error.message || 'Unknown error'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSaveLimits = async () => {
    setSaving(true);
    try {
      const limitsDocRef = doc(db, 'settings', 'payAsYouGoUserLimits');
      await setDoc(limitsDocRef, limits);
      toast.success('Pay-As-You-Go user limits saved successfully');
    } catch (error) {
      console.error('Error saving Pay-As-You-Go user limits:', error);
      toast.error('Error saving Pay-As-You-Go user limits');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof PayAsYouGoLimitSettings, value: number) => {
    setLimits(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const resetToDefaults = () => {
    setLimits({
      maxImages: 20,
      deleteDelayHours: 24,
      maxSavedPrompts: 30,
      maxCustomPrompts: 5,
      totalGenerationLimit: 15
    });
    toast.success('Reset to default values');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-white">Pay-As-You-Go User Limit Settings</h2>
          <p className="text-gray-400 mt-1">Configure limits for Pay-As-You-Go users</p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
            onClick={resetToDefaults}
            disabled={saving}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>
          <Button
            className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700"
            onClick={handleSaveLimits}
            disabled={saving}
          >
            {saving ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </span>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Settings
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Max Images */}
        <Card className="bg-gray-800 border-gray-700 text-white">
          <CardHeader>
            <CardTitle className="text-lg font-medium flex items-center">
              <Settings className="h-5 w-5 mr-2 text-purple-400" />
              Max Images Upload Limit
            </CardTitle>
            <CardDescription className="text-gray-400">
              Maximum number of images a Pay-As-You-Go user can upload
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Current value: {limits.maxImages}</span>
                <Input
                  type="number"
                  value={limits.maxImages}
                  onChange={(e) => handleInputChange('maxImages', parseInt(e.target.value) || 0)}
                  min="1"
                  max="100"
                  className="w-24 bg-gray-700 border-gray-600 text-white"
                />
              </div>
              <Slider
                value={[limits.maxImages]}
                min={1}
                max={100}
                step={1}
                onValueChange={(value) => handleInputChange('maxImages', value[0])}
                className="py-4"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>1</span>
                <span>25</span>
                <span>50</span>
                <span>75</span>
                <span>100</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Delete Delay Hours */}
        <Card className="bg-gray-800 border-gray-700 text-white">
          <CardHeader>
            <CardTitle className="text-lg font-medium flex items-center">
              <Settings className="h-5 w-5 mr-2 text-purple-400" />
              Image Deletion Delay (Hours)
            </CardTitle>
            <CardDescription className="text-gray-400">
              Hours a Pay-As-You-Go user must wait before deleting an uploaded image
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Current value: {limits.deleteDelayHours}</span>
                <Input
                  type="number"
                  value={limits.deleteDelayHours}
                  onChange={(e) => handleInputChange('deleteDelayHours', parseInt(e.target.value) || 0)}
                  min="0"
                  max="72"
                  className="w-24 bg-gray-700 border-gray-600 text-white"
                />
              </div>
              <Slider
                value={[limits.deleteDelayHours]}
                min={0}
                max={72}
                step={1}
                onValueChange={(value) => handleInputChange('deleteDelayHours', value[0])}
                className="py-4"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>0</span>
                <span>24</span>
                <span>48</span>
                <span>72</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Max Saved Prompts */}
        <Card className="bg-gray-800 border-gray-700 text-white">
          <CardHeader>
            <CardTitle className="text-lg font-medium flex items-center">
              <Settings className="h-5 w-5 mr-2 text-purple-400" />
              Max Saved Prompts Limit
            </CardTitle>
            <CardDescription className="text-gray-400">
              Maximum number of prompts a Pay-As-You-Go user can save
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Current value: {limits.maxSavedPrompts}</span>
                <Input
                  type="number"
                  value={limits.maxSavedPrompts}
                  onChange={(e) => handleInputChange('maxSavedPrompts', parseInt(e.target.value) || 0)}
                  min="1"
                  max="100"
                  className="w-24 bg-gray-700 border-gray-600 text-white"
                />
              </div>
              <Slider
                value={[limits.maxSavedPrompts]}
                min={1}
                max={100}
                step={1}
                onValueChange={(value) => handleInputChange('maxSavedPrompts', value[0])}
                className="py-4"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>1</span>
                <span>25</span>
                <span>50</span>
                <span>75</span>
                <span>100</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Max Custom Prompts */}
        <Card className="bg-gray-800 border-gray-700 text-white">
          <CardHeader>
            <CardTitle className="text-lg font-medium flex items-center">
              <Settings className="h-5 w-5 mr-2 text-purple-400" />
              Max Custom Prompts Limit
            </CardTitle>
            <CardDescription className="text-gray-400">
              Maximum number of custom prompts a Pay-As-You-Go user can create
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Current value: {limits.maxCustomPrompts}</span>
                <Input
                  type="number"
                  value={limits.maxCustomPrompts}
                  onChange={(e) => handleInputChange('maxCustomPrompts', parseInt(e.target.value) || 0)}
                  min="0"
                  max="20"
                  className="w-24 bg-gray-700 border-gray-600 text-white"
                />
              </div>
              <Slider
                value={[limits.maxCustomPrompts]}
                min={0}
                max={20}
                step={1}
                onValueChange={(value) => handleInputChange('maxCustomPrompts', value[0])}
                className="py-4"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>0</span>
                <span>5</span>
                <span>10</span>
                <span>15</span>
                <span>20</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total Generation Limit */}
        <Card className="bg-gray-800 border-gray-700 text-white">
          <CardHeader>
            <CardTitle className="text-lg font-medium flex items-center">
              <Settings className="h-5 w-5 mr-2 text-purple-400" />
              Max Prompts Generated Lifetime
            </CardTitle>
            <CardDescription className="text-gray-400">
              Maximum number of prompts a Pay-As-You-Go user can generate in total
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Current value: {limits.totalGenerationLimit}</span>
                <Input
                  type="number"
                  value={limits.totalGenerationLimit}
                  onChange={(e) => handleInputChange('totalGenerationLimit', parseInt(e.target.value) || 0)}
                  min="1"
                  max="100"
                  className="w-24 bg-gray-700 border-gray-600 text-white"
                />
              </div>
              <Slider
                value={[limits.totalGenerationLimit]}
                min={1}
                max={100}
                step={1}
                onValueChange={(value) => handleInputChange('totalGenerationLimit', value[0])}
                className="py-4"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>1</span>
                <span>25</span>
                <span>50</span>
                <span>75</span>
                <span>100</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default PayAsYouGoUserLimitsManagement;
