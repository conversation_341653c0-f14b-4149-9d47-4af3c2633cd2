const functions = require('firebase-functions');
const admin = require('firebase-admin');
const crypto = require('crypto');

/**
 * Paddle Webhook Handler
 * Handles payment events from Paddle including successful payments, failures, and subscription changes
 */
exports.handlePaddleWebhook = functions.https.onRequest(async (req, res) => {
  try {
    // CORS headers
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'POST');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, Paddle-Signature');

    if (req.method === 'OPTIONS') {
      res.status(200).send();
      return;
    }

    if (req.method !== 'POST') {
      res.status(405).send('Method Not Allowed');
      return;
    }

    const body = req.body;
    const signature = req.headers['paddle-signature'];

    console.log('Received Paddle webhook:', {
      eventType: body.event_type,
      eventId: body.event_id,
      timestamp: new Date().toISOString()
    });

    // Verify webhook signature (if webhook secret is configured)
    // For now, we'll process without signature verification
    // You should configure webhook secret in Paddle dashboard and add verification

    // Process different event types
    switch (body.event_type) {
      case 'transaction.completed':
        await handleTransactionCompleted(body.data);
        break;
      
      case 'transaction.payment_failed':
        await handleTransactionFailed(body.data);
        break;
      
      case 'subscription.created':
        await handleSubscriptionCreated(body.data);
        break;
      
      case 'subscription.updated':
        await handleSubscriptionUpdated(body.data);
        break;
      
      case 'subscription.canceled':
        await handleSubscriptionCanceled(body.data);
        break;
      
      case 'subscription.paused':
        await handleSubscriptionPaused(body.data);
        break;
      
      case 'subscription.resumed':
        await handleSubscriptionResumed(body.data);
        break;
      
      default:
        console.log('Unhandled event type:', body.event_type);
    }

    res.status(200).json({ received: true });
  } catch (error) {
    console.error('Error processing Paddle webhook:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Handle completed transaction (successful payment)
 */
async function handleTransactionCompleted(transactionData) {
  try {
    console.log('Processing completed transaction:', transactionData.id);

    const customData = transactionData.custom_data || {};
    const userId = customData.userId;
    const priceId = transactionData.items[0]?.price?.id;

    if (!userId) {
      console.error('No userId found in transaction custom data');
      return;
    }

    // Determine the plan based on price ID
    const planInfo = getPlanFromPriceId(priceId);
    if (!planInfo) {
      console.error('Unknown price ID:', priceId);
      return;
    }    // Update user subscription in Firestore
    // Query by uid field instead of using document ID
    const usersRef = admin.firestore().collection('users');
    const userQuery = usersRef.where('uid', '==', userId);
    const userQuerySnapshot = await userQuery.get();

    let userRef;
    if (userQuerySnapshot.empty) {
      console.warn('User document not found, creating basic user record for userId:', userId);

      // Create a new document with auto-generated ID
      userRef = usersRef.doc();
      await userRef.set({
        uid: userId,
        email: '<EMAIL>', // Will be updated when user signs in
        displayName: 'Webhook User',
        createdAt: admin.firestore.Timestamp.now(),
        role: 'Free User',
        packageName: 'Free',
        status: 'active',
        createdViaWebhook: true
      });
    } else {
      // Use the existing document
      userRef = userQuerySnapshot.docs[0].ref;
      console.log('Found existing user document:', userRef.id);
    }
    
    const updateData = {
      subscriptionStatus: 'active',
      plan: planInfo.plan,
      billingCycle: planInfo.billingCycle,
      paddleTransactionId: transactionData.id,
      paddleCustomerId: transactionData.customer_id,
      subscriptionStartDate: admin.firestore.Timestamp.now(),
      lastPaymentDate: admin.firestore.Timestamp.now(),
      paymentMethod: 'paddle',
      updatedAt: admin.firestore.Timestamp.now()
    };

    // Set the correct packageName and role based on the plan
    if (planInfo.plan === 'payAsYouGo') {
      updateData.packageName = 'Pay-As-You-Go';
      updateData.role = 'Pay-As-You-Go User';
    } else if (planInfo.plan === 'pro') {
      updateData.packageName = 'Pro';
      updateData.role = 'Pro User';
    } else if (planInfo.plan === 'enterprise') {
      updateData.packageName = 'Enterprise';
      updateData.role = 'Enterprise User';
    }

    // For Pay-As-You-Go, add credits instead of subscription
    if (planInfo.plan === 'payAsYouGo') {
      const currentUserDoc = await userRef.get();
      const currentCredits = currentUserDoc.data()?.credits || 0;
      updateData.credits = currentCredits + planInfo.credits;
      updateData.subscriptionStatus = 'none'; // Pay-as-you-go doesn't have subscription
      delete updateData.billingCycle;
    } else {
      // For subscription plans, set expiry date
      const expiryDate = new Date();
      if (planInfo.billingCycle === 'monthly') {
        expiryDate.setMonth(expiryDate.getMonth() + 1);
      } else if (planInfo.billingCycle === 'yearly') {
        expiryDate.setFullYear(expiryDate.getFullYear() + 1);
      }
      // Use the field name that the frontend expects
      updateData.subscriptionExpireDate = expiryDate.toISOString();
    }

    await userRef.update(updateData);

    console.log('User subscription updated successfully:', userId);
    console.log('Update data applied:', JSON.stringify(updateData, null, 2));
    console.log('Payment processing completed for transaction:', transactionData.id);

    // Log the transaction for record keeping
    await admin.firestore().collection('transactions').add({
      userId,
      transactionId: transactionData.id,
      customerId: transactionData.customer_id,
      amount: transactionData.details.totals.total,
      currency: transactionData.currency_code,
      status: 'completed',
      plan: planInfo.plan,
      billingCycle: planInfo.billingCycle,
      paymentMethod: 'paddle',
      createdAt: admin.firestore.Timestamp.now()
    });

    // Also create a payment log entry for the payment history component
    const paymentLogData = {
      userId,
      transactionId: transactionData.id,
      sessionId: transactionData.checkout?.id || transactionData.id,
      status: 'SUCCESS',
      packageName: planInfo.plan === 'payAsYouGo' ? 'Pay-As-You-Go' :
                   planInfo.plan === 'pro' ? 'Pro' :
                   planInfo.plan === 'enterprise' ? 'Enterprise' : planInfo.plan,
      amount: parseInt(transactionData.details.totals.total),
      currency: transactionData.currency_code,
      paymentMethod: 'Paddle',
      billingPeriod: planInfo.billingCycle || 'one-time',
      timestamp: admin.firestore.Timestamp.now(),
      source: 'paddle_webhook'
    };

    // Only add userEmail and userName if they exist (to avoid undefined values)
    if (customData.userEmail || transactionData.customer?.email) {
      paymentLogData.userEmail = customData.userEmail || transactionData.customer?.email;
    }
    if (customData.userName || transactionData.customer?.name) {
      paymentLogData.userName = customData.userName || transactionData.customer?.name;
    }

    await admin.firestore().collection('paymentLogs').add(paymentLogData);

  } catch (error) {
    console.error('Error handling completed transaction:', error);
    throw error;
  }
}

/**
 * Handle failed transaction
 */
async function handleTransactionFailed(transactionData) {
  try {
    console.log('Processing failed transaction:', transactionData.id);

    const customData = transactionData.custom_data || {};
    const userId = customData.userId;

    if (!userId) {
      console.error('No userId found in failed transaction custom data');
      return;
    }

    // Log the failed transaction
    await admin.firestore().collection('transactions').add({
      userId,
      transactionId: transactionData.id,
      customerId: transactionData.customer_id,
      amount: transactionData.details.totals.total,
      currency: transactionData.currency_code,
      status: 'failed',
      paymentMethod: 'paddle',
      createdAt: admin.firestore.Timestamp.now()
    });

    // Also create a payment log entry for the payment history component
    const failedPaymentLogData = {
      userId,
      transactionId: transactionData.id,
      sessionId: transactionData.checkout?.id || transactionData.id,
      status: 'FAILED',
      packageName: 'Unknown',
      amount: parseInt(transactionData.details.totals.total),
      currency: transactionData.currency_code,
      paymentMethod: 'Paddle',
      billingPeriod: 'unknown',
      timestamp: admin.firestore.Timestamp.now(),
      source: 'paddle_webhook'
    };

    // Only add userEmail and userName if they exist (to avoid undefined values)
    if (customData.userEmail || transactionData.customer?.email) {
      failedPaymentLogData.userEmail = customData.userEmail || transactionData.customer?.email;
    }
    if (customData.userName || transactionData.customer?.name) {
      failedPaymentLogData.userName = customData.userName || transactionData.customer?.name;
    }

    await admin.firestore().collection('paymentLogs').add(failedPaymentLogData);

    console.log('Failed transaction logged:', transactionData.id);

  } catch (error) {
    console.error('Error handling failed transaction:', error);
    throw error;
  }
}

/**
 * Handle subscription created
 */
async function handleSubscriptionCreated(subscriptionData) {
  try {
    console.log('Processing subscription created:', subscriptionData.id);

    const customData = subscriptionData.custom_data || {};
    const userId = customData.userId;

    if (!userId) {
      console.error('No userId found in subscription custom data');
      return;
    }

    // Update user with subscription details
    const userRef = admin.firestore().collection('users').doc(userId);
    await userRef.update({
      paddleSubscriptionId: subscriptionData.id,
      subscriptionStatus: subscriptionData.status,
      updatedAt: admin.firestore.Timestamp.now()
    });

    console.log('Subscription created successfully:', subscriptionData.id);

  } catch (error) {
    console.error('Error handling subscription created:', error);
    throw error;
  }
}

/**
 * Handle subscription updated
 */
async function handleSubscriptionUpdated(subscriptionData) {
  try {
    console.log('Processing subscription updated:', subscriptionData.id);

    const customData = subscriptionData.custom_data || {};
    const userId = customData.userId;

    if (!userId) {
      console.error('No userId found in subscription update custom data');
      return;
    }

    // Update user subscription status
    const userRef = admin.firestore().collection('users').doc(userId);
    await userRef.update({
      subscriptionStatus: subscriptionData.status,
      updatedAt: admin.firestore.Timestamp.now()
    });

    console.log('Subscription updated successfully:', subscriptionData.id);

  } catch (error) {
    console.error('Error handling subscription updated:', error);
    throw error;
  }
}

/**
 * Handle subscription canceled
 */
async function handleSubscriptionCanceled(subscriptionData) {
  try {
    console.log('Processing subscription canceled:', subscriptionData.id);

    const customData = subscriptionData.custom_data || {};
    const userId = customData.userId;

    if (!userId) {
      console.error('No userId found in subscription cancel custom data');
      return;
    }

    // Update user subscription status
    const userRef = admin.firestore().collection('users').doc(userId);
    await userRef.update({
      subscriptionStatus: 'canceled',
      updatedAt: admin.firestore.Timestamp.now()
    });

    console.log('Subscription canceled successfully:', subscriptionData.id);

  } catch (error) {
    console.error('Error handling subscription canceled:', error);
    throw error;
  }
}

/**
 * Handle subscription paused
 */
async function handleSubscriptionPaused(subscriptionData) {
  try {
    console.log('Processing subscription paused:', subscriptionData.id);

    const customData = subscriptionData.custom_data || {};
    const userId = customData.userId;

    if (!userId) {
      console.error('No userId found in subscription pause custom data');
      return;
    }

    // Update user subscription status
    const userRef = admin.firestore().collection('users').doc(userId);
    await userRef.update({
      subscriptionStatus: 'paused',
      updatedAt: admin.firestore.Timestamp.now()
    });

    console.log('Subscription paused successfully:', subscriptionData.id);

  } catch (error) {
    console.error('Error handling subscription paused:', error);
    throw error;
  }
}

/**
 * Handle subscription resumed
 */
async function handleSubscriptionResumed(subscriptionData) {
  try {
    console.log('Processing subscription resumed:', subscriptionData.id);

    const customData = subscriptionData.custom_data || {};
    const userId = customData.userId;

    if (!userId) {
      console.error('No userId found in subscription resume custom data');
      return;
    }

    // Update user subscription status
    const userRef = admin.firestore().collection('users').doc(userId);
    await userRef.update({
      subscriptionStatus: 'active',
      updatedAt: admin.firestore.Timestamp.now()
    });

    console.log('Subscription resumed successfully:', subscriptionData.id);

  } catch (error) {
    console.error('Error handling subscription resumed:', error);
    throw error;
  }
}

/**
 * Get plan information from Paddle price ID
 */
function getPlanFromPriceId(priceId) {
  const priceMapping = {
    // Production Price IDs
    // Pay-As-You-Go
    'pri_01jxbve4a77pt0545ntrvaea5t': {
      plan: 'payAsYouGo',
      credits: 50, // $2 = 50 credits
      billingCycle: null
    },
    // Pro Monthly
    'pri_01jxbve4kcgjvmq2jk65bcjw1w': {
      plan: 'pro',
      billingCycle: 'monthly'
    },
    // Pro Yearly
    'pri_01jxbve4wp9g66xjxcbhjjw8c2': {
      plan: 'pro',
      billingCycle: 'yearly'
    },
    // Enterprise Monthly
    'pri_01jxbve5q9se3877q6vs3sdmzs': {
      plan: 'enterprise',
      billingCycle: 'monthly'
    },
    // Enterprise Yearly
    'pri_01jxbve61ky5hvyt93ytq7rnzp': {
      plan: 'enterprise',
      billingCycle: 'yearly'
    },

    // Sandbox Price IDs
    // Pay-As-You-Go Sandbox
    'pri_01jxejv3jrz3qpfynwg325zr91': {
      plan: 'payAsYouGo',
      credits: 50, // $2 = 50 credits
      billingCycle: null
    },
    // Pro Monthly Sandbox
    'pri_01jxejy2yd3zkpva6p7pre4bbx': {
      plan: 'pro',
      billingCycle: 'monthly'
    },
    // Pro Yearly Sandbox
    'pri_01jxejzssvekxchyy0sk229xt0': {
      plan: 'pro',
      billingCycle: 'yearly'
    },
    // Enterprise Monthly Sandbox
    'pri_01jxek1vgmzbbxhs3nc4w08rht': {
      plan: 'enterprise',
      billingCycle: 'monthly'
    },
    // Enterprise Yearly Sandbox
    'pri_01jxek2y2rpmbq67qq660g3eg8': {
      plan: 'enterprise',
      billingCycle: 'yearly'
    }
  };

  return priceMapping[priceId] || null;
}
