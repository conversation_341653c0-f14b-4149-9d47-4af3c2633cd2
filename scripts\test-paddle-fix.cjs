/**
 * Test Paddle Integration - Environment Parameter Fix Verification
 * This script tests the fixed Paddle setup without the environment parameter
 */

// Check if we're in the correct directory
const path = require('path');
const fs = require('fs');

console.log('🧪 Testing Paddle Integration Fix...\n');

// Read the paddle.ts file to verify environment parameter removal
const paddleFilePath = path.join(__dirname, '..', 'src', 'lib', 'paddle.ts');

if (fs.existsSync(paddleFilePath)) {
  const paddleContent = fs.readFileSync(paddleFilePath, 'utf8');
  
  console.log('✅ Paddle.ts file found');
  
  // Check if environment parameter is removed from config
  if (!paddleContent.includes('environment: import.meta.env.VITE_PADDLE_ENVIRONMENT')) {
    console.log('✅ Environment parameter removed from paddleConfig');
  } else {
    console.log('❌ Environment parameter still present in paddleConfig');
  }
  
  // Check if environment parameter is removed from interface
  if (!paddleContent.includes('environment?: ')) {
    console.log('✅ Environment parameter removed from Paddle interface');
  } else {
    console.log('❌ Environment parameter still present in Paddle interface');
  }
  
  // Check if Setup function only uses token
  const setupMatch = paddleContent.match(/window\.Paddle\.Setup\(\{[^}]*\}\)/);
  if (setupMatch && setupMatch[0].includes('token:') && !setupMatch[0].includes('environment:')) {
    console.log('✅ Paddle.Setup() only uses token parameter');
  } else {
    console.log('❌ Paddle.Setup() still uses environment parameter or missing token');
  }
  
} else {
  console.log('❌ Paddle.ts file not found');
}

// Check .env file
const envFilePath = path.join(__dirname, '..', '.env');
if (fs.existsSync(envFilePath)) {
  const envContent = fs.readFileSync(envFilePath, 'utf8');
  
  console.log('✅ .env file found');
  
  if (envContent.includes('VITE_PADDLE_CLIENT_SIDE_TOKEN=live_')) {
    console.log('✅ Live Paddle client-side token configured');
  } else {
    console.log('❌ Live Paddle client-side token not found or not live token');
  }
  
  if (envContent.includes('VITE_PADDLE_API_KEY=pdl_live_')) {
    console.log('✅ Live Paddle API key configured');
  } else {
    console.log('❌ Live Paddle API key not found or not live key');
  }
  
} else {
  console.log('❌ .env file not found');
}

console.log('\n🎯 Integration Fix Summary:');
console.log('- Removed environment parameter from paddleConfig object');
console.log('- Removed environment parameter from Paddle TypeScript interface');
console.log('- Updated Paddle.Setup() to only use token parameter');
console.log('- Environment is now automatically determined by token type (live_ = production)');
console.log('- Ready for end-to-end testing!\n');

console.log('🚀 Next Steps:');
console.log('1. Open http://localhost:5173/test/paddle in browser');
console.log('2. Check that Paddle Status shows "Ready" (not environment error)');
console.log('3. Test checkout flows for all pricing plans');
console.log('4. Verify webhook processing with real transactions');
