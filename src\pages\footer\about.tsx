import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Footer } from '../../components/footer';

export function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-12">
        <Link to="/" className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Home
        </Link>

        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-white mb-8">About eComEasyAI</h1>

          <div className="prose prose-lg prose-invert max-w-none">
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 mb-8">
              <h3 className="text-xl font-semibold text-white mb-3">Business Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-gray-400 mb-1"><span className="text-purple-400">Trade License No:</span> 46</p>
                  <p className="text-gray-400 mb-1"><span className="text-purple-400">Serial No:</span> 548</p>
                  <p className="text-gray-400 mb-1"><span className="text-purple-400">BCS Member ID:</span> 2207</p>
                </div>
                <div>
                  <p className="text-gray-400 mb-1"><span className="text-purple-400">Vat Reg. No:</span> ***********</p>
                  <p className="text-gray-400 mb-1"><span className="text-purple-400">TIN:</span> 158667282160</p>
                  <p className="text-gray-400 mb-1"><span className="text-purple-400">BIN:</span> **********</p>
                </div>
              </div>
            </div>

            <p className="text-gray-300 mb-6">
              Founded in 2024, eComEasyAI is revolutionizing how e-commerce businesses create product descriptions.
              Our AI-powered platform transforms ordinary product images into compelling, SEO-optimized descriptions
              that drive conversions and save countless hours of copywriting work.
            </p>

            <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 p-6 rounded-xl border border-purple-500/20 mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">Our Story</h2>
              <p className="text-gray-300">
                eComEasyAI began when our founder, an e-commerce store owner, struggled with writing hundreds of
                product descriptions. After spending countless hours crafting descriptions, they realized there had to be
                a better way. By combining advanced AI image recognition with natural language processing,
                eComEasyAI was born—a tool that could instantly generate high-quality product descriptions from images alone.
              </p>
            </div>

            <h2 className="text-2xl font-semibold text-white mb-4">What Sets Us Apart</h2>
            <p className="text-gray-300 mb-6">
              Unlike generic AI writing tools, eComEasyAI is specifically designed for e-commerce. Our algorithms
              understand product features, benefits, and selling points—creating descriptions that actually convert.
              With support for over 30 languages and industry-specific terminology, we help businesses of all sizes
              scale their product listings without sacrificing quality.
            </p>

            <div className="grid md:grid-cols-2 gap-8 mb-8">
              <div className="bg-gray-800 p-6 rounded-xl border border-gray-700">
                <h3 className="text-xl font-semibold text-white mb-3">Our Approach</h3>
                <p className="text-gray-400">
                  We combine computer vision, natural language processing, and e-commerce expertise to create a
                  platform that understands products visually and describes them persuasively.
                </p>
              </div>
              <div className="bg-gray-800 p-6 rounded-xl border border-gray-700">
                <h3 className="text-xl font-semibold text-white mb-3">Our Impact</h3>
                <p className="text-gray-400">
                  Our clients report saving an average of 15 hours per week on content creation while seeing up to
                  35% improvement in conversion rates from our optimized descriptions.
                </p>
              </div>
            </div>

            <h2 className="text-2xl font-semibold text-white mb-4">Looking Forward</h2>
            <p className="text-gray-300 mb-8">
              As e-commerce continues to evolve, so does eComEasyAI. We're constantly improving our AI models,
              adding new features, and expanding our language support. Our vision is to become the essential content
              creation tool for every e-commerce business worldwide.
            </p>

            <div className="text-center mb-8">
              <Link to="/auth?mode=signup">
                <Button size="lg" className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700">
                  Join Our Journey
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

export default AboutPage;