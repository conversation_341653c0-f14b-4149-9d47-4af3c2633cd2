/**
 * Test Discount Codes in Paddle
 * This script tests if discount codes are working correctly in both environments
 */

const https = require('https');

// Environment configuration
const ENVIRONMENT = process.env.PADDLE_ENVIRONMENT || 'sandbox';
const PADDLE_API_KEY = ENVIRONMENT === 'production'
  ? (process.env.VITE_PADDLE_API_KEY_PRODUCTION || 'pdl_live_apikey_01jxb4n39mr2v9h5j9ge8m31d3_E8kmrRzxqetZVCNRT47wJy_ACc')
  : (process.env.VITE_PADDLE_API_KEY_SANDBOX || 'pdl_sdbx_apikey_01jxhkdd1qcvtqsf4kajykxjdx_G1r7Xtc2edq8HsFn8cg7qZ_AIH');
const PADDLE_API_URL = ENVIRONMENT === 'production' ? 'https://api.paddle.com' : 'https://sandbox-api.paddle.com';

// Test discount codes
const TEST_CODES = ['KKK', 'SAVE10', 'WELCOME'];

/**
 * Make API request to Paddle
 */
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(PADDLE_API_URL);
    const options = {
      hostname: url.hostname,
      path: path,
      method: method,
      headers: {
        'Authorization': `Bearer ${PADDLE_API_KEY}`,
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(response);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${JSON.stringify(response)}`));
          }
        } catch (error) {
          reject(new Error(`Invalid JSON response: ${body}`));
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

/**
 * List all discount codes
 */
async function listDiscountCodes() {
  try {
    const response = await makeRequest('GET', '/discounts?per_page=50');
    return response.data || [];
  } catch (error) {
    console.error('❌ Failed to list discount codes:', error.message);
    return [];
  }
}

/**
 * Test if a discount code exists and is active
 */
async function testDiscountCode(code) {
  try {
    const discounts = await listDiscountCodes();
    const discount = discounts.find(d => d.code === code);
    
    if (!discount) {
      return { exists: false, active: false, details: null };
    }
    
    return {
      exists: true,
      active: discount.status === 'active',
      details: {
        id: discount.id,
        code: discount.code,
        type: discount.type,
        amount: discount.amount,
        status: discount.status,
        enabled_for_checkout: discount.enabled_for_checkout
      }
    };
  } catch (error) {
    console.error(`❌ Failed to test discount code ${code}:`, error.message);
    return { exists: false, active: false, details: null, error: error.message };
  }
}

/**
 * Main test function
 */
async function testDiscountCodes() {
  console.log('🧪 PADDLE DISCOUNT CODE TEST');
  console.log('=' .repeat(50));
  console.log(`🌍 Environment: ${ENVIRONMENT.toUpperCase()}`);
  console.log(`🔗 API URL: ${PADDLE_API_URL}`);
  console.log(`🔑 API Key: ${PADDLE_API_KEY.substring(0, 20)}...`);
  console.log('=' .repeat(50));

  console.log('\n🔍 Testing discount codes...\n');

  for (const code of TEST_CODES) {
    console.log(`Testing code: ${code}`);
    const result = await testDiscountCode(code);
    
    if (result.error) {
      console.log(`❌ ${code}: Error - ${result.error}`);
    } else if (!result.exists) {
      console.log(`❌ ${code}: Does not exist`);
    } else if (!result.active) {
      console.log(`⚠️  ${code}: Exists but not active (${result.details.status})`);
    } else {
      console.log(`✅ ${code}: Active - ${result.details.type} ${result.details.amount}%`);
    }
    
    if (result.details) {
      console.log(`   ID: ${result.details.id}`);
      console.log(`   Enabled for checkout: ${result.details.enabled_for_checkout}`);
    }
    console.log('');
  }

  console.log('🎯 RECOMMENDATIONS:');
  console.log('1. If codes don\'t exist, run: PADDLE_ENVIRONMENT=sandbox node scripts/sync-paddle-discounts.cjs');
  console.log('2. If codes exist but not active, check Paddle dashboard');
  console.log('3. If codes are active, the issue might be in the frontend integration');
  console.log('4. Test the checkout at: http://localhost:5173/test/paddle');
}

// Run the test
if (require.main === module) {
  testDiscountCodes().catch(console.error);
}

module.exports = { testDiscountCodes };
