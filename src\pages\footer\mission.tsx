import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Footer } from '../../components/footer';

export function MissionPage() {
  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-12">
        <Link to="/" className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Home
        </Link>
        
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-white mb-8">Our Mission & Vision</h1>
          
          <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 p-8 rounded-xl border border-purple-500/20 mb-12">
            <h2 className="text-2xl font-semibold text-white mb-4">Our Mission</h2>
            <p className="text-gray-300 text-lg">
              To empower e-commerce businesses of all sizes with AI-powered content creation tools that save time, 
              increase conversions, and help them compete in an increasingly crowded marketplace.
            </p>
          </div>
          
          <div className="bg-gradient-to-r from-pink-500/10 to-purple-500/10 p-8 rounded-xl border border-pink-500/20 mb-12">
            <h2 className="text-2xl font-semibold text-white mb-4">Our Vision</h2>
            <p className="text-gray-300 text-lg">
              To become the global standard for e-commerce content creation, where any product image can be instantly 
              transformed into compelling, conversion-focused descriptions in any language.
            </p>
          </div>
          
          <h2 className="text-2xl font-semibold text-white mb-6">Our Core Values</h2>
          
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700">
              <h3 className="text-xl font-semibold text-white mb-3">Innovation</h3>
              <p className="text-gray-400">
                We're constantly pushing the boundaries of what's possible with AI and e-commerce, 
                developing new features and capabilities that help our customers stay ahead.
              </p>
            </div>
            
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700">
              <h3 className="text-xl font-semibold text-white mb-3">Accessibility</h3>
              <p className="text-gray-400">
                We believe powerful AI tools should be accessible to businesses of all sizes, 
                not just enterprises with large budgets and technical teams.
              </p>
            </div>
            
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700">
              <h3 className="text-xl font-semibold text-white mb-3">Quality</h3>
              <p className="text-gray-400">
                We're committed to delivering the highest quality AI-generated content that 
                truly represents products accurately and persuasively.
              </p>
            </div>
            
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700">
              <h3 className="text-xl font-semibold text-white mb-3">Customer Success</h3>
              <p className="text-gray-400">
                We measure our success by our customers' success. If our tools aren't helping 
                you sell more products and save time, we're not doing our job.
              </p>
            </div>
          </div>
          
          <div className="bg-gray-800 p-8 rounded-xl border border-gray-700 mb-12">
            <h2 className="text-2xl font-semibold text-white mb-4">Our Commitment</h2>
            <p className="text-gray-300 mb-6">
              At eComEasyAI, we're committed to continuous improvement. We regularly update our AI models 
              with the latest advancements in machine learning and natural language processing. We actively 
              seek customer feedback to ensure our platform meets the evolving needs of e-commerce businesses.
            </p>
            <p className="text-gray-300">
              We're also committed to ethical AI practices. We ensure our technology is used responsibly, 
              with transparency about what our AI can and cannot do. We never claim our AI-generated content 
              is human-written, but rather strive to make it so good that the distinction becomes less important.
            </p>
          </div>
          
          <div className="text-center">
            <Link to="/auth?mode=signup" className="inline-block">
              <button className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                Join Us in Our Mission
              </button>
            </Link>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

export default MissionPage;