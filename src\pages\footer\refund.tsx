import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Footer } from '../../components/footer';

export function RefundPage() {
  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-12">
        <Link to="/" className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Home
        </Link>
        
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-white mb-8">Refund Policy</h1>
          
          <div className="prose prose-lg prose-invert max-w-none">
            <p className="text-gray-300 mb-6">
              Last Updated: June 1, 2024
            </p>
            
            <div className="bg-gray-800 p-6 rounded-xl border border-gray-700 mb-8">
              <p className="text-gray-300">
                At eComEasyAI, we want you to be completely satisfied with our services. This Refund Policy outlines when and how you can request a refund for your subscription.
              </p>
            </div>
            
            <h2 className="text-2xl font-semibold text-white mb-4">1. Subscription Refunds</h2>
            <p className="text-gray-300 mb-6">
              We offer a 30-day money-back guarantee for all new subscriptions. If you're not satisfied with our service within the first 30 days of your subscription, you may request a full refund.
            </p>
            <p className="text-gray-300 mb-6">
              To be eligible for a refund, you must submit your request within 30 days of the initial subscription purchase date. Refund requests made after this period will not be accepted.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">2. How to Request a Refund</h2>
            <p className="text-gray-300 mb-6">
              To request a refund, please contact our customer support <NAME_EMAIL> with the following information:
            </p>
            <ul className="list-disc pl-6 text-gray-300 mb-6">
              <li>Your full name</li>
              <li>Email address associated with your account</li>
              <li>Date of purchase</li>
              <li>Reason for requesting a refund</li>
            </ul>
            <p className="text-gray-300 mb-6">
              Our team will review your request and respond within 2 business days. If approved, refunds will be processed to the original payment method used for the purchase.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">3. Exceptions</h2>
            <p className="text-gray-300 mb-6">
              The following situations are not eligible for refunds:
            </p>
            <ul className="list-disc pl-6 text-gray-300 mb-6">
              <li>Requests made after the 30-day refund period</li>
              <li>Subscription renewals (after the initial subscription period)</li>
              <li>Accounts that have violated our Terms of Service</li>
              <li>Accounts that have consumed a significant portion of their monthly image processing quota (more than 50%)</li>
            </ul>
            
            <h2 className="text-2xl font-semibold text-white mb-4">4. Refund Processing Time</h2>
            <p className="text-gray-300 mb-6">
              Once approved, refunds typically take 5-10 business days to process, depending on your payment provider. Credit card refunds may appear on your statement within 1-2 billing cycles.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">5. Cancellation vs. Refund</h2>
            <p className="text-gray-300 mb-6">
              Cancelling your subscription will prevent future billing but does not automatically issue a refund for previous payments. If you wish to cancel your subscription and request a refund, you must do both separately.
            </p>
            <p className="text-gray-300 mb-6">
              To cancel your subscription without requesting a refund, you can do so from your account settings page.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">6. Changes to This Policy</h2>
            <p className="text-gray-300 mb-6">
              We reserve the right to modify this Refund Policy at any time. Changes will be effective immediately upon posting to our website. It is your responsibility to review this Refund Policy periodically for changes.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">7. Contact Us</h2>
            <p className="text-gray-300 mb-6">
              If you have any questions about our Refund Policy, please contact <NAME_EMAIL>.
            </p>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

export default RefundPage;