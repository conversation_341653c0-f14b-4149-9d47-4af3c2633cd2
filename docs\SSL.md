# SSLCOMMERZ Payment Gateway Integration

This document provides comprehensive information about the SSLCOMMERZ payment gateway integration in the eComEasy.AI application.

## Overview

SSLCOMMERZ is a popular payment gateway in Bangladesh that allows users to pay using various local payment methods. This integration enables users from Bangladesh to make payments using SSLCOMMERZ, while users from other countries will continue to use Paddle.

## Setup Instructions

### Environment Variables

The following environment variables need to be set in the `.env` file:

```
# SSLCOMMERZ Configuration
VITE_SSLCOMMERZ_STORE_ID="your_store_id"
VITE_SSLCOMMERZ_STORE_PASSWORD="your_store_password"
VITE_SSLCOMMERZ_IS_LIVE=true  # Set to false for sandbox/test mode
```

For Firebase Functions, the same variables need to be set in the `functions/.env` file:

```
# SSLCOMMERZ Configuration
SSLCOMMERZ_STORE_ID="your_store_id"
SSLCOMMERZ_STORE_PASSWORD="your_store_password"
SSLCOMMERZ_IS_LIVE=true  # Set to false for sandbox/test mode

# Application URLs
APP_URL="https://your-app-url.com"
LOCAL_APP_URL="http://localhost:5173"
```

### Dependencies

The following dependencies are required for the SSLCOMMERZ integration:

- `sslcommerz-lts`: The official SSLCOMMERZ Node.js library
- `dotenv`: For loading environment variables
- `uuid`: For generating unique transaction IDs

These dependencies are already added to the `functions/package.json` file.

## Implementation Details

### Firebase Cloud Functions

The following Firebase Cloud Functions have been implemented for the SSLCOMMERZ integration:

1. `initSSLCommerzPayment`: Initializes a payment session with SSLCOMMERZ
2. `handleSSLCommerzSuccess`: Handles successful payments
3. `handleSSLCommerzFail`: Handles failed payments
4. `handleSSLCommerzCancel`: Handles canceled payments
5. `handleSSLCommerzIPN`: Handles Instant Payment Notifications (IPN)

### Frontend Integration

The frontend integration consists of the following components:

1. `src/lib/sslcommerz.ts`: Contains the functions for initializing and processing SSLCOMMERZ payments
2. `src/components/billing-details-step.tsx`: Displays the billing details form and payment gateway selection
3. `src/components/ui/upgrade-plan-modal.tsx`: Handles the upgrade plan flow
4. `src/components/ui/expired-package-modal.tsx`: Handles the expired package renewal flow
5. `src/components/payment-status.tsx`: Displays the payment status (success, failure, or cancellation)
6. `src/pages/payment/success.tsx`, `src/pages/payment/failed.tsx`, `src/pages/payment/canceled.tsx`: Payment status pages

## Payment Flow

1. User selects a plan and clicks "Upgrade" or "Get Started"
2. User fills in or confirms billing details
3. If the user's country is Bangladesh, SSLCOMMERZ is selected as the default payment gateway
4. User clicks "Proceed to Checkout"
5. The application initializes a payment session with SSLCOMMERZ
6. User is redirected to the SSLCOMMERZ payment page
7. User completes the payment on the SSLCOMMERZ payment page
8. SSLCOMMERZ redirects the user back to the application
9. The application verifies the payment and updates the user's subscription

## Testing

### Sandbox Testing

For testing purposes, you can use the SSLCOMMERZ sandbox environment. Set `VITE_SSLCOMMERZ_IS_LIVE=false` in the `.env` file and `SSLCOMMERZ_IS_LIVE=false` in the `functions/.env` file.

In sandbox mode, you can use the following test cards:

- Visa: ****************
- Mastercard: ****************
- American Express: ***************

For all test cards, use any future expiry date and any 3-digit CVV.

### IPN Testing

To test IPN (Instant Payment Notification), you need to configure the IPN URL in the SSLCOMMERZ merchant panel:

1. Log in to the SSLCOMMERZ merchant panel
2. Go to "My Stores" > "IPN Settings"
3. Set the IPN URL to `https://your-app-url.com/api/sslcommerz/ipn`

## Troubleshooting

### Common Issues

1. **Payment Initialization Failed**: Check if the SSLCOMMERZ credentials are correct and if the required parameters are provided.
2. **Validation Failed**: Check if the transaction ID is valid and if the payment was actually completed.
3. **Redirect URLs Not Working**: Make sure the success, fail, cancel, and IPN URLs are correctly configured and accessible.

### Debugging

For debugging purposes, you can check the following:

1. Firebase Functions logs in the Firebase Console
2. Transaction records in the `sslcommerzTransactions` collection in Firestore
3. Network requests in the browser's developer tools

## Security Considerations

1. All sensitive operations (payment initialization, validation, etc.) are performed on the server-side using Firebase Cloud Functions.
2. SSLCOMMERZ credentials are stored in environment variables and not exposed to the client.
3. All transactions are validated with SSLCOMMERZ before updating the user's subscription.

## References

- [SSLCOMMERZ Official Documentation](https://developer.sslcommerz.com/doc/v4/)
- [SSLCOMMERZ Node.js Library](https://github.com/sslcommerz/SSLCommerz-NodeJS)
- [Firebase Cloud Functions Documentation](https://firebase.google.com/docs/functions)
