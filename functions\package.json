{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "firebase-admin": "^11.8.0", "firebase-functions": "^5.0.1", "sslcommerz-lts": "^1.1.0", "uuid": "^9.0.1"}, "devDependencies": {"firebase-functions-test": "^3.1.0"}, "private": true}