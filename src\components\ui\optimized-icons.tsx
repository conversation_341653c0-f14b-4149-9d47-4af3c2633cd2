import { lazy, Suspense } from 'react';
import { LucideProps } from 'lucide-react';

// Create a simple loading placeholder for icons
const IconPlaceholder = ({ size = 24 }: { size?: number }) => (
  <div 
    className="animate-pulse bg-gray-600 rounded" 
    style={{ width: size, height: size }}
  />
);

// Lazy load icons to reduce initial bundle size
const LazyImageIcon = lazy(() => import('lucide-react').then(module => ({ default: module.ImageIcon })));
const LazyShoppingBag = lazy(() => import('lucide-react').then(module => ({ default: module.ShoppingBag })));
const LazySparkles = lazy(() => import('lucide-react').then(module => ({ default: module.Sparkles })));
const LazyZap = lazy(() => import('lucide-react').then(module => ({ default: module.Zap })));
const LazyMenu = lazy(() => import('lucide-react').then(module => ({ default: module.Menu })));
const LazyX = lazy(() => import('lucide-react').then(module => ({ default: module.X })));
const LazyGlobe = lazy(() => import('lucide-react').then(module => ({ default: module.Globe })));
const LazySearch = lazy(() => import('lucide-react').then(module => ({ default: module.Search })));
const LazyLayers = lazy(() => import('lucide-react').then(module => ({ default: module.Layers })));
const LazyCamera = lazy(() => import('lucide-react').then(module => ({ default: module.Camera })));
const LazySave = lazy(() => import('lucide-react').then(module => ({ default: module.Save })));
const LazyBookOpen = lazy(() => import('lucide-react').then(module => ({ default: module.BookOpen })));
const LazyEye = lazy(() => import('lucide-react').then(module => ({ default: module.Eye })));
const LazyMessageSquare = lazy(() => import('lucide-react').then(module => ({ default: module.MessageSquare })));
const LazyFileText = lazy(() => import('lucide-react').then(module => ({ default: module.FileText })));
const LazyPenTool = lazy(() => import('lucide-react').then(module => ({ default: module.PenTool })));
const LazyMail = lazy(() => import('lucide-react').then(module => ({ default: module.Mail })));
const LazyCheckCircle = lazy(() => import('lucide-react').then(module => ({ default: module.CheckCircle })));
const LazyClock = lazy(() => import('lucide-react').then(module => ({ default: module.Clock })));
const LazyShield = lazy(() => import('lucide-react').then(module => ({ default: module.Shield })));
const LazyAward = lazy(() => import('lucide-react').then(module => ({ default: module.Award })));
const LazyHeartHandshake = lazy(() => import('lucide-react').then(module => ({ default: module.HeartHandshake })));
const LazyLoader2 = lazy(() => import('lucide-react').then(module => ({ default: module.Loader2 })));
const LazyLanguages = lazy(() => import('lucide-react').then(module => ({ default: module.Languages })));

// Wrapper component for lazy-loaded icons
const LazyIcon = ({ 
  component: Component, 
  fallbackSize = 24, 
  ...props 
}: { 
  component: React.LazyExoticComponent<React.ComponentType<LucideProps>>;
  fallbackSize?: number;
} & LucideProps) => (
  <Suspense fallback={<IconPlaceholder size={fallbackSize} />}>
    <Component {...props} />
  </Suspense>
);

// Export optimized icon components
export const ImageIcon = (props: LucideProps) => <LazyIcon component={LazyImageIcon} {...props} />;
export const ShoppingBag = (props: LucideProps) => <LazyIcon component={LazyShoppingBag} {...props} />;
export const Sparkles = (props: LucideProps) => <LazyIcon component={LazySparkles} {...props} />;
export const Zap = (props: LucideProps) => <LazyIcon component={LazyZap} {...props} />;
export const Menu = (props: LucideProps) => <LazyIcon component={LazyMenu} {...props} />;
export const X = (props: LucideProps) => <LazyIcon component={LazyX} {...props} />;
export const Globe = (props: LucideProps) => <LazyIcon component={LazyGlobe} {...props} />;
export const Search = (props: LucideProps) => <LazyIcon component={LazySearch} {...props} />;
export const Layers = (props: LucideProps) => <LazyIcon component={LazyLayers} {...props} />;
export const Camera = (props: LucideProps) => <LazyIcon component={LazyCamera} {...props} />;
export const Save = (props: LucideProps) => <LazyIcon component={LazySave} {...props} />;
export const BookOpen = (props: LucideProps) => <LazyIcon component={LazyBookOpen} {...props} />;
export const Eye = (props: LucideProps) => <LazyIcon component={LazyEye} {...props} />;
export const MessageSquare = (props: LucideProps) => <LazyIcon component={LazyMessageSquare} {...props} />;
export const FileText = (props: LucideProps) => <LazyIcon component={LazyFileText} {...props} />;
export const PenTool = (props: LucideProps) => <LazyIcon component={LazyPenTool} {...props} />;
export const Mail = (props: LucideProps) => <LazyIcon component={LazyMail} {...props} />;
export const CheckCircle = (props: LucideProps) => <LazyIcon component={LazyCheckCircle} {...props} />;
export const Clock = (props: LucideProps) => <LazyIcon component={LazyClock} {...props} />;
export const Shield = (props: LucideProps) => <LazyIcon component={LazyShield} {...props} />;
export const Award = (props: LucideProps) => <LazyIcon component={LazyAward} {...props} />;
export const HeartHandshake = (props: LucideProps) => <LazyIcon component={LazyHeartHandshake} {...props} />;
export const Loader2 = (props: LucideProps) => <LazyIcon component={LazyLoader2} {...props} />;
export const Languages = (props: LucideProps) => <LazyIcon component={LazyLanguages} {...props} />;
