# AppSumo Licensing API (v2) - Webhook Documentation

**Source URLs:**
- https://docs.licensing.appsumo.com/webhook/webhook__overview.html
- https://docs.licensing.appsumo.com/webhook/webhook__getting_started.html
- https://docs.licensing.appsumo.com/webhook/webhook__connect.html
- https://docs.licensing.appsumo.com/webhook/webhook__security.html

---

## Overview

AppSumo uses webhooks to notify partners when a license for their product is **purchased**, **activated**, **upgraded**, **downgraded**, or **deactivated**.

### What is a Webhook?

A webhook, or web callback, is a way for apps to send real-time data to other applications automatically. Unlike standard APIs that require constant polling, webhooks push data immediately when events happen, making them more efficient.

### AppSumo Webhook Event Types

AppSumo sends the following webhook events related to your product's licenses:

- `purchase`: Triggered when a user buys your product.
- `activate`: Triggered when a user activates their purchase.
- `upgrade`: Triggered when a user upgrades their product tier (e.g., Tier 1 to Tier 2).
- `downgrade`: Triggered when a user downgrades their product tier (e.g., Tier 2 to Tier 1).
- `deactivate`: Triggered when a license is deactivated, often due to a refund or cancellation.

#### Test Webhooks

Test webhooks validate your webhook URL and include `"test": true` in the `POST` request. These should not trigger actions within your product but should respond successfully to confirm your URL is working.

---

## Getting Started

### Prerequisites

- **Approved Application:** Ensure your application is approved on AppSumo.
- **Redirect URL:** Have a Redirect URL that can handle requests from AppSumo.
- **Allow Requests:** Ensure your server accepts requests from `appsumo.com`.

### Adding and Testing a Webhook URL

To add your webhook URL, visit the [AppSumo Partner Portal](https://www.appsumo.com/partners/products/) and select your product. AppSumo will send a test request to verify the URL, which must respond successfully to be saved.

Before saving, the URL will receive a test `POST` request containing a "**test**" field in the body. This request is for verification only, and the accompanying data is not valid.

Your URL must respond with a `200 OK` status and a `JSON` response indicating `success = true` along with the received event type.

Ensure all test requests return a successful response to confirm that your webhook is functioning correctly.

![Webhook URL](https://docs.licensing.appsumo.com/assets/img/Webhook_url.74266dc8.png)

**Example**:
```json
{
  "license_key": "00000000-aaaa-1111-bbbb-abcdef012345",
  "event": "purchase",
  "license_status": "inactive",
  "event_timestamp": *************,
  "created_at": **********,
  "test": true
}
```

---

## Connect to AppSumo

### Receiving webhook requests

Receiving webhook requests from AppSumo requires 4 steps.

1. Saving your webhook URL _(See [Webhooks: Getting started](https://docs.licensing.appsumo.com/webhook/webhook__getting_started.html))_
2. Validating the request
3. Processing the webhook request
4. Return a "success" response

There is also a [test webhook](https://docs.licensing.appsumo.com/webhook/webhook__connect.html#test-webhook-events) request that verifies the webhook saved in the [AppSumo Partner Portal](https://www.appsumo.com/partners/products/).

### Validating the request

Anytime a user makes a purchase (or activates/deactivates a license) a webhook is sent to the webhook URL provided. To ensure that the request is valid (and not someone trying to malicious), you will need to authenticate the request _(See [Webhook security](https://docs.licensing.appsumo.com/webhook/webhook__security.html))_.

### Processing the webhook request

After validating the request, you will need to process the data that is sent to you. The request sent will allow onboarding of new users or deactivating existing ones. What you decide to do with this information is up to you. However, we suggest that you update the status of the license in your system as well, for auditing purposes. For example, if you receive a `deactivate` event, set the license status for your user's account to `deactivated`.

Looking for a list of webhook definitions? Check out [Webhook Object](https://docs.licensing.appsumo.com/webhook/webhook__connect.html#webhook-object) located at the bottom of this page.

#### Purchase events

After a license is purchased, the `purchase` data will be sent to your webhook. You can use the `license_key` to set up a placeholder account for the user (so it will be ready when they `activate` and access your product.)

**Example**:
```json
{
  "license_key": "3794577c-3dbc-11ec-9bbc-0242ac130002",
  "event": "purchase",
  "license_status": "inactive",
  "event_timestamp": *************,
  "created_at": **********,
  "test": false
}
```

#### Activate events

After a license is activated, the `activate` data will be sent to your webhook.

**Example**:
```json
{
  "license_key": "3794577c-3dbc-11ec-9bbc-0242ac130002",
  "event": "activate",
  "license_status": "inactive",
  "event_timestamp": *************,
  "created_at": **********,
  "tier": 1,
  "test": false,
  "extra": {
    "reason": "Purchased by the customer"
  }
}
```

Notice that the `event` is `activate` but the license status is `inactive`. The reason for this is because AppSumo needs a successful (`200`) response from your application to `activate` it on AppSumo. Once activated, the user will be able to access it via AppSumo.

**Note: It is the responsibility of the AppSumo Partner to activate the user in their application as well.**

#### Upgrade events

An `upgrade` event is sent when customer enhances the license tier of a product.

**Example (Upgrade from tier 1 to tier 2)**:
```json
{
  "license_key": "c86ad3d7-3942-4d11-8814-b0bd81971691",
  "prev_license_key": "3794577c-3dbc-11ec-9bbc-0242ac130002",
  "event": "upgrade",
  "event_timestamp": 1671586387628,
  "created_at": 1671586387624,
  "license_status": "inactive",
  "tier": 2,
  "test": false,
  "extra": {
    "reason": "Upgraded by the customer"
  }
}
```

Each time a license is upgraded, it generates a new and unique `license_key` UUID. This key will be the customer's current **activated** license key for your product. The license status will be `inactive` and you need to `activate` it on your side. Once you reply with a [successful](https://docs.licensing.appsumo.com/webhook/webhook__connect.html#sending-a-successful-response) response AppSumo is going to `activate` it in our side.

The purpose of the new UUID is to provide a better license history and traceability. The `prev_license_key` and `tier` properties will allow you to apply the new tier to a customer. A very brief example would be to:

- Find the customer by their `license_key`
- Either:
  - Remove old license key entry and create a new one
  - Replace the license key with the new license key and update the tier.

In addition to the `upgrade` event, you will receive a simultaneous `deactivate` event for the previous license for traceability purposes:

```json
{
  "license_key": "3794577c-3dbc-11ec-9bbc-0242ac130002",
  "event": "deactivate",
  "event_timestamp": 1671586388077,
  "created_at": 1671586388072,
  "license_status": "deactivated",
  "tier": 1,
  "test": false,
  "extra": {
    "reason": "Upgraded by the customer"
  }
}
```

**Note:** The `license_status` in the payload is **deactivated**. AppSumo deactivates the license after sending the `upgrade` event and receives a successful response from the Partner.

#### Downgrade events

A `downgrade` event is sent when customer reduces the license tier. An example would be downgrading tier 2 to tier 1.

**Example**:
```json
{
  "license_key": "c8e57fa3-ea5b-4c39-a2bf-74f7f51d01b0",
  "prev_license_key": "c86ad3d7-3942-4d11-8814-b0bd81971691",
  "event": "downgrade",
  "event_timestamp": 1671586699435,
  "created_at": 1671586699431,
  "license_status": "inactive",
  "tier": 1,
  "test": false,
  "extra": {
    "reason": "Downgraded by the customer"
  }
}
```

Similarly to **upgrade** events, each license's **downgrade** event generates a new and unique inactive `license_key` UUID. Even if a product is **upgraded** to tier 2, then **downgraded** to tier 1 and finally **upgraded** back again to tier 2, AppSumo will send the new `license_key` for each upgrade and downgrade.

In addition to the `downgrade` event, you will receive a simultaneous `deactivate` event for the previous license for traceability purpose:

```json
{
  "license_key": "c86ad3d7-3942-4d11-8814-b0bd81971691",
  "event": "deactivate",
  "event_timestamp": 1671586699927,
  "created_at": 1671586699922,
  "license_status": "deactivated",
  "tier": 2,
  "test": false,
  "extra": {
    "reason": "Downgraded by the customer"
  }
}
```

#### Deactivate events

After a license is deactivated, the `deactivate` data will be sent to your webhook. A separate, unrelated `deactivate` event will be triggered by a user requesting a refund or intervention by AppSumo staff.

**Example**:
```json
{
  "license_key": "c8e57fa3-ea5b-4c39-a2bf-74f7f51d01b0",
  "event": "deactivate",
  "license_status": "active",
  "event_timestamp": 1671586699927,
  "created_at": 1671586699922,
  "test": false,
  "extra": {
    "reason": "Refunded by the user"
  }
}
```

Notice that the `event` is `deactivate` but the status is `active`. The reason for this is because AppSumo needs a successful (`200`) response from your application to `deactivate` it on AppSumo. Once deactivated, the user will no longer be able to access it via AppSumo.

**Note: It is the responsibility of the AppSumo Partner to deactivate the user in their application as well.**

#### Test webhook events

Test events are initiated when trying to save a webhook via the [AppSumo Partner Portal](https://www.appsumo.com/partners/products/). Notice that the `test` is `true`. This means that the data can be ignored, but it **must still send a successful (`200`) response**.

**Example**:
```json
{
  "license_key": "00000000-aaaa-1111-bbbb-abcdef012345",
  "event": "purchase",
  "license_status": "inactive",
  "event_timestamp": *************,
  "created_at": **********,
  "tier": 1,
  "test": true,
  "extra": {
    "reason": "Test event"
  }
}
```

### Sending a successful response

In order to ensure that the webhook was successfully received, AppSumo needs a confirmation response. All that is needed in the response is to repeat the event back (for event types [see above](https://docs.licensing.appsumo.com/webhook/webhook__connect.html#processing-the-webhook-request)) and with `success` = `true`.

**Example**:
```python
from django.views.generic import View
from django.http import JsonResponse
from rest_framework.status import HTTP_200_OK

# Use your urls.py to use this view
class MyAppSumoWebhook(View):
  def post(self, request, *args, **kwargs):

    body_unicode = request.body.decode('utf-8')
    body = json.loads(body_unicode)

    license_key = body.get('license_key')
    event = body.get('event')
    status = body.get('status')
    created_at = body.get('created_at')
    event_timestamp = body.get('event_timestamp')
    test = body.get('test')

    # TODO -- Process license_key, status, and event for your user
    # If test is true, do not process

    return JsonResponse({
         "success": True,
         "event": event,
         "message": "Your OPTIONAL message"
    }, status=HTTP_200_OK)
```

### Webhook Object

#### Attributes

**license_key** `string` - UUID

The Product's license key the webhook belongs to. This is a UUID string based on [RFC 4122](https://www.rfc-editor.org/rfc/rfc4122)

**prev_license_key** `string`

The previous Product's license key. This field is sent only for `upgrade` or `downgrade` webhook.

**event** `string`

The event is the customer action for the Product's license key, it's one of `activate`, `deactivate`, `purchase`, `upgrade`, `downgrade`.

**event_timestamp** `integer` - timestamp

Time at which the webhook was sent. It changes when retrying to deliver the webhook.

**created_at** `integer` - timestamp

Time at which the license key was created. It does not change on retrying.

**license_status** `string`

The current status of the license on AppSumo side.

**tier** `integer`

The tier applied for the Product license key.

**test** `boolean`

Indicates if it's a test request or not.

**extra** `extra`

Metadata information.

#### Extra Object

**reason** `string`

The reason why the webhook was sent.

---

## Webhook Security

### Overview

When data is exchanged over the internet, there is a risk of interception and modification, known as a man-in-the-middle attack. To protect against this, AppSumo includes security measures in every webhook sent to partners.

![Man-in-the-middle](https://docs.licensing.appsumo.com/assets/img/webhook-Man-in-the-middle.33780565.png)

#### Message Authenticity

Each webhook from AppSumo contains two security headers that help validate the integrity of the message, ensuring it has not been altered.

![Message-Authenticity](https://docs.licensing.appsumo.com/assets/img/webhook-Message-Authenticity.6fe02fb0.png)

### HMAC SHA256

AppSumo uses the HMAC SHA256 algorithm, which creates a secure, one-way encryption to verify that the received data is exactly as sent.

- **How It Works:**
  - AppSumo and the partner share an API Key used for both encryption and API requests.
  - AppSumo creates a message by combining a timestamp with the request body and encrypts it using the shared API Key _(See [API: Getting started](https://docs.licensing.appsumo.com/api/api__getting_started.html))_
  - This encrypted message (SHA) is sent to the partner in the HTTP header `X-Appsumo-Signature`.
  - The timestamp is sent in the HTTP header `X-Appsumo-Timestamp`, and the data is sent in unencrypted JSON format in the request body.
- **Validation Process:**
  - The partner receives the timestamp and data, combines them to form a single message, and encrypts it using the API Key.
  - The partner then compares the generated SHA with the value in `X-Appsumo-Signature`.
  - If they match, the request is confirmed as authentic and unmodified.

### Verifying webhook requests (Optional)

The following codes are example of how an AppSumo Partner can generate the SHA with the data sent by AppSumo and compare both SHA message.

```python
import hashlib
import hmac

def main():
  # The Partner finds their Api Key in their Partner Portal along with their Partner Secrets.
  api_key = "c73455d8-0f64-4354-bd64-1f454a227104"

  # Get from X-Appsumo-Timestamp http header
  timestamp = 1625165884119

  # Get from X-Appsumo-Signature http header
  sha = "****************************************************************"

  # Get from request body
  body = '{"license_key":"3794577c-3dbc-11ec-9bbc-0242ac130002","event":"purchase","status":"active","event_timestamp":*************,"test":false}'

  # build the message
  message = "{}{}".format(timestamp, body)

  # Generate sha
  signature = hmac.new(
      key=bytes(api_key, 'utf-8'),
      msg=bytes(message, 'utf-8'),
      digestmod=hashlib.sha256
  ).hexdigest()

  # compare generated signature with the one received from X-Appsumo-Signature
  # to make sure it is a trusted request
  print(signature == sha)

if __name__ == '__main__':
  main()
```
