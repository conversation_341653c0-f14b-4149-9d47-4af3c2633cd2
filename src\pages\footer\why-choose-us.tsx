import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, CheckCircle, Zap, Shield, Clock, Globe, Award, HeartHandshake } from 'lucide-react';
import { Footer } from '../../components/footer';

export function WhyChooseUsPage() {
  const benefits = [
    {
      title: 'AI-Powered Accuracy',
      description: 'Our advanced computer vision and natural language processing algorithms ensure your product descriptions are accurate, compelling, and conversion-focused.',
      icon: <Zap className="h-8 w-8 text-purple-400" />
    },
    {
      title: 'Time Savings',
      description: 'What would take hours of manual writing can be done in seconds. Our customers save an average of 15 hours per week on content creation.',
      icon: <Clock className="h-8 w-8 text-pink-400" />
    },
    {
      title: 'Global Reach',
      description: 'With support for over 30 languages, you can instantly create localized product descriptions for international markets without hiring translators.',
      icon: <Globe className="h-8 w-8 text-blue-400" />
    },
    {
      title: 'Enterprise-Grade Security',
      description: 'Your data is protected with bank-level encryption. We never share your images or content with third parties.',
      icon: <Shield className="h-8 w-8 text-green-400" />
    },
    {
      title: 'Award-Winning Results',
      description: 'Our platform has been recognized by industry experts for its exceptional quality and innovation in e-commerce content creation.',
      icon: <Award className="h-8 w-8 text-yellow-400" />
    },
    {
      title: 'Dedicated Support',
      description: 'Our customer success team is available to help you get the most out of our platform, with personalized guidance and training.',
      icon: <HeartHandshake className="h-8 w-8 text-red-400" />
    }
  ];

  const testimonials = [
    {
      quote: "eComEasyAI has transformed our product listing process. What used to take our team days now happens in minutes, and our conversion rates have increased by 28%.",
      author: "Sarah T.",
      company: "Fashion Retailer, 10,000+ SKUs"
    },
    {
      quote: "As a small business owner, I couldn't afford to hire a copywriter. eComEasyAI gives me professional-quality descriptions at a fraction of the cost.",
      author: "Michael R.",
      company: "Boutique Home Goods Store"
    },
    {
      quote: "The multilingual support is a game-changer. We've expanded to 5 new international markets without having to hire additional content creators.",
      author: "Elena K.",
      company: "Global Electronics Distributor"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-12">
        <Link to="/" className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Home
        </Link>
        
        <div className="max-w-5xl mx-auto">
          <h1 className="text-4xl font-bold text-white mb-4">Why Choose eComEasyAI</h1>
          <p className="text-gray-300 mb-12 text-xl">
            Discover how our AI-powered platform can transform your e-commerce content strategy
          </p>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {benefits.map((benefit, index) => (
              <div key={index} className="bg-gray-800 p-6 rounded-xl border border-gray-700">
                <div className="mb-4">
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-semibold text-white mb-3">{benefit.title}</h3>
                <p className="text-gray-400">{benefit.description}</p>
              </div>
            ))}
          </div>
          
          <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 p-8 rounded-xl border border-purple-500/20 mb-16">
            <h2 className="text-2xl font-semibold text-white mb-6">How We Compare</h2>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left py-3 px-4 text-gray-300">Feature</th>
                    <th className="text-center py-3 px-4 text-white">eComEasyAI</th>
                    <th className="text-center py-3 px-4 text-gray-300">Generic AI Tools</th>
                    <th className="text-center py-3 px-4 text-gray-300">Manual Copywriting</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-700">
                    <td className="py-3 px-4 text-gray-300">Processing Time</td>
                    <td className="py-3 px-4 text-center text-white">Seconds</td>
                    <td className="py-3 px-4 text-center text-gray-300">Minutes</td>
                    <td className="py-3 px-4 text-center text-gray-300">Hours</td>
                  </tr>
                  <tr className="border-b border-gray-700">
                    <td className="py-3 px-4 text-gray-300">E-commerce Specific</td>
                    <td className="py-3 px-4 text-center text-white"><CheckCircle className="h-5 w-5 text-green-500 mx-auto" /></td>
                    <td className="py-3 px-4 text-center text-gray-300">Limited</td>
                    <td className="py-3 px-4 text-center text-gray-300">Varies</td>
                  </tr>
                  <tr className="border-b border-gray-700">
                    <td className="py-3 px-4 text-gray-300">Visual Analysis</td>
                    <td className="py-3 px-4 text-center text-white"><CheckCircle className="h-5 w-5 text-green-500 mx-auto" /></td>
                    <td className="py-3 px-4 text-center text-gray-300">Basic</td>
                    <td className="py-3 px-4 text-center text-gray-300">Manual</td>
                  </tr>
                  <tr className="border-b border-gray-700">
                    <td className="py-3 px-4 text-gray-300">Language Support</td>
                    <td className="py-3 px-4 text-center text-white">35+ Languages</td>
                    <td className="py-3 px-4 text-center text-gray-300">Limited</td>
                    <td className="py-3 px-4 text-center text-gray-300">Single Language</td>
                  </tr>
                  <tr>
                    <td className="py-3 px-4 text-gray-300">Cost Efficiency</td>
                    <td className="py-3 px-4 text-center text-white">High</td>
                    <td className="py-3 px-4 text-center text-gray-300">Medium</td>
                    <td className="py-3 px-4 text-center text-gray-300">Low</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <h2 className="text-2xl font-semibold text-white mb-6">What Our Customers Say</h2>
          
          <div className="grid md:grid-cols-3 gap-6 mb-12">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-gray-800 p-6 rounded-xl border border-gray-700">
                <p className="text-gray-300 mb-4 italic">"{testimonial.quote}"</p>
                <div className="mt-4">
                  <p className="text-white font-medium">{testimonial.author}</p>
                  <p className="text-gray-400 text-sm">{testimonial.company}</p>
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center">
            <Link to="/auth?mode=signup" className="inline-block">
              <button className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                Start Your Free Trial
              </button>
            </Link>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

export default WhyChooseUsPage;