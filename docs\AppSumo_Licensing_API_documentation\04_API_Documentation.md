# AppSumo Licensing API (v2) - API Documentation

**Source URLs:**
- https://docs.licensing.appsumo.com/api/api__overview.html
- https://docs.licensing.appsumo.com/api/api__getting_started.html
- https://docs.licensing.appsumo.com/api/api__license.html
- https://docs.licensing.appsumo.com/api/api__profile.html

---

## Overview

The Partner API enables AppSumo Partners to programmatically access the data found in their Partner Dashboard. This access enables Partners to effectively scale their business by automating their backend, freeing up time to focus their product.

### What's an API?

API is the acronym for Application Programming Interface, which that allows two applications to talk to each other. This allows your application to access the AppSumo service and receive information related to your application.

### Do you need to use this?

In short, no you don't. The Partner Portal API is an option for our partner's to fetch data for auditing or to perform automation tasks. For example, you have a user that hasn't logged in a long time, and you want to double check that their license is still valid. Another example would be a monthly check to see that the status of all your licenses are in sync with AppSumo.

---

## Getting Started

### Prerequisites

- An approved application on AppSumo
- A valid API key

**Header `Content Type`'s allowed for POST requests**
- `application/json`

### API key

You can find your API on your product page in the [AppSumo Partner Portal](https://www.appsumo.com/partners/products/). The key is hidden by default, clicking the eye symbol will make it visible.

![Licensing OAuth keys](https://docs.licensing.appsumo.com/assets/img/private_keys.d80967b3.png)

Using your API key you can get license information about your application. There are two types of information you can query:

1. Licensing
2. Your Profile

In order to access this information you'll need to make a request. _(See [Making the request](https://docs.licensing.appsumo.com/api/api__getting_started.html#making-the-request))_.
**All requests are rate limited to 20 requests per minute**

### Making the request

Using your API, you will make either a request to our server using one of the endpoints below. The request must include a header key called `X-AppSumo-Licensing-Key` with the value being your API key.

Example request:
`GET /v2/licenses/:license_key`

```python
import requests

headers = {'X-AppSumo-Licensing-Key': '94b5fb6f-4b5f-453c-b8f5-83ae071e2d43'}
url = 'https://api.licensing.appsumo.com/v2/licenses/d8bfa201-d8c0-4bc8-a27c-b1c12efa4a5a'
response = requests.get(url, headers=headers)

# Process the response
```

Example response:
```json
{
  "license_key": "d8bfa201-d8c0-4bc8-a27c-b1c12efa4a5a",
  "license_redemption_url":"https://appsumo.com/licensing/a81169a1-eb05-4db8-8d65-2d1a8d5af719/redeem/",
  "license_change_plan_url":"https://appsumo.com/licensing/a81169a1-eb05-4db8-8d65-2d1a8d5af719/change_plan/",
  "status": "active",
  "tier": 1,
  "created_at": "2022-01-01 00:00:00+00",
  "updated_at": "2022-01-01 00:00:00+00"
}
```

### Quick start

Here is a list of all the endpoints that is available to all AppSumo Partners. Using your API key you can make requests to get information on your application's licenses or partner profile.

#### Base API URL
```text
https://api.licensing.appsumo.com/v2/
```

#### Licensing API
_For more details see [License API](https://docs.licensing.appsumo.com/api/api__license.html)_

```text
GET `/licenses`
GET `/licenses/events`
GET `/licenses/:license_key`
GET `/licenses/:license_key/events`
GET `/licenses/:license_key/webhook-responses`
```

#### Partner profile API
_For more details see [Partner profile API](https://docs.licensing.appsumo.com/api/api__profile.html)_

```text
GET    `/profile`
PUT    `/profile`
POST   `/profile/contact`
DELETE `/profile/contact:contact_id`
```

---

## Licensing API

For more information about licenses, events, or webhook-responses see [Key terms](https://docs.licensing.appsumo.com/api/api__license.html#key-terms))

### Endpoints

#### `GET` `/v2/licenses`

Gets all the licenses for you application

**Query parameters**
- `status` _(See [Status types](https://docs.licensing.appsumo.com/api/api__license.html#status-types))_.
- `page` _(See [Pagination](https://docs.licensing.appsumo.com/api/api__license.html#pagination))_.
- `limit` _(See [Pagination](https://docs.licensing.appsumo.com/api/api__license.html#pagination))_.

**Example request**
`GET https://api.licensing.appsumo.com/v2/licenses?limit=1&page=1&status=active`

```python
import requests

headers = {'X-AppSumo-Licensing-Key': '94b5fb6f-4b5f-453c-b8f5-83ae071e2d43'}
url = 'https://api.licensing.appsumo.com/v2/licenses?limit=1&page=1&status=active'
response = requests.get(url, headers=headers)

# Process the response
```

**Example response**
```json
{
  "size": 1,
  "items": [{
    "license_key": "1ed6fdd3-f772-4b91-b12d-efb772d23cc0",
    "license_redemption_url":"https://appsumo.com/licensing/a81169a1-eb05-4db8-8d65-2d1a8d5af719/redeem/",
    "license_change_plan_url":"https://appsumo.com/licensing/a81169a1-eb05-4db8-8d65-2d1a8d5af719/change_plan/",
    "status": "active",
    "tier": 1,
    "created_at": "2022-03-29T17:21:57.444033Z",
    "updated_at": "2022-03-29T17:23:14.791672Z"
  }]
}
```

#### `GET` `/v2/licenses/events`

Gets all the license events for you application. Webhook `responses` are limited to 10 items. If you need to see more responses, use the `license_key` in [webhook-responses](https://docs.licensing.appsumo.com/api/api__license.html#get-licenses-license-key-webhook-responses)

**Query parameters**
- `status` _(See [Status types](https://docs.licensing.appsumo.com/api/api__license.html#status-types))_.
- `page` _(See [Pagination](https://docs.licensing.appsumo.com/api/api__license.html#pagination))_.
- `limit` _(See [Pagination](https://docs.licensing.appsumo.com/api/api__license.html#pagination))_.

**Example request**
`GET https://appsumo.com/v2/licenses/events?limit=1&page=1&status=active`

```python
import requests

headers = {'X-AppSumo-Licensing-Key': '94b5fb6f-4b5f-453c-b8f5-83ae071e2d43'}
url = 'https://api.licensing.appsumo.com/v2/licenses/events?limit=1&page=1&status=active'
response = requests.get(url, headers=headers)

# Process the response
```

#### `GET` `/licenses/:license_key`

Gets the information pertaining to a specific a license

**URL parameters**
- `:license_key` The user's license key

**Example request**
`GET https://api.licensing.appsumo.com/v2/licenses/2191a2c1-01a9-4060-8067-1b466484f21b`

```python
import requests

headers = {'X-AppSumo-Licensing-Key': '94b5fb6f-4b5f-453c-b8f5-83ae071e2d43'}
url = 'https://api.licensing.appsumo.com/v2/licenses/2191a2c1-01a9-4060-8067-1b466484f21b'
response = requests.get(url, headers=headers)

# Process the response
```

**Example response**
```json
{
  "license_key": "2191a2c1-01a9-4060-8067-1b466484f21b",
  "license_redemption_url":"https://appsumo.com/licensing/a81169a1-eb05-4db8-8d65-2d1a8d5af719/redeem/",
  "license_change_plan_url":"https://appsumo.com/licensing/a81169a1-eb05-4db8-8d65-2d1a8d5af719/change_plan/",
  "status": "inactive",
  "tier": 1,
  "created_at": "2022-04-20T20:59:53.796937Z",
  "updated_at": "2022-04-20T20:59:53.796937Z"
}
```

#### `GET` `/licenses/:license_key/events`

Gets the license events pertaining to a specific a license. Webhook `responses` are limited to 10 items. If you need to see more responses, use the `license_key` in [webhook-responses](https://docs.licensing.appsumo.com/api/api__license.html#get-licenses-license-key-webhook-responses)

**URL parameters**
- `:license_key` The user's license key

**Query parameters**
- `page` _(See [Pagination](https://docs.licensing.appsumo.com/api/api__license.html#pagination))_.
- `limit` _(See [Pagination](https://docs.licensing.appsumo.com/api/api__license.html#pagination))_.

#### `GET` `/licenses/:license_key/webhook-responses`

Gets the webhook responses pertaining to a specific a license

**URL parameters**
- `:license_key` - The user's license key

**Query parameters**
- `page` _(See [Pagination](https://docs.licensing.appsumo.com/api/api__license.html#pagination))_.
- `limit` _(See [Pagination](https://docs.licensing.appsumo.com/api/api__license.html#pagination))_.

### Status types

- `inactive` - Inactive licenses
- `active` - Active licenses
- `deactivated` - Deactivated licenses

### Pagination

Pagination allows you to greater control on the amount of information received.

- `page` - The current page (a group of results)
- `limit` - The maximum number of results returned for that page

(_Note: `page` starts a 1 and the `limit` maximum number is 100_)

Example:
```json
https://www.appsumo.com/v2/licenses/571c5f3a-42e1-4b25-b209-a46f905462f6?page=2&limit=100
```

This (assuming they exist) will return the second 100 results (100 - 199)

### Key terms

#### License
This is the license that a user has. Using the license key attched to the license, you can request information about it.

#### Events
When an action is performed on a license, a record of the event is saved.

License events:
- `activate` - A license is activated
- `deactivate` - A licenses is deactivated
- `purchase` - A license is purchased

#### Webhook reponses
Anytime a license event is performed, a webhook request is made. This request is saved including the response from your application.

---

## Partner Profile API

For more information about contacts or profile urls see [Key terms](https://docs.licensing.appsumo.com/api/api__profile.html#key-terms))

### Endpoints

#### `GET` `/profile`

Gets your profile information

**Example request**
`GET https://api.licensing.appsumo.com/v2/profile`

```python
import requests

headers = {'X-AppSumo-Licensing-Key': '94b5fb6f-4b5f-453c-b8f5-83ae071e2d43'}
url = 'https://api.licensing.appsumo.com/v2/profile'
response = requests.get(url, headers=headers)

# Process the response
```

**Example response**
```json
{
  "profile_id": 1,
  "webhook_url": "https://your-url.com/appsumo-webhook",
  "redirect_url": "https://your-url.com/",
  "contacts": {
    "size": 1,
    "items": [{
      "contact_id": 1,
      "name": "Support",
      "email": "<EMAIL>",
      "created_at": "2022-03-29T17:09:06.430447Z",
      "updated_at": "2022-03-29T17:09:06.430447Z"
    }]
  },
  "created_at": "2022-03-29T17:09:05.478031Z",
  "updated_at": "2022-05-04T16:57:27.136789Z"
}
```

#### `POST` `/profile/contact`

Add a contact to your partner profile

**Field parameters**
- `email` - Your contact's email
- `name` - Your contact's name

**Example request**
`POST https://api.licensing.appsumo.com/v2/profile/contact`

```python
import requests

url = 'https://api.licensing.appsumo.com/v2/profile/contact/'
headers = {
  'Content-type': 'application/json',
  'X-AppSumo-Licensing-Key': '94b5fb6f-4b5f-453c-b8f5-83ae071e2d43'
}
data = {
  'name': 'Beckett Mariner'
  'email': '<EMAIL>'
}

response = requests.post(url, headers=headers, data=data)

# View the response
```

**Example response**
```json
{
  "contact_id": 19,
  "name": "Beckett Mariner",
  "email": "<EMAIL>",
  "created_at": "2022-05-06T16:32:30.241060816Z",
  "updated_at": "2022-05-06T16:32:30.241060816Z"
}
```

#### `DELETE` `/profile/contact/:contact_id`

Delete a contact from your contact list

**URL parameters**
- `:contact_id` - The contact's ID. This can be fetched from `GET` `/profile`

**Example request**
`POST https://api.licensing.appsumo.com/v2/profile/contact/19`

```python
import requests

url = 'https://api.licensing.appsumo.com/v2/profile/contact/19'
headers = { 'X-AppSumo-Licensing-Key': '94b5fb6f-4b5f-453c-b8f5-83ae071e2d43' }
response = requests.delete(url, headers=headers)

# View the response
```

**Example response**
```text
Contact deleted
```

### Key terms

#### Contacts
Adding a contact can be very important if AppSumo needs to contact you in the event that AppSumo cannot make requests to your application.

#### Profile URLS
The redirect and webhook URLs are the same as the ones that can be found on the [AppSumo Partner Portal](https://www.appsumo.com/partners/products/). Using the API can be an option to programmatically update them if needed.
