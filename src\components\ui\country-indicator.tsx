import { useState } from 'react';
import { Globe, ChevronDown } from 'lucide-react';
import { useCountry } from '@/lib/countryContext';
import { CountrySelectionPopup } from './country-selection-popup';

interface CountryIndicatorProps {
  variant?: 'header' | 'popup';
  showLabel?: boolean;
  className?: string;
}

export function CountryIndicator({
  variant = 'header',
  showLabel = true,
  className = ''
}: CountryIndicatorProps) {
  const { country, currency } = useCountry();
  const [showCountryPopup, setShowCountryPopup] = useState(false);

  // Get country code for flag (2-letter code)
  const getCountryCode = (countryName: string): string => {
    // Map country names to ISO 3166-1 alpha-2 codes
    const countryCodes: Record<string, string> = {
      // North America
      'United States': 'us',
      'Canada': 'ca',
      'Mexico': 'mx',

      // South America
      'Argentina': 'ar',
      'Bolivia': 'bo',
      'Brazil': 'br',
      'Chile': 'cl',
      'Colombia': 'co',
      'Ecuador': 'ec',
      'Guyana': 'gy',
      'Paraguay': 'py',
      'Peru': 'pe',
      'Suriname': 'sr',
      'Uruguay': 'uy',
      'Venezuela': 've',

      // Europe
      'Albania': 'al',
      'Andorra': 'ad',
      'Austria': 'at',
      'Belarus': 'by',
      'Belgium': 'be',
      'Bosnia and Herzegovina': 'ba',
      'Bulgaria': 'bg',
      'Croatia': 'hr',
      'Cyprus': 'cy',
      'Czech Republic': 'cz',
      'Denmark': 'dk',
      'Estonia': 'ee',
      'Finland': 'fi',
      'France': 'fr',
      'Germany': 'de',
      'Greece': 'gr',
      'Hungary': 'hu',
      'Iceland': 'is',
      'Ireland': 'ie',
      'Italy': 'it',
      'Latvia': 'lv',
      'Liechtenstein': 'li',
      'Lithuania': 'lt',
      'Luxembourg': 'lu',
      'Malta': 'mt',
      'Moldova': 'md',
      'Monaco': 'mc',
      'Montenegro': 'me',
      'Netherlands': 'nl',
      'North Macedonia': 'mk',
      'Norway': 'no',
      'Poland': 'pl',
      'Portugal': 'pt',
      'Romania': 'ro',
      'Russia': 'ru',
      'San Marino': 'sm',
      'Serbia': 'rs',
      'Slovakia': 'sk',
      'Slovenia': 'si',
      'Spain': 'es',
      'Sweden': 'se',
      'Switzerland': 'ch',
      'Ukraine': 'ua',
      'United Kingdom': 'gb',
      'Vatican City': 'va',

      // Asia
      'Afghanistan': 'af',
      'Armenia': 'am',
      'Azerbaijan': 'az',
      'Bahrain': 'bh',
      'Bangladesh': 'bd',
      'Bhutan': 'bt',
      'Brunei': 'bn',
      'Cambodia': 'kh',
      'China': 'cn',
      'Georgia': 'ge',
      'India': 'in',
      'Indonesia': 'id',
      'Iran': 'ir',
      'Iraq': 'iq',
      'Israel': 'il',
      'Japan': 'jp',
      'Jordan': 'jo',
      'Kazakhstan': 'kz',
      'Kuwait': 'kw',
      'Kyrgyzstan': 'kg',
      'Laos': 'la',
      'Lebanon': 'lb',
      'Malaysia': 'my',
      'Maldives': 'mv',
      'Mongolia': 'mn',
      'Myanmar': 'mm',
      'Nepal': 'np',
      'North Korea': 'kp',
      'Oman': 'om',
      'Pakistan': 'pk',
      'Palestine': 'ps',
      'Philippines': 'ph',
      'Qatar': 'qa',
      'Saudi Arabia': 'sa',
      'Singapore': 'sg',
      'South Korea': 'kr',
      'Sri Lanka': 'lk',
      'Syria': 'sy',
      'Taiwan': 'tw',
      'Tajikistan': 'tj',
      'Thailand': 'th',
      'Timor-Leste': 'tl',
      'Turkey': 'tr',
      'Turkmenistan': 'tm',
      'United Arab Emirates': 'ae',
      'Uzbekistan': 'uz',
      'Vietnam': 'vn',
      'Yemen': 'ye',

      // Africa
      'Algeria': 'dz',
      'Angola': 'ao',
      'Benin': 'bj',
      'Botswana': 'bw',
      'Burkina Faso': 'bf',
      'Burundi': 'bi',
      'Cabo Verde': 'cv',
      'Cameroon': 'cm',
      'Central African Republic': 'cf',
      'Chad': 'td',
      'Comoros': 'km',
      'Congo': 'cg',
      'Djibouti': 'dj',
      'Egypt': 'eg',
      'Equatorial Guinea': 'gq',
      'Eritrea': 'er',
      'Eswatini': 'sz',
      'Ethiopia': 'et',
      'Gabon': 'ga',
      'Gambia': 'gm',
      'Ghana': 'gh',
      'Guinea': 'gn',
      'Guinea-Bissau': 'gw',
      'Ivory Coast': 'ci',
      'Kenya': 'ke',
      'Lesotho': 'ls',
      'Liberia': 'lr',
      'Libya': 'ly',
      'Madagascar': 'mg',
      'Malawi': 'mw',
      'Mali': 'ml',
      'Mauritania': 'mr',
      'Mauritius': 'mu',
      'Morocco': 'ma',
      'Mozambique': 'mz',
      'Namibia': 'na',
      'Niger': 'ne',
      'Nigeria': 'ng',
      'Rwanda': 'rw',
      'Sao Tome and Principe': 'st',
      'Senegal': 'sn',
      'Seychelles': 'sc',
      'Sierra Leone': 'sl',
      'Somalia': 'so',
      'South Africa': 'za',
      'South Sudan': 'ss',
      'Sudan': 'sd',
      'Tanzania': 'tz',
      'Togo': 'tg',
      'Tunisia': 'tn',
      'Uganda': 'ug',
      'Zambia': 'zm',
      'Zimbabwe': 'zw',

      // Oceania
      'Australia': 'au',
      'Fiji': 'fj',
      'Kiribati': 'ki',
      'Marshall Islands': 'mh',
      'Micronesia': 'fm',
      'Nauru': 'nr',
      'New Zealand': 'nz',
      'Palau': 'pw',
      'Papua New Guinea': 'pg',
      'Samoa': 'ws',
      'Solomon Islands': 'sb',
      'Tonga': 'to',
      'Tuvalu': 'tv',
      'Vanuatu': 'vu',

      // Caribbean
      'Antigua and Barbuda': 'ag',
      'Bahamas': 'bs',
      'Barbados': 'bb',
      'Cuba': 'cu',
      'Dominica': 'dm',
      'Dominican Republic': 'do',
      'Grenada': 'gd',
      'Haiti': 'ht',
      'Jamaica': 'jm',
      'Saint Kitts and Nevis': 'kn',
      'Saint Lucia': 'lc',
      'Saint Vincent and the Grenadines': 'vc',
      'Trinidad and Tobago': 'tt'
    };

    // Return the code or a default
    return countryCodes[countryName]?.toLowerCase() || 'globe';
  };

  const countryCode = getCountryCode(country);

  // Determine if we should show the actual flag or a globe icon
  const showFlag = countryCode !== 'globe';

  // Base styles based on variant
  const baseStyles = variant === 'header'
    ? 'flex items-center space-x-1 text-gray-300 hover:text-white transition-colors cursor-pointer'
    : 'flex items-center space-x-1 text-gray-300 hover:text-white transition-colors cursor-pointer';

  return (
    <>
      <div
        className={`${baseStyles} ${className}`}
        onClick={() => setShowCountryPopup(true)}
        title={`Currency: ${currency.code}`}
      >
        {showFlag ? (
          <span className="inline-block w-5 h-5 rounded-full overflow-hidden">
            <img
              src={`https://flagcdn.com/w20/${countryCode}.png`}
              srcSet={`https://flagcdn.com/w40/${countryCode}.png 2x`}
              width="20"
              height="20"
              alt={country}
              className="w-5 h-5 object-cover rounded-full"
              onError={(e) => {
                // If the flag image fails to load, replace with globe icon
                e.currentTarget.style.display = 'none';
                const parent = e.currentTarget.parentElement;
                if (parent) {
                  const globe = document.createElement('span');
                  globe.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4"><circle cx="12" cy="12" r="10"></circle><line x1="2" y1="12" x2="22" y2="12"></line><path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path></svg>';
                  globe.className = 'text-gray-300';
                  parent.appendChild(globe);
                }
              }}
            />
          </span>
        ) : (
          <Globe className="h-4 w-4" />
        )}

        {showLabel && (
          <>
            <span className="text-sm font-medium">{country}</span>
            <ChevronDown className="h-3 w-3" />
          </>
        )}
      </div>

      <CountrySelectionPopup
        isOpen={showCountryPopup}
        onClose={() => setShowCountryPopup(false)}
      />
    </>
  );
}
