// Clear Paddle Environment Cache
// This script helps clear browser localStorage cache that might override environment settings

console.log('🧹 Paddle Environment Cache Cleaner');
console.log('====================================');
console.log('');
console.log('If your Paddle checkout is showing the wrong environment (sandbox vs production),');
console.log('it might be due to cached environment settings in browser localStorage.');
console.log('');
console.log('📋 To fix this issue:');
console.log('');
console.log('1️⃣ Open your browser Developer Tools (F12)');
console.log('2️⃣ Go to the Console tab');
console.log('3️⃣ Run this command:');
console.log('');
console.log('   localStorage.removeItem("paddle_environment")');
console.log('');
console.log('4️⃣ Refresh the page');
console.log('');
console.log('🔍 To check current environment settings:');
console.log('');
console.log('   console.log("localStorage:", localStorage.getItem("paddle_environment"))');
console.log('   console.log("Environment variable:", import.meta.env.VITE_PADDLE_ENVIRONMENT)');
console.log('');
console.log('✅ After clearing cache, the app will use the .env file setting:');

// Read current .env setting
const fs = require('fs');
const path = require('path');

try {
  const envPath = path.join(__dirname, '..', '.env');
  const envContent = fs.readFileSync(envPath, 'utf8');
  const match = envContent.match(/^VITE_PADDLE_ENVIRONMENT=(.*)$/m);
  const envVar = match ? match[1].trim() : 'not found';
  
  console.log(`   VITE_PADDLE_ENVIRONMENT = ${envVar}`);
  
  if (envVar === 'production') {
    console.log('   🚀 This will use PRODUCTION environment (live payments)');
  } else if (envVar === 'sandbox') {
    console.log('   🧪 This will use SANDBOX environment (test payments)');
  } else {
    console.log('   ⚠️  Environment not properly configured');
  }
} catch (error) {
  console.log('   ❌ Could not read .env file');
}

console.log('');
console.log('🎯 Alternative: Use the environment switcher:');
console.log('   node scripts/switch-environment.cjs production');
console.log('   node scripts/switch-environment.cjs sandbox');
