import { db } from './firebase';
import { collection, addDoc, query, where, getDocs, Timestamp, doc, getDoc, updateDoc, deleteDoc, setDoc } from 'firebase/firestore';
import { nanoid } from 'nanoid';
import { TeamMemberLimits, DEFAULT_TEAM_MEMBER_LIMITS } from './teamMemberLimits';

/**
 * Generate a unique invite token
 */
export const generateInviteToken = () => {
  return nanoid(10); // Generate a 10-character unique ID
};

/**
 * Create a team invite in Firestore
 */
export const createTeamInvite = async (
  ownerId: string,
  ownerEmail: string,
  expiresInDays = 7,
  limits?: TeamMemberLimits,
  companyName?: string
) => {
  try {
    console.log('Creating team invite');
    console.log('Owner ID:', ownerId);
    console.log('Owner Email:', ownerEmail);

    // Generate a unique token
    const token = generateInviteToken();
    console.log('Generated token:', token);

    // Use provided limits or default limits
    const memberLimits = limits || DEFAULT_TEAM_MEMBER_LIMITS;

    // For multiple registrations invites, we don't set an expiration date
    // For one-time registrations, we set the default expiration (7 days)
    const now = new Date();
    let expiresAt;

    if (memberLimits.isMultipleRegistrations) {
      // Set a very distant future date (100 years from now) for multiple registrations
      expiresAt = new Date(now.getTime() + (100 * 365 * 24 * 60 * 60 * 1000));
      console.log('Creating multiple registrations invite with no practical expiration');
    } else {
      // Calculate expiration date (7 days from now by default)
      expiresAt = new Date(now.getTime() + (expiresInDays * 24 * 60 * 60 * 1000));
      console.log('Creating one-time invite with expiration:', expiresAt);
    }

    // If company name is not provided, try to get it from the user's profile
    let ownerCompanyName = companyName;
    if (!ownerCompanyName) {
      try {
        const usersRef = collection(db, 'users');
        const q = query(usersRef, where('uid', '==', ownerId));
        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          const userData = querySnapshot.docs[0].data();
          ownerCompanyName = userData.companyName;
        }
      } catch (error) {
        console.error('Error fetching company name:', error);
      }
    }

    // Create the invite document
    const invitesRef = collection(db, 'teamInvites');
    await addDoc(invitesRef, {
      token,
      ownerId,
      ownerEmail,
      companyName: ownerCompanyName || '',
      createdAt: Timestamp.now(),
      expiresAt: Timestamp.fromDate(expiresAt),
      used: false,
      limits: memberLimits
    });

    return token;
  } catch (error) {
    console.error('Error creating team invite:', error);
    throw error;
  }
};

/**
 * Get all active invites for a team owner
 */
export const getTeamInvites = async (ownerId: string) => {
  try {
    console.log('Getting team invites');
    console.log('Owner ID:', ownerId);

    const invitesRef = collection(db, 'teamInvites');
    const q = query(invitesRef, where('ownerId', '==', ownerId));
    const querySnapshot = await getDocs(q);

    const invites: any[] = [];
    querySnapshot.forEach((doc) => {
      invites.push({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt.toDate(),
        expiresAt: doc.data().expiresAt.toDate()
      });
    });

    return invites;
  } catch (error) {
    console.error('Error fetching team invites:', error);
    throw error;
  }
};

/**
 * Validate an invite token
 */
export const validateInviteToken = async (token: string) => {
  console.log('validateInviteToken called with token:', token);
  try {
    // Always get fresh data from the database
    const invitesRef = collection(db, 'teamInvites');
    console.log('Created invitesRef for collection teamInvites');
    const q = query(invitesRef, where('token', '==', token));
    console.log('Created query with token:', token);

    // Force a fresh query to the database to get the latest data
    const querySnapshot = await getDocs(q);
    console.log('Query executed, empty?', querySnapshot.empty, 'size:', querySnapshot.size);

    if (querySnapshot.empty) {
      console.log('No invite found with this token');
      return { valid: false, message: 'Invalid invite link' };
    }

    const inviteDoc = querySnapshot.docs[0];
    // Get the latest data from the document
    const invite = inviteDoc.data();
    console.log('Found invite:', invite);

    // Check if invite has been used (only for one-time registration invites)
    const isOneTimeInvite = !invite.limits || !invite.limits.isMultipleRegistrations;
    const isUsed = invite.used === true;

    console.log('Validating invite usage status:', {
      inviteId: inviteDoc.id,
      token,
      isOneTimeInvite,
      isUsed,
      rawUsedValue: invite.used,
      usedBy: invite.usedBy,
      usedAt: invite.usedAt
    });

    if (isUsed && isOneTimeInvite) {
      console.log('One-time invite has already been used');
      return { valid: false, message: 'This invite link has already been used' };
    }

    // Get the expiration date
    const now = new Date();
    const expiresAt = invite.expiresAt.toDate();

    // For multiple registrations invites, we skip the expiration check
    // They're valid until manually deleted
    if (!invite.limits || !invite.limits.isMultipleRegistrations) {
      console.log('Checking expiration for one-time invite, now:', now, 'expiresAt:', expiresAt);

      if (now > expiresAt) {
        console.log('One-time invite has expired');
        return { valid: false, message: 'This invite link has expired' };
      }
    } else {
      console.log('Multiple registrations invite - skipping expiration check');

      // For multiple registrations invites, check the team member limit
      try {
        const ownerId = invite.ownerId;

        // Get the max team members limit for this owner
        const limitsDocRef = doc(db, 'settings', 'enterpriseAdminOwnerLimits');
        const limitsDoc = await getDoc(limitsDocRef);

        let maxTeamMembers = 2; // Default fallback - setting to 2 to match the UI
        if (limitsDoc.exists()) {
          const adminOwnerLimits = limitsDoc.data();
          maxTeamMembers = adminOwnerLimits.maxTeamMembers || 2;
        }

        // Count existing team members
        const teamMembersRef = collection(db, 'teamMembers');
        const teamMembersQuery = query(teamMembersRef, where('ownerId', '==', ownerId));
        const teamMembersSnapshot = await getDocs(teamMembersQuery);
        const teamMembersCount = teamMembersSnapshot.size;

        console.log(`Validating invite: Team members count: ${teamMembersCount}, Max team members: ${maxTeamMembers}`);

        // If the team member limit has been reached, return an error
        if (teamMembersCount >= maxTeamMembers) {
          console.log('Team member limit reached during validation');
          return {
            valid: false,
            message: `The team member limit (${maxTeamMembers}) has been reached. Please contact the team owner.`
          };
        }
      } catch (error) {
        console.error('Error checking team member limit during validation:', error);
        // We'll continue with validation but the limit will be checked again during registration
        // This is to handle the case where the user doesn't have permissions yet
      }
    }

    console.log('Invite is valid, returning invite details');
    return {
      valid: true,
      invite: {
        id: inviteDoc.id,
        ...invite,
        createdAt: invite.createdAt.toDate(),
        expiresAt: expiresAt
      }
    };
  } catch (error) {
    console.error('Error validating invite token:', error);
    return { valid: false, message: 'An error occurred while validating the invite' };
  }
};

/**
 * Mark an invite as used
 */
export const markInviteAsUsed = async (inviteId: string, email: string) => {
  try {
    console.log('Marking invite as used');
    console.log('Invite ID:', inviteId);

    // First, check if this is a multiple registrations invite
    const inviteRef = doc(db, 'teamInvites', inviteId);
    const inviteDoc = await getDoc(inviteRef);

    if (!inviteDoc.exists()) {
      console.error('Invite not found');
      return { success: false, message: 'Invite not found' };
    }

    const invite = inviteDoc.data();

    // If this is a multiple registrations invite, don't mark it as used
    if (invite.limits && invite.limits.isMultipleRegistrations) {
      console.log('This is a multiple registrations invite, not marking as used');

      // Check if the team member limit has been reached for multiple registrations invites
      try {
        const ownerId = invite.ownerId;

        // First, check if this email is already registered as a team member for this owner
        // If so, we'll allow the registration (it's just an update of an existing team member)
        const existingTeamMembersRef = collection(db, 'teamMembers');
        const existingTeamMemberQuery = query(existingTeamMembersRef,
          where('ownerId', '==', ownerId),
          where('email', '==', email)
        );
        const existingTeamMemberSnapshot = await getDocs(existingTeamMemberQuery);

        if (existingTeamMemberSnapshot.size > 0) {
          console.log('User is already a team member, allowing registration');
          // This is an existing team member, so we'll allow the registration
        } else {
          // Get the max team members limit for this owner
          const limitsDocRef = doc(db, 'settings', 'enterpriseAdminOwnerLimits');
          const limitsDoc = await getDoc(limitsDocRef);

          let maxTeamMembers = 2; // Default fallback - setting to 2 to match the UI
          if (limitsDoc.exists()) {
            const adminOwnerLimits = limitsDoc.data();
            maxTeamMembers = adminOwnerLimits.maxTeamMembers || 2;
          }

          // Count existing team members
          const teamMembersRef = collection(db, 'teamMembers');
          const teamMembersQuery = query(teamMembersRef, where('ownerId', '==', ownerId));
          const teamMembersSnapshot = await getDocs(teamMembersQuery);
          const teamMembersCount = teamMembersSnapshot.size;

          console.log(`Marking invite: Team members count: ${teamMembersCount}, Max team members: ${maxTeamMembers}`);

          // If the team member limit has been reached, return an error
          if (teamMembersCount >= maxTeamMembers) {
            console.log('Team member limit reached during markInviteAsUsed');
            return {
              success: false,
              message: `The team member limit (${maxTeamMembers}) has been reached. Please contact the team owner.`,
              isMultipleRegistrations: true
            };
          }
        }
      } catch (error) {
        console.error('Error checking team member limit during markInviteAsUsed:', error);
        // Continue with marking the invite as used, but the limit will be checked again during registration
      }

      // Instead, add this email to the usedBy array
      const usedBy = invite.usedBy || [];
      const usedAt = invite.usedAt || [];

      // Update the invite with the new user
      await updateDoc(inviteRef, {
        usedBy: [...usedBy, email],
        usedAt: [...usedAt, Timestamp.now()]
      });

      return {
        success: true,
        isMultipleRegistrations: true,
        message: 'Email added to multiple registrations invite'
      };
    }

    // For one-time registration invites, mark as used as before
    console.log(`Marking one-time invite ${inviteId} as used by ${email}`);

    // Create update data with explicit boolean value
    const updateData = {
      used: true,  // Explicitly use boolean true
      usedBy: email,
      usedAt: Timestamp.now()
    };

    console.log('Update data for one-time invite:', updateData);

    try {
      // Update the document
      await updateDoc(inviteRef, updateData);

      // Verify the update was successful by fetching the document again
      const updatedDoc = await getDoc(inviteRef);
      const updatedData = updatedDoc.data();

      console.log('Invite after update:', {
        id: inviteId,
        used: updatedData?.used,
        usedBy: updatedData?.usedBy,
        usedAt: updatedData?.usedAt ? updatedData.usedAt.toDate() : null
      });

      if (updatedData?.used !== true) {
        console.error('WARNING: Invite was not properly marked as used!');
      }
    } catch (updateError) {
      console.error('Error updating invite:', updateError);
      throw updateError; // Re-throw to be caught by the outer try/catch
    }

    return {
      success: true,
      isMultipleRegistrations: false,
      message: 'One-time invite marked as used'
    };
  } catch (error) {
    console.error('Error marking invite as used:', error);
    return {
      success: false,
      message: 'Error marking invite as used'
    };
  }
};

/**
 * Delete an invite
 */
export const deleteInvite = async (inviteId: string) => {
  try {
    console.log('Deleting invite');
    console.log('Invite ID:', inviteId);

    const inviteRef = doc(db, 'teamInvites', inviteId);
    await deleteDoc(inviteRef);
    return true;
  } catch (error) {
    console.error('Error deleting invite:', error);
    return false;
  }
};

/**
 * Register a team member using an invite
 */
export const registerTeamMember = async (
  inviteId: string,
  name: string,
  email: string,
  phone: string,
  password: string,
  uid: string
) => {
  try {
    console.log('Registering team member');
    console.log('Invite ID:', inviteId);
    console.log('Name:', name);
    console.log('Email:', email);
    console.log('Phone:', phone);
    console.log('UID:', uid);

    // Get the invite details
    const inviteRef = doc(db, 'teamInvites', inviteId);
    const inviteDoc = await getDoc(inviteRef);

    if (!inviteDoc.exists()) {
      return { success: false, message: 'Invalid invite' };
    }

    const invite = inviteDoc.data();

    // Check if the team member limit has been reached
    try {
      const ownerId = invite.ownerId;

      // First, check if this email is already registered as a team member for this owner
      // If so, we'll allow the registration (it's just an update of an existing team member)
      const existingTeamMembersRef = collection(db, 'teamMembers');
      const existingTeamMemberQuery = query(existingTeamMembersRef,
        where('ownerId', '==', ownerId),
        where('email', '==', email)
      );
      const existingTeamMemberSnapshot = await getDocs(existingTeamMemberQuery);

      if (existingTeamMemberSnapshot.size > 0) {
        console.log('User is already a team member, allowing registration');
        // This is an existing team member, so we'll allow the registration
        return;
      }

      // Get the max team members limit for this owner
      const limitsDocRef = doc(db, 'settings', 'enterpriseAdminOwnerLimits');
      const limitsDoc = await getDoc(limitsDocRef);

      let maxTeamMembers = 2; // Default fallback - setting to 2 to match the UI
      if (limitsDoc.exists()) {
        const adminOwnerLimits = limitsDoc.data();
        maxTeamMembers = adminOwnerLimits.maxTeamMembers || 2;
      }

      // Count existing team members
      const teamMembersRef = collection(db, 'teamMembers');
      const teamMembersQuery = query(teamMembersRef, where('ownerId', '==', ownerId));
      const teamMembersSnapshot = await getDocs(teamMembersQuery);
      const teamMembersCount = teamMembersSnapshot.size;

      console.log(`Team members count: ${teamMembersCount}, Max team members: ${maxTeamMembers}`);

      // If the team member limit has been reached, return an error
      if (teamMembersCount >= maxTeamMembers) {
        return {
          success: false,
          message: `The team member limit (${maxTeamMembers}) has been reached. Please contact the team owner.`
        };
      }
    } catch (error) {
      console.error('Error checking team member limit:', error);

      // IMPORTANT: Instead of continuing, we'll block the registration if we can't verify the limit
      // This ensures we don't exceed the limit due to permission issues
      return {
        success: false,
        message: `Unable to verify team member limit. Please try again or contact support.`
      };
    }

    // Create the team member record
    try {
      const teamMembersRef = collection(db, 'teamMembers');
      await addDoc(teamMembersRef, {
        ownerId: invite.ownerId,
        email: email,
        name: name,
        phone: phone,
        status: 'active',
        addedAt: Timestamp.now(),
        packageName: 'Enterprise'
      });
    } catch (error) {
      console.error('Error creating team member record:', error);
      return { success: false, message: 'Failed to create team member record. Please try again.' };
    }

    // Create or update user record
    try {
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('email', '==', email));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        // Create a new user record
        console.log('Creating new user record with limits:', invite.limits || DEFAULT_TEAM_MEMBER_LIMITS);
        try {
          await addDoc(usersRef, {
            uid: uid,
            email: email,
            displayName: name,
            phone: phone,
            packageName: 'Enterprise',
            isTeamMember: true,
            teamOwnerId: invite.ownerId,
            teamCompanyName: invite.companyName || '',
            createdAt: new Date().toISOString(),
            role: 'Enterprise User',
            limits: invite.limits || DEFAULT_TEAM_MEMBER_LIMITS
          });
        } catch (userCreateError) {
          console.error('Error creating user record:', userCreateError);
          return { success: false, message: 'Failed to create user record. Please try again.' };
        }

        // Create initial usage record for the user
        try {
          await setDoc(doc(db, 'userUsage', uid), {
            userId: uid,
            dailyGenerationCount: 0,
            lastGenerationDate: Timestamp.now(),
            customPromptsCount: 0,
            savedPromptsCount: 0,
            uploadedImagesCount: 0
          });
        } catch (usageError) {
          console.error('Error creating user usage record:', usageError);
          // Continue with registration even if usage record creation fails
          // The usage record will be created automatically when needed
        }
      } else {
        // Update existing user record
        const userDoc = querySnapshot.docs[0];
        const userData = userDoc.data();

        // If this is an existing user (from the auth/email-already-in-use error handler)
        // we want to preserve their existing UID
        const uidToUse = uid.startsWith('existing-user-') ? userData.uid || uid : uid;

        console.log('Updating existing user record with limits:', invite.limits || DEFAULT_TEAM_MEMBER_LIMITS);
        try {
          await updateDoc(doc(db, 'users', userDoc.id), {
            uid: uidToUse,
            displayName: name,
            phone: phone,
            packageName: 'Enterprise',
            isTeamMember: true,
            teamOwnerId: invite.ownerId,
            teamCompanyName: invite.companyName || '',
            role: 'Enterprise User',
            limits: invite.limits || DEFAULT_TEAM_MEMBER_LIMITS
          });
        } catch (userUpdateError) {
          console.error('Error updating user record:', userUpdateError);
          return { success: false, message: 'Failed to update user record. Please try again.' };
        }

        // Check if user has a usage record, create one if not
        try {
          const usageRef = doc(db, 'userUsage', uidToUse);
          const usageDoc = await getDoc(usageRef);

          if (!usageDoc.exists()) {
            await setDoc(usageRef, {
              userId: uidToUse,
              dailyGenerationCount: 0,
              lastGenerationDate: Timestamp.now(),
              customPromptsCount: 0,
              savedPromptsCount: 0,
              uploadedImagesCount: 0
            });
          }
        } catch (usageError) {
          console.error('Error checking/creating user usage record:', usageError);
          // Continue with registration even if usage record creation fails
        }
      }
    } catch (queryError) {
      console.error('Error querying user records:', queryError);
      return { success: false, message: 'Failed to check existing user records. Please try again.' };
    }

    // Mark the invite as used
    let markResult;
    try {
      markResult = await markInviteAsUsed(inviteId, email);

      if (!markResult.success) {
        console.warn('Failed to mark invite as used:', markResult.message);
      } else {
        console.log('Successfully marked invite:', markResult.message);
      }
    } catch (markError) {
      console.error('Error marking invite as used:', markError);
      markResult = { success: false, message: 'Failed to mark invite as used', isMultipleRegistrations: false };
      // Continue with registration even if marking the invite fails
    }

    return {
      success: true,
      inviteMarked: markResult.success,
      isOneTimeInvite: !markResult.isMultipleRegistrations
    };
  } catch (error) {
    console.error('Error registering team member:', error);
    return { success: false, message: 'An error occurred during registration' };
  }
};
