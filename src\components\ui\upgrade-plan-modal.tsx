import { useState, useEffect } from 'react';
import { X, Check, ArrowRight, CreditCard, Package, Building, Info } from 'lucide-react';
import { Button } from './button';
import { toast } from 'react-hot-toast';
import { BillingDetailsStep } from '@/components/billing-details-step';
import { useCountry } from '@/lib/countryContext';
import { CountryIndicator } from './country-indicator';
import { db } from '@/lib/firebase';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { useStore } from '@/lib/store';
import { initSSLCommerzPayment } from '@/lib/sslcommerz';
import { getPaddlePriceId, initializePaddleCheckout, setupPaddle, getCurrentEnvironment } from '@/lib/paddle';

interface UpgradePlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentPlan?: string;
}

export function UpgradePlanModal({ isOpen, onClose, currentPlan = 'Free' }: UpgradePlanModalProps) {
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');
  // Default to Pro plan (marked as "MOST POPULAR") for better user experience
  const [selectedPlan, setSelectedPlan] = useState<string | null>('Pro');
  const [showBillingDetails, setShowBillingDetails] = useState(false);
  const { formatCurrency, currency } = useCountry();
  const user = useStore(state => state.user);
  // Prevent body scrolling when modal is open
  useEffect(() => {
    if (isOpen || showBillingDetails) {
      // Save the current overflow value
      const originalOverflow = document.body.style.overflow;
      // Lock scrolling
      document.body.style.overflow = 'hidden';

      // Restore scrolling when component unmounts or modal closes
      return () => {
        document.body.style.overflow = originalOverflow;
      };
    }
  }, [isOpen, showBillingDetails]);

  // Setup Paddle when component mounts with correct environment
  useEffect(() => {
    // Clear any cached environment from localStorage to ensure we use .env setting
    if (typeof window !== 'undefined') {
      const storedEnv = localStorage.getItem('paddle_environment');
      const envVar = import.meta.env.VITE_PADDLE_ENVIRONMENT?.toLowerCase();

      console.log('🔍 Environment check:');
      console.log('- localStorage paddle_environment:', storedEnv);
      console.log('- VITE_PADDLE_ENVIRONMENT:', envVar);

      // If there's a mismatch, clear localStorage to use .env setting
      if (storedEnv && storedEnv !== envVar) {
        console.log('🧹 Clearing localStorage environment to use .env setting');
        localStorage.removeItem('paddle_environment');
      }
    }

    const currentEnvironment = getCurrentEnvironment();
    console.log('🚀 Setting up Paddle for environment:', currentEnvironment);

    try {
      setupPaddle(currentEnvironment);
      console.log('✅ Paddle setup successful');
    } catch (error) {
      console.error('❌ Paddle setup failed:', error);
    }
  }, []);

  if (!isOpen && !showBillingDetails) return null;

  const handlePlanSelect = (plan: string) => {
    setSelectedPlan(plan);
  };

  const handleUpgrade = () => {
    if (!selectedPlan) {
      toast.error('Please select a plan to continue');
      return;
    }

    // Instead of directly opening checkout, show the billing details step
    setShowBillingDetails(true);
  };

  const handleProceedToCheckout = async (discountAmount?: number, couponCode?: string, paymentGateway?: string) => {
    if (!selectedPlan) {
      toast.error('Please select a plan to continue');
      return;
    }

    // Now proceed to checkout after billing details are confirmed
    const planName = selectedPlan;
    const period = billingPeriod === 'yearly' ? 'yearly' : 'monthly';

    // Check if SSLCOMMERZ is selected as the payment gateway
    if (paymentGateway === 'SSLCOMMERZ') {
      // Show loading toast
      const loadingToast = toast.loading('Initializing payment...');

      try {
        console.log('User state:', user); // Debug log

        // Validate user is authenticated
        if (!user) {
          console.error('User not authenticated');
          toast.error('User not authenticated. Please log in and try again.');
          toast.dismiss(loadingToast);
          return;
        }

        if (!user.id || !user.email) {
          console.error('Invalid user data:', user);
          toast.error('Invalid user data. Please log in again and try again.');
          toast.dismiss(loadingToast);
          return;
        }

        // Calculate the amount based on the plan and period
        // NOTE: Do NOT apply discount here - the Firebase function will handle discount calculation
        let amount = 0;
        if (planName === 'Pay-As-You-Go') {
          amount = 2;
        } else if (planName === 'Pro') {
          amount = period === 'monthly' ? 10 : 96;
        } else if (planName === 'Enterprise') {
          amount = period === 'monthly' ? 100 : 960;
        }

        // The discount will be applied by the Firebase Cloud Function
        // based on the couponCode and discountAmount parameters

        console.log('Fetching billing details for user:', user.id); // Debug log

        // Get the billing details from the BillingDetailsStep component
        const billingDetailsRef = collection(db, 'billingDetails');
        const q = query(billingDetailsRef, where('userId', '==', user.id));
        const querySnapshot = await getDocs(q);

        if (querySnapshot.empty) {
          console.error('No billing details found for user:', user.id);
          toast.error('Billing details not found. Please complete your billing details first.');
          toast.dismiss(loadingToast);
          return;
        }        const billingDetails = querySnapshot.docs[0].data() as any;
        console.log('Billing details found:', billingDetails); // Debug log

        // Initialize SSLCOMMERZ payment with proper error handling
        try {
          const redirectUrl = await initSSLCommerzPayment({
            userId: user.id,
            userEmail: user.email,
            userName: billingDetails.fullName,
            amount,
            plan: planName,
            billingPeriod: period === 'yearly' ? 'yearly' : 'monthly',
            billingDetails: billingDetails,
            couponCode,
            discountAmount
          });

          // Dismiss loading toast
          toast.dismiss(loadingToast);

          // Show success toast
          toast.success(`Redirecting to SSLCOMMERZ payment gateway...`);

          // Redirect to SSLCOMMERZ payment page
          window.location.href = redirectUrl;

          // Close both modals
          setShowBillingDetails(false);
          onClose();
        } catch (paymentError: any) {
          console.error('SSLCOMMERZ payment initialization error:', paymentError);
          toast.dismiss(loadingToast);

          if (paymentError.message) {
            toast.error(`Payment error: ${paymentError.message}`);
          } else {
            toast.error('Failed to initialize payment. Please try again.');
          }
        }
      } catch (error: any) {
        console.error('Error in payment process:', error);

        // Dismiss loading toast
        toast.dismiss(loadingToast);

        // Show specific error message if available
        if (error.message) {
          toast.error(`Error: ${error.message}`);
        } else {
          toast.error('Failed to process payment. Please try again.');
        }
      }    } else {
      // Use Paddle checkout for other payment gateways
      try {
        if (!selectedPlan) {
          toast.error('Please select a plan to continue');
          return;
        }

        // Get the appropriate price ID based on plan and billing period
        const priceId = getPaddlePriceId(selectedPlan, billingPeriod);

        // Calculate the amount based on the selected plan and billing period
        let baseAmount = 0;
        if (selectedPlan === 'Pay-As-You-Go') {
          baseAmount = 2; // $2 for Pay-As-You-Go
        } else if (selectedPlan === 'Pro') {
          baseAmount = billingPeriod === 'yearly' ? 96 : 10; // $96/year or $10/month
        } else if (selectedPlan === 'Enterprise') {
          baseAmount = billingPeriod === 'yearly' ? 960 : 100; // $960/year or $100/month
        }

        // Apply discount if applicable
        const finalAmount = discountAmount ? baseAmount - discountAmount : baseAmount;

        // Calculate plan details for payment processing
        const planDetails = {
          plan: selectedPlan,
          billingPeriod: billingPeriod,
          amount: finalAmount, // Use the calculated amount (with discount if applicable)
          currency: 'USD' // Default currency for Paddle
        };

        // Get current environment to ensure we use the correct one
        const currentEnvironment = getCurrentEnvironment();
        console.log('🌍 Using Paddle environment:', currentEnvironment);

        // Initialize Paddle checkout
        initializePaddleCheckout(
          priceId,
          user?.email,
          {
            userId: user?.id, // Include user ID for webhook processing
            plan: selectedPlan,
            billingPeriod: billingPeriod,
            discountAmount: discountAmount,
            couponCode: couponCode
          },
          couponCode, // Pass coupon code as Paddle discount code
          currentEnvironment, // Use current environment explicitly
          planDetails // Pass plan details for payment tracking
        );

        toast.success(`Opening Paddle checkout for ${selectedPlan} ${billingPeriod} plan`);

        // Close both modals
        setShowBillingDetails(false);
        onClose();

      } catch (error: any) {
        console.error('Error opening Paddle checkout:', error);
        toast.error(`Failed to open checkout: ${error.message || 'Please try again'}`);
      }
    }
  };

  const handleBackToPlanSelection = () => {
    setShowBillingDetails(false);
  };

  const handleCloseBillingDetails = () => {
    setShowBillingDetails(false);
    onClose();
  };

  // Calculate prices based on billing period
  const prices = {
    payg: { price: 2, unit: 'per 15 generations' },
    pro: { monthly: 10, yearly: 96 },
    enterprise: { monthly: 100, yearly: 960 }
  };

  return (
    <>
      {showBillingDetails ? (
        <BillingDetailsStep
          isOpen={showBillingDetails}
          onClose={handleCloseBillingDetails}
          selectedPlan={selectedPlan || ''}
          billingPeriod={billingPeriod}
          onProceedToCheckout={handleProceedToCheckout}
          onBack={handleBackToPlanSelection}
        />
      ) : (
        <div
          className="fixed inset-0 z-50 flex items-start md:items-center lg:items-center justify-center bg-black/80 backdrop-blur-sm overflow-hidden"
          onClick={onClose}
          onTouchMove={(e) => e.stopPropagation()}
        >
          <div
            className="relative w-full max-w-4xl p-4 md:p-5 lg:p-5 bg-gray-800 rounded-xl shadow-2xl max-h-[90vh] md:max-h-[85vh] lg:h-auto overflow-y-auto md:overflow-y-auto lg:overflow-visible custom-scrollbar my-4 md:my-0"
            onClick={(e) => e.stopPropagation()}
            onTouchMove={(e) => e.stopPropagation()}
          >
            {/* Close button */}
            <button
              onClick={onClose}
              className="fixed md:absolute top-2 right-2 md:top-4 md:right-4 p-2 text-gray-400 hover:text-white rounded-full hover:bg-gray-700 transition-colors bg-gray-800 z-10"
            >
              <X size={20} />
            </button>

        {/* Header */}
        <div className="text-center mb-4 md:mb-5 lg:mb-4 pt-4 md:pt-2 lg:pt-0">
          <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent">
            Upgrade Your Plan
          </h2>
          <p className="mt-1 lg:mt-2 text-gray-300 text-sm md:text-base px-2">
            Choose the perfect plan to boost your e-commerce product descriptions
          </p>
          <div className="flex items-center justify-center mt-1 lg:mt-2 text-xs md:text-sm">
            <span className="text-gray-400 mr-1">Pricing for</span>
            <CountryIndicator variant="popup" className="text-gray-300" />
          </div>
        </div>

        {/* Billing toggle */}
        <div className="flex justify-center mb-4 md:mb-5 lg:mb-4">
          <div className="flex items-center p-1 bg-gray-700 rounded-lg">
            <button
              className={`px-3 md:px-4 py-2 rounded-md transition-colors text-sm md:text-base ${billingPeriod === 'monthly' ? 'bg-purple-600 text-white' : 'text-gray-300 hover:text-white'}`}
              onClick={() => setBillingPeriod('monthly')}
            >
              Monthly
            </button>
            <button
              className={`px-3 md:px-4 py-2 rounded-md transition-colors text-sm md:text-base ${billingPeriod === 'yearly' ? 'bg-purple-600 text-white' : 'text-gray-300 hover:text-white'}`}
              onClick={() => setBillingPeriod('yearly')}
            >
              Yearly <span className="text-xs text-green-300">Save 20%</span>
            </button>
          </div>
        </div>

        {/* Plans */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-4 lg:gap-3 px-1 md:px-0">
          {/* Pay-As-You-Go Plan */}
          <div
            className={`relative bg-gray-700 rounded-xl overflow-hidden border-2 transition-all flex flex-col ${selectedPlan === 'Pay-As-You-Go' ? 'border-purple-500 md:scale-105 shadow-lg shadow-purple-500/20' : 'border-gray-600 hover:border-gray-500'}`}
            onClick={() => handlePlanSelect('Pay-As-You-Go')}
          >
            <div className="p-4 md:p-5 lg:p-4">
              <div className="flex justify-center mb-2 md:mb-3 lg:mb-2">
                <div className="p-2 md:p-3 lg:p-2 bg-purple-500/20 rounded-full">
                  <CreditCard className="h-6 w-6 md:h-7 md:w-7 lg:h-6 lg:w-6 text-purple-400" />
                </div>
              </div>
              <h3 className="text-lg md:text-xl lg:text-lg font-bold text-center text-white mb-1 lg:mb-1">Pay-As-You-Go</h3>
              <div className="text-center mb-2 md:mb-3 lg:mb-2">
                <span className="text-2xl md:text-3xl lg:text-2xl font-bold text-white">{formatCurrency(prices.payg.price)}</span>
                <span className="text-gray-300 ml-1 text-sm md:text-base lg:text-sm">{prices.payg.unit}</span>
                {currency.code !== 'USD' && (
                  <div className="text-xs text-gray-400 mt-1 flex items-center justify-center">
                    <Info className="h-3 w-3 mr-1" />
                    <span>${prices.payg.price} USD</span>
                  </div>
                )}
              </div>
              <ul className="space-y-1 md:space-y-2 lg:space-y-1 text-sm md:text-base lg:text-sm mb-3 md:mb-4 lg:mb-2">
                <li className="flex items-start">
                  <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">20 stored images</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">5 custom prompts</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">24h image deletion delay</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Credits never expire</span>
                </li>
              </ul>
            </div>
            <div className="p-3 md:p-4 lg:p-3 bg-gray-800 border-t border-gray-600 mt-auto">
              <Button
                className={`w-full py-1.5 md:py-2 lg:py-1.5 text-sm md:text-base lg:text-sm font-medium ${selectedPlan === 'Pay-As-You-Go' ? 'bg-purple-600 hover:bg-purple-700' : 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'}`}
                onClick={(e) => {
                  e.stopPropagation();
                  handlePlanSelect('Pay-As-You-Go');
                  handleUpgrade();
                }}
              >
                Get Started
                <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4" />
              </Button>
            </div>
            {selectedPlan === 'Pay-As-You-Go' && (
              <div className="absolute top-0 right-0 p-1 md:p-1.5 lg:p-1">
                <div className="bg-purple-600 text-white text-xs font-bold px-2 py-0.5 lg:py-0.5 rounded-full">
                  Selected
                </div>
              </div>
            )}
          </div>

          {/* Pro Plan */}
          <div
            className={`relative bg-gray-700 rounded-xl overflow-hidden border-2 transition-all flex flex-col ${selectedPlan === 'Pro' ? 'border-purple-500 md:scale-105 shadow-lg shadow-purple-500/20' : 'border-gray-600 hover:border-gray-500'}`}
            onClick={() => handlePlanSelect('Pro')}
          >
            <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-center text-xs font-bold py-1">
              MOST POPULAR
            </div>
            <div className="p-4 md:p-5 lg:p-4 pt-6 md:pt-8 lg:pt-6">
              <div className="flex justify-center mb-2 md:mb-3 lg:mb-2">
                <div className="p-2 md:p-3 lg:p-2 bg-purple-500/20 rounded-full">
                  <Package className="h-6 w-6 md:h-7 md:w-7 lg:h-6 lg:w-6 text-purple-400" />
                </div>
              </div>
              <h3 className="text-lg md:text-xl lg:text-lg font-bold text-center text-white mb-1 lg:mb-1">Pro</h3>
              <div className="text-center mb-2 md:mb-3 lg:mb-2">
                <span className="text-2xl md:text-3xl lg:text-2xl font-bold text-white">
                  {formatCurrency(billingPeriod === 'monthly' ? prices.pro.monthly : prices.pro.yearly)}
                </span>
                <span className="text-gray-300 ml-1 text-sm md:text-base lg:text-sm">/{billingPeriod === 'monthly' ? 'month' : 'year'}</span>
                {currency.code !== 'USD' && (
                  <div className="text-xs text-gray-400 mt-1 flex items-center justify-center">
                    <Info className="h-3 w-3 mr-1" />
                    <span>${billingPeriod === 'monthly' ? prices.pro.monthly : prices.pro.yearly} USD</span>
                  </div>
                )}
              </div>
              <ul className="space-y-1 md:space-y-2 lg:space-y-1 text-sm md:text-base lg:text-sm mb-3 md:mb-4 lg:mb-2">
                <li className="flex items-start">
                  <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">50 stored images</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">150 generations per month</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">10 custom prompts</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Instant image deletion</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Priority support</span>
                </li>
              </ul>
            </div>
            <div className="p-3 md:p-4 lg:p-3 bg-gray-800 border-t border-gray-600 mt-auto">
              <Button
                className={`w-full py-1.5 md:py-2 lg:py-1.5 text-sm md:text-base lg:text-sm font-medium ${selectedPlan === 'Pro' ? 'bg-purple-600 hover:bg-purple-700' : 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'}`}
                onClick={(e) => {
                  e.stopPropagation();
                  handlePlanSelect('Pro');
                  handleUpgrade();
                }}
              >
                Upgrade to Pro
                <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4" />
              </Button>
            </div>
            {selectedPlan === 'Pro' && (
              <div className="absolute top-0 right-0 p-1 md:p-1.5 lg:p-1">
                <div className="bg-purple-600 text-white text-xs font-bold px-2 py-0.5 lg:py-0.5 rounded-full">
                  Selected
                </div>
              </div>
            )}
          </div>

          {/* Enterprise Plan */}
          <div
            className={`relative bg-gray-700 rounded-xl overflow-hidden border-2 transition-all flex flex-col ${selectedPlan === 'Enterprise' ? 'border-purple-500 md:scale-105 shadow-lg shadow-purple-500/20' : 'border-gray-600 hover:border-gray-500'}`}
            onClick={() => handlePlanSelect('Enterprise')}
          >
            <div className="p-4 md:p-5 lg:p-4">
              <div className="flex justify-center mb-2 md:mb-3 lg:mb-2">
                <div className="p-2 md:p-3 lg:p-2 bg-purple-500/20 rounded-full">
                  <Building className="h-6 w-6 md:h-7 md:w-7 lg:h-6 lg:w-6 text-purple-400" />
                </div>
              </div>
              <h3 className="text-lg md:text-xl lg:text-lg font-bold text-center text-white mb-1 lg:mb-1">Enterprise</h3>
              <div className="text-center mb-2 md:mb-3 lg:mb-2">
                <span className="text-2xl md:text-3xl lg:text-2xl font-bold text-white">
                  {formatCurrency(billingPeriod === 'monthly' ? prices.enterprise.monthly : prices.enterprise.yearly)}
                </span>
                <span className="text-gray-300 ml-1 text-sm md:text-base lg:text-sm">/{billingPeriod === 'monthly' ? 'month' : 'year'}</span>
                {currency.code !== 'USD' && (
                  <div className="text-xs text-gray-400 mt-1 flex items-center justify-center">
                    <Info className="h-3 w-3 mr-1" />
                    <span>${billingPeriod === 'monthly' ? prices.enterprise.monthly : prices.enterprise.yearly} USD</span>
                  </div>
                )}
              </div>
              <ul className="space-y-1 md:space-y-2 lg:space-y-1 text-sm md:text-base lg:text-sm mb-3 md:mb-4 lg:mb-2">
                <li className="flex items-start">
                  <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">10 team members</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">50 stored images per member</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">200 generations per member</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Team management dashboard</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Dedicated account manager</span>
                </li>
              </ul>
            </div>
            <div className="p-3 md:p-4 lg:p-3 bg-gray-800 border-t border-gray-600 mt-auto">
              <Button
                className={`w-full py-1.5 md:py-2 lg:py-1.5 text-sm md:text-base lg:text-sm font-medium ${selectedPlan === 'Enterprise' ? 'bg-purple-600 hover:bg-purple-700' : 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'}`}
                onClick={(e) => {
                  e.stopPropagation();
                  handlePlanSelect('Enterprise');
                  handleUpgrade();
                }}
              >
                Upgrade to Enterprise
                <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4" />
              </Button>
            </div>
            {selectedPlan === 'Enterprise' && (
              <div className="absolute top-0 right-0 p-1 md:p-1.5 lg:p-1">
                <div className="bg-purple-600 text-white text-xs font-bold px-2 py-0.5 lg:py-0.5 rounded-full">
                  Selected
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="mt-4 md:mt-5 lg:mt-3 text-center text-gray-400 text-xs md:text-sm px-2 md:px-0">
          <p>All plans include our core features: AI-powered image analysis, multiple language support, and secure data storage.</p>
          <p className="mt-1 lg:mt-1">Need help choosing? <a href="/footer/contact" className="text-purple-400 hover:text-purple-300">Contact our sales team</a></p>
        </div>
      </div>
    </div>
      )}
    </>
  );
}