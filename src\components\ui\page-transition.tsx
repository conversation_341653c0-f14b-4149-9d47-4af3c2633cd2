import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useLocation } from 'react-router-dom';
import '../../styles/page-transitions.css';

interface PageTransitionProps {
  children: React.ReactNode;
}

export function PageTransition({ children }: PageTransitionProps) {
  const { pathname } = useLocation();
  const [isVisible, setIsVisible] = useState(false);

  // Reset visibility on route change
  useEffect(() => {
    window.scrollTo(0, 0); // Scroll to top on page change
    setIsVisible(false);

    // Trigger animation after a short delay
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);

    return () => clearTimeout(timer);
  }, [pathname]);

  return (
    <motion.div
      className="page-reveal overflow-y-visible"
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.8,
        ease: [0.16, 1, 0.3, 1],
        when: "beforeChildren"
      }}
    >
      <div className={`content-wrapper ${isVisible ? 'visible' : ''} overflow-y-visible`}>
        {children}
      </div>
    </motion.div>
  );
}