import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useStore } from '@/lib/store';
import { PaymentHistory } from '@/components/payment-history';

export function PaymentHistoryPage() {
  const navigate = useNavigate();
  const { user } = useStore();

  if (!user) {
    navigate('/login');
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button
            onClick={() => navigate('/app/profile')}
            className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors mr-4"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Back to Profile</span>
          </button>
          <div>
            <h1 className="text-3xl font-bold text-white">Payment History</h1>
            <p className="text-gray-400 mt-1">Complete transaction history for your account</p>
          </div>
        </div>

        {/* Payment History Component with showAll=true */}
        <PaymentHistory userId={user.id} showAll={true} />
      </div>
    </div>
  );
}
