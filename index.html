<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="interkassa-verification" content="b20fa4255ec9022e5d9f11fadca96467" />

    <!-- Performance optimizations -->
    <meta name="theme-color" content="#111827" />
    <meta name="description" content="Transform your product images into captivating stories that sell! Our revolutionary AI analyzes every detail to instantly create magnetic descriptions." />

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://cdn.paddle.com" />
    <link rel="dns-prefetch" href="https://firebase.googleapis.com" />
    <link rel="dns-prefetch" href="https://firestore.googleapis.com" />

    <!-- Critical CSS inline -->
    <style>
      /* Critical above-the-fold styles */
      *,*::before,*::after{box-sizing:border-box}html{line-height:1.15;-webkit-text-size-adjust:100%}body{margin:0;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;background-color:#111827;color:#ffffff}.min-h-screen{min-height:100vh}.relative{position:relative}.fixed{position:fixed}.z-50{z-index:50}.container{width:100%;margin-left:auto;margin-right:auto;padding-left:1rem;padding-right:1rem}.flex{display:flex}.items-center{align-items:center}.justify-center{justify-content:center}.text-center{text-align:center}.text-white{color:#ffffff}.gradient-text{background:linear-gradient(to right,#a855f7,#ec4899,#ef4444);-webkit-background-clip:text;background-clip:text;-webkit-text-fill-color:transparent}.pt-32{padding-top:8rem}.px-4{padding-left:1rem;padding-right:1rem}.mb-6{margin-bottom:1.5rem}.mx-auto{margin-left:auto;margin-right:auto}
    </style>

    <title>eComEasyAI - AI-Powered Product Descriptions</title>

    <!-- Paddle Checkout JS - Load asynchronously -->
    <script async src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>

    <!-- Canvas element for background animation -->
    <canvas id="canvas" style="position:fixed;top:0;left:0;width:100%;height:100%;pointer-events:none;z-index:0;"></canvas>
  </head>
  <body>
    <!-- Loading screen -->
    <div id="loading-screen" style="position:fixed;inset:0;background:#111827;display:flex;align-items:center;justify-content:center;z-index:9999;">
      <div style="text-align:center;">
        <div style="width:40px;height:40px;border:3px solid #374151;border-top:3px solid #a855f7;border-radius:50%;animation:spin 1s linear infinite;margin:0 auto 16px;"></div>
        <div style="color:#9ca3af;">Loading eComEasyAI...</div>
      </div>
    </div>

    <div id="root"></div>

    <!-- Remove loading screen when app loads -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            loadingScreen.style.transition = 'opacity 0.3s ease-out';
            setTimeout(() => loadingScreen.remove(), 300);
          }
        }, 100);
      });
    </script>

    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>

    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
