import React, { createContext, useContext, useEffect, useState } from 'react';
import { onAuthStateChanged, User as FirebaseUser } from 'firebase/auth';
import { auth } from './firebase';
import { useStore } from './store';

interface AuthContextType {
  currentUser: FirebaseUser | null;
  loading: boolean;
  isEmailVerified: boolean;
}

const AuthContext = createContext<AuthContextType>({
  currentUser: null,
  loading: true,
  isEmailVerified: false,
});

export const useAuth = () => {
  return useContext(AuthContext);
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<FirebaseUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const { setUser } = useStore();

  useEffect(() => {
    console.log('Setting up Firebase auth state listener...');
    
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      console.log('Firebase auth state changed:', user ? `User: ${user.email}, Verified: ${user.emailVerified}` : 'No user');

      setCurrentUser(user);
      setIsEmailVerified(user?.emailVerified || false);

      if (user) {
        // Update Zustand store with user data
        setUser({
          id: user.uid,
          email: user.email || '',
        });
        console.log('User state restored from Firebase Auth');
      } else {
        // Clear Zustand store
        setUser(null);
        console.log('User state cleared');
      }

      setLoading(false);
    });

    // Cleanup subscription on unmount
    return () => {
      console.log('Cleaning up Firebase auth state listener');
      unsubscribe();
    };
  }, [setUser]);

  const value = {
    currentUser,
    loading,
    isEmailVerified,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
