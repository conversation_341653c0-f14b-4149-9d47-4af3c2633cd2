// Create Paddle products step by step
const https = require('https');
require('dotenv').config();

const PADDLE_API_KEY = process.env.VITE_PADDLE_API_KEY;

function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.paddle.com',
      port: 443,
      path: path,
      method: method,
      headers: {
        'Authorization': `Bearer ${PADDLE_API_KEY}`,
        'Content-Type': 'application/json',
      },
    };

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(response);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${JSON.stringify(response)}`));
          }
        } catch (error) {
          reject(new Error(`Failed to parse response: ${body}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function createProducts() {
  console.log('🚀 Creating Paddle products...');

  try {
    // Create Pay-As-You-Go Product
    console.log('🔄 Creating Pay-As-You-Go product...');    const payAsYouGoProduct = await makeRequest('POST', '/products', {
      name: 'Pay-As-You-Go',
      description: 'Flexible credits for AI-powered product descriptions. 15 generations per purchase, perfect for occasional use. Credits never expire.',
      tax_category: 'standard'
    });
    console.log(`✅ Pay-As-You-Go Product ID: ${payAsYouGoProduct.data.id}`);

    // Create Pro Product
    console.log('🔄 Creating Pro product...');    const proProduct = await makeRequest('POST', '/products', {
      name: 'Pro',
      description: 'Perfect for regular users. 50 stored images, 150 generations per month, 10 custom prompts, instant image deletion, and priority support.',
      tax_category: 'standard'
    });
    console.log(`✅ Pro Product ID: ${proProduct.data.id}`);

    // Create Enterprise Product
    console.log('🔄 Creating Enterprise product...');    const enterpriseProduct = await makeRequest('POST', '/products', {
      name: 'Enterprise',
      description: 'For teams and businesses. 10 team members, 50 stored images per member, 200 generations per member, team management dashboard, and dedicated account manager.',
      tax_category: 'standard'
    });
    console.log(`✅ Enterprise Product ID: ${enterpriseProduct.data.id}`);

    console.log('\n📋 Product IDs Summary:');
    console.log(`Pay-As-You-Go: ${payAsYouGoProduct.data.id}`);
    console.log(`Pro: ${proProduct.data.id}`);
    console.log(`Enterprise: ${enterpriseProduct.data.id}`);

    return {
      payAsYouGo: payAsYouGoProduct.data,
      pro: proProduct.data,
      enterprise: enterpriseProduct.data
    };

  } catch (error) {
    console.error('❌ Error creating products:', error.message);
    throw error;
  }
}

// Run the script
createProducts()
  .then((products) => {
    console.log('\n✅ All products created successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
