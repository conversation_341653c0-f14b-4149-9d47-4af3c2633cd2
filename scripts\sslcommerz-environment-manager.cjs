// SSLCOMMERZ Environment Manager
// This script helps manage SSLCOMMERZ environment settings and credentials

const fs = require('fs');
const path = require('path');

// SSLCOMMERZ credentials for different environments
const SSLCOMMERZ_CREDENTIALS = {
  production: {
    storeId: 'httpsecomeasyai0live',
    storePassword: '6808DA135CE7539998',
    isLive: true,
    description: 'Production credentials for live payments'
  },
  sandbox: {
    storeId: 'testbox',
    storePassword: 'qwerty',
    isLive: false,
    description: 'Sandbox credentials for testing'
  }
};

// File paths
const FRONTEND_ENV_PATH = path.join(__dirname, '..', '.env');
const BACKEND_ENV_PATH = path.join(__dirname, '..', 'functions', '.env');

// Read environment file
function readEnvFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    return '';
  }
}

// Write environment file
function writeEnvFile(filePath, content) {
  fs.writeFileSync(filePath, content);
}

// Update environment variables in a file
function updateEnvVariables(content, updates) {
  let updatedContent = content;
  
  Object.entries(updates).forEach(([key, value]) => {
    const regex = new RegExp(`^${key}=.*$`, 'm');
    const newLine = `${key}=${typeof value === 'string' ? value : JSON.stringify(value)}`;
    
    if (regex.test(updatedContent)) {
      updatedContent = updatedContent.replace(regex, newLine);
    } else {
      // Add the variable if it doesn't exist
      updatedContent += `\n${newLine}`;
    }
  });
  
  return updatedContent;
}

// Switch environment
function switchEnvironment(environment) {
  if (!SSLCOMMERZ_CREDENTIALS[environment]) {
    console.error(`❌ Unknown environment: ${environment}`);
    console.log('Available environments: production, sandbox');
    return false;
  }
  
  const credentials = SSLCOMMERZ_CREDENTIALS[environment];
  
  console.log(`🔄 Switching to ${environment.toUpperCase()} environment...`);
  console.log(`📝 ${credentials.description}`);
  
  try {
    // Update frontend .env
    console.log('\n1️⃣ Updating frontend configuration (.env)...');
    const frontendContent = readEnvFile(FRONTEND_ENV_PATH);
    const updatedFrontendContent = updateEnvVariables(frontendContent, {
      'VITE_SSLCOMMERZ_STORE_ID': `"${credentials.storeId}"`,
      'VITE_SSLCOMMERZ_STORE_PASSWORD': `"${credentials.storePassword}"`,
      'VITE_SSLCOMMERZ_IS_LIVE': credentials.isLive.toString()
    });
    writeEnvFile(FRONTEND_ENV_PATH, updatedFrontendContent);
    console.log('✅ Frontend configuration updated');
    
    // Update backend functions/.env
    console.log('\n2️⃣ Updating backend configuration (functions/.env)...');
    const backendContent = readEnvFile(BACKEND_ENV_PATH);
    const updatedBackendContent = updateEnvVariables(backendContent, {
      'SSLCOMMERZ_STORE_ID': `"${credentials.storeId}"`,
      'SSLCOMMERZ_STORE_PASSWORD': `"${credentials.storePassword}"`,
      'SSLCOMMERZ_IS_LIVE': credentials.isLive.toString()
    });
    writeEnvFile(BACKEND_ENV_PATH, updatedBackendContent);
    console.log('✅ Backend configuration updated');
    
    console.log(`\n✅ Successfully switched to ${environment.toUpperCase()} environment`);
    
    if (environment === 'production') {
      console.log('\n🚀 You are now in PRODUCTION mode');
      console.log('⚠️  Real payments will be processed!');
      console.log('💳 Real money will be charged to customers');
    } else {
      console.log('\n🧪 You are now in SANDBOX mode');
      console.log('💡 Test payments only - no real charges');
      console.log('🟢 Safe for testing and development');
    }
    
    console.log('\n📋 Next steps:');
    console.log('1. Restart your development server (npm run dev)');
    console.log('2. Redeploy Firebase Functions if in production');
    console.log('3. Test the payment flow');
    console.log('4. Verify the environment with: node scripts/sslcommerz-production-setup.cjs check');
    
    return true;
  } catch (error) {
    console.error('❌ Failed to switch environment:', error.message);
    return false;
  }
}

// Get current environment status
function getCurrentStatus() {
  console.log('🌍 Current SSLCOMMERZ Environment Status');
  console.log('========================================\n');
  
  // Read current configurations
  const frontendContent = readEnvFile(FRONTEND_ENV_PATH);
  const backendContent = readEnvFile(BACKEND_ENV_PATH);
  
  // Parse frontend config
  const frontendStoreId = frontendContent.match(/VITE_SSLCOMMERZ_STORE_ID="?([^"\n]+)"?/)?.[1];
  const frontendIsLive = frontendContent.match(/VITE_SSLCOMMERZ_IS_LIVE=([^\n]+)/)?.[1] === 'true';
  
  // Parse backend config
  const backendStoreId = backendContent.match(/SSLCOMMERZ_STORE_ID="?([^"\n]+)"?/)?.[1];
  const backendIsLive = backendContent.match(/SSLCOMMERZ_IS_LIVE=([^\n]+)/)?.[1] === 'true';
  
  console.log('📱 Frontend Configuration:');
  console.log(`   Store ID: ${frontendStoreId || 'Not set'}`);
  console.log(`   Environment: ${frontendIsLive ? 'PRODUCTION' : 'SANDBOX'}`);
  
  console.log('\n🔧 Backend Configuration:');
  console.log(`   Store ID: ${backendStoreId || 'Not set'}`);
  console.log(`   Environment: ${backendIsLive ? 'PRODUCTION' : 'SANDBOX'}`);
  
  // Check consistency
  console.log('\n🔍 Consistency Check:');
  const storeIdMatch = frontendStoreId === backendStoreId;
  const environmentMatch = frontendIsLive === backendIsLive;
  
  console.log(`   Store ID Match: ${storeIdMatch ? '✅' : '❌'}`);
  console.log(`   Environment Match: ${environmentMatch ? '✅' : '❌'}`);
  
  if (storeIdMatch && environmentMatch) {
    console.log('\n✅ Configuration is consistent');
    
    if (frontendIsLive) {
      console.log('🚀 Currently in PRODUCTION mode');
      console.log('💳 Real payments will be processed');
    } else {
      console.log('🧪 Currently in SANDBOX mode');
      console.log('💡 Test payments only');
    }
  } else {
    console.log('\n⚠️  Configuration inconsistency detected');
    console.log('   Run environment switch to fix this issue');
  }
  
  // Determine current environment
  let currentEnvironment = 'unknown';
  if (frontendStoreId === SSLCOMMERZ_CREDENTIALS.production.storeId) {
    currentEnvironment = 'production';
  } else if (frontendStoreId === SSLCOMMERZ_CREDENTIALS.sandbox.storeId) {
    currentEnvironment = 'sandbox';
  }
  
  console.log(`\n🎯 Detected Environment: ${currentEnvironment.toUpperCase()}`);
  
  return {
    frontend: { storeId: frontendStoreId, isLive: frontendIsLive },
    backend: { storeId: backendStoreId, isLive: backendIsLive },
    consistent: storeIdMatch && environmentMatch,
    environment: currentEnvironment
  };
}

// Fix configuration issues
function fixConfiguration() {
  console.log('🔧 Fixing SSLCOMMERZ Configuration Issues');
  console.log('========================================\n');
  
  const status = getCurrentStatus();
  
  if (status.consistent) {
    console.log('✅ Configuration is already consistent');
    return true;
  }
  
  console.log('🔄 Detecting and fixing configuration issues...');
  
  // Determine which environment to use based on frontend setting
  let targetEnvironment = 'sandbox'; // default
  
  if (status.frontend.storeId === SSLCOMMERZ_CREDENTIALS.production.storeId) {
    targetEnvironment = 'production';
  } else if (status.frontend.storeId === SSLCOMMERZ_CREDENTIALS.sandbox.storeId) {
    targetEnvironment = 'sandbox';
  } else if (status.frontend.isLive) {
    targetEnvironment = 'production';
  }
  
  console.log(`🎯 Target environment: ${targetEnvironment.toUpperCase()}`);
  
  return switchEnvironment(targetEnvironment);
}

// Main function
function main() {
  const [,, command, environment] = process.argv;
  
  switch (command) {
    case 'status':
    case 'current':
      getCurrentStatus();
      break;
      
    case 'production':
    case 'prod':
    case 'live':
      switchEnvironment('production');
      break;
      
    case 'sandbox':
    case 'test':
    case 'dev':
      switchEnvironment('sandbox');
      break;
      
    case 'fix':
      fixConfiguration();
      break;
      
    default:
      console.log('🔄 SSLCOMMERZ Environment Manager');
      console.log('\nUsage: node scripts/sslcommerz-environment-manager.cjs [command]');
      console.log('\nCommands:');
      console.log('  production        - Switch to production environment (live payments)');
      console.log('  sandbox           - Switch to sandbox environment (test payments)');
      console.log('  status            - Show current environment status');
      console.log('  fix               - Fix configuration inconsistencies');
      console.log('\nAliases:');
      console.log('  prod, live        - Same as production');
      console.log('  test, dev         - Same as sandbox');
      console.log('  current           - Same as status');
      console.log('\nExamples:');
      console.log('  node scripts/sslcommerz-environment-manager.cjs production');
      console.log('  node scripts/sslcommerz-environment-manager.cjs sandbox');
      console.log('  node scripts/sslcommerz-environment-manager.cjs status');
      console.log('  node scripts/sslcommerz-environment-manager.cjs fix');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { switchEnvironment, getCurrentStatus, fixConfiguration };
