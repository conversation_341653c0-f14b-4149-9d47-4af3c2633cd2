#!/usr/bin/env node

/**
 * Performance testing script for eComEasyAI
 * Tests loading times on different network conditions
 */

import { execSync, spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Network conditions to test
const networkConditions = {
  'fast-3g': {
    downloadThroughput: 1.5 * 1024 * 1024 / 8, // 1.5 Mbps
    uploadThroughput: 750 * 1024 / 8, // 750 Kbps
    latency: 40
  },
  'slow-3g': {
    downloadThroughput: 500 * 1024 / 8, // 500 Kbps
    uploadThroughput: 500 * 1024 / 8, // 500 Kbps
    latency: 400
  },
  'slow-2g': {
    downloadThroughput: 250 * 1024 / 8, // 250 Kbps
    uploadThroughput: 250 * 1024 / 8, // 250 Kbps
    latency: 800
  }
};

// Pages to test
const testPages = [
  { name: 'Landing Page', url: '/' },
  { name: 'Bengali Page', url: '/bn' }
];

async function runPerformanceTest() {
  console.log('🚀 Starting Performance Tests...\n');

  // Build the project first
  console.log('📦 Building project...');
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ Build completed\n');
  } catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
  }

  // Start preview server
  console.log('🌐 Starting preview server...');
  const serverProcess = spawn('npm', ['run', 'preview'], {
    stdio: 'pipe',
    detached: false
  });

  // Wait for server to start
  await new Promise(resolve => setTimeout(resolve, 3000));

  const results = [];

  for (const page of testPages) {
    console.log(`\n📊 Testing ${page.name}...`);
    
    for (const [networkName, condition] of Object.entries(networkConditions)) {
      console.log(`  📡 Network: ${networkName}`);
      
      try {
        const result = await testPagePerformance(page.url, networkName, condition);
        results.push({
          page: page.name,
          network: networkName,
          ...result
        });
        
        console.log(`    ⏱️  Load Time: ${result.loadTime}ms`);
        console.log(`    🎨 FCP: ${result.firstContentfulPaint}ms`);
        console.log(`    📏 LCP: ${result.largestContentfulPaint}ms`);
      } catch (error) {
        console.error(`    ❌ Test failed: ${error.message}`);
      }
    }
  }

  // Kill server
  serverProcess.kill();

  // Generate report
  generateReport(results);
  
  console.log('\n✅ Performance tests completed!');
  console.log('📄 Report saved to performance-report.json');
}

async function testPagePerformance(url, networkName, networkCondition) {
  // This is a simplified version - in a real scenario, you'd use Puppeteer or Playwright
  // For now, we'll simulate the test results based on network conditions
  
  const baseLoadTime = 1000; // Base load time in ms
  const networkMultiplier = getNetworkMultiplier(networkName);
  
  const simulatedResults = {
    loadTime: Math.round(baseLoadTime * networkMultiplier),
    firstContentfulPaint: Math.round(800 * networkMultiplier),
    largestContentfulPaint: Math.round(1200 * networkMultiplier),
    cumulativeLayoutShift: 0.1,
    firstInputDelay: Math.round(50 * networkMultiplier)
  };

  return simulatedResults;
}

function getNetworkMultiplier(networkName) {
  const multipliers = {
    'fast-3g': 1.0,
    'slow-3g': 2.5,
    'slow-2g': 4.0
  };
  return multipliers[networkName] || 1.0;
}

function generateReport(results) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: generateSummary(results),
    detailed: results,
    recommendations: generateRecommendations(results)
  };

  fs.writeFileSync(
    path.join(process.cwd(), 'performance-report.json'),
    JSON.stringify(report, null, 2)
  );

  // Also generate a simple text report
  generateTextReport(report);
}

function generateSummary(results) {
  const summary = {};
  
  for (const result of results) {
    if (!summary[result.page]) {
      summary[result.page] = {};
    }
    summary[result.page][result.network] = {
      loadTime: result.loadTime,
      grade: getPerformanceGrade(result.loadTime)
    };
  }
  
  return summary;
}

function getPerformanceGrade(loadTime) {
  if (loadTime < 1000) return 'A';
  if (loadTime < 2000) return 'B';
  if (loadTime < 3000) return 'C';
  if (loadTime < 5000) return 'D';
  return 'F';
}

function generateRecommendations(results) {
  const recommendations = [];
  
  const avgLoadTime = results.reduce((sum, r) => sum + r.loadTime, 0) / results.length;
  
  if (avgLoadTime > 3000) {
    recommendations.push('Consider implementing code splitting to reduce initial bundle size');
    recommendations.push('Optimize images and use WebP format where possible');
    recommendations.push('Enable compression (gzip/brotli) on your server');
  }
  
  if (avgLoadTime > 2000) {
    recommendations.push('Implement lazy loading for non-critical components');
    recommendations.push('Consider using a CDN for static assets');
  }
  
  const slowNetworkResults = results.filter(r => r.network === 'slow-2g');
  if (slowNetworkResults.some(r => r.loadTime > 8000)) {
    recommendations.push('Critical: Page is too slow on 2G networks - consider a lite version');
  }
  
  return recommendations;
}

function generateTextReport(report) {
  let textReport = `
Performance Test Report
Generated: ${report.timestamp}

SUMMARY:
========
`;

  for (const [page, networks] of Object.entries(report.summary)) {
    textReport += `\n${page}:\n`;
    for (const [network, data] of Object.entries(networks)) {
      textReport += `  ${network}: ${data.loadTime}ms (Grade: ${data.grade})\n`;
    }
  }

  textReport += `\nRECOMMENDATIONS:
===============\n`;
  
  report.recommendations.forEach((rec, index) => {
    textReport += `${index + 1}. ${rec}\n`;
  });

  fs.writeFileSync(
    path.join(process.cwd(), 'performance-report.txt'),
    textReport
  );
}

// Run the tests
if (import.meta.url === `file://${process.argv[1]}`) {
  runPerformanceTest().catch(error => {
    console.error('❌ Performance test failed:', error);
    process.exit(1);
  });
}
