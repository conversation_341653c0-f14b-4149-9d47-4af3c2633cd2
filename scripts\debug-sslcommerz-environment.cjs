// Debug SSLCOMMERZ Environment Detection
// This script helps debug why SSLCOMMERZ is still using sandbox

const fs = require('fs');
const path = require('path');

console.log('🔍 SSLCOMMERZ Environment Debug Tool');
console.log('====================================\n');

// Read environment files
const frontendEnvPath = path.join(__dirname, '..', '.env');
const backendEnvPath = path.join(__dirname, '..', 'functions', '.env');

console.log('📁 Reading environment files...');
console.log(`Frontend: ${frontendEnvPath}`);
console.log(`Backend: ${backendEnvPath}\n`);

// Parse frontend .env
const frontendEnv = fs.readFileSync(frontendEnvPath, 'utf8');
const frontendStoreId = frontendEnv.match(/VITE_SSLCOMMERZ_STORE_ID="?([^"\n]+)"?/)?.[1];
const frontendStorePassword = frontendEnv.match(/VITE_SSLCOMMERZ_STORE_PASSWORD="?([^"\n]+)"?/)?.[1];
const frontendIsLive = frontendEnv.match(/VITE_SSLCOMMERZ_IS_LIVE=([^\n]+)/)?.[1] === 'true';

// Parse backend .env
const backendEnv = fs.readFileSync(backendEnvPath, 'utf8');
const backendStoreId = backendEnv.match(/SSLCOMMERZ_STORE_ID="?([^"\n]+)"?/)?.[1];
const backendStorePassword = backendEnv.match(/SSLCOMMERZ_STORE_PASSWORD="?([^"\n]+)"?/)?.[1];
const backendIsLive = backendEnv.match(/SSLCOMMERZ_IS_LIVE=([^\n]+)/)?.[1] === 'true';

console.log('🎯 Environment Variable Analysis:');
console.log('================================');

console.log('\n📱 Frontend Configuration (.env):');
console.log(`   VITE_SSLCOMMERZ_STORE_ID: "${frontendStoreId}"`);
console.log(`   VITE_SSLCOMMERZ_STORE_PASSWORD: "${frontendStorePassword ? '*'.repeat(frontendStorePassword.length) : 'NOT SET'}"`);
console.log(`   VITE_SSLCOMMERZ_IS_LIVE: ${frontendIsLive}`);

console.log('\n🔧 Backend Configuration (functions/.env):');
console.log(`   SSLCOMMERZ_STORE_ID: "${backendStoreId}"`);
console.log(`   SSLCOMMERZ_STORE_PASSWORD: "${backendStorePassword ? '*'.repeat(backendStorePassword.length) : 'NOT SET'}"`);
console.log(`   SSLCOMMERZ_IS_LIVE: ${backendIsLive}`);

console.log('\n🔍 Environment Detection:');
console.log('========================');

// Determine expected environment
let expectedEnvironment = 'UNKNOWN';
if (frontendStoreId === 'httpsecomeasyai0live' && frontendIsLive) {
  expectedEnvironment = 'PRODUCTION';
} else if (frontendStoreId === 'testbox' && !frontendIsLive) {
  expectedEnvironment = 'SANDBOX';
} else if (frontendIsLive) {
  expectedEnvironment = 'PRODUCTION (by flag)';
} else {
  expectedEnvironment = 'SANDBOX (by flag)';
}

console.log(`Expected Environment: ${expectedEnvironment}`);

// Check consistency
const isConsistent = (
  frontendStoreId === backendStoreId &&
  frontendIsLive === backendIsLive
);

console.log(`Configuration Consistent: ${isConsistent ? '✅ YES' : '❌ NO'}`);

if (!isConsistent) {
  console.log('\n⚠️  INCONSISTENCY DETECTED:');
  if (frontendStoreId !== backendStoreId) {
    console.log(`   Store ID mismatch: Frontend="${frontendStoreId}" vs Backend="${backendStoreId}"`);
  }
  if (frontendIsLive !== backendIsLive) {
    console.log(`   Environment flag mismatch: Frontend=${frontendIsLive} vs Backend=${backendIsLive}`);
  }
}

console.log('\n🚨 Potential Issues:');
console.log('===================');

const issues = [];

// Check for sandbox credentials
if (frontendStoreId === 'testbox' || backendStoreId === 'testbox') {
  issues.push('Using sandbox credentials (testbox)');
}

// Check for production credentials with sandbox flag
if ((frontendStoreId === 'httpsecomeasyai0live' || backendStoreId === 'httpsecomeasyai0live') && (!frontendIsLive || !backendIsLive)) {
  issues.push('Using production credentials but environment flag is false');
}

// Check for missing credentials
if (!frontendStoreId || !backendStoreId) {
  issues.push('Missing store ID configuration');
}

if (!frontendStorePassword || !backendStorePassword) {
  issues.push('Missing store password configuration');
}

if (issues.length === 0) {
  console.log('✅ No obvious configuration issues detected');
} else {
  issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue}`);
  });
}

console.log('\n🔧 Troubleshooting Steps:');
console.log('========================');

if (expectedEnvironment.includes('PRODUCTION')) {
  console.log('Expected: PRODUCTION mode');
  console.log('1. Verify Firebase Functions are deployed with latest config');
  console.log('2. Check browser console for environment logs');
  console.log('3. Clear browser cache and localStorage');
  console.log('4. Test payment initialization');
  
  if (frontendStoreId !== 'httpsecomeasyai0live') {
    console.log('5. ⚠️  Store ID should be "httpsecomeasyai0live" for production');
  }
  
  if (!frontendIsLive || !backendIsLive) {
    console.log('5. ⚠️  SSLCOMMERZ_IS_LIVE should be true for production');
  }
} else {
  console.log('Expected: SANDBOX mode');
  console.log('1. This is test mode - payments will go to sandbox');
  console.log('2. To switch to production, run:');
  console.log('   node scripts/sslcommerz-environment-manager.cjs production');
}

console.log('\n📋 Next Steps:');
console.log('==============');

if (expectedEnvironment.includes('PRODUCTION')) {
  console.log('1. Test a payment to see if it goes to production');
  console.log('2. Check the payment URL - should be securepay.sslcommerz.com');
  console.log('3. If still going to sandbox, check browser console logs');
  console.log('4. Verify Firebase Functions logs show isLive: true');
} else {
  console.log('1. Switch to production mode:');
  console.log('   node scripts/sslcommerz-environment-manager.cjs production');
  console.log('2. Redeploy Firebase Functions:');
  console.log('   cd functions && firebase deploy --only functions');
  console.log('3. Restart development server:');
  console.log('   npm run dev');
}

console.log('\n🔗 Useful Commands:');
console.log('==================');
console.log('Check current status: node scripts/sslcommerz-environment-manager.cjs status');
console.log('Switch to production: node scripts/sslcommerz-environment-manager.cjs production');
console.log('Run readiness check: node scripts/sslcommerz-production-setup.cjs check');
console.log('Deploy functions: cd functions && firebase deploy --only functions');

// Final assessment
console.log('\n🎯 FINAL ASSESSMENT:');
console.log('===================');

if (expectedEnvironment.includes('PRODUCTION') && isConsistent) {
  console.log('✅ Configuration appears correct for PRODUCTION');
  console.log('   If payments still go to sandbox, the issue is likely:');
  console.log('   - Firebase Functions not deployed with latest config');
  console.log('   - Browser cache/localStorage interference');
  console.log('   - SSLCOMMERZ library internal routing');
} else if (!isConsistent) {
  console.log('❌ Configuration is INCONSISTENT');
  console.log('   Run: node scripts/sslcommerz-environment-manager.cjs fix');
} else {
  console.log('ℹ️  Configuration is for SANDBOX mode');
  console.log('   This is expected if you want to test payments');
}
