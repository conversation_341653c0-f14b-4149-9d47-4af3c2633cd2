// Simple test script to verify Paddle API connection
const https = require('https');
require('dotenv').config();

const PADDLE_API_KEY = process.env.VITE_PADDLE_API_KEY;

console.log('🔍 Testing Paddle API connection...');
console.log(`📡 API Key: ${PADDLE_API_KEY ? `${PADDLE_API_KEY.substring(0, 20)}...` : 'NOT FOUND'}`);

if (!PADDLE_API_KEY) {
  console.error('❌ PADDLE_API_KEY not found in environment variables');
  process.exit(1);
}

// Test API connection by listing existing products
const options = {
  hostname: 'api.paddle.com',
  port: 443,
  path: '/products',
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${PADDLE_API_KEY}`,
    'Content-Type': 'application/json',
  },
};

const req = https.request(options, (res) => {
  let body = '';
  res.on('data', (chunk) => {
    body += chunk;
  });
  res.on('end', () => {
    console.log(`📊 Response Status: ${res.statusCode}`);
    
    try {
      const response = JSON.parse(body);
      if (res.statusCode >= 200 && res.statusCode < 300) {
        console.log('✅ API connection successful!');
        console.log(`📋 Existing products: ${response.data ? response.data.length : 0}`);
        if (response.data && response.data.length > 0) {
          console.log('🔍 Existing products:');
          response.data.forEach(product => {
            console.log(`  - ${product.name} (${product.id})`);
          });
        }
      } else {
        console.error('❌ API Error:', JSON.stringify(response, null, 2));
      }
    } catch (error) {
      console.error('❌ Failed to parse response:', body);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Request failed:', error);
});

req.end();
