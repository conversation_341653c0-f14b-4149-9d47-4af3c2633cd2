const admin = require('firebase-admin');

// Initialize Firebase Admin SDK with project ID
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      projectId: 'ecomeasyai-v1'
    });
    console.log('✅ Initialized Firebase Admin with project ID');
  } catch (error) {
    console.error('❌ Could not initialize Firebase Admin SDK:', error.message);
    process.exit(1);
  }
}

const db = admin.firestore();

async function debugTeamManagement() {
  try {
    console.log('🔍 DEBUGGING TEAM MANAGEMENT VISIBILITY');
    console.log('=====================================');

    // Get the current user (you) - replace with your actual user ID
    const userId = 'SO43DGRA3ebNRv5AsKPJ8uqUoeB3'; // Your user ID from the screenshot

    console.log(`\n📋 Checking user: ${userId}`);

    // 1. Check user document
    const userRef = db.collection('users').doc(userId);
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
      console.log('❌ User document not found!');
      return;
    }

    const userData = userDoc.data();
    console.log('\n👤 User Data:');
    console.log('  - Role:', userData.role);
    console.log('  - Package Name:', userData.packageName);
    console.log('  - Is Team Member:', userData.isTeamMember);
    console.log('  - Subscription Expire Date:', userData.subscriptionExpireDate);
    console.log('  - Created At:', userData.createdAt);

    // 2. Check if user has Enterprise package
    let hasEnterprisePackage = false;
    if (userData.role === 'Enterprise User' || userData.packageName === 'Enterprise') {
      hasEnterprisePackage = true;
      console.log('\n✅ User has Enterprise package');

      // Check subscription expiration
      if (userData.subscriptionExpireDate) {
        const expireDate = new Date(userData.subscriptionExpireDate);
        const now = new Date();

        console.log('  - Expire Date:', expireDate.toISOString());
        console.log('  - Current Date:', now.toISOString());
        console.log('  - Is Expired:', expireDate < now);

        if (expireDate < now) {
          console.log('⚠️  Subscription has expired!');
          hasEnterprisePackage = false;
        }
      } else {
        console.log('  - No expiration date set');
      }
    } else {
      console.log('\n❌ User does NOT have Enterprise package');
    }

    // 3. Check if user is a team member
    let isTeamMember = false;

    // Check direct field
    if (userData.isTeamMember === true) {
      isTeamMember = true;
      console.log('\n❌ User is marked as team member (isTeamMember: true)');
    } else {
      console.log('\n✅ User is NOT marked as team member in user document');
    }

    // Check team invites collection
    const teamInvitesRef = db.collection('teamInvites');
    const teamInviteQuery = teamInvitesRef.where('invitedUserId', '==', userId);
    const teamInviteSnapshot = await teamInviteQuery.get();

    if (!teamInviteSnapshot.empty) {
      console.log('\n🔍 Found team invite records:');
      teamInviteSnapshot.forEach(doc => {
        const inviteData = doc.data();
        console.log('  - Invite ID:', doc.id);
        console.log('  - Admin Owner ID:', inviteData.adminOwnerId);
        console.log('  - Used:', inviteData.used);
        console.log('  - Created At:', inviteData.createdAt);

        if (inviteData.used === true) {
          isTeamMember = true;
          console.log('  ❌ This invite was used - user is a team member');
        }
      });
    } else {
      console.log('\n✅ No team invite records found');
    }

    // 4. Final determination
    console.log('\n🎯 FINAL DETERMINATION:');
    console.log('  - Has Enterprise Package:', hasEnterprisePackage);
    console.log('  - Is Team Member:', isTeamMember);

    const isEnterpriseAdminOwner = hasEnterprisePackage && !isTeamMember;
    console.log('  - Is Enterprise Admin Owner:', isEnterpriseAdminOwner);

    if (isEnterpriseAdminOwner) {
      console.log('\n🎉 Team Management button SHOULD be visible!');
    } else {
      console.log('\n❌ Team Management button should NOT be visible');

      if (!hasEnterprisePackage) {
        console.log('   Reason: User does not have Enterprise package');
      }
      if (isTeamMember) {
        console.log('   Reason: User is a team member');
      }
    }

    // 5. Check enterprise limits
    console.log('\n📊 ENTERPRISE LIMITS CHECK:');
    const enterpriseLimitsRef = db.collection('settings').doc('enterpriseAdminOwnerLimits');
    const enterpriseLimitsDoc = await enterpriseLimitsRef.get();

    if (enterpriseLimitsDoc.exists) {
      const limitsData = enterpriseLimitsDoc.data();
      console.log('  - Max Team Members:', limitsData.maxTeamMembers);
      console.log('  - Max Images:', limitsData.maxImages);
      console.log('  - Max Saved Prompts:', limitsData.maxSavedPrompts);
    } else {
      console.log('  ❌ Enterprise limits document not found');
    }

  } catch (error) {
    console.error('❌ Error debugging team management:', error);
  }
}

debugTeamManagement();