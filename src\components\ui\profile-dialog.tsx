import { useState, useEffect } from 'react';
import { User, Package, Calendar, Hash } from 'lucide-react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from './dialog';
import { db } from '@/lib/firebase';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { useStore } from '@/lib/store';
import { getLimitSettings, getUserUsage } from '@/lib/userLimits';

interface ProfileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface UserProfile {
  displayName: string;
  email: string;
  packageName: string;
  registrationDate: string;
  promptCount: number;
  totalPromptsRemaining?: number;
}

export function ProfileDialog({ open, onOpenChange }: ProfileDialogProps) {
  const { user } = useStore();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (open && user) {
      fetchUserProfile();
    }
  }, [open, user]);

  const fetchUserProfile = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      // Fetch user data from Firestore
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('uid', '==', user.id));
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        // Get default limits for free users
        const limits = await getLimitSettings();
        
        setProfile({
          displayName: user.email.split('@')[0],
          email: user.email,
          packageName: 'Free',
          registrationDate: 'N/A',
          promptCount: 0,
          totalPromptsRemaining: limits.totalGenerationLimit
        });
        return;
      }
      
      const userData = querySnapshot.docs[0].data();
      
      // Count total prompts generated
      const captionsRef = collection(db, 'savedCaptions');
      const captionsQuery = query(captionsRef, where('userId', '==', user.id));
      const captionsSnapshot = await getDocs(captionsQuery);
      const totalPrompts = captionsSnapshot.size;
      
      // Get user's package and usage information
      const packageName = userData.packageName || 'Free';
      
      // For free users, get their total usage and remaining limit
      let totalPromptsRemaining;
      
      if (packageName === 'Free') {
        // Get the limit settings and user usage
        const limits = await getLimitSettings();
        const usage = await getUserUsage(user.id);
        
        // Calculate remaining prompts based on total limit
        totalPromptsRemaining = Math.max(0, limits.totalGenerationLimit - usage.dailyGenerationCount);
      }
      
      // Format registration date
      const registrationDate = userData.createdAt ? 
        new Date(userData.createdAt).toLocaleDateString() : 
        'N/A';
      
      setProfile({
        displayName: userData.displayName || user.email.split('@')[0],
        email: user.email,
        packageName: packageName,
        registrationDate: registrationDate,
        promptCount: totalPrompts,
        totalPromptsRemaining
      });
    } catch (error) {
      console.error('Error fetching user profile:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-800 text-white border border-gray-700 max-w-md mx-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-center bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent">
            Your Profile
          </DialogTitle>
        </DialogHeader>
        
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
          </div>
        ) : profile ? (
          <div className="space-y-4 py-2">
            <div className="flex items-center space-x-3 p-3 bg-gray-700 rounded-lg">
              <div className="bg-purple-500 p-2 rounded-full">
                <User className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="text-sm text-gray-400">User</p>
                <p className="font-medium">{profile.displayName}</p>
                <p className="text-sm text-gray-300">{profile.email}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-3 bg-gray-700 rounded-lg">
              <div className="bg-pink-500 p-2 rounded-full">
                <Package className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Package</p>
                <p className="font-medium">{profile.packageName}</p>
                {profile.totalPromptsRemaining !== undefined && (
                  <p className="text-sm text-gray-300">
                    {profile.totalPromptsRemaining} prompts remaining in total
                  </p>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-3 bg-gray-700 rounded-lg">
              <div className="bg-blue-500 p-2 rounded-full">
                <Calendar className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Registration Date</p>
                <p className="font-medium">{profile.registrationDate}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-3 bg-gray-700 rounded-lg">
              <div className="bg-green-500 p-2 rounded-full">
                <Hash className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Total Prompts Generated</p>
                <p className="font-medium">{profile.promptCount}</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-400">
            Unable to load profile information
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}