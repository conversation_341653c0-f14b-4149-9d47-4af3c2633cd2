# Country Selection and Currency Localization

## Overview

The Country Selection and Currency Localization feature enhances the user experience by personalizing pricing display based on the user's location. This feature allows users to see prices in their local currency while still referencing the original USD price, making the purchasing decision more transparent and convenient.

## Features

### Country Selection Popup

- A popup appears for first-time visitors to select their country
- The selection is saved in browser cookies for future visits
- If the user cancels, USD is set as the default currency
- The popup has a clean design consistent with the site's aesthetic

### Country Indicator in UI

- A clickable country indicator is displayed in the header next to the language toggle
- The indicator shows the currently selected country name and/or flag
- Clicking the indicator opens the country selection popup
- The indicator is visible on both desktop and mobile layouts

### Currency Localization

- All pricing throughout the site is displayed in the user's local currency
- A small indicator shows the original USD price for reference
- Exchange rates are defined in the currencies.ts file
- Supports different currency symbols and formats

### Country Display in Popups

- The selected country is prominently displayed in:
  - The "Upgrade Your Plan" popup window
  - The "Billing Details" popup window
- Users can change their country directly from these interfaces

### Billing Details Integration

- The user's saved country is automatically selected in:
  - The "Billing Details" section of the Profile and Settings page
  - The "Billing Details" step/popup during checkout
- Users can change their country selection at any time

## Implementation Details

### File Structure

- `src/lib/currencies.ts`: Contains currency data, exchange rates, and formatting functions
- `src/lib/countryContext.tsx`: Provides context for country selection and currency formatting
- `src/components/ui/country-selection-popup.tsx`: The popup component for country selection
- `src/components/ui/country-indicator.tsx`: Reusable component for displaying the selected country
- Various components that display pricing have been updated to use the currency context

### Data Storage

- The selected country is stored in browser cookies with a 1-year expiration
- Cookie functions are implemented in the CountryContext provider

### Currency Data

The currency data includes:
- Currency code (e.g., USD, EUR)
- Currency symbol (e.g., $, €)
- Currency name (e.g., US Dollar, Euro)
- Exchange rate relative to USD
- Symbol position (before or after the amount)

## How to Use

### For Users

1. First-time visitors will see a country selection popup
2. Select your country from the dropdown list
3. All prices will be displayed in your local currency with USD reference
4. You can change your country selection at any time by:
   - Clicking the country indicator in the header
   - Using the country selector in the "Upgrade Your Plan" popup
   - Using the country selector in the "Billing Details" popup
   - Using the country selector in the Profile and Settings page

### For Developers

#### Adding a New Currency

To add a new currency to the system:

1. Open `src/lib/currencies.ts`
2. Add a new entry to the `countryCurrencies` object:

```typescript
"Country Name": {
  code: "XXX",
  symbol: "X",
  name: "Currency Name",
  exchangeRate: 1.23, // Exchange rate relative to USD
  symbolPosition: 'before' // or 'after'
}
```

#### Updating Exchange Rates

To update exchange rates:

1. Open `src/lib/currencies.ts`
2. Update the `exchangeRate` value for the currencies that need updating
3. The changes will take effect immediately for all users

#### Displaying Prices in Local Currency

To display a price in the user's selected currency:

```typescript
import { useCountry } from '@/lib/countryContext';

function MyComponent() {
  const { formatCurrency } = useCountry();

  // Display a price of 10 USD in the user's local currency
  return <div>{formatCurrency(10)}</div>;

  // Display with USD reference
  return <div>{formatCurrency(10, true)}</div>;
}
```

## Testing

To test the country selection and currency localization feature:

1. Clear your browser cookies to simulate a first-time visit
2. Load the landing page and verify the country selection popup appears
3. Select different countries and verify the pricing updates accordingly
4. Check that the country selection persists across page refreshes
5. Verify that the selected country is pre-filled in the billing details forms
6. Test the currency formatting for different amounts and currencies

## Troubleshooting

### Common Issues

- **Popup not appearing**: Check if the 'userCountry' cookie exists. If it does, the popup won't show.
- **Currency not updating**: Verify that the country is correctly mapped to a currency in the `countryCurrencies` object.
- **Incorrect formatting**: Check the `symbolPosition` property for the currency to ensure proper symbol placement.

### Debugging

- Use browser developer tools to inspect cookies and verify the 'userCountry' cookie
- Check the console for any errors related to currency formatting
- Verify that components are correctly using the `useCountry` hook

## Future Improvements

- Implement automatic country detection based on IP address
- Add more currencies and keep exchange rates updated regularly
- Provide an option to manually switch currencies regardless of country
- Implement server-side exchange rate updates from a reliable API
- Add more detailed country flags and improve visual design
- Implement a currency conversion calculator for users to compare prices
- Add support for regional pricing strategies (different base prices per region)
- Implement automatic exchange rate updates from a financial API
