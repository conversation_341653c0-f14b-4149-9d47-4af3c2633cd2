/* Animation for fade in effect */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Animation for shake effect (for error) */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

/* Animation for success pulse */
@keyframes successPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

.animate-success-pulse {
  animation: successPulse 1.5s ease-in-out;
}

/* Billing Details Modal Optimizations */
@media (min-width: 768px) {
  .billing-modal-container {
    height: fit-content;
    max-height: 95vh;
    overflow: visible;
  }
  
  .billing-modal-content {
    overflow: visible;
  }
}

/* Additional optimizations for larger screens */
@media (min-width: 1024px) {
  .billing-modal-container {
    max-height: 90vh;
  }
}

@media (min-width: 1440px) {
  .billing-modal-container {
    max-height: 85vh;
  }
}

/* Ensure compact layout on desktop */
@media (min-width: 768px) {
  .billing-compact {
    padding: 1.25rem 1.5rem;
  }
  
  .billing-form-compact {
    gap: 0.75rem;
  }
  
  .billing-section-compact {
    margin: 0.5rem 0;
  }
}

/* Payment initialization loading animations */
@keyframes paymentPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes paymentFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.payment-loading-container {
  animation: paymentFadeIn 0.3s ease-out;
}

.payment-loading-text {
  animation: paymentPulse 2s ease-in-out infinite;
}

/* Description generation loading animations */
@keyframes descriptionPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes descriptionFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.description-loading-container {
  animation: descriptionFadeIn 0.3s ease-out;
}

.description-loading-text {
  animation: descriptionPulse 2s ease-in-out infinite;
}

/* Animation for coupon input field border glow */
@keyframes borderPulse {
  0% {
    border-color: rgba(168, 85, 247, 0.4);
    box-shadow: 0 0 0 0 rgba(168, 85, 247, 0.1);
  }
  50% {
    border-color: rgba(168, 85, 247, 0.8);
    box-shadow: 0 0 5px 0 rgba(168, 85, 247, 0.3);
  }
  100% {
    border-color: rgba(168, 85, 247, 0.4);
    box-shadow: 0 0 0 0 rgba(168, 85, 247, 0.1);
  }
}

.animate-border-pulse {
  animation: borderPulse 2s ease-in-out infinite;
}

/* Animation for checkbox error highlight */
@keyframes checkboxPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

.checkbox-error {
  animation: checkboxPulse 1.5s ease-in-out;
  border-color: rgb(239, 68, 68);
}

/* Responsive styles for terms checkbox */
@media (max-width: 640px) {
  #terms-checkbox-container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  #terms-checkbox-container label {
    font-size: 0.75rem; /* 12px */
    line-height: 1rem; /* 16px */
  }

  #terms-checkbox-container .flex-shrink-0 {
    margin-top: 0.25rem;
  }
}
