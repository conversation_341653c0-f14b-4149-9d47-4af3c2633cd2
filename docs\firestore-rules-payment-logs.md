# Firestore Security Rules for Payment Logs

Add these rules to your Firestore security rules to secure the `paymentLogs` collection:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Payment logs collection - users can only read their own payment logs
    match /paymentLogs/{logId} {
      // Users can only read their own payment logs
      allow read: if request.auth != null && 
                     request.auth.uid == resource.data.userId;
      
      // Only Cloud Functions can write payment logs
      allow write: if false; // Only server-side functions can write
    }
    
    // Existing rules for other collections...
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /savedCaptions/{captionId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // Add other existing rules here...
  }
}
```

## Key Security Features:

1. **Read Access**: Users can only read their own payment logs (where `userId` matches their auth UID)
2. **Write Protection**: Only Cloud Functions can write payment logs (client-side writes are blocked)
3. **Authentication Required**: All access requires user authentication
4. **Data Isolation**: Users cannot access other users' payment history

## Implementation Steps:

1. Go to Firebase Console → Firestore Database → Rules
2. Add the `paymentLogs` rules to your existing rules
3. Test the rules in the Firebase Console simulator
4. Deploy the rules

## Data Structure:

The `paymentLogs` collection documents should have this structure:

```javascript
{
  transactionId: string,
  userId: string,           // Required for security rules
  status: 'SUCCESS' | 'FAILED' | 'CANCELED',
  packageName: string,
  amount: number,
  currency: string,
  paymentMethod: string,
  billingPeriod: string,
  timestamp: Timestamp,
  userEmail?: string,
  userName?: string,
  validationResponse?: object,
  failureDetails?: object,
  cancelDetails?: object
}
```
