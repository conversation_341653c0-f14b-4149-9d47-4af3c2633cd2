// SSLCOMMERZ Production Setup and Readiness Checker
// This script helps you prepare and verify SSLCOMMERZ for production use

const fs = require('fs');
const path = require('path');
const https = require('https');
require('dotenv').config();

// SSLCOMMERZ API endpoints
const SSLCOMMERZ_ENDPOINTS = {
  production: {
    session: 'https://securepay.sslcommerz.com/gwprocess/v4/api.php',
    validation: 'https://securepay.sslcommerz.com/validator/api/validationserverAPI.php'
  },
  sandbox: {
    session: 'https://sandbox.sslcommerz.com/gwprocess/v4/api.php',
    validation: 'https://sandbox.sslcommerz.com/validator/api/validationserverAPI.php'
  }
};

// Read environment configurations
function getEnvironmentConfig() {
  // Frontend config from .env
  const frontendConfig = {
    storeId: process.env.VITE_SSLCOMMERZ_STORE_ID,
    storePassword: process.env.VITE_SSLCOMMERZ_STORE_PASSWORD,
    isLive: process.env.VITE_SSLCOMMERZ_IS_LIVE === 'true',
    appUrl: process.env.VITE_APP_URL,
    localAppUrl: process.env.VITE_LOCAL_APP_URL
  };

  // Backend config from functions/.env
  let backendConfig = {};
  try {
    const functionsEnvPath = path.join(__dirname, '..', 'functions', '.env');
    if (fs.existsSync(functionsEnvPath)) {
      const functionsEnv = fs.readFileSync(functionsEnvPath, 'utf8');
      const lines = functionsEnv.split('\n');
      
      lines.forEach(line => {
        const match = line.match(/^([^=]+)=(.*)$/);
        if (match) {
          const key = match[1].trim();
          const value = match[2].trim().replace(/^["']|["']$/g, '');
          
          if (key === 'SSLCOMMERZ_STORE_ID') backendConfig.storeId = value;
          if (key === 'SSLCOMMERZ_STORE_PASSWORD') backendConfig.storePassword = value;
          if (key === 'SSLCOMMERZ_IS_LIVE') backendConfig.isLive = value === 'true';
          if (key === 'APP_URL') backendConfig.appUrl = value;
          if (key === 'LOCAL_APP_URL') backendConfig.localAppUrl = value;
        }
      });
    }
  } catch (error) {
    console.warn('Could not read functions/.env file');
  }

  return { frontend: frontendConfig, backend: backendConfig };
}

// Validate configuration
function validateConfiguration(config) {
  console.log('🔧 SSLCOMMERZ Configuration Validation');
  console.log('======================================\n');

  let isValid = true;
  const issues = [];

  // Check frontend configuration
  console.log('1️⃣ Frontend Configuration (.env):');
  
  if (!config.frontend.storeId) {
    console.log('❌ VITE_SSLCOMMERZ_STORE_ID is missing');
    issues.push('Missing VITE_SSLCOMMERZ_STORE_ID');
    isValid = false;
  } else {
    console.log(`✅ Store ID: ${config.frontend.storeId}`);
  }

  if (!config.frontend.storePassword) {
    console.log('❌ VITE_SSLCOMMERZ_STORE_PASSWORD is missing');
    issues.push('Missing VITE_SSLCOMMERZ_STORE_PASSWORD');
    isValid = false;
  } else {
    console.log(`✅ Store Password: ${'*'.repeat(config.frontend.storePassword.length)}`);
  }

  console.log(`✅ Environment: ${config.frontend.isLive ? 'PRODUCTION' : 'SANDBOX'}`);
  console.log(`✅ App URL: ${config.frontend.appUrl}`);
  console.log(`✅ Local URL: ${config.frontend.localAppUrl}`);

  console.log('\n2️⃣ Backend Configuration (functions/.env):');
  
  if (!config.backend.storeId) {
    console.log('❌ SSLCOMMERZ_STORE_ID is missing in functions/.env');
    issues.push('Missing SSLCOMMERZ_STORE_ID in functions/.env');
    isValid = false;
  } else {
    console.log(`✅ Store ID: ${config.backend.storeId}`);
  }

  if (!config.backend.storePassword) {
    console.log('❌ SSLCOMMERZ_STORE_PASSWORD is missing in functions/.env');
    issues.push('Missing SSLCOMMERZ_STORE_PASSWORD in functions/.env');
    isValid = false;
  } else {
    console.log(`✅ Store Password: ${'*'.repeat(config.backend.storePassword.length)}`);
  }

  console.log(`✅ Environment: ${config.backend.isLive ? 'PRODUCTION' : 'SANDBOX'}`);

  // Check for consistency between frontend and backend
  console.log('\n3️⃣ Configuration Consistency Check:');
  
  if (config.frontend.storeId !== config.backend.storeId) {
    console.log('⚠️  Store ID mismatch between frontend and backend');
    issues.push('Store ID mismatch between frontend and backend');
  } else {
    console.log('✅ Store ID consistent across frontend and backend');
  }

  if (config.frontend.storePassword !== config.backend.storePassword) {
    console.log('⚠️  Store Password mismatch between frontend and backend');
    issues.push('Store Password mismatch between frontend and backend');
  } else {
    console.log('✅ Store Password consistent across frontend and backend');
  }

  if (config.frontend.isLive !== config.backend.isLive) {
    console.log('⚠️  Environment setting mismatch between frontend and backend');
    issues.push('Environment setting mismatch between frontend and backend');
  } else {
    console.log('✅ Environment setting consistent across frontend and backend');
  }

  console.log(`\n📊 Validation Result: ${isValid ? '✅ PASSED' : '❌ FAILED'}`);
  
  if (issues.length > 0) {
    console.log('\n🚨 Issues Found:');
    issues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue}`);
    });
  }

  return { isValid, issues };
}

// Test API connectivity
async function testAPIConnectivity(config) {
  console.log('\n🔍 SSLCOMMERZ API Connectivity Test');
  console.log('===================================\n');

  const environment = config.frontend.isLive ? 'production' : 'sandbox';
  const endpoint = SSLCOMMERZ_ENDPOINTS[environment].session;
  
  console.log(`Testing ${environment.toUpperCase()} environment...`);
  console.log(`Endpoint: ${endpoint}`);

  // Test data for connectivity check
  const testData = {
    store_id: config.frontend.storeId,
    store_passwd: config.frontend.storePassword,
    total_amount: '10.00',
    currency: 'BDT',
    tran_id: 'test_' + Date.now(),
    success_url: 'https://example.com/success',
    fail_url: 'https://example.com/fail',
    cancel_url: 'https://example.com/cancel',
    product_name: 'Test Product',
    product_category: 'Test',
    product_profile: 'general',
    cus_name: 'Test Customer',
    cus_email: '<EMAIL>',
    cus_add1: 'Test Address',
    cus_city: 'Dhaka',
    cus_country: 'Bangladesh',
    cus_phone: '01700000000',
    shipping_method: 'NO'
  };

  return new Promise((resolve) => {
    const postData = new URLSearchParams(testData).toString();
    
    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(endpoint, options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          
          if (response.status === 'SUCCESS') {
            console.log('✅ API connectivity test PASSED');
            console.log(`   Session URL: ${response.sessionkey ? 'Generated' : 'Not generated'}`);
            console.log(`   Gateway URL: ${response.GatewayPageURL ? 'Available' : 'Not available'}`);
            resolve({ success: true, response });
          } else {
            console.log('❌ API connectivity test FAILED');
            console.log(`   Error: ${response.failedreason || 'Unknown error'}`);
            resolve({ success: false, error: response.failedreason });
          }
        } catch (error) {
          console.log('❌ API connectivity test FAILED');
          console.log(`   Parse Error: ${error.message}`);
          resolve({ success: false, error: error.message });
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ API connectivity test FAILED');
      console.log(`   Network Error: ${error.message}`);
      resolve({ success: false, error: error.message });
    });

    req.write(postData);
    req.end();
  });
}

// Production readiness check
async function productionReadinessCheck() {
  console.log('🚀 SSLCOMMERZ PRODUCTION READINESS CHECK');
  console.log('=========================================\n');

  const config = getEnvironmentConfig();
  
  // Step 1: Configuration validation
  const validation = validateConfiguration(config);
  
  // Step 2: API connectivity test
  const connectivityTest = await testAPIConnectivity(config);
  
  // Step 3: Final assessment
  console.log('\n🎯 FINAL ASSESSMENT');
  console.log('==================\n');
  
  const allChecks = validation.isValid && connectivityTest.success;
  
  if (allChecks) {
    console.log('✅ ALL CHECKS PASSED - Ready for production!');
    console.log('\n📋 Current Status:');
    console.log(`   Environment: ${config.frontend.isLive ? 'PRODUCTION' : 'SANDBOX'}`);
    console.log(`   Store ID: ${config.frontend.storeId}`);
    console.log(`   API Connectivity: Working`);
    
    if (!config.frontend.isLive) {
      console.log('\n⚠️  Currently in SANDBOX mode');
      console.log('   To go live: Set SSLCOMMERZ_IS_LIVE=true in both .env files');
    } else {
      console.log('\n🚀 Currently in PRODUCTION mode');
      console.log('   Real payments will be processed!');
    }
  } else {
    console.log('❌ SOME CHECKS FAILED - Please fix issues before going to production');
    
    if (validation.issues.length > 0) {
      console.log('\n🔧 Configuration Issues:');
      validation.issues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`);
      });
    }
    
    if (!connectivityTest.success) {
      console.log('\n🌐 Connectivity Issues:');
      console.log(`   ${connectivityTest.error}`);
    }
  }
  
  return allChecks;
}

// Main function
async function main() {
  const [,, command] = process.argv;
  
  switch (command) {
    case 'check':
    case 'readiness':
      await productionReadinessCheck();
      break;
      
    case 'validate':
      const config = getEnvironmentConfig();
      validateConfiguration(config);
      break;
      
    case 'test':
      const testConfig = getEnvironmentConfig();
      await testAPIConnectivity(testConfig);
      break;
      
    default:
      console.log('🚀 SSLCOMMERZ Production Setup Tool');
      console.log('\nUsage: node scripts/sslcommerz-production-setup.cjs [command]');
      console.log('\nCommands:');
      console.log('  check             - Run complete production readiness check');
      console.log('  validate          - Validate configuration only');
      console.log('  test              - Test API connectivity only');
      console.log('\nExample:');
      console.log('  node scripts/sslcommerz-production-setup.cjs check');
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  });
}

module.exports = { getEnvironmentConfig, validateConfiguration, testAPIConnectivity, productionReadinessCheck };
