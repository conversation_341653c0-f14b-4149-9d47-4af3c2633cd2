import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../ui/button';
import { useStore } from '../../lib/store';
import { toast } from 'react-hot-toast';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, AlertTriangle } from 'lucide-react';
import { signIn, signUp, resetPassword, signInWithGoogle } from '../../lib/firebase';

export function AuthForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [isLogin, setIsLogin] = useState(() => {
    const searchParams = new URLSearchParams(window.location.search);
    return searchParams.get('mode') !== 'signup';
  });
  const [isResetPassword, setIsResetPassword] = useState(false);
  const [emailValidating, setEmailValidating] = useState(false);
  const [isDisposableEmail, setIsDisposableEmail] = useState(false);
  const [emailChecked, setEmailChecked] = useState(false);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const setUser = useStore((state) => state.setUser);
  const navigate = useNavigate();

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Function to check if email is disposable
  const checkDisposableEmail = async (email: string): Promise<boolean> => {
    if (!email || !email.includes('@')) {
      return false;
    }

    try {
      setEmailValidating(true);
      setEmailChecked(false);
      setIsDisposableEmail(false);

      const response = await fetch(`https://disposable.debounce.io/?email=${encodeURIComponent(email)}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });
      
      if (!response.ok) {
        console.warn('Failed to check disposable email, allowing registration');
        return false;
      }

      const data = await response.json();
      const isDisposable = data.disposable === 'true' || data.disposable === true;
      
      setIsDisposableEmail(isDisposable);
      setEmailChecked(true);
      
      if (isDisposable) {
        console.log('Disposable email detected:', email);
      }
      
      return isDisposable;
    } catch (error) {
      console.warn('Error checking disposable email:', error);
      // If API fails, allow registration to continue to avoid blocking legitimate users
      setEmailChecked(true);
      setIsDisposableEmail(false);
      return false;
    } finally {
      setEmailValidating(false);
    }
  };

  // Debounced email validation
  const handleEmailChange = async (value: string) => {
    setEmail(value);
    
    // Reset validation state when email changes
    setEmailChecked(false);
    setIsDisposableEmail(false);
    
    // Only check if it's for signup and email looks valid
    if (!isLogin && value.includes('@') && value.includes('.')) {
      // Add a small delay to avoid too many API calls
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      debounceTimeoutRef.current = setTimeout(() => {
        // Get the current email value from the DOM to ensure we're checking the latest value
        const currentEmail = (document.querySelector('input[type="email"]') as HTMLInputElement)?.value;
        if (currentEmail === value) { // Only check if email hasn't changed
          checkDisposableEmail(value);
        }
      }, 1000);
    }
  };

  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    if (!isLogin && password !== confirmPassword) {
      toast.error('Passwords do not match!');
      setLoading(false);
      return;
    }

    // Check for disposable email on signup
    if (!isLogin) {
      // Force check if not already checked
      if (!emailChecked) {
        const isDisposable = await checkDisposableEmail(email);
        if (isDisposable) {
          toast.error('Disposable email addresses are not allowed. Please use a permanent email address.');
          setLoading(false);
          return;
        }
      } else if (isDisposableEmail) {
        toast.error('Disposable email addresses are not allowed. Please use a permanent email address.');
        setLoading(false);
        return;
      }
    }

    try {
      const result = isLogin
        ? await signIn(email, password)
        : await signUp(email, password);

      if (result.error) {
        toast.error(result.error);
      } else if (result.user) {
        setUser({ id: result.user.uid, email: result.user.email || '' });

        if (isLogin) {
          // For login, check if email is verified
          if (!result.user.emailVerified) {
            toast.success('Welcome back! Please verify your email to access all features.');
            navigate('/email-verification');
          } else {
            toast.success('Welcome back!');
            navigate('/app');
          }
        } else {
          // For signup, always redirect to verification page
          toast.success('Account created successfully! Please check your email for verification.');
          navigate('/email-verification');
        }
      }
    } catch (error) {
      toast.error('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  const handleGoogleSignIn = async () => {
    setLoading(true);
    try {
      const result = await signInWithGoogle();

      if (result.error) {
        toast.error(result.error);
      } else if (result.user) {
        setUser({ id: result.user.uid, email: result.user.email || '' });

        // Google accounts are typically pre-verified, but check anyway
        if (!result.user.emailVerified) {
          toast.success('Signed in with Google! Please verify your email to access all features.');
          navigate('/email-verification');
        } else {
          toast.success('Signed in with Google successfully!');
          navigate('/app');
        }
      }
    } catch (error) {
      toast.error('An unexpected error occurred with Google sign in');
    } finally {
      setLoading(false);
    }
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const result = await resetPassword(email);
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('Password reset link sent to your email!');
        setIsResetPassword(false);
        setIsLogin(true);
      }
    } catch (error) {
      toast.error('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Reset email validation when switching between login/signup
  useEffect(() => {
    setEmailChecked(false);
    setIsDisposableEmail(false);
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
  }, [isLogin]);

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <button
            onClick={() => navigate('/')}
            className="inline-flex items-center text-gray-400 hover:text-white mb-8"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </button>
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-gray-700 mb-4">
            <Sparkles className="h-8 w-8 text-purple-400" />
          </div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent">
            {isResetPassword ? 'Reset Password' : isLogin ? 'Welcome Back' : 'Create Account'}
          </h2>
          <p className="mt-2 text-gray-400">
            Demo Mode - Any credentials will work
          </p>
        </div>

        <div className="bg-gray-800 p-8 rounded-xl border border-gray-700">
          {isResetPassword ? (
            <form onSubmit={handleResetPassword} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Email address
                </label>
                <input
                  type="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400"
                />
              </div>
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? 'Sending...' : 'Send Reset Link'}
              </Button>
            </form>
          ) : (
            <form onSubmit={handleAuth} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Email address
                </label>
                <input
                  type="email"
                  required
                  value={email}
                  onChange={(e) => handleEmailChange(e.target.value)}
                  placeholder="Enter your email"
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400"
                />
                {emailValidating && (
                  <p className="mt-2 text-sm text-gray-400">
                    Validating email...
                  </p>
                )}
                {emailChecked && isDisposableEmail && (
                  <p className="mt-2 text-sm text-red-400 flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    Disposable email detected! Please use a different email.
                  </p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Password
                </label>
                <input
                  type="password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400"
                />
              </div>
              {!isLogin && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Confirm Password
                  </label>
                  <input
                    type="password"
                    required
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Confirm your password"
                    className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400"
                  />
                </div>
              )}
              {isLogin && (
                <button
                  type="button"
                  onClick={() => setIsResetPassword(true)}
                  className="text-sm text-purple-400 hover:text-purple-300"
                >
                  Forgot your password?
                </button>
              )}
              <Button 
                type="submit" 
                className="w-full" 
                disabled={loading || (emailValidating || (isDisposableEmail && !isLogin))}
              >
                {loading ? 'Loading...' : isLogin ? 'Sign In' : 'Sign Up'}
              </Button>
              
              <div className="relative my-4">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-600"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-gray-800 text-gray-400">Or continue with</span>
                </div>
              </div>
              
              <button
                type="button"
                onClick={handleGoogleSignIn}
                disabled={loading}
                className="w-full flex items-center justify-center gap-2 bg-white hover:bg-gray-100 text-gray-800 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors duration-300"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                  <path fill="none" d="M1 1h22v22H1z" />
                </svg>
                <span>{isLogin ? 'Sign in with Google' : 'Sign up with Google'}</span>
              </button>
            </form>
          )}
        </div>

        <button
          onClick={() => {
            setIsLogin(!isLogin);
            setIsResetPassword(false);
          }}
          className="w-full text-center text-sm text-gray-400 hover:text-white"
        >
          {isResetPassword
            ? 'Back to Sign In'
            : isLogin
            ? "Don't have an account? Sign up"
            : 'Already have an account? Sign in'}
        </button>
      </div>
    </div>
  );
}