// Node.js script to create Paddle products and prices
// Run this with: node scripts/create-paddle-products.js

const https = require('https');

// Load environment variables from .env file
require('dotenv').config();

const PADDLE_API_KEY = process.env.VITE_PADDLE_API_KEY;
const BASE_URL = 'https://api.paddle.com';

if (!PADDLE_API_KEY) {
  console.error('❌ PADDLE_API_KEY not found in environment variables');
  process.exit(1);
}

// Helper function to make API requests
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.paddle.com',
      port: 443,
      path: path,
      method: method,
      headers: {
        'Authorization': `Bearer ${PADDLE_API_KEY}`,
        'Content-Type': 'application/json',
      },
    };

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(response);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${JSON.stringify(response)}`));
          }
        } catch (error) {
          reject(new Error(`Failed to parse response: ${body}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Function to create a product
async function createProduct(name, description, taxCategory) {
  try {
    console.log(`🔄 Creating product: ${name}`);
    const product = await makeRequest('POST', '/products', {
      name,
      description,
      taxCategory,
    });
    console.log(`✅ Created product: ${name} (ID: ${product.data.id})`);
    return product.data;
  } catch (error) {
    console.error(`❌ Failed to create product ${name}:`, error.message);
    throw error;
  }
}

// Function to create a price
async function createPrice(productId, description, unitPrice, billingCycle = null, unitPriceOverrides = []) {
  try {
    console.log(`🔄 Creating price for product: ${productId}`);
    const priceData = {
      productId,
      description,
      unitPrice,
    };

    if (billingCycle) {
      priceData.billingCycle = billingCycle;
    }

    if (unitPriceOverrides && unitPriceOverrides.length > 0) {
      priceData.unitPriceOverrides = unitPriceOverrides;
    }

    const price = await makeRequest('POST', '/prices', priceData);
    console.log(`✅ Created price: ${description} (ID: ${price.data.id})`);
    return price.data;
  } catch (error) {
    console.error(`❌ Failed to create price ${description}:`, error.message);
    throw error;
  }
}

// Main function to create all products and prices
async function createPaddleProductsAndPrices() {
  console.log('🚀 Starting Paddle products and prices creation...');
  console.log(`📡 Using API Key: ${PADDLE_API_KEY.substring(0, 20)}...`);

  try {
    // 1. Create Pay-As-You-Go Product
    const payAsYouGoProduct = await createProduct(
      'Pay-As-You-Go',
      'Flexible credits for AI-powered product descriptions. 15 generations per purchase, perfect for occasional use. Credits never expire.',
      'saas'
    );

    // Create Pay-As-You-Go Price (one-time payment)
    const payAsYouGoPrice = await createPrice(
      payAsYouGoProduct.id,
      'Pay-As-You-Go - 15 generations',
      {
        amount: '200', // $2.00 USD
        currencyCode: 'USD'
      },
      null, // No billing cycle for one-time payment
      [
        // Regional pricing
        {
          countryCodes: ['BD'], // Bangladesh
          unitPrice: {
            amount: '20000', // 200 BDT
            currencyCode: 'BDT'
          }
        },
        {
          countryCodes: ['IN'], // India
          unitPrice: {
            amount: '16700', // 167 INR
            currencyCode: 'INR'
          }
        },
        {
          countryCodes: ['GB'], // United Kingdom
          unitPrice: {
            amount: '159', // £1.59 GBP
            currencyCode: 'GBP'
          }
        },
        {
          countryCodes: ['EU', 'DE', 'FR', 'IT', 'ES'], // Eurozone
          unitPrice: {
            amount: '185', // €1.85 EUR
            currencyCode: 'EUR'
          }
        }
      ]
    );

    // 2. Create Pro Product
    const proProduct = await createProduct(
      'Pro',
      'Perfect for regular users. 50 stored images, 150 generations per month, 10 custom prompts, instant image deletion, and priority support.',
      'saas'
    );

    // Create Pro Monthly Price
    const proMonthlyPrice = await createPrice(
      proProduct.id,
      'Pro Plan - Monthly',
      {
        amount: '1000', // $10.00 USD
        currencyCode: 'USD'
      },
      {
        interval: 'month',
        frequency: 1
      },
      [
        // Regional pricing for monthly
        {
          countryCodes: ['BD'], // Bangladesh
          unitPrice: {
            amount: '100000', // 1000 BDT
            currencyCode: 'BDT'
          }
        },
        {
          countryCodes: ['IN'], // India
          unitPrice: {
            amount: '83500', // 835 INR
            currencyCode: 'INR'
          }
        },
        {
          countryCodes: ['GB'], // United Kingdom
          unitPrice: {
            amount: '795', // £7.95 GBP
            currencyCode: 'GBP'
          }
        },
        {
          countryCodes: ['EU', 'DE', 'FR', 'IT', 'ES'], // Eurozone
          unitPrice: {
            amount: '925', // €9.25 EUR
            currencyCode: 'EUR'
          }
        }
      ]
    );

    // Create Pro Yearly Price (20% discount)
    const proYearlyPrice = await createPrice(
      proProduct.id,
      'Pro Plan - Yearly (20% off)',
      {
        amount: '9600', // $96.00 USD (20% discount)
        currencyCode: 'USD'
      },
      {
        interval: 'year',
        frequency: 1
      },
      [
        // Regional pricing for yearly
        {
          countryCodes: ['BD'], // Bangladesh
          unitPrice: {
            amount: '960000', // 9600 BDT
            currencyCode: 'BDT'
          }
        },
        {
          countryCodes: ['IN'], // India
          unitPrice: {
            amount: '801600', // 8016 INR
            currencyCode: 'INR'
          }
        },
        {
          countryCodes: ['GB'], // United Kingdom
          unitPrice: {
            amount: '7632', // £76.32 GBP
            currencyCode: 'GBP'
          }
        },
        {
          countryCodes: ['EU', 'DE', 'FR', 'IT', 'ES'], // Eurozone
          unitPrice: {
            amount: '8880', // €88.80 EUR
            currencyCode: 'EUR'
          }
        }
      ]
    );

    // 3. Create Enterprise Product
    const enterpriseProduct = await createProduct(
      'Enterprise',
      'For teams and businesses. 10 team members, 50 stored images per member, 200 generations per member, team management dashboard, and dedicated account manager.',
      'saas'
    );

    // Create Enterprise Monthly Price
    const enterpriseMonthlyPrice = await createPrice(
      enterpriseProduct.id,
      'Enterprise Plan - Monthly',
      {
        amount: '10000', // $100.00 USD
        currencyCode: 'USD'
      },
      {
        interval: 'month',
        frequency: 1
      },
      [
        // Regional pricing for monthly
        {
          countryCodes: ['BD'], // Bangladesh
          unitPrice: {
            amount: '1000000', // 10000 BDT
            currencyCode: 'BDT'
          }
        },
        {
          countryCodes: ['IN'], // India
          unitPrice: {
            amount: '835000', // 8350 INR
            currencyCode: 'INR'
          }
        },
        {
          countryCodes: ['GB'], // United Kingdom
          unitPrice: {
            amount: '7950', // £79.50 GBP
            currencyCode: 'GBP'
          }
        },
        {
          countryCodes: ['EU', 'DE', 'FR', 'IT', 'ES'], // Eurozone
          unitPrice: {
            amount: '9250', // €92.50 EUR
            currencyCode: 'EUR'
          }
        }
      ]
    );

    // Create Enterprise Yearly Price (20% discount)
    const enterpriseYearlyPrice = await createPrice(
      enterpriseProduct.id,
      'Enterprise Plan - Yearly (20% off)',
      {
        amount: '96000', // $960.00 USD (20% discount)
        currencyCode: 'USD'
      },
      {
        interval: 'year',
        frequency: 1
      },
      [
        // Regional pricing for yearly
        {
          countryCodes: ['BD'], // Bangladesh
          unitPrice: {
            amount: '9600000', // 96000 BDT
            currencyCode: 'BDT'
          }
        },
        {
          countryCodes: ['IN'], // India
          unitPrice: {
            amount: '8016000', // 80160 INR
            currencyCode: 'INR'
          }
        },
        {
          countryCodes: ['GB'], // United Kingdom
          unitPrice: {
            amount: '76320', // £763.20 GBP
            currencyCode: 'GBP'
          }
        },
        {
          countryCodes: ['EU', 'DE', 'FR', 'IT', 'ES'], // Eurozone
          unitPrice: {
            amount: '88800', // €888.00 EUR
            currencyCode: 'EUR'
          }
        }
      ]
    );

    // Generate output for the paddle.ts file
    const productData = {
      payAsYouGo: {
        productId: payAsYouGoProduct.id,
        priceId: payAsYouGoPrice.id,
      },
      pro: {
        productId: proProduct.id,
        monthlyPriceId: proMonthlyPrice.id,
        yearlyPriceId: proYearlyPrice.id,
      },
      enterprise: {
        productId: enterpriseProduct.id,
        monthlyPriceId: enterpriseMonthlyPrice.id,
        yearlyPriceId: enterpriseYearlyPrice.id,
      },
    };

    console.log('\n🎉 All products and prices created successfully!');
    console.log('\n📋 Product and Price IDs:');
    console.log(JSON.stringify(productData, null, 2));

    console.log('\n📝 Update your paddle.ts file with these IDs:');
    console.log(`export const paddleProducts = ${JSON.stringify(productData, null, 2)};`);

    return productData;

  } catch (error) {
    console.error('❌ Failed to create products and prices:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  createPaddleProductsAndPrices()
    .then(() => {
      console.log('\n✅ Script completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createPaddleProductsAndPrices };
