import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// The bucket name we'll use for storing images
export const STORAGE_BUCKET = 'images';

// Function to check if we can access the storage
export const checkStorageAccess = async () => {
  console.log('Checking Supabase storage access...');
  try {
    // Try to list files in the bucket to check if we have access
    const { data, error } = await supabase.storage
      .from(STORAGE_BUCKET)
      .list();
    
    if (error) {
      // If the error is about the bucket not existing
      if (error.message.includes('The resource was not found')) {
        console.error(`Bucket '${STORAGE_BUCKET}' does not exist. Please create it in the Supabase dashboard.`);
        throw new Error(`Bucket '${STORAGE_BUCKET}' does not exist. Please create it in the Supabase dashboard.`);
      }
      
      console.error('Error accessing storage:', error);
      throw error;
    }
    
    console.log('Storage access check successful');
    return true;
  } catch (error) {
    console.error('Error checking storage access:', error);
    throw error;
  }
};