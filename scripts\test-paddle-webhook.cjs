const https = require('https');

// Test data mimicking a Paddle webhook transaction.completed event
const testWebhookData = {
  event_type: 'transaction.completed',
  event_id: 'evt_test_01234567890',
  data: {
    id: 'txn_test_01234567890',
    customer_id: 'ctm_test_01234567890',
    currency_code: 'USD',
    items: [
      {
        price: {
          id: 'pri_01jxbve4kcgjvmq2jk65bcjw1w' // Pro Monthly price ID
        },
        quantity: 1
      }
    ],
    details: {
      totals: {
        total: '1000' // $10.00 in cents
      }
    },
    custom_data: {
      userId: 'test-user-123',
      plan: 'Pro',
      billingPeriod: 'monthly'
    }
  }
};

// Function to test the webhook
function testPaddleWebhook() {
  const data = JSON.stringify(testWebhookData);

  const options = {
    hostname: 'us-central1-product-img-2-ecom.cloudfunctions.net',
    port: 443,
    path: '/handlePaddleWebhook',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length,
      'Paddle-Signature': 'test-signature' // In production, this would be a real signature
    },
  };

  const req = https.request(options, (res) => {
    console.log(`Webhook Response Status: ${res.statusCode}`);
    console.log('Response Headers:', res.headers);

    let responseBody = '';
    res.on('data', (chunk) => {
      responseBody += chunk;
    });

    res.on('end', () => {
      console.log('Response Body:', responseBody);
      
      if (res.statusCode === 200) {
        console.log('✅ Webhook test successful!');
      } else {
        console.log('❌ Webhook test failed!');
      }
    });
  });

  req.on('error', (error) => {
    console.error('Request error:', error);
  });

  // Send the test data
  req.write(data);
  req.end();
}

console.log('Testing Paddle webhook integration...');
console.log('Webhook URL: https://us-central1-product-img-2-ecom.cloudfunctions.net/handlePaddleWebhook');
console.log('Test Data:', JSON.stringify(testWebhookData, null, 2));
console.log('\nSending test webhook...\n');

testPaddleWebhook();
