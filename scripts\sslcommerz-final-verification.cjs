// SSLCOMMERZ Final Verification Script
// This script performs a comprehensive verification of SSLCOMMERZ setup

const fs = require('fs');
const path = require('path');
const https = require('https');

// Test webhook URLs accessibility
async function testWebhookUrls() {
  console.log('🔗 Testing Webhook URL Accessibility');
  console.log('===================================\n');

  const baseUrl = 'https://us-central1-product-img-2-ecom.cloudfunctions.net';
  const webhookEndpoints = [
    '/handleSSLCommerzSuccess',
    '/handleSSLCommerzFail', 
    '/handleSSLCommerzCancel',
    '/handleSSLCommerzIPN'
  ];

  const results = [];

  for (const endpoint of webhookEndpoints) {
    const url = baseUrl + endpoint;
    console.log(`Testing: ${url}`);
    
    try {
      const result = await testUrl(url);
      console.log(`✅ ${endpoint}: Accessible`);
      results.push({ endpoint, status: 'accessible', url });
    } catch (error) {
      console.log(`❌ ${endpoint}: ${error.message}`);
      results.push({ endpoint, status: 'error', error: error.message, url });
    }
  }

  console.log('\n📊 Webhook Accessibility Summary:');
  const accessible = results.filter(r => r.status === 'accessible').length;
  const total = results.length;
  console.log(`${accessible}/${total} webhook endpoints accessible`);

  if (accessible === total) {
    console.log('✅ All webhook URLs are accessible');
  } else {
    console.log('⚠️  Some webhook URLs may not be accessible');
    console.log('   This could cause payment processing issues');
  }

  return results;
}

// Test URL accessibility
function testUrl(url) {
  return new Promise((resolve, reject) => {
    const request = https.get(url, (response) => {
      // Any response (even 404) means the URL is accessible
      resolve({ statusCode: response.statusCode, accessible: true });
    });

    request.on('error', (error) => {
      reject(error);
    });

    request.setTimeout(10000, () => {
      request.destroy();
      reject(new Error('Timeout'));
    });
  });
}

// Verify Firebase Functions deployment
async function verifyFunctionsDeployment() {
  console.log('\n🔧 Verifying Firebase Functions Deployment');
  console.log('==========================================\n');

  // Check if functions directory exists
  const functionsDir = path.join(__dirname, '..', 'functions');
  if (!fs.existsSync(functionsDir)) {
    console.log('❌ Functions directory not found');
    return false;
  }

  // Check if package.json exists
  const packageJsonPath = path.join(functionsDir, 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    console.log('❌ Functions package.json not found');
    return false;
  }

  // Check if required dependencies are installed
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const dependencies = packageJson.dependencies || {};
    
    const requiredDeps = ['sslcommerz-lts', 'firebase-functions', 'firebase-admin'];
    const missingDeps = requiredDeps.filter(dep => !dependencies[dep]);
    
    if (missingDeps.length > 0) {
      console.log('❌ Missing required dependencies:', missingDeps.join(', '));
      return false;
    }
    
    console.log('✅ All required dependencies present');
  } catch (error) {
    console.log('❌ Error reading package.json:', error.message);
    return false;
  }

  // Check if SSLCOMMERZ functions exist
  const sslcommerzFunctionsPath = path.join(functionsDir, 'sslcommerz-functions.js');
  if (!fs.existsSync(sslcommerzFunctionsPath)) {
    console.log('❌ SSLCOMMERZ functions file not found');
    return false;
  }

  console.log('✅ SSLCOMMERZ functions file exists');

  // Check if index.js exports SSLCOMMERZ functions
  const indexPath = path.join(functionsDir, 'index.js');
  if (fs.existsSync(indexPath)) {
    const indexContent = fs.readFileSync(indexPath, 'utf8');
    const sslcommerzExports = [
      'initSSLCommerzPayment',
      'handleSSLCommerzSuccess',
      'handleSSLCommerzFail',
      'handleSSLCommerzCancel',
      'handleSSLCommerzIPN'
    ];
    
    const missingExports = sslcommerzExports.filter(exp => !indexContent.includes(exp));
    
    if (missingExports.length > 0) {
      console.log('❌ Missing function exports:', missingExports.join(', '));
      return false;
    }
    
    console.log('✅ All SSLCOMMERZ functions exported');
  }

  return true;
}

// Check environment consistency
function checkEnvironmentConsistency() {
  console.log('\n🔍 Environment Consistency Check');
  console.log('================================\n');

  // Read frontend .env
  const frontendEnvPath = path.join(__dirname, '..', '.env');
  const frontendEnv = fs.readFileSync(frontendEnvPath, 'utf8');
  
  // Read backend .env
  const backendEnvPath = path.join(__dirname, '..', 'functions', '.env');
  const backendEnv = fs.readFileSync(backendEnvPath, 'utf8');

  // Parse values
  const frontendStoreId = frontendEnv.match(/VITE_SSLCOMMERZ_STORE_ID="?([^"\n]+)"?/)?.[1];
  const frontendIsLive = frontendEnv.match(/VITE_SSLCOMMERZ_IS_LIVE=([^\n]+)/)?.[1] === 'true';
  
  const backendStoreId = backendEnv.match(/SSLCOMMERZ_STORE_ID="?([^"\n]+)"?/)?.[1];
  const backendIsLive = backendEnv.match(/SSLCOMMERZ_IS_LIVE=([^\n]+)/)?.[1] === 'true';

  console.log('Frontend Configuration:');
  console.log(`  Store ID: ${frontendStoreId}`);
  console.log(`  Is Live: ${frontendIsLive}`);
  
  console.log('\nBackend Configuration:');
  console.log(`  Store ID: ${backendStoreId}`);
  console.log(`  Is Live: ${backendIsLive}`);

  const storeIdMatch = frontendStoreId === backendStoreId;
  const isLiveMatch = frontendIsLive === backendIsLive;

  console.log('\nConsistency Check:');
  console.log(`  Store ID Match: ${storeIdMatch ? '✅' : '❌'}`);
  console.log(`  Environment Match: ${isLiveMatch ? '✅' : '❌'}`);

  const isConsistent = storeIdMatch && isLiveMatch;
  console.log(`\nOverall: ${isConsistent ? '✅ Consistent' : '❌ Inconsistent'}`);

  return isConsistent;
}

// Run comprehensive verification
async function runComprehensiveVerification() {
  console.log('🚀 SSLCOMMERZ COMPREHENSIVE VERIFICATION');
  console.log('========================================\n');

  const results = {
    environmentConsistency: false,
    functionsDeployment: false,
    webhookAccessibility: false,
    overallStatus: false
  };

  // Step 1: Environment consistency
  results.environmentConsistency = checkEnvironmentConsistency();

  // Step 2: Functions deployment
  results.functionsDeployment = await verifyFunctionsDeployment();

  // Step 3: Webhook accessibility
  const webhookResults = await testWebhookUrls();
  results.webhookAccessibility = webhookResults.every(r => r.status === 'accessible');

  // Final assessment
  results.overallStatus = results.environmentConsistency && 
                         results.functionsDeployment && 
                         results.webhookAccessibility;

  console.log('\n🎯 FINAL VERIFICATION RESULTS');
  console.log('============================\n');

  console.log(`Environment Consistency: ${results.environmentConsistency ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Functions Deployment: ${results.functionsDeployment ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Webhook Accessibility: ${results.webhookAccessibility ? '✅ PASS' : '❌ FAIL'}`);

  console.log(`\n🏆 OVERALL STATUS: ${results.overallStatus ? '✅ READY FOR PRODUCTION' : '❌ NEEDS ATTENTION'}`);

  if (results.overallStatus) {
    console.log('\n🎉 Congratulations! Your SSLCOMMERZ integration is ready for production.');
    console.log('\n📋 Next Steps:');
    console.log('1. Deploy your application to production');
    console.log('2. Test with a small real payment');
    console.log('3. Monitor payment processing');
    console.log('4. Set up payment monitoring and alerts');
  } else {
    console.log('\n⚠️  Please address the failed checks before going to production.');
    console.log('\n🔧 Recommended Actions:');
    
    if (!results.environmentConsistency) {
      console.log('- Run: node scripts/sslcommerz-environment-manager.cjs fix');
    }
    
    if (!results.functionsDeployment) {
      console.log('- Deploy Firebase Functions: cd functions && firebase deploy --only functions');
    }
    
    if (!results.webhookAccessibility) {
      console.log('- Ensure Firebase Functions are deployed and accessible');
      console.log('- Check Firebase project configuration');
    }
  }

  return results;
}

// Main function
async function main() {
  const [,, command] = process.argv;
  
  switch (command) {
    case 'webhooks':
      await testWebhookUrls();
      break;
      
    case 'functions':
      await verifyFunctionsDeployment();
      break;
      
    case 'environment':
      checkEnvironmentConsistency();
      break;
      
    case 'all':
    default:
      await runComprehensiveVerification();
      break;
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Verification failed:', error.message);
    process.exit(1);
  });
}

module.exports = { 
  testWebhookUrls, 
  verifyFunctionsDeployment, 
  checkEnvironmentConsistency, 
  runComprehensiveVerification 
};
