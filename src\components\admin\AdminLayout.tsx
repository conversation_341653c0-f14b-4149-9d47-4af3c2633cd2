import { Outlet, NavLink, useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import { Users, Package, Home, LogOut, Settings, Tag, Percent } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { auth } from '@/lib/firebase';
import { signOut } from 'firebase/auth';
import { toast } from 'react-hot-toast';

export function AdminLayout() {
  const navigate = useNavigate();

  // Check if user is authenticated as admin
  useEffect(() => {
    const checkAdminAuth = async () => {
      const adminEmail = import.meta.env.VITE_ADMIN_EMAIL;

      // Check if current user is admin
      if (!auth.currentUser || auth.currentUser.email !== adminEmail) {
        toast.error('Admin authentication required');
        navigate('/admin/login');
      }
    };

    checkAdminAuth();

    // Set up auth state listener
    const unsubscribe = auth.onAuthStateChanged((user) => {
      const adminEmail = import.meta.env.VITE_ADMIN_EMAIL;
      if (!user || user.email !== adminEmail) {
        navigate('/admin/login');
      }
    });

    return () => unsubscribe();
  }, [navigate]);

  const handleLogout = async () => {
    try {
      // Sign out from Firebase
      await signOut(auth);
      // Clear admin authentication from localStorage
      localStorage.removeItem('adminAuthenticated');
      toast.success('Logged out successfully');
      navigate('/admin/login');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Error logging out');
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-900">
      <nav className="w-64 bg-gray-800 border-r border-gray-700 p-4 space-y-4 relative">
        <div className="text-2xl font-bold bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent p-4">
          Admin Panel
        </div>

        <NavLink
          to="/admin/dashboard"
          end
          className={({ isActive }) =>
            `flex items-center space-x-2 p-2 rounded-lg ${isActive
              ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white'
              : 'text-gray-300 hover:bg-gray-700'
            }`
          }
        >
          <Home size={20} />
          <span>Dashboard</span>
        </NavLink>

        <NavLink
          to="/admin/users"
          className={({ isActive }) =>
            `flex items-center space-x-2 p-2 rounded-lg ${isActive
              ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white'
              : 'text-gray-300 hover:bg-gray-700'
            }`
          }
        >
          <Users size={20} />
          <span>User Management</span>
        </NavLink>

        <NavLink
          to="/admin/packages"
          className={({ isActive }) =>
            `flex items-center space-x-2 p-2 rounded-lg ${isActive
              ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white'
              : 'text-gray-300 hover:bg-gray-700'
            }`
          }
        >
          <Package size={20} />
          <span>Package Management</span>
        </NavLink>

        <NavLink
          to="/admin/settings"
          className={({ isActive }) =>
            `flex items-center space-x-2 p-2 rounded-lg ${isActive
              ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white'
              : 'text-gray-300 hover:bg-gray-700'
            }`
          }
        >
          <Settings size={20} />
          <span>Settings</span>
        </NavLink>

        <NavLink
          to="/admin/user-limits"
          className={({ isActive }) =>
            `flex items-center space-x-2 p-2 rounded-lg ${isActive
              ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white'
              : 'text-gray-300 hover:bg-gray-700'
            }`
          }
        >
          <Settings size={20} />
          <span>Free User Limit Settings</span>
        </NavLink>

        <NavLink
          to="/admin/pay-as-you-go-user-limits"
          className={({ isActive }) =>
            `flex items-center space-x-2 p-2 rounded-lg ${isActive
              ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white'
              : 'text-gray-300 hover:bg-gray-700'
            }`
          }
        >
          <Settings size={20} />
          <span>Pay-As-You-Go User Limit Settings</span>
        </NavLink>

        <NavLink
          to="/admin/pro-user-limits"
          className={({ isActive }) =>
            `flex items-center space-x-2 p-2 rounded-lg ${isActive
              ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white'
              : 'text-gray-300 hover:bg-gray-700'
            }`
          }
        >
          <Settings size={20} />
          <span>Pro User Limit Settings</span>
        </NavLink>

        <NavLink
          to="/admin/enterprise-admin-owner-limits"
          className={({ isActive }) =>
            `flex items-center space-x-2 p-2 rounded-lg ${isActive
              ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white'
              : 'text-gray-300 hover:bg-gray-700'
            }`
          }
        >
          <Settings size={20} />
          <span>Enterprise Admin Owner Limit Settings</span>
        </NavLink>

        <NavLink
          to="/admin/custom-limit-settings"
          className={({ isActive }) =>
            `flex items-center space-x-2 p-2 rounded-lg ${isActive
              ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white'
              : 'text-gray-300 hover:bg-gray-700'
            }`
          }
        >
          <Settings size={20} />
          <span>Custom Limit Settings</span>
        </NavLink>

        <NavLink
          to="/admin/coupon-codes"
          className={({ isActive }) =>
            `flex items-center space-x-2 p-2 rounded-lg ${isActive
              ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white'
              : 'text-gray-300 hover:bg-gray-700'
            }`
          }
        >
          <Tag size={20} />
          <span>Coupon Code Management</span>
        </NavLink>

        <div className="absolute bottom-4 left-4 right-4">
          <Button
            variant="outline"
            size="sm"
            className="w-full flex items-center justify-center space-x-2 border-gray-600 text-gray-300 hover:bg-gray-700"
            onClick={handleLogout}
          >
            <LogOut size={16} />
            <span>Logout</span>
          </Button>
        </div>
      </nav>

      <main className="flex-1 p-8">
        <Outlet />
      </main>
    </div>
  );
}

export default AdminLayout;