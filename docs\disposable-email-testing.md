# Disposable Email Testing Guide

## Test Email Addresses

### Known Disposable Email Domains (for testing)
Use these email addresses to test the disposable email detection:

1. **10minutemail.com**
   - <EMAIL>
   - <EMAIL>

2. **guerrillamail.com**
   - <EMAIL>
   - <EMAIL>

3. **temp-mail.org**
   - <EMAIL>
   - <EMAIL>

4. **mailinator.com**
   - <EMAIL>
   - <EMAIL>

5. **throwaway.email**
   - <EMAIL>
   - <EMAIL>

### Valid Email Domains (should NOT be blocked)
Use these to verify legitimate emails work:

1. **gmail.com**
   - <EMAIL>
   - <EMAIL>

2. **outlook.com**
   - <EMAIL>
   - <EMAIL>

3. **yahoo.com**
   - <EMAIL>
   - <EMAIL>

4. **company.com**
   - <EMAIL>
   - <EMAIL>

## Testing Steps

### 1. Manual Testing
1. Go to the signup page (`/auth?mode=signup`)
2. Enter a disposable email address
3. Wait for validation (1 second delay)
4. Verify warning message appears
5. Verify signup button is disabled
6. Try to submit form - should show error toast

### 2. Valid Email Testing
1. Enter a legitimate email address
2. Verify no warning appears
3. Verify signup button remains enabled
4. Form should submit normally

### 3. Edge Case Testing
1. **Incomplete emails**: "test@" should not trigger validation
2. **No domain**: "test" should not trigger validation
3. **Mode switching**: Switch to login, warning should disappear
4. **Fast typing**: Type quickly, only final email should be validated

### 4. Network Testing
1. **Offline mode**: Disable network, should not block signup
2. **Slow network**: Simulate slow connection, should show loading state
3. **API failure**: Block the API domain, should fail gracefully

## Expected Behavior

### ✅ Disposable Email Flow
1. User types disposable email
2. After 1 second delay, API call is made
3. "Validating email..." message appears briefly
4. Warning message appears with red color and alert icon
5. Signup button becomes disabled
6. Form submission shows error toast

### ✅ Valid Email Flow
1. User types legitimate email
2. After 1 second delay, API call is made
3. "Validating email..." message appears briefly
4. No warning message appears
5. Signup button remains enabled
6. Form can be submitted normally

### ✅ Error Handling Flow
1. API call fails (network issue, service down)
2. No warning message appears
3. Form submission continues normally
4. User is not blocked from signing up

## Console Logs
Check browser console for these messages:

- `"Disposable email detected: [email]"` - When disposable email found
- `"Failed to check disposable email, allowing registration"` - When API call fails
- `"Error checking disposable email: [error]"` - When exception occurs

## Performance Verification

### API Call Optimization
1. Type "t" - no API call
2. Type "test@" - no API call
3. Type "test@gmail" - no API call
4. Type "<EMAIL>" - API call after 1 second
5. Delete and retype - previous call cancelled, new call made

### Debouncing Verification
1. Type email quickly - only one API call at the end
2. Type, pause, type more - only final version validated
3. Switch between login/signup - validation state reset

## Troubleshooting Common Issues

### Issue: Button Always Disabled
**Cause**: Validation state not reset properly
**Fix**: Check that `setIsDisposableEmail(false)` is called on email change

### Issue: No Validation Happening
**Cause**: Email format check failing
**Fix**: Verify email contains both "@" and "." characters

### Issue: Multiple API Calls
**Cause**: Debouncing not working
**Fix**: Check that timeout is being cleared properly

### Issue: False Positives
**Cause**: API returning unexpected results
**Fix**: Check API response format and parsing logic

## Test Report Template

```
Date: ___________
Tester: ___________

Disposable Email Detection Test Results:

[ ] Disposable emails detected correctly
[ ] Warning message displays properly
[ ] Button disabled for disposable emails
[ ] Form submission blocked
[ ] Valid emails pass validation
[ ] API failures handled gracefully
[ ] Debouncing works correctly
[ ] Mode switching resets state
[ ] Performance is acceptable

Issues Found:
- ___________
- ___________

Notes:
___________
```
