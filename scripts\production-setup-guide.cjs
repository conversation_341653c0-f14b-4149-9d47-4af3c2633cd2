// Production Setup Guide for Paddle Integration
// This script helps you set up and verify production environment for Paddle

const https = require('https');
require('dotenv').config();

// Environment configuration
const ENVIRONMENTS = {
  production: {
    apiKey: process.env.VITE_PADDLE_API_KEY_PRODUCTION || process.env.VITE_PADDLE_API_KEY,
    clientToken: process.env.VITE_PADDLE_CLIENT_SIDE_TOKEN_PRODUCTION || process.env.VITE_PADDLE_CLIENT_SIDE_TOKEN,
    apiEndpoint: 'https://api.paddle.com',
    name: 'Production'
  },
  sandbox: {
    apiKey: process.env.VITE_PADDLE_API_KEY_SANDBOX,
    clientToken: process.env.VITE_PADDLE_CLIENT_SIDE_TOKEN_SANDBOX,
    apiEndpoint: 'https://sandbox-api.paddle.com',
    name: 'Sandbox'
  }
};

// Make API request to Paddle
function makeRequest(environment, method, path, data = null) {
  const config = ENVIRONMENTS[environment];
  if (!config) {
    throw new Error(`Unknown environment: ${environment}`);
  }

  if (!config.apiKey) {
    throw new Error(`API key not configured for ${environment} environment`);
  }

  const url = new URL(path, config.apiEndpoint);
  
  const options = {
    hostname: url.hostname,
    port: 443,
    path: url.pathname + url.search,
    method: method,
    headers: {
      'Authorization': `Bearer ${config.apiKey}`,
      'Content-Type': 'application/json',
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(parsedData);
          } else {
            reject(new Error(`API Error ${res.statusCode}: ${parsedData.error?.detail || parsedData.error?.message || 'Unknown error'}`));
          }
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => reject(error));

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Validate environment configuration
function validateEnvironment(environment) {
  const config = ENVIRONMENTS[environment];
  console.log(`🔧 Validating ${config.name} environment configuration...`);
  
  let isValid = true;
  
  // Check API Key
  if (!config.apiKey) {
    console.log(`❌ Missing API key for ${environment}`);
    isValid = false;
  } else {
    // Paddle API keys: pdl_live_apikey_* for production, pdl_sdbx_apikey_* for sandbox
    const keyType = config.apiKey.includes('_live_') ? 'LIVE' : 'TEST';
    const expectedType = environment === 'production' ? 'LIVE' : 'TEST';
    
    if (keyType === expectedType) {
      console.log(`✅ API key type: ${keyType} (correct for ${environment})`);
    } else {
      console.log(`❌ API key type: ${keyType} (expected ${expectedType} for ${environment})`);
      isValid = false;
    }
  }
  
  // Check Client Token
  if (!config.clientToken) {
    console.log(`❌ Missing client token for ${environment}`);
    isValid = false;
  } else {
    const tokenType = config.clientToken.startsWith('live_') ? 'LIVE' : 'TEST';
    const expectedType = environment === 'production' ? 'LIVE' : 'TEST';
    
    if (tokenType === expectedType) {
      console.log(`✅ Client token type: ${tokenType} (correct for ${environment})`);
    } else {
      console.log(`❌ Client token type: ${tokenType} (expected ${expectedType} for ${environment})`);
      isValid = false;
    }
  }
  
  console.log(`📊 ${config.name} validation: ${isValid ? '✅ PASSED' : '❌ FAILED'}\n`);
  return isValid;
}

// Test environment connection
async function testEnvironment(environment) {
  console.log(`🔍 Testing ${ENVIRONMENTS[environment].name} environment...`);
  
  try {
    const response = await makeRequest(environment, 'GET', '/products?per_page=1');
    console.log(`✅ ${ENVIRONMENTS[environment].name} API connection successful`);
    console.log(`📊 Found ${response.meta?.pagination?.total || 0} products`);
    return true;
  } catch (error) {
    console.error(`❌ ${ENVIRONMENTS[environment].name} API connection failed:`, error.message);
    return false;
  }
}

// List products for environment
async function listProducts(environment) {
  console.log(`📦 Listing products for ${ENVIRONMENTS[environment].name}...`);
  
  try {
    const response = await makeRequest(environment, 'GET', '/products');
    const products = response.data || [];
    
    console.log(`Found ${products.length} products:`);
    products.forEach(product => {
      console.log(`  - ${product.name} (${product.id})`);
      console.log(`    Status: ${product.status}`);
      console.log(`    Type: ${product.type}`);
      console.log('');
    });
    
    return products;
  } catch (error) {
    console.error(`❌ Failed to list products:`, error.message);
    return [];
  }
}

// Production readiness check
async function productionReadinessCheck() {
  console.log('🚀 PRODUCTION READINESS CHECK');
  console.log('================================\n');
  
  let allChecks = true;
  
  // 1. Validate both environments
  console.log('1️⃣ Environment Configuration Check:');
  const prodValid = validateEnvironment('production');
  const sandboxValid = validateEnvironment('sandbox');
  
  if (!prodValid || !sandboxValid) {
    allChecks = false;
  }
  
  // 2. Test API connections
  console.log('2️⃣ API Connection Test:');
  const prodConnection = await testEnvironment('production');
  const sandboxConnection = await testEnvironment('sandbox');
  console.log('');
  
  if (!prodConnection || !sandboxConnection) {
    allChecks = false;
  }
  
  // 3. Check products exist
  console.log('3️⃣ Product Configuration Check:');
  const prodProducts = await listProducts('production');
  const sandboxProducts = await listProducts('sandbox');
  
  if (prodProducts.length === 0) {
    console.log('❌ No products found in production environment');
    allChecks = false;
  } else {
    console.log(`✅ Found ${prodProducts.length} products in production`);
  }
  
  if (sandboxProducts.length === 0) {
    console.log('❌ No products found in sandbox environment');
    allChecks = false;
  } else {
    console.log(`✅ Found ${sandboxProducts.length} products in sandbox`);
  }
  
  console.log('\n🎯 FINAL RESULT:');
  if (allChecks) {
    console.log('✅ ALL CHECKS PASSED - Ready for production!');
    console.log('\n📋 Next Steps:');
    console.log('1. Set VITE_PADDLE_ENVIRONMENT=production in your .env file');
    console.log('2. Deploy your application');
    console.log('3. Test a small transaction in production');
    console.log('4. Monitor webhook delivery');
  } else {
    console.log('❌ SOME CHECKS FAILED - Please fix issues before going to production');
  }
  
  return allChecks;
}

// Main function
async function main() {
  const [,, command] = process.argv;
  
  switch (command) {
    case 'check':
    case 'readiness':
      await productionReadinessCheck();
      break;
      
    case 'validate-prod':
      validateEnvironment('production');
      break;
      
    case 'test-prod':
      await testEnvironment('production');
      break;
      
    case 'list-prod':
      await listProducts('production');
      break;
      
    default:
      console.log('🚀 Production Setup Guide for Paddle');
      console.log('\nUsage: node scripts/production-setup-guide.cjs [command]');
      console.log('\nCommands:');
      console.log('  check             - Run complete production readiness check');
      console.log('  validate-prod     - Validate production environment only');
      console.log('  test-prod         - Test production API connection');
      console.log('  list-prod         - List production products');
      console.log('\nExample:');
      console.log('  node scripts/production-setup-guide.cjs check');
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  });
}

module.exports = { validateEnvironment, testEnvironment, listProducts, productionReadinessCheck };
