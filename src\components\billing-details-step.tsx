import { useState, useEffect, useRef } from 'react';
import { Save, ArrowLeft, ArrowRight, Check, X, Edit, CheckCircle, Search, ChevronDown, ChevronUp, Tag, Percent, AlertCircle, ExternalLink } from 'lucide-react';
import { db } from '@/lib/firebase';
import { collection, query, where, getDocs, updateDoc, doc, setDoc, addDoc, Timestamp } from 'firebase/firestore';
import { useStore } from '@/lib/store';
import { toast } from 'react-hot-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { countries } from '@/lib/countries';
import { useOnClickOutside } from '@/hooks/use-on-click-outside';
import { useCountry } from '@/lib/countryContext';
import { CountryIndicator } from '@/components/ui/country-indicator';

// Add CSS for animations
import './billing-details.css';

interface BillingDetailsStepProps {
  isOpen: boolean;
  onClose: () => void;
  selectedPlan: string;
  billingPeriod: 'monthly' | 'yearly';
  onProceedToCheckout: (discountAmount?: number, couponCode?: string, paymentGateway?: string) => void;
  onBack: () => void;
}

interface BillingInfo {
  fullName: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  paymentGateway?: string;
}

export function BillingDetailsStep({
  isOpen,
  onClose,
  selectedPlan,
  billingPeriod,
  onProceedToCheckout,
  onBack
}: BillingDetailsStepProps) {
  const { user } = useStore();
  const { country: selectedCountry, formatCurrency } = useCountry();

  // Determine default payment gateway based on country
  const getDefaultPaymentGateway = (country: string) => {
    console.log('Setting default payment gateway for country:', country);
    return country === 'Bangladesh' ? 'SSLCOMMERZ' : 'Paddle';
  };

  const [billingInfo, setBillingInfo] = useState<BillingInfo>({
    fullName: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: selectedCountry, // Initialize with the user's selected country
    paymentGateway: getDefaultPaymentGateway(selectedCountry) // Set default payment gateway based on country
  });

  // Track available payment gateways based on country
  const [availablePaymentGateways, setAvailablePaymentGateways] = useState<string[]>(
    selectedCountry === 'Bangladesh'
      ? ['SSLCOMMERZ', 'bKash (Coming Soon)', 'Nagad (Coming Soon)']
      : ['Paddle']
  );

  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [hasBillingInfo, setHasBillingInfo] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isCountryDropdownOpen, setIsCountryDropdownOpen] = useState(false);
  const [countrySearch, setCountrySearch] = useState('');
  const countryDropdownRef = useRef<HTMLDivElement>(null);

  // Coupon code states
  const [couponCode, setCouponCode] = useState('');
  const [validatingCoupon, setValidatingCoupon] = useState(false);
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discountType: 'percentage' | 'fixed';
    discountValue: number;
  } | null>(null);
  const [couponError, setCouponError] = useState('');

  // Terms agreement state
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [termsError, setTermsError] = useState('');

  // Payment initialization state
  const [initializingPayment, setInitializingPayment] = useState(false);

  // Helper function to add shake animation to an element
  const addShakeAnimation = (elementId: string) => {
    const element = document.getElementById(elementId);
    if (element) {
      element.classList.add('animate-shake');
      setTimeout(() => {
        element.classList.remove('animate-shake');
      }, 500);
    }
  };

  // Filter countries based on search input
  const filteredCountries = countries.filter(country =>
    country.toLowerCase().includes(countrySearch.toLowerCase())
  );

  // Close dropdown when clicking outside
  useOnClickOutside(countryDropdownRef, () => setIsCountryDropdownOpen(false));

  useEffect(() => {
    if (isOpen && user) {
      fetchBillingDetails();
    }
  }, [isOpen, user]);

  // Update available payment gateways when country changes
  useEffect(() => {
    // Update available payment gateways based on the current country
    const newAvailableGateways = billingInfo.country === 'Bangladesh'
      ? ['SSLCOMMERZ', 'bKash (Coming Soon)', 'Nagad (Coming Soon)']
      : ['Paddle'];

    setAvailablePaymentGateways(newAvailableGateways);

    // If the current payment gateway is not available for the country, update it to the default
    const currentGateway = billingInfo.paymentGateway || '';
    const isCurrentGatewayAvailable = newAvailableGateways.includes(currentGateway) ||
                                     newAvailableGateways.includes(currentGateway.replace(' (Coming Soon)', ''));

    if (!isCurrentGatewayAvailable) {
      const defaultGateway = getDefaultPaymentGateway(billingInfo.country);
      setBillingInfo(prev => ({
        ...prev,
        paymentGateway: defaultGateway
      }));
    }
  }, [billingInfo.country]);

  const fetchBillingDetails = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Fetch billing data from Firestore
      const billingRef = collection(db, 'billingDetails');
      const q = query(billingRef, where('userId', '==', user.id));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const data = querySnapshot.docs[0].data() as Omit<BillingInfo, 'id'>;
        const country = data.country || '';

        // Update available payment gateways based on country
        const newAvailableGateways = country === 'Bangladesh'
          ? ['SSLCOMMERZ', 'bKash (Coming Soon)', 'Nagad (Coming Soon)']
          : ['Paddle'];

        setAvailablePaymentGateways(newAvailableGateways);

        // Set appropriate payment gateway based on country and existing data
        let paymentGateway = data.paymentGateway || getDefaultPaymentGateway(country);

        // If the saved payment gateway is not available for the country, use the default
        if (!newAvailableGateways.includes(paymentGateway) &&
            !newAvailableGateways.includes(paymentGateway.replace(' (Coming Soon)', ''))) {
          paymentGateway = getDefaultPaymentGateway(country);
        }

        setBillingInfo({
          fullName: data.fullName || '',
          address: data.address || '',
          city: data.city || '',
          state: data.state || '',
          zipCode: data.zipCode || '',
          country: country,
          paymentGateway: paymentGateway
        });
        setHasBillingInfo(true);
      } else {
        setHasBillingInfo(false);
      }
    } catch (error) {
      console.error('Error fetching billing details:', error);
      toast.error('Failed to load billing information');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setBillingInfo(prev => ({ ...prev, [name]: value }));
  };

  const handleCountrySelect = (country: string) => {
    // Update available payment gateways based on the selected country
    const newAvailableGateways = country === 'Bangladesh'
      ? ['SSLCOMMERZ', 'bKash (Coming Soon)', 'Nagad (Coming Soon)']
      : ['Paddle'];

    setAvailablePaymentGateways(newAvailableGateways);

    // Get the default payment gateway for the selected country
    const defaultGateway = getDefaultPaymentGateway(country);

    // Update billing info with the new country and appropriate payment gateway
    setBillingInfo(prev => {
      // If the current payment gateway is not available for the new country, use the default
      const currentGateway = prev.paymentGateway || '';
      const isCurrentGatewayAvailable = newAvailableGateways.includes(currentGateway) ||
                                       newAvailableGateways.includes(currentGateway.replace(' (Coming Soon)', ''));

      return {
        ...prev,
        country,
        paymentGateway: isCurrentGatewayAvailable ? currentGateway : defaultGateway
      };
    });
  };

  const handlePaymentGatewaySelect = (gateway: string) => {
    console.log('Selected payment gateway:', gateway);
    setBillingInfo(prev => {
      const updated = { ...prev, paymentGateway: gateway };
      console.log('Updated billing info with payment gateway:', updated);
      return updated;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      console.error('User not authenticated');
      toast.error('User not authenticated. Please log in and try again.');
      return;
    }

    console.log('Submitting billing details for user:', user.id);
    console.log('Current billing info:', billingInfo);

    // Validate form
    if (!billingInfo.fullName.trim()) {
      toast.error('Full name is required');
      return;
    }
    if (!billingInfo.address.trim()) {
      toast.error('Address is required');
      return;
    }
    if (!billingInfo.city.trim()) {
      toast.error('City is required');
      return;
    }
    if (!billingInfo.zipCode.trim()) {
      toast.error('ZIP/Postal code is required');
      return;
    }
    if (!billingInfo.country.trim()) {
      toast.error('Country is required');
      return;
    }

    // Ensure payment gateway is selected
    if (!billingInfo.paymentGateway) {
      console.error('No payment gateway selected');
      toast.error('Please select a payment method');
      return;
    }

    console.log('Selected payment gateway:', billingInfo.paymentGateway);

    setUpdating(true);
    try {
      if (!user) {
        toast.error('User not authenticated');
        return;
      }

      // Find if billing details already exist
      const billingRef = collection(db, 'billingDetails');
      const q = query(billingRef, where('userId', '==', user.id));
      const querySnapshot = await getDocs(q);

      const billingData = {
        userId: user.id,
        ...billingInfo,
        updatedAt: new Date().toISOString()
      };

      if (querySnapshot.empty) {
        // Create new billing details
        await setDoc(doc(collection(db, 'billingDetails')), {
          ...billingData,
          createdAt: new Date().toISOString()
        });
      } else {
        // Update existing billing details
        const docRef = querySnapshot.docs[0].ref;
        await updateDoc(docRef, billingData);
      }

      toast.success('Billing details saved successfully');
      setHasBillingInfo(true);

      // If we're in edit mode, just turn off edit mode
      if (isEditMode) {
        setIsEditMode(false);
      } else {
        // Check if user has agreed to terms before proceeding to checkout
        if (!agreedToTerms) {
          setTermsError('You must agree to the Terms of Service and Privacy Policy to proceed');
          // Add shake animation to the checkbox
          addShakeAnimation('terms-checkbox-container');
          setUpdating(false);
          return;
        }

        setTermsError('');
        // Set loading state for payment initialization
        setInitializingPayment(true);
        // Otherwise, proceed to checkout
        onProceedToCheckout(undefined, undefined, billingInfo.paymentGateway);
      }
    } catch (error) {
      console.error('Error saving billing details:', error);
      toast.error('Failed to save billing details');
    } finally {
      setUpdating(false);
    }
  };

  const handleProceedWithSavedDetails = () => {
    console.log('Proceeding with saved details...');
    console.log('Current billing info:', billingInfo);

    // Check if user has agreed to terms
    if (!agreedToTerms) {
      console.error('User has not agreed to terms');
      setTermsError('You must agree to the Terms of Service and Privacy Policy to proceed');
      // Add shake animation to the checkbox
      addShakeAnimation('terms-checkbox-container');
      return;
    }

    // Check if payment gateway is selected
    if (!billingInfo.paymentGateway) {
      console.error('No payment gateway selected');
      toast.error('Please select a payment method');
      return;
    }

    console.log('Payment gateway selected:', billingInfo.paymentGateway);
    setTermsError('');

    // Set loading state for payment initialization
    setInitializingPayment(true);

    // Calculate discount if coupon is applied
    let discountAmount = undefined;
    let couponCode = undefined;

    if (appliedCoupon) {
      console.log('Applied coupon:', appliedCoupon);

      // Calculate the actual discount amount in USD
      const originalPrice = getPlanPrice();
      if (appliedCoupon.discountType === 'percentage') {
        discountAmount = originalPrice * (appliedCoupon.discountValue / 100);
      } else {
        discountAmount = Math.min(appliedCoupon.discountValue, originalPrice);
      }

      couponCode = appliedCoupon.code;

      console.log('Calculated discount amount:', {
        originalPrice,
        discountType: appliedCoupon.discountType,
        discountValue: appliedCoupon.discountValue,
        calculatedDiscountAmount: discountAmount
      });
    }

    console.log('Proceeding to checkout with:', {
      discountAmount,
      couponCode,
      paymentGateway: billingInfo.paymentGateway
    });

    // Call the onProceedToCheckout function with the appropriate parameters
    onProceedToCheckout(discountAmount, couponCode, billingInfo.paymentGateway);
  };

  // Validate and apply coupon code
  const validateCoupon = async () => {
    if (!couponCode.trim()) {
      setCouponError('Please enter a coupon code');
      addShakeAnimation('couponCode');
      return;
    }

    setValidatingCoupon(true);
    setCouponError('');

    try {
      // Query Firestore for the coupon code
      const couponsRef = collection(db, 'coupons');
      const q = query(couponsRef, where('code', '==', couponCode.toUpperCase()));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        setCouponError('Invalid coupon code');
        setAppliedCoupon(null);
        setValidatingCoupon(false);
        addShakeAnimation('couponCode');
        return;
      }

      const couponData = querySnapshot.docs[0].data();
      const couponId = querySnapshot.docs[0].id;

      // Check if coupon is active
      if (!couponData.isActive) {
        setCouponError('This coupon code is inactive');
        setAppliedCoupon(null);
        setValidatingCoupon(false);
        addShakeAnimation('couponCode');
        return;
      }

      // Check if coupon is expired
      const now = new Date();
      const validFrom = new Date(couponData.validFrom);
      const validUntil = new Date(couponData.validUntil);

      if (now < validFrom || now > validUntil) {
        setCouponError('This coupon code has expired');
        setAppliedCoupon(null);
        setValidatingCoupon(false);
        addShakeAnimation('couponCode');
        return;
      }

      // Check if coupon has reached maximum uses
      if (couponData.currentUses >= couponData.maxUses) {
        setCouponError('This coupon code has reached its usage limit');
        setAppliedCoupon(null);
        setValidatingCoupon(false);
        addShakeAnimation('couponCode');
        return;
      }

      // Check if coupon is applicable to the selected plan
      if (!couponData.applicablePlans.includes(selectedPlan)) {
        setCouponError(`This coupon is not valid for the ${selectedPlan} plan`);
        setAppliedCoupon(null);
        setValidatingCoupon(false);
        addShakeAnimation('couponCode');
        return;
      }

      // Check minimum purchase amount if applicable
      const planPrice = getPlanPrice();
      if (couponData.minPurchaseAmount && planPrice < couponData.minPurchaseAmount) {
        setCouponError(`This coupon requires a minimum purchase of ${formatCurrency(couponData.minPurchaseAmount)}`);
        setAppliedCoupon(null);
        setValidatingCoupon(false);
        addShakeAnimation('couponCode');
        return;
      }

      // Apply the coupon
      setAppliedCoupon({
        code: couponData.code,
        discountType: couponData.discountType,
        discountValue: couponData.discountValue
      });

      // Record coupon usage (will be finalized at checkout)
      if (user) {
        try {
          await addDoc(collection(db, 'couponUsage'), {
            couponId,
            couponCode: couponData.code,
            userId: user.id,
            userEmail: user.email,
            plan: selectedPlan,
            billingPeriod,
            appliedAt: Timestamp.now(),
            completed: false // Will be set to true when checkout is completed
          });
        } catch (error) {
          console.error('Error recording coupon usage:', error);
          // Don't block the user if this fails
        }
      }

      // Show success message with discount information
      const discountMessage = couponData.discountType === 'percentage'
        ? `${couponData.discountValue}% off`
        : `${formatCurrency(couponData.discountValue)} off`;

      toast.success(
        <div className="flex flex-col">
          <span className="font-medium">Coupon applied successfully!</span>
          <span className="text-sm">{discountMessage} your order</span>
        </div>,
        { duration: 5000 }
      );
    } catch (error) {
      console.error('Error validating coupon:', error);
      setCouponError('Error validating coupon. Please try again.');
      setAppliedCoupon(null);
      addShakeAnimation('couponCode');

      // Show a more detailed error toast
      toast.error(
        <div className="flex flex-col">
          <span className="font-medium">Coupon validation failed</span>
          <span className="text-sm">There was a problem processing your coupon</span>
        </div>
      );
    } finally {
      setValidatingCoupon(false);
    }
  };

  // Remove applied coupon
  const removeCoupon = () => {
    setAppliedCoupon(null);
    setCouponCode('');
    setCouponError('');
  };

  // Get numeric price for calculations
  const getPlanPrice = (): number => {
    switch (selectedPlan) {
      case 'Pay-As-You-Go':
        return 2;
      case 'Pro':
        return billingPeriod === 'monthly' ? 10 : 96;
      case 'Enterprise':
        return billingPeriod === 'monthly' ? 100 : 960;
      default:
        return 0;
    }
  };

  // Calculate discounted price
  const calculateDiscountedPrice = (): number => {
    if (!appliedCoupon) return getPlanPrice();

    const originalPrice = getPlanPrice();

    if (appliedCoupon.discountType === 'percentage') {
      return originalPrice * (1 - appliedCoupon.discountValue / 100);
    } else {
      return Math.max(0, originalPrice - appliedCoupon.discountValue);
    }
  };

  const toggleEditMode = async () => {
    // If we're currently in edit mode and switching to view mode, save the changes
    if (isEditMode) {
      // Validate form before saving
      if (!billingInfo.fullName.trim()) {
        toast.error('Full name is required');
        return;
      }
      if (!billingInfo.address.trim()) {
        toast.error('Address is required');
        return;
      }
      if (!billingInfo.city.trim()) {
        toast.error('City is required');
        return;
      }
      if (!billingInfo.zipCode.trim()) {
        toast.error('ZIP/Postal code is required');
        return;
      }
      if (!billingInfo.country.trim()) {
        toast.error('Country is required');
        return;
      }
      if (!billingInfo.paymentGateway) {
        toast.error('Please select a payment method');
        return;
      }

      setUpdating(true);
      try {
        if (!user) {
          toast.error('User not authenticated');
          setUpdating(false);
          return;
        }

        // Find if billing details already exist
        const billingRef = collection(db, 'billingDetails');
        const q = query(billingRef, where('userId', '==', user.id));
        const querySnapshot = await getDocs(q);

        const billingData = {
          userId: user.id,
          ...billingInfo,
          updatedAt: new Date().toISOString()
        };

        if (querySnapshot.empty) {
          // Create new billing details
          await setDoc(doc(collection(db, 'billingDetails')), {
            ...billingData,
            createdAt: new Date().toISOString()
          });
        } else {
          // Update existing billing details
          const docRef = querySnapshot.docs[0].ref;
          await updateDoc(docRef, billingData);
        }

        toast.success('Billing details saved successfully');
        setHasBillingInfo(true);
        setIsEditMode(false);
      } catch (error) {
        console.error('Error saving billing details:', error);
        toast.error('Failed to save billing details');
        // Don't toggle edit mode if there was an error
        return;
      } finally {
        setUpdating(false);
      }
    } else {
      // Just toggle to edit mode
      setIsEditMode(true);
    }
  };

  if (!isOpen) return null;

  // Get plan details for display
  const getPlanDetails = () => {
    switch (selectedPlan) {
      case 'Pay-As-You-Go':
        return {
          title: 'Pay-As-You-Go',
          price: formatCurrency(2),
          period: 'per 15 generations',
          features: [
            '20 stored images',
            '5 custom prompts',
            '24h image deletion delay',
            'Credits never expire'
          ]
        };
      case 'Pro':
        return {
          title: 'Pro',
          price: formatCurrency(billingPeriod === 'monthly' ? 10 : 96),
          period: billingPeriod === 'monthly' ? '/month' : '/year',
          features: [
            '50 stored images',
            '150 generations per month',
            '10 custom prompts',
            'Instant image deletion',
            'Priority support'
          ]
        };
      case 'Enterprise':
        return {
          title: 'Enterprise',
          price: formatCurrency(billingPeriod === 'monthly' ? 100 : 960),
          period: billingPeriod === 'monthly' ? '/month' : '/year',
          features: [
            '10 team members',
            '50 stored images per member',
            '200 generations per member',
            'Team management dashboard',
            'Dedicated account manager'
          ]
        };
      default:
        return {
          title: 'Unknown Plan',
          price: formatCurrency(0),
          period: '',
          features: []
        };
    }
  };

  const planDetails = getPlanDetails();

  // Prevent body scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      // Save the current overflow value
      const originalOverflow = document.body.style.overflow;
      // Lock scrolling
      document.body.style.overflow = 'hidden';

      // Restore scrolling when component unmounts or modal closes
      return () => {
        document.body.style.overflow = originalOverflow;
      };
    }
  }, [isOpen]);

  return (
    <>
      {/* Payment Initialization Loading Overlay */}
      {initializingPayment && (
        <div className="fixed inset-0 bg-black/90 flex items-center justify-center z-[60]">
          <div className="text-center payment-loading-container">
            <div className="animate-spin rounded-full h-16 w-16 md:h-20 md:w-20 border-t-4 border-b-4 border-purple-500 mx-auto mb-6"></div>
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-2 payment-loading-text">
              Initializing Payment
            </h2>
            <p className="text-gray-300 text-sm md:text-base">
              Please wait while we set up your payment...
            </p>
          </div>
        </div>
      )}

      {/* Main Billing Details Modal */}
      <div
        className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-2 md:p-4"
        onClick={onClose}
      >
      <div
        className="bg-gray-800 rounded-xl max-w-4xl w-full h-auto max-h-[95vh] md:max-h-none md:h-auto overflow-hidden md:overflow-visible flex flex-col billing-modal-container"
        onClick={(e) => e.stopPropagation()}
        onTouchMove={(e) => e.stopPropagation()}
      >
        <div className="flex flex-col h-full md:h-auto overflow-y-auto md:overflow-visible custom-scrollbar billing-modal-content">
          <div className="p-4 md:p-5 lg:p-6 flex-1 md:flex-none billing-compact">
          <div className="flex justify-between items-center mb-3 md:mb-4 lg:mb-3">
            <div>
              <h2 className="text-xl md:text-2xl font-bold text-white">Billing Details</h2>
              <div className="flex items-center mt-1 text-xs md:text-sm text-gray-400">
                <span>For</span>
                <div className="ml-1">
                  <CountryIndicator variant="popup" className="text-gray-300" />
                </div>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="h-5 w-5 md:h-6 md:w-6" />
            </button>
          </div>

          <div className="grid md:grid-cols-2 gap-3 md:gap-5 lg:gap-6 flex-1 md:flex-none">
            {/* Left side - Billing Form */}
            <div>
              <div className="flex justify-between items-center mb-2 md:mb-3">
                <h3 className="text-lg md:text-xl font-semibold text-white">
                  {hasBillingInfo ? 'Your Billing Information' : 'Enter Billing Information'}
                </h3>

                {hasBillingInfo && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={toggleEditMode}
                    disabled={updating || initializingPayment}
                    className="border-gray-600 text-gray-300 hover:bg-gray-700 text-xs md:text-sm py-1 md:py-2 px-2 md:px-3"
                  >
                    {updating && isEditMode ? (
                      <>
                        <div className="animate-spin rounded-full h-3 w-3 md:h-4 md:w-4 border-t-2 border-b-2 border-white mr-1 md:mr-2"></div>
                        <span className="hidden md:inline">Saving...</span>
                        <span className="inline md:hidden">Save</span>
                      </>
                    ) : isEditMode ? (
                      <>
                        <CheckCircle className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                        <span className="hidden md:inline">Done Editing</span>
                        <span className="inline md:hidden">Done</span>
                      </>
                    ) : (
                      <>
                        <Edit className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                        <span className="hidden md:inline">Edit Details</span>
                        <span className="inline md:hidden">Edit</span>
                      </>
                    )}
                  </Button>
                )}
              </div>

              {loading ? (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-purple-500"></div>
                </div>
              ) : hasBillingInfo && !isEditMode ? (
                // Read-only view of billing details
                <div className="bg-gray-700 rounded-lg p-3 md:p-4 space-y-3 md:space-y-4">
                  <div>
                    <p className="text-sm text-gray-400 mb-1">Full Name</p>
                    <p className="text-white font-medium">{billingInfo.fullName}</p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-400 mb-1">Address</p>
                    <p className="text-white font-medium">{billingInfo.address}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-400 mb-1">City</p>
                      <p className="text-white font-medium">{billingInfo.city}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-400 mb-1">State/Province</p>
                      <p className="text-white font-medium">{billingInfo.state || 'N/A'}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-400 mb-1">ZIP/Postal Code</p>
                      <p className="text-white font-medium">{billingInfo.zipCode}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-400 mb-1">Country</p>
                      <p className="text-white font-medium">{billingInfo.country}</p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-400 mb-1">Payment Method</p>
                    <p className="text-white font-medium">{billingInfo.paymentGateway || 'Paddle'}</p>
                  </div>
                </div>
              ) : (
                // Editable form
                <form onSubmit={handleSubmit} className="space-y-3 md:space-y-4 billing-form-compact">
                  <div>
                    <label htmlFor="fullName" className="text-sm text-gray-400 mb-1 block">
                      Full Name
                    </label>
                    <Input
                      id="fullName"
                      name="fullName"
                      type="text"
                      value={billingInfo.fullName}
                      onChange={handleInputChange}
                      placeholder="Full Name"
                      className="bg-gray-700 border-gray-600 text-white"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="address" className="text-sm text-gray-400 mb-1 block">
                      Address
                    </label>
                    <Input
                      id="address"
                      name="address"
                      type="text"
                      value={billingInfo.address}
                      onChange={handleInputChange}
                      placeholder="Address"
                      className="bg-gray-700 border-gray-600 text-white"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="city" className="text-sm text-gray-400 mb-1 block">
                        City
                      </label>
                      <Input
                        id="city"
                        name="city"
                        type="text"
                        value={billingInfo.city}
                        onChange={handleInputChange}
                        placeholder="City"
                        className="bg-gray-700 border-gray-600 text-white"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="state" className="text-sm text-gray-400 mb-1 block">
                        State/Province
                      </label>
                      <Input
                        id="state"
                        name="state"
                        type="text"
                        value={billingInfo.state}
                        onChange={handleInputChange}
                        placeholder="State/Province"
                        className="bg-gray-700 border-gray-600 text-white"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="zipCode" className="text-sm text-gray-400 mb-1 block">
                        ZIP/Postal Code
                      </label>
                      <Input
                        id="zipCode"
                        name="zipCode"
                        type="text"
                        value={billingInfo.zipCode}
                        onChange={handleInputChange}
                        placeholder="ZIP/Postal Code"
                        className="bg-gray-700 border-gray-600 text-white"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="country" className="text-sm text-gray-400 mb-1 block">
                        Country
                      </label>
                      <div className="relative" ref={countryDropdownRef}>
                        <div
                          className="flex items-center justify-between bg-gray-700 border border-gray-600 rounded-md p-2 cursor-pointer"
                          onClick={() => setIsCountryDropdownOpen(!isCountryDropdownOpen)}
                        >
                          <span className={`text-sm ${billingInfo.country ? 'text-white' : 'text-gray-400'}`}>
                            {billingInfo.country || 'Select a country'}
                          </span>
                          {isCountryDropdownOpen ? (
                            <ChevronUp className="h-4 w-4 text-gray-400" />
                          ) : (
                            <ChevronDown className="h-4 w-4 text-gray-400" />
                          )}
                        </div>

                        {isCountryDropdownOpen && (
                          <div className="absolute z-10 mt-1 w-full bg-gray-800 border border-gray-700 rounded-md shadow-lg max-h-60 overflow-y-auto">
                            <div className="sticky top-0 bg-gray-800 p-2 border-b border-gray-700">
                              <div className="relative">
                                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                                <Input
                                  type="text"
                                  placeholder="Search countries..."
                                  className="pl-8 bg-gray-700 border-gray-600 text-white"
                                  value={countrySearch}
                                  onChange={(e) => setCountrySearch(e.target.value)}
                                  onClick={(e) => e.stopPropagation()}
                                />
                              </div>
                            </div>

                            <div className="py-1">
                              {filteredCountries.length > 0 ? (
                                filteredCountries.map((country) => (
                                  <div
                                    key={country}
                                    className={`px-4 py-2 text-sm cursor-pointer flex items-center justify-between ${billingInfo.country === country ? 'bg-purple-600 text-white' : 'text-gray-200 hover:bg-gray-700'}`}
                                    onClick={() => {
                                      handleCountrySelect(country);
                                      setIsCountryDropdownOpen(false);
                                      setCountrySearch('');
                                    }}
                                  >
                                    {country}
                                    {billingInfo.country === country && (
                                      <Check className="h-4 w-4" />
                                    )}
                                  </div>
                                ))
                              ) : (
                                <div className="px-4 py-2 text-sm text-gray-400">No countries found</div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </form>
              )}
            </div>

            {/* Right side - Plan Summary */}
            <div className="bg-gray-700 rounded-xl p-4 lg:p-5">
              <h3 className="text-lg font-semibold text-white mb-2 md:mb-3">Order Summary</h3>

              <div className="bg-gray-800 rounded-lg p-3 mb-3">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="text-base font-medium text-white">{planDetails.title}</h4>
                  <div className="text-right">
                    <span className="text-lg font-bold text-white">{planDetails.price}</span>
                    <span className="text-gray-400 text-xs">{planDetails.period}</span>
                  </div>
                </div>

                <div className="border-t border-gray-600 my-2"></div>

                <ul className="space-y-1">
                  {planDetails.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-4 w-4 text-green-400 mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Payment Gateway Selection */}
              <div className="bg-gray-800 rounded-lg p-3 mb-3">
                <h4 className="text-sm font-medium text-white mb-2">Select Payment Method</h4>

                <div className="space-y-2">
                  {/* Conditionally render Paddle payment option */}
                  {availablePaymentGateways.includes('Paddle') && (
                    <label className="flex items-center space-x-2 p-1.5 rounded-md cursor-pointer hover:bg-gray-700 transition-colors">
                      <input
                        type="radio"
                        name="paymentGateway"
                        value="Paddle"
                        checked={billingInfo.paymentGateway === 'Paddle'}
                        onChange={() => handlePaymentGatewaySelect('Paddle')}
                        className="text-purple-500 bg-gray-700 border-gray-600 focus:ring-purple-500 focus:ring-offset-gray-800"
                      />
                      <div className="flex flex-col">
                        <span className="text-white font-medium text-sm">Paddle</span>
                        <span className="text-xs text-gray-400">Credit/Debit Card, PayPal</span>
                      </div>
                    </label>
                  )}

                  {/* Conditionally render SSLCOMMERZ payment option */}
                  {availablePaymentGateways.includes('SSLCOMMERZ') && (
                    <label className="flex items-center space-x-2 p-1.5 rounded-md cursor-pointer hover:bg-gray-700 transition-colors">
                      <input
                        type="radio"
                        name="paymentGateway"
                        value="SSLCOMMERZ"
                        checked={billingInfo.paymentGateway === 'SSLCOMMERZ'}
                        onChange={() => handlePaymentGatewaySelect('SSLCOMMERZ')}
                        className="text-purple-500 bg-gray-700 border-gray-600 focus:ring-purple-500 focus:ring-offset-gray-800"
                      />
                      <div className="flex flex-col">
                        <span className="text-white font-medium text-sm">SSLCOMMERZ</span>
                        <span className="text-xs text-gray-400">Local Bangladesh Payment Methods</span>
                      </div>
                    </label>
                  )}

                  {/* Payment method validation message */}
                  {!billingInfo.paymentGateway && (
                    <div className="text-red-500 text-xs mt-1 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      Please select a payment method
                    </div>
                  )}

                  {/* Debug info for payment gateway */}
                  {billingInfo.paymentGateway && (
                    <div className="text-green-500 text-xs mt-1 flex items-center">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Selected payment method: {billingInfo.paymentGateway}
                    </div>
                  )}

                  {/* Conditionally render bKash (Coming Soon) payment option */}
                  {availablePaymentGateways.includes('bKash (Coming Soon)') && (
                    <label className="flex items-center space-x-2 p-1.5 rounded-md cursor-not-allowed bg-gray-700/50">
                      <input
                        type="radio"
                        name="paymentGateway"
                        value="bKash"
                        disabled
                        className="text-gray-500 bg-gray-700 border-gray-600 cursor-not-allowed"
                      />
                      <div className="flex flex-col">
                        <div className="flex items-center">
                          <span className="text-gray-400 font-medium text-sm">bKash</span>
                          <span className="ml-1 text-xs bg-gray-600 text-gray-300 px-1.5 py-0.5 rounded-full">Coming Soon</span>
                        </div>
                        <span className="text-xs text-gray-500">Mobile Banking for Bangladesh</span>
                      </div>
                    </label>
                  )}

                  {/* Conditionally render Nagad (Coming Soon) payment option */}
                  {availablePaymentGateways.includes('Nagad (Coming Soon)') && (
                    <label className="flex items-center space-x-2 p-1.5 rounded-md cursor-not-allowed bg-gray-700/50">
                      <input
                        type="radio"
                        name="paymentGateway"
                        value="Nagad"
                        disabled
                        className="text-gray-500 bg-gray-700 border-gray-600 cursor-not-allowed"
                      />
                      <div className="flex flex-col">
                        <div className="flex items-center">
                          <span className="text-gray-400 font-medium text-sm">Nagad</span>
                          <span className="ml-1 text-xs bg-gray-600 text-gray-300 px-1.5 py-0.5 rounded-full">Coming Soon</span>
                        </div>
                        <span className="text-xs text-gray-500">Mobile Banking for Bangladesh</span>
                      </div>
                    </label>
                  )}
                </div>
              </div>

              {/* Coupon Code Section */}
              <div className="border-t border-gray-600 my-2 md:my-3 pt-2 md:pt-3">
                <div className="mb-3">
                  <label htmlFor="couponCode" className="text-xs font-medium text-purple-300 mb-1.5 block flex items-center">
                    <Tag className="h-3.5 w-3.5 mr-1 text-purple-400" />
                    Have a coupon code?
                  </label>
                  {appliedCoupon ? (
                    <div className="flex items-center justify-between bg-gradient-to-r from-green-900/30 to-green-800/30 border border-green-500/30 rounded-md p-3 shadow-inner animate-fadeIn animate-success-pulse">
                      <div className="flex items-center">
                        <div className="bg-green-500/20 p-1.5 rounded-full mr-3">
                          <Tag className="h-4 w-4 text-green-400" />
                        </div>
                        <div>
                          <span className="text-green-400 font-medium block">{appliedCoupon.code}</span>
                          <span className="text-green-300/80 text-xs">
                            {appliedCoupon.discountType === 'percentage'
                              ? `${appliedCoupon.discountValue}% discount applied`
                              : `$${appliedCoupon.discountValue} discount applied`}
                          </span>
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={removeCoupon}
                        className="text-gray-400 hover:text-white hover:bg-gray-700 h-8 px-2 rounded-full border-0"
                        title="Remove coupon"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <div className="flex space-x-2">
                      <div className="flex-1 relative">
                        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-400">
                          <Tag className="h-4 w-4" />
                        </div>
                        <Input
                          id="couponCode"
                          value={couponCode}
                          onChange={(e) => {
                            setCouponCode(e.target.value.toUpperCase());
                            setCouponError('');
                          }}
                          placeholder="ENTER COUPON CODE"
                          className={`
                            bg-gray-800
                            border-2
                            ${couponError ? 'border-red-500 focus-visible:ring-red-500' : 'border-purple-500/70 animate-border-pulse'}
                            text-white
                            font-medium
                            uppercase
                            pl-10
                            placeholder:text-purple-300/90
                            placeholder:font-medium
                            shadow-sm
                            shadow-purple-500/30
                            focus-visible:ring-purple-500
                            focus-visible:border-purple-500
                            focus-visible:ring-opacity-70
                            focus-visible:placeholder:text-purple-200
                            transition-all
                            duration-200
                            tracking-wide
                          `}
                        />
                        {couponError && (
                          <div className="text-red-400 text-xs mt-1 absolute left-0 right-0 bg-gray-800/90 p-1.5 rounded border border-red-500/50 shadow-lg">
                            <div className="flex items-center">
                              <X className="h-3.5 w-3.5 text-red-400 mr-1 flex-shrink-0" />
                              <span>{couponError}</span>
                            </div>
                          </div>
                        )}
                      </div>
                      <Button
                        type="button"
                        onClick={validateCoupon}
                        disabled={validatingCoupon || !couponCode.trim()}
                        className={`whitespace-nowrap transition-all duration-200 ${
                          couponCode.trim() && !validatingCoupon
                            ? 'bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white'
                            : 'border border-gray-600 text-gray-300 hover:bg-gray-700 bg-transparent'
                        }`}
                      >
                        {validatingCoupon ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                            Validating...
                          </>
                        ) : (
                          <>
                            <Tag className="h-4 w-4 mr-1.5" />
                            Apply Coupon
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </div>

                <div className="flex justify-between mb-1.5 text-sm">
                  <span className="text-gray-300">Subtotal</span>
                  <span className="text-white font-medium">{planDetails.price}</span>
                </div>

                {billingPeriod === 'yearly' && (
                  <div className="flex justify-between mb-1.5 text-sm">
                    <span className="text-green-400">Yearly discount (20%)</span>
                    <span className="text-green-400">{formatCurrency(-24)}</span>
                  </div>
                )}

                {appliedCoupon && (
                  <div className="flex justify-between mb-1.5 text-sm">
                    <span className="text-green-400 flex items-center">
                      <Percent className="h-3.5 w-3.5 mr-1" />
                      Coupon discount
                      {appliedCoupon.discountType === 'percentage'
                        ? ` (${appliedCoupon.discountValue}%)`
                        : ''}
                    </span>
                    <span className="text-green-400">
                      {appliedCoupon.discountType === 'percentage'
                        ? formatCurrency(-(getPlanPrice() * appliedCoupon.discountValue / 100))
                        : formatCurrency(-appliedCoupon.discountValue)}
                    </span>
                  </div>
                )}

                <div className="flex justify-between font-bold mt-1.5 text-sm">
                  <span className="text-white">Total</span>
                  <span className="text-white">
                    {formatCurrency(appliedCoupon ? calculateDiscountedPrice() : getPlanPrice())}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Separator line */}
          <div className="flex justify-center my-2 md:my-3">
            <div className="w-3/4 border-t border-gray-700"></div>
          </div>

          {/* Terms and Conditions Checkbox - Centered at bottom */}
          <div className="flex justify-center mb-2 md:mb-3" id="terms-checkbox-container">
            <div className="flex items-start sm:items-center max-w-md mx-auto">
              <div className="flex-shrink-0 mt-0.5 sm:mt-0">
                <input
                  id="terms"
                  type="checkbox"
                  checked={agreedToTerms}
                  onChange={(e) => {
                    setAgreedToTerms(e.target.checked);
                    if (e.target.checked) setTermsError('');
                  }}
                  className={`h-4 w-4 rounded border-2 border-gray-500 bg-gray-700 text-purple-500 focus:ring-purple-500 focus:ring-offset-gray-800 cursor-pointer ${
                    termsError ? 'border-red-500 checkbox-error' : ''
                  }`}
                />
              </div>
              <div className="ml-2 sm:ml-2">
                <label htmlFor="terms" className="text-xs text-gray-300 cursor-pointer">
                  I agree to the{' '}
                  <a
                    href="/footer/terms"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-purple-400 hover:text-purple-300 underline inline-flex items-center"
                  >
                    Terms of Service
                    <ExternalLink className="h-2.5 w-2.5 ml-0.5" />
                  </a>
                  {' '}and{' '}
                  <a
                    href="/footer/privacy"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-purple-400 hover:text-purple-300 underline inline-flex items-center"
                  >
                    Privacy Policy
                    <ExternalLink className="h-2.5 w-2.5 ml-0.5" />
                  </a>
                </label>
              </div>
            </div>
          </div>

          {/* Error message for terms - centered */}
          {termsError && (
            <div className="flex justify-center -mt-2 mb-2 md:-mt-3 md:mb-3">
              <div className="text-red-400 text-xs flex items-center bg-red-900/20 px-2 py-1 rounded-md border border-red-500/30">
                <AlertCircle className="h-3 w-3 mr-1 text-red-400 flex-shrink-0" />
                <span>{termsError}</span>
              </div>
            </div>
          )}

          <div className="flex flex-col md:flex-row justify-between gap-2 md:gap-0 mt-2 md:mt-3">
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
              className="border-gray-600 text-gray-300 hover:bg-gray-700 text-xs md:text-sm py-1.5 md:py-2"
            >
              <ArrowLeft className="h-3.5 w-3.5 mr-1.5" />
              <span className="hidden md:inline">Back to Plans</span>
              <span className="inline md:hidden">Back</span>
            </Button>

            {hasBillingInfo && !isEditMode ? (
              // When user has billing info and is not in edit mode
              <Button
                type="button"
                onClick={handleProceedWithSavedDetails}
                disabled={initializingPayment}
                className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white text-xs md:text-sm py-1.5 md:py-2"
              >
                {initializingPayment ? (
                  <>
                    <div className="animate-spin rounded-full h-3.5 w-3.5 border-t-2 border-b-2 border-white mr-1.5"></div>
                    <span className="hidden md:inline">Initializing...</span>
                    <span className="inline md:hidden">Loading...</span>
                  </>
                ) : (
                  <>
                    <span className="hidden md:inline">Proceed to Checkout</span>
                    <span className="inline md:hidden">Checkout</span>
                    <ArrowRight className="h-3.5 w-3.5 ml-1.5" />
                  </>
                )}
              </Button>
            ) : hasBillingInfo && isEditMode ? (
              // When user has billing info and is in edit mode
              <Button
                type="button"
                onClick={handleSubmit}
                disabled={updating || initializingPayment}
                className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white text-xs md:text-sm py-1.5 md:py-2"
              >
                {updating ? (
                  <>
                    <div className="animate-spin rounded-full h-3.5 w-3.5 border-t-2 border-b-2 border-white mr-1.5"></div>
                    <span className="hidden md:inline">Saving...</span>
                    <span className="inline md:hidden">Saving...</span>
                  </>
                ) : (
                  <>
                    <Save className="h-3.5 w-3.5 mr-1.5" />
                    <span className="hidden md:inline">Save Changes</span>
                    <span className="inline md:hidden">Save</span>
                  </>
                )}
              </Button>
            ) : (
              // When user doesn't have billing info
              <Button
                type="button"
                onClick={handleSubmit}
                disabled={updating || initializingPayment}
                className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white text-xs md:text-sm py-1.5 md:py-2"
              >
                {updating ? (
                  <>
                    <div className="animate-spin rounded-full h-3.5 w-3.5 border-t-2 border-b-2 border-white mr-1.5"></div>
                    <span className="hidden md:inline">Saving...</span>
                    <span className="inline md:hidden">Saving...</span>
                  </>
                ) : initializingPayment ? (
                  <>
                    <div className="animate-spin rounded-full h-3.5 w-3.5 border-t-2 border-b-2 border-white mr-1.5"></div>
                    <span className="hidden md:inline">Initializing...</span>
                    <span className="inline md:hidden">Loading...</span>
                  </>
                ) : (
                  <>
                    <span className="hidden md:inline">Save & Proceed to Checkout</span>
                    <span className="inline md:hidden">Save & Checkout</span>
                    <ArrowRight className="h-3.5 w-3.5 ml-1.5" />
                  </>
                )}
              </Button>
            )}
          </div>
          </div>
        </div>
      </div>
      </div>
    </>
  );
}
