/* Page Transition Animations */

/* Slide in from top animation */
.page-transition-enter {
  opacity: 0;
  transform: translateY(-30px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 500ms, transform 500ms;
}

.page-transition-exit {
  opacity: 1;
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-30px);
  transition: opacity 300ms, transform 300ms;
}

/* Cool reveal animation */
@keyframes revealFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.page-reveal {
  animation: revealFromTop 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  overflow-y: visible;
  height: auto;
}

/* Staggered content reveal */
.content-reveal {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.content-reveal.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Delay classes for staggered animations */
.delay-1 {
  transition-delay: 0.1s;
}

.delay-2 {
  transition-delay: 0.2s;
}

.delay-3 {
  transition-delay: 0.3s;
}

.delay-4 {
  transition-delay: 0.4s;
}

.delay-5 {
  transition-delay: 0.5s;
}