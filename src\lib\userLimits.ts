import { db } from './firebase';
import { doc, getDoc, setDoc, updateDoc, increment, collection, query, where, getDocs, Timestamp } from 'firebase/firestore';
import { toast } from 'react-hot-toast';
import { getTeamMemberLimits } from './teamMemberLimits';

export interface LimitSettings {
  maxImages: number;
  deleteDelayHours: number;
  maxSavedPrompts: number;
  maxCustomPrompts: number;
  totalGenerationLimit: number;
}

export interface PayAsYouGoLimitSettings {
  maxImages: number;
  deleteDelayHours: number;
  maxSavedPrompts: number;
  maxCustomPrompts: number;
  totalGenerationLimit: number;
}

export interface ProLimitSettings {
  maxImages: number;
  deleteDelayHours: number;
  maxSavedPrompts: number;
  maxCustomPrompts: number;
  monthlyGenerationLimit: number;
}

export interface EnterpriseLimitSettings {
  maxSubUsers: number;
  maxImagesPerSubUser: number;
  deleteDelayHoursPerSubUser: number;
  maxSavedPromptsPerSubUser: number;
  maxCustomPromptsPerSubUser: number;
  maxTeamMembers: number;
}

// Custom Package Types
export interface CustomPackage {
  id: string;
  name: string;
  type: 'pro-based' | 'enterprise-based';
  limits: CustomProLimitSettings | CustomEnterpriseLimitSettings;
  createdAt: string;
  updatedAt: string;
  createdBy: string; // Admin user ID who created this package
  isActive: boolean;
}

export interface CustomProLimitSettings {
  maxImages: number;
  deleteDelayHours: number;
  maxSavedPrompts: number;
  maxCustomPrompts: number;
  monthlyGenerationLimit: number;
}

export interface CustomEnterpriseLimitSettings {
  maxTeamMembers: number;
  maxImages: number;
  deleteDelayHours: number;
  maxSavedPrompts: number;
  maxCustomPrompts: number;
  monthlyGenerationLimit: number;
}

export interface UserUsage {
  userId: string;
  dailyGenerationCount: number; // For free users, this represents total generations
  lastGenerationDate: Timestamp;
  customPromptsCount: number;
  savedPromptsCount: number;
  uploadedImagesCount: number;
}

// Default limits if not set in admin panel
const DEFAULT_LIMITS: LimitSettings = {
  maxImages: 10,
  deleteDelayHours: 24,
  maxSavedPrompts: 20,
  maxCustomPrompts: 3,
  totalGenerationLimit: 10
};

// Default pay-as-you-go user limits if not set in admin panel
const DEFAULT_PAY_AS_YOU_GO_LIMITS: PayAsYouGoLimitSettings = {
  maxImages: 20,
  deleteDelayHours: 24,
  maxSavedPrompts: 30,
  maxCustomPrompts: 5,
  totalGenerationLimit: 15
};

// Default pro user limits if not set in admin panel
const DEFAULT_PRO_LIMITS: ProLimitSettings = {
  maxImages: 100,
  deleteDelayHours: 0,
  maxSavedPrompts: 100,
  maxCustomPrompts: 20,
  monthlyGenerationLimit: 300
};

// Default enterprise user limits if not set in admin panel
const DEFAULT_ENTERPRISE_LIMITS: EnterpriseLimitSettings = {
  maxSubUsers: 5,
  maxImagesPerSubUser: 200,
  deleteDelayHoursPerSubUser: 0,
  maxSavedPromptsPerSubUser: 150,
  maxCustomPromptsPerSubUser: 20,
  maxTeamMembers: 5
};

/**
 * Get the pay-as-you-go user limit settings from Firestore
 */
export const getPayAsYouGoLimitSettings = async (): Promise<PayAsYouGoLimitSettings> => {
  try {
    // Check if user is authenticated
    const { getAuth } = await import('firebase/auth');
    const auth = getAuth();
    const currentUser = auth.currentUser;

    if (!currentUser) {
      console.error('User not authenticated when fetching pay-as-you-go limit settings');
      return DEFAULT_PAY_AS_YOU_GO_LIMITS;
    }

    const limitsDocRef = doc(db, 'settings', 'payAsYouGoUserLimits');
    const limitsDoc = await getDoc(limitsDocRef);

    if (limitsDoc.exists()) {
      return limitsDoc.data() as PayAsYouGoLimitSettings;
    }

    // If no document exists, create one with default values
    // Only admins should be able to create this document (handled by Firestore rules)
    try {
      await setDoc(limitsDocRef, DEFAULT_PAY_AS_YOU_GO_LIMITS);
    } catch (setError: any) {
      // If permission denied, just return defaults without creating document
      if (setError.code === 'permission-denied') {
        console.warn('Permission denied when creating default pay-as-you-go limits');
      } else {
        throw setError;
      }
    }
    return DEFAULT_PAY_AS_YOU_GO_LIMITS;
  } catch (error: any) {
    console.error('Error fetching pay-as-you-go user limits:', error);
    if (error.code === 'permission-denied') {
      console.warn('Permission denied when accessing pay-as-you-go user limits');
    }
    return DEFAULT_PAY_AS_YOU_GO_LIMITS;
  }
};

/**
 * Get the pro user limit settings from Firestore
 */
export const getProLimitSettings = async (): Promise<ProLimitSettings> => {
  try {
    // Check if user is authenticated
    const { getAuth } = await import('firebase/auth');
    const auth = getAuth();
    const currentUser = auth.currentUser;

    if (!currentUser) {
      console.error('User not authenticated when fetching pro limit settings');
      return DEFAULT_PRO_LIMITS;
    }

    const limitsDocRef = doc(db, 'settings', 'proUserLimits');
    const limitsDoc = await getDoc(limitsDocRef);

    if (limitsDoc.exists()) {
      return limitsDoc.data() as ProLimitSettings;
    }

    // If no document exists, create one with default values
    // Only admins should be able to create this document (handled by Firestore rules)
    try {
      await setDoc(limitsDocRef, DEFAULT_PRO_LIMITS);
    } catch (setError: any) {
      // If permission denied, just return defaults without creating document
      if (setError.code === 'permission-denied') {
        console.warn('Permission denied when creating default pro limits');
      } else {
        throw setError;
      }
    }
    return DEFAULT_PRO_LIMITS;
  } catch (error: any) {
    console.error('Error fetching pro user limits:', error);
    if (error.code === 'permission-denied') {
      console.warn('Permission denied when accessing pro user limits');
    }
    return DEFAULT_PRO_LIMITS;
  }
};

/**
 * Get the enterprise user limit settings from Firestore
 */
export const getEnterpriseLimitSettings = async (): Promise<EnterpriseLimitSettings> => {
  try {
    // Check if user is authenticated
    const { getAuth } = await import('firebase/auth');
    const auth = getAuth();
    const currentUser = auth.currentUser;

    if (!currentUser) {
      console.error('User not authenticated when fetching enterprise limit settings');
      return DEFAULT_ENTERPRISE_LIMITS;
    }

    const limitsDocRef = doc(db, 'settings', 'enterpriseUserLimits');
    const limitsDoc = await getDoc(limitsDocRef);

    if (limitsDoc.exists()) {
      return limitsDoc.data() as EnterpriseLimitSettings;
    }

    // If no document exists, create one with default values
    // Only admins should be able to create this document (handled by Firestore rules)
    try {
      await setDoc(limitsDocRef, DEFAULT_ENTERPRISE_LIMITS);
    } catch (setError: any) {
      // If permission denied, just return defaults without creating document
      if (setError.code === 'permission-denied') {
        console.warn('Permission denied when creating default enterprise limits');
      } else {
        throw setError;
      }
    }
    return DEFAULT_ENTERPRISE_LIMITS;
  } catch (error: any) {
    console.error('Error fetching enterprise user limits:', error);
    if (error.code === 'permission-denied') {
      console.warn('Permission denied when accessing enterprise user limits');
    }
    return DEFAULT_ENTERPRISE_LIMITS;
  }
};

/**
 * Get the current limit settings from Firestore
 */
export const getLimitSettings = async (): Promise<LimitSettings> => {
  try {
    // Check if user is authenticated
    const { getAuth } = await import('firebase/auth');
    const auth = getAuth();
    const currentUser = auth.currentUser;

    if (!currentUser) {
      console.error('User not authenticated when fetching limit settings');
      return DEFAULT_LIMITS;
    }

    const limitsDocRef = doc(db, 'settings', 'userLimits');
    const limitsDoc = await getDoc(limitsDocRef);

    if (limitsDoc.exists()) {
      return limitsDoc.data() as LimitSettings;
    }

    // If no document exists, create one with default values
    // Only admins should be able to create this document (handled by Firestore rules)
    try {
      await setDoc(limitsDocRef, DEFAULT_LIMITS);
    } catch (setError: any) {
      // If permission denied, just return defaults without creating document
      if (setError.code === 'permission-denied') {
        console.warn('Permission denied when creating default limits');
      } else {
        throw setError;
      }
    }
    return DEFAULT_LIMITS;
  } catch (error: any) {
    console.error('Error fetching user limits:', error);
    if (error.code === 'permission-denied') {
      console.warn('Permission denied when accessing user limits');
    }
    return DEFAULT_LIMITS;
  }
};

/**
 * Get or create a user's usage record
 */
export const getUserUsage = async (userId: string): Promise<UserUsage> => {
  if (!userId) {
    throw new Error('User ID is required');
  }

  try {
    // Check if user is authenticated
    const { getAuth } = await import('firebase/auth');
    const auth = getAuth();
    const currentUser = auth.currentUser;

    if (!currentUser) {
      throw new Error('User not authenticated when accessing usage data');
    }

    // Verify the user is accessing their own data or is an admin
    if (currentUser.uid !== userId && currentUser.email !== '<EMAIL>') {
      throw new Error('You can only access your own usage data');
    }

    const userUsageRef = doc(db, 'userUsage', userId);
    const userUsageDoc = await getDoc(userUsageRef);

    if (userUsageDoc.exists()) {
      return userUsageDoc.data() as UserUsage;
    }

    // Create a new usage record if none exists
    const newUsage: UserUsage = {
      userId,
      dailyGenerationCount: 0,
      lastGenerationDate: Timestamp.now(),
      customPromptsCount: 0,
      savedPromptsCount: 0,
      uploadedImagesCount: 0
    };

    try {
      await setDoc(userUsageRef, newUsage);
      return newUsage;
    } catch (setError: any) {
      if (setError.code === 'permission-denied') {
        console.warn('Permission denied when creating user usage record');
        // Return the new usage object even if we couldn't save it
        return newUsage;
      }
      throw setError;
    }
  } catch (error: any) {
    console.error('Error fetching user usage:', error);
    if (error.code === 'permission-denied') {
      console.warn('Permission denied when accessing user usage data');
      // Return a default usage object to prevent app crashes
      return {
        userId,
        dailyGenerationCount: 0,
        lastGenerationDate: Timestamp.now(),
        customPromptsCount: 0,
        savedPromptsCount: 0,
        uploadedImagesCount: 0
      };
    }
    throw error;
  }
};

/**
 * Check if a user is a team member (not a team owner)
 */
export const isTeamMember = async (userId: string): Promise<boolean> => {
  if (!userId) {
    console.error('Invalid userId provided to isTeamMember');
    return false;
  }

  try {
    // First, try to get the user document by ID
    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();
        if (!userData) {
          console.warn('User document exists but data is empty');
          return false;
        }

        // A user is a team member if they have isTeamMember set to true
        return userData.isTeamMember === true;
      }
    } catch (docError) {
      console.error('Error getting user document by ID:', docError);
      // Continue to try by UID
    }

    // If user document not found by ID, try to find by UID
    try {
      const { getAuth } = await import('firebase/auth');
      const auth = getAuth();
      const currentUser = auth.currentUser;

      if (currentUser) {
        const usersRef = collection(db, 'users');
        const q = query(usersRef, where('uid', '==', currentUser.uid));

        try {
          const querySnapshot = await getDocs(q);

          if (!querySnapshot.empty) {
            const userData = querySnapshot.docs[0].data();
            if (!userData) {
              console.warn('User document found by UID but data is empty');
              return false;
            }
            return userData.isTeamMember === true;
          }
        } catch (queryError) {
          console.error('Error executing query for user by UID:', queryError);
          return false;
        }
      }
    } catch (authError) {
      console.error('Error getting current user:', authError);
      return false;
    }

    return false;
  } catch (error) {
    console.error('Error checking if user is a team member:', error);
    return false;
  }
};

/**
 * Get user's package name from Firestore with proper role and subscription checks
 */
export const getUserPackage = async (userId: string): Promise<string> => {
  try {
    // First, try to get the user document by ID
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      const userData = userDoc.data();

      // Check if user has a custom package (role starts with "Custom:")
      if (userData.role && userData.role.startsWith('Custom:')) {
        // Extract custom package name from role (format: "Custom:PackageName")
        const customPackageName = userData.role.substring(7); // Remove "Custom:" prefix

        // Check if subscription has expired for custom packages (if they have expiration)
        if (userData.subscriptionExpireDate && userData.subscriptionExpireDate !== 'lifetime') {
          const expireDate = new Date(userData.subscriptionExpireDate);
          const now = new Date();

          if (expireDate < now) {
            console.warn('User custom package subscription has expired, downgrading to Free package');
            return 'Free';
          }
        }

        return customPackageName;
      }

      // Check if user is Pay-As-You-Go, Pro, or Enterprise
      if (userData.role === 'Pay-As-You-Go User' || userData.role === 'Pro User' || userData.role === 'Enterprise User' ||
          userData.packageName === 'Pay-As-You-Go' || userData.packageName === 'Pro' || userData.packageName === 'Enterprise') {

        // Check if subscription has expired for Pro and Enterprise users
        if ((userData.role === 'Pro User' || userData.role === 'Enterprise User' ||
            userData.packageName === 'Pro' || userData.packageName === 'Enterprise') &&
            userData.subscriptionExpireDate && userData.subscriptionExpireDate !== 'lifetime') {
          const expireDate = new Date(userData.subscriptionExpireDate);
          const now = new Date();

          if (expireDate < now) {
            console.warn('User subscription has expired, downgrading to Free package');
            return 'Free';
          }
        }

        // Return the appropriate package name based on role
        if (userData.role === 'Pay-As-You-Go User' || userData.packageName === 'Pay-As-You-Go') {
          return 'Pay-As-You-Go';
        } else if (userData.role === 'Pro User' || userData.packageName === 'Pro') {
          return 'Pro';
        } else if (userData.role === 'Enterprise User' || userData.packageName === 'Enterprise') {
          return 'Enterprise';
        }
      }

      // Default to Free if not Pro or Enterprise, or if no role is specified
      return userData.packageName || 'Free';
    }

    // If user document not found by ID, try to find by UID
    const { getAuth } = await import('firebase/auth');
    const auth = getAuth();
    const currentUser = auth.currentUser;

    if (currentUser) {
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('uid', '==', currentUser.uid));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const userData = querySnapshot.docs[0].data();

        // Check if user has a custom package (role starts with "Custom:")
        if (userData.role && userData.role.startsWith('Custom:')) {
          // Extract custom package name from role (format: "Custom:PackageName")
          const customPackageName = userData.role.substring(7); // Remove "Custom:" prefix

          // Check if subscription has expired for custom packages (if they have expiration)
          if (userData.subscriptionExpireDate && userData.subscriptionExpireDate !== 'lifetime') {
            const expireDate = new Date(userData.subscriptionExpireDate);
            const now = new Date();

            if (expireDate < now) {
              console.warn('User custom package subscription has expired, downgrading to Free package');
              return 'Free';
            }
          }

          return customPackageName;
        }

        // Check if user is Pay-As-You-Go, Pro, or Enterprise
        if (userData.role === 'Pay-As-You-Go User' || userData.role === 'Pro User' || userData.role === 'Enterprise User' ||
            userData.packageName === 'Pay-As-You-Go' || userData.packageName === 'Pro' || userData.packageName === 'Enterprise') {

          // Check if subscription has expired for Pro and Enterprise users
          if ((userData.role === 'Pro User' || userData.role === 'Enterprise User' ||
              userData.packageName === 'Pro' || userData.packageName === 'Enterprise') &&
              userData.subscriptionExpireDate && userData.subscriptionExpireDate !== 'lifetime') {
            const expireDate = new Date(userData.subscriptionExpireDate);
            const now = new Date();

            if (expireDate < now) {
              console.warn('User subscription has expired, downgrading to Free package');
              return 'Free';
            }
          }

          // Return the appropriate package name based on role
          if (userData.role === 'Pay-As-You-Go User' || userData.packageName === 'Pay-As-You-Go') {
            return 'Pay-As-You-Go';
          } else if (userData.role === 'Pro User' || userData.packageName === 'Pro') {
            return 'Pro';
          } else if (userData.role === 'Enterprise User' || userData.packageName === 'Enterprise') {
            return 'Enterprise';
          }
        }

        // Default to Free if not Pro or Enterprise, or if no role is specified
        return userData.packageName || 'Free';
      }
    }

    return 'Free';
  } catch (error) {
    console.error('Error fetching user package:', error);
    return 'Free';
  }
};

/**
 * Check if a user's subscription has expired
 */
export const isSubscriptionExpired = async (userId: string): Promise<boolean> => {
  try {
    // Get user data to check subscription expiration
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return false; // No user document, assume not expired (will default to Free)
    }

    const userData = userDoc.data();

    // If user is not Pro, Enterprise, or Custom package, they don't have a subscription
    if (userData.role !== 'Pro User' &&
        userData.role !== 'Enterprise User' &&
        !(userData.role && userData.role.startsWith('Custom:'))) {
      return false;
    }

    // Check if user has lifetime access
    if (userData.subscriptionExpireDate === 'lifetime') {
      return false; // Lifetime access never expires
    }

    // Check if subscription has expired
    if (userData.subscriptionExpireDate) {
      // Lifetime deals never expire
      if (userData.subscriptionExpireDate === 'lifetime') {
        return false;
      }

      const expireDate = new Date(userData.subscriptionExpireDate);
      const now = new Date();
      return expireDate < now;
    }

    return false; // No expiration date set, assume not expired
  } catch (error) {
    console.error('Error checking subscription expiration:', error);
    return false; // On error, assume not expired to avoid blocking users
  }
};

/**
 * Get the appropriate limit settings based on user's package
 */
export const getUserLimitSettings = async (userId: string): Promise<LimitSettings | ProLimitSettings | EnterpriseLimitSettings> => {
  try {
    // Get user data to determine package
    const { getAuth } = await import('firebase/auth');
    const auth = getAuth();
    const currentUser = auth.currentUser;

    if (!currentUser) {
      console.error('User not authenticated when fetching user package');
      return DEFAULT_LIMITS;
    }

    // Get user's package using the new getUserPackage function
    let packageName = await getUserPackage(userId);

    // Check if it's a custom package first
    if (packageName !== 'Free' && packageName !== 'Pay-As-You-Go' && packageName !== 'Pro' && packageName !== 'Enterprise') {
      // This is a custom package, get its limits
      const { getCustomPackageLimits } = await import('./customPackages');
      const customLimits = await getCustomPackageLimits(packageName);

      if (customLimits) {
        return customLimits;
      } else {
        console.warn(`Custom package "${packageName}" not found, defaulting to Free limits`);
        return DEFAULT_LIMITS;
      }
    }

    // Return appropriate limits based on package
    if (packageName === 'Pay-As-You-Go') {
      return await getPayAsYouGoLimitSettings();
    } else if (packageName === 'Pro') {
      return await getProLimitSettings();
    } else if (packageName === 'Enterprise') {
      // Check if user is a team member
      const isUserTeamMember = await isTeamMember(userId);

      if (isUserTeamMember) {
        // Get team member specific limits
        const teamMemberLimits = await getTeamMemberLimits(userId);
        console.log('Team member limits for user', userId, ':', teamMemberLimits);

        // Convert team member limits to the format expected by the application
        return {
          maxSubUsers: 0, // Team members can't have sub-users
          maxImagesPerSubUser: teamMemberLimits.maxImages,
          deleteDelayHoursPerSubUser: teamMemberLimits.deleteDelayHours,
          maxSavedPromptsPerSubUser: teamMemberLimits.maxSavedPrompts,
          maxCustomPromptsPerSubUser: teamMemberLimits.maxCustomPrompts,
          maxTeamMembers: 0, // Team members can't have team members
          monthlyGenerationLimit: teamMemberLimits.monthlyGenerationLimit // Add monthly generation limit
        };
      } else {
        // Get enterprise admin owner limits
        const limitsDocRef = doc(db, 'settings', 'enterpriseAdminOwnerLimits');
        const limitsDoc = await getDoc(limitsDocRef);

        if (limitsDoc.exists()) {
          const adminOwnerLimits = limitsDoc.data();
          console.log('Found enterprise admin owner limits:', adminOwnerLimits);

          // Convert the admin owner limits to the format expected by the application
          return {
            maxSubUsers: 5, // Default value
            maxImagesPerSubUser: adminOwnerLimits.maxImages || DEFAULT_ENTERPRISE_LIMITS.maxImagesPerSubUser,
            deleteDelayHoursPerSubUser: adminOwnerLimits.deleteDelayHours || DEFAULT_ENTERPRISE_LIMITS.deleteDelayHoursPerSubUser,
            maxSavedPromptsPerSubUser: adminOwnerLimits.maxSavedPrompts || DEFAULT_ENTERPRISE_LIMITS.maxSavedPromptsPerSubUser,
            maxCustomPromptsPerSubUser: adminOwnerLimits.maxCustomPrompts || DEFAULT_ENTERPRISE_LIMITS.maxCustomPromptsPerSubUser,
            maxTeamMembers: adminOwnerLimits.maxTeamMembers || DEFAULT_ENTERPRISE_LIMITS.maxTeamMembers
          };
        }

        console.log('No enterprise admin owner limits found, using defaults');
        return DEFAULT_ENTERPRISE_LIMITS;
      }
    } else {
      return await getLimitSettings();
    }
  } catch (error) {
    console.error('Error determining user package:', error);
    return DEFAULT_LIMITS;
  }
};

/**
 * Check if a user can upload more images
 */
export const canUploadMoreImages = async (userId: string): Promise<boolean> => {
  try {
    // Get user's package using the new getUserPackage function
    let packageName;
    try {
      packageName = await getUserPackage(userId);
    } catch (packageError: any) {
      console.warn('Error fetching user package, defaulting to Free:', packageError);
      packageName = 'Free';
    }

    // Get user's usage data
    const usage = await getUserUsage(userId);

    // For Enterprise users, check if they are team members
    if (packageName === 'Enterprise') {
      const isUserTeamMember = await isTeamMember(userId);
      if (isUserTeamMember) {
        // For team members, get their specific limits
        const teamMemberLimits = await getTeamMemberLimits(userId);
        console.log('Team member images limit:', teamMemberLimits.maxImages);
        return usage.uploadedImagesCount < teamMemberLimits.maxImages;
      }
    }

    // For non-team members, use the getUserLimitSettings function
    const limits = await getUserLimitSettings(userId) as any;

    // Different limit property names based on package type
    const maxImages = limits.maxImages || limits.maxImagesPerSubUser || DEFAULT_LIMITS.maxImages;

    return usage.uploadedImagesCount < maxImages;
  } catch (error) {
    console.error('Error checking image upload limit:', error);
    return false;
  }
};

/**
 * Increment the user's uploaded images count
 */
export const incrementUploadedImagesCount = async (userId: string, count: number = 1): Promise<void> => {
  try {
    const userUsageRef = doc(db, 'userUsage', userId);
    await updateDoc(userUsageRef, {
      uploadedImagesCount: increment(count)
    });
  } catch (error) {
    console.error('Error updating uploaded images count:', error);
    throw error;
  }
};

/**
 * Check if a user can delete an image (based on upload time)
 */
export const canDeleteImage = async (userId: string, uploadTimestamp: Timestamp): Promise<boolean> => {
  try {
    // Get user's package using the new getUserPackage function
    let packageName;
    try {
      packageName = await getUserPackage(userId);
    } catch (packageError: any) {
      console.warn('Error fetching user package, defaulting to Free:', packageError);
      packageName = 'Free';
    }

    // Pro, Enterprise, and Custom package users - check their delete delay settings
    if (packageName === 'Pro' || packageName === 'Enterprise') {
      return true;
    }

    // For custom packages, check their specific delete delay settings
    if (packageName !== 'Free' && packageName !== 'Pay-As-You-Go') {
      try {
        const { getCustomPackageLimits } = await import('./customPackages');
        const customLimits = await getCustomPackageLimits(packageName);

        if (customLimits && customLimits.deleteDelayHours === 0) {
          return true;
        }

        if (customLimits && customLimits.deleteDelayHours > 0) {
          // Check if enough time has passed
          const now = Timestamp.now();
          const timeDiff = now.toMillis() - uploadTimestamp.toMillis();
          const hoursDiff = timeDiff / (1000 * 60 * 60);
          return hoursDiff >= customLimits.deleteDelayHours;
        }
      } catch (error) {
        console.error('Error checking custom package delete limits:', error);
        // Default to not allowing deletion if we can't check limits
        return false;
      }
    }

    // For Pay-As-You-Go users, check the time delay
    if (packageName === 'Pay-As-You-Go') {
      const limits = await getPayAsYouGoLimitSettings();

      // If deleteDelayHours is 0, user can delete immediately
      if (limits.deleteDelayHours === 0) {
        return true;
      }

      const now = Timestamp.now();
      const uploadTime = uploadTimestamp;
      const hoursSinceUpload = (now.seconds - uploadTime.seconds) / 3600;

      return hoursSinceUpload >= limits.deleteDelayHours;
    }

    // For Free users, check the time delay
    const limits = await getLimitSettings();

    // If deleteDelayHours is 0, user can delete immediately
    if (limits.deleteDelayHours === 0) {
      return true;
    }

    const now = Timestamp.now();
    const uploadTime = uploadTimestamp;
    const hoursSinceUpload = (now.seconds - uploadTime.seconds) / 3600;

    return hoursSinceUpload >= limits.deleteDelayHours;
  } catch (error) {
    console.error('Error checking image deletion limit:', error);
    return false;
  }
};

/**
 * Check if a user can save more prompts
 */
export const canSaveMorePrompts = async (userId: string): Promise<boolean> => {
  try {
    // Get user's package using the new getUserPackage function
    let packageName;
    try {
      packageName = await getUserPackage(userId);
    } catch (packageError: any) {
      console.warn('Error fetching user package, defaulting to Free:', packageError);
      packageName = 'Free';
    }

    // Get user's usage data
    const usage = await getUserUsage(userId);

    // For Enterprise users, check if they are team members
    if (packageName === 'Enterprise') {
      const isUserTeamMember = await isTeamMember(userId);
      if (isUserTeamMember) {
        // For team members, get their specific limits
        const teamMemberLimits = await getTeamMemberLimits(userId);
        console.log('Team member saved prompts limit:', teamMemberLimits.maxSavedPrompts);
        return usage.savedPromptsCount < teamMemberLimits.maxSavedPrompts;
      } else {
        // For Enterprise admin owners, get their specific limits from enterpriseAdminOwnerLimits
        try {
          const limitsDocRef = doc(db, 'settings', 'enterpriseAdminOwnerLimits');
          const limitsDoc = await getDoc(limitsDocRef);

          if (limitsDoc.exists()) {
            const adminOwnerLimits = limitsDoc.data();
            const maxSavedPrompts = adminOwnerLimits.maxSavedPrompts || 150; // Default fallback
            console.log('Enterprise admin owner saved prompts limit:', maxSavedPrompts);
            return usage.savedPromptsCount < maxSavedPrompts;
          } else {
            console.warn('Enterprise admin owner limits not found, using default Enterprise limits');
            // Fall through to regular Enterprise limits handling below
          }
        } catch (error) {
          console.error('Error fetching enterprise admin owner limits:', error);
          // Fall through to regular Enterprise limits handling below
        }
      }
    }

    // For non-team members, get appropriate limits based on package
    let limits;
    if (packageName === 'Pay-As-You-Go') {
      limits = await getPayAsYouGoLimitSettings();
    } else if (packageName === 'Pro') {
      limits = await getProLimitSettings();
    } else if (packageName === 'Enterprise') {
      limits = await getEnterpriseLimitSettings();
      // Use the per-sub-user limit for enterprise users (this should only be reached if admin owner limits fetch failed)
      limits = { ...limits, maxSavedPrompts: limits.maxSavedPromptsPerSubUser };
    } else if (packageName !== 'Free') {
      // This is a custom package, get its limits
      console.log(`🔍 canSaveMorePrompts: Fetching custom package limits for "${packageName}"`);
      const { getCustomPackageLimits } = await import('./customPackages');
      const customLimits = await getCustomPackageLimits(packageName);

      if (customLimits) {
        console.log(`✅ canSaveMorePrompts: Custom package limits found:`, customLimits);
        limits = customLimits;
      } else {
        console.warn(`❌ canSaveMorePrompts: Custom package "${packageName}" not found, defaulting to Free limits`);
        limits = await getLimitSettings();
      }
    } else {
      limits = await getLimitSettings();
    }

    // Different limit property names based on package type
    const maxSavedPrompts = limits.maxSavedPrompts || DEFAULT_LIMITS.maxSavedPrompts;

    console.log(`🔍 canSaveMorePrompts result:`, {
      userId,
      packageName,
      currentCount: usage.savedPromptsCount,
      maxAllowed: maxSavedPrompts,
      canSave: usage.savedPromptsCount < maxSavedPrompts
    });

    return usage.savedPromptsCount < maxSavedPrompts;
  } catch (error) {
    console.error('Error checking saved prompts limit:', error);
    return false;
  }
};

/**
 * Increment the user's saved prompts count
 */
export const incrementSavedPromptsCount = async (userId: string): Promise<void> => {
  try {
    const userUsageRef = doc(db, 'userUsage', userId);
    await updateDoc(userUsageRef, {
      savedPromptsCount: increment(1)
    });
  } catch (error) {
    console.error('Error updating saved prompts count:', error);
    throw error;
  }
};

/**
 * Check if a user can create more custom prompts
 */
export const canCreateMoreCustomPrompts = async (userId: string): Promise<boolean> => {
  try {
    // Get user's package using the new getUserPackage function
    let packageName;
    try {
      packageName = await getUserPackage(userId);
    } catch (packageError: any) {
      console.warn('Error fetching user package, defaulting to Free:', packageError);
      packageName = 'Free';
    }

    // Get user's usage data
    const usage = await getUserUsage(userId);

    // For Enterprise users, check if they are team members
    if (packageName === 'Enterprise') {
      const isUserTeamMember = await isTeamMember(userId);
      if (isUserTeamMember) {
        // For team members, get their specific limits
        const teamMemberLimits = await getTeamMemberLimits(userId);
        console.log('Team member custom prompts limit:', teamMemberLimits.maxCustomPrompts);
        return usage.customPromptsCount < teamMemberLimits.maxCustomPrompts;
      } else {
        // For Enterprise admin owners, get their specific limits from enterpriseAdminOwnerLimits
        try {
          const limitsDocRef = doc(db, 'settings', 'enterpriseAdminOwnerLimits');
          const limitsDoc = await getDoc(limitsDocRef);

          if (limitsDoc.exists()) {
            const adminOwnerLimits = limitsDoc.data();
            const maxCustomPrompts = adminOwnerLimits.maxCustomPrompts || 20; // Default fallback
            console.log('Enterprise admin owner custom prompts limit:', maxCustomPrompts);
            return usage.customPromptsCount < maxCustomPrompts;
          } else {
            console.warn('Enterprise admin owner limits not found, using default Enterprise limits');
            // Fall through to regular Enterprise limits handling below
          }
        } catch (error) {
          console.error('Error fetching enterprise admin owner limits:', error);
          // Fall through to regular Enterprise limits handling below
        }
      }
    }

    // For non-team members, get appropriate limits based on package
    let limits;
    if (packageName === 'Pay-As-You-Go') {
      limits = await getPayAsYouGoLimitSettings();
    } else if (packageName === 'Pro') {
      limits = await getProLimitSettings();
    } else if (packageName === 'Enterprise') {
      limits = await getEnterpriseLimitSettings();
      // Use the per-sub-user limit for enterprise users (this should only be reached if admin owner limits fetch failed)
      limits = { ...limits, maxCustomPrompts: limits.maxCustomPromptsPerSubUser };
    } else if (packageName !== 'Free') {
      // This is a custom package, get its limits
      console.log(`🔍 canCreateMoreCustomPrompts: Fetching custom package limits for "${packageName}"`);
      const { getCustomPackageLimits } = await import('./customPackages');
      const customLimits = await getCustomPackageLimits(packageName);

      if (customLimits) {
        console.log(`✅ canCreateMoreCustomPrompts: Custom package limits found:`, customLimits);
        limits = customLimits;
      } else {
        console.warn(`❌ canCreateMoreCustomPrompts: Custom package "${packageName}" not found, defaulting to Free limits`);
        limits = await getLimitSettings();
      }
    } else {
      limits = await getLimitSettings();
    }

    // Different limit property names based on package type
    // Use nullish coalescing to properly handle 0 values
    const maxCustomPrompts = limits.maxCustomPrompts ?? DEFAULT_LIMITS.maxCustomPrompts;

    console.log('Custom prompts limit check:', {
      userId,
      currentCount: usage.customPromptsCount,
      maxAllowed: maxCustomPrompts,
      canCreate: usage.customPromptsCount < maxCustomPrompts
    });

    return usage.customPromptsCount < maxCustomPrompts;
  } catch (error) {
    console.error('Error checking custom prompts limit:', error);
    return false;
  }
};

/**
 * Increment the user's custom prompts count
 */
export const incrementCustomPromptsCount = async (userId: string): Promise<void> => {
  try {
    const userUsageRef = doc(db, 'userUsage', userId);
    await updateDoc(userUsageRef, {
      customPromptsCount: increment(1)
    });
  } catch (error) {
    console.error('Error updating custom prompts count:', error);
    throw error;
  }
};

/**
 * Check if a user can generate more prompts based on their package and limits
 */
export const canGenerateMorePrompts = async (userId: string): Promise<boolean> => {
  try {
    // Get user data to determine package
    const { getAuth } = await import('firebase/auth');
    const auth = getAuth();
    const currentUser = auth.currentUser;

    if (!currentUser) {
      console.error('User not authenticated when checking generation limit');
      return false;
    }

    // First try to get user's usage data - this should be accessible to the user
    let usage;
    try {
      usage = await getUserUsage(userId);
    } catch (usageError: any) {
      console.error('Error fetching user usage:', usageError);
      // If we can't get usage data, assume user has reached limit
      return false;
    }

    // Get user's package using the new getUserPackage function
    let packageName;
    try {
      packageName = await getUserPackage(userId);
    } catch (packageError: any) {
      console.warn('Error fetching user package, defaulting to Free:', packageError);
      packageName = 'Free';
    }

    // For Pay-As-You-Go users, check against the total generation limit
    if (packageName === 'Pay-As-You-Go') {
      let limits;
      try {
        limits = await getPayAsYouGoLimitSettings();
      } catch (limitsError: any) {
        console.warn('Error fetching pay-as-you-go limit settings, using defaults:', limitsError);
        limits = DEFAULT_PAY_AS_YOU_GO_LIMITS;
      }

      // Check against the total limit
      return usage.dailyGenerationCount < limits.totalGenerationLimit;
    }

    // For Pro, Enterprise, and Custom package users, use the existing daily/monthly logic
    if (packageName === 'Pro' || packageName === 'Enterprise' ||
        (packageName !== 'Free' && packageName !== 'Pay-As-You-Go')) {
      let limits;
      try {
        limits = await getUserLimitSettings(userId) as any;
      } catch (limitsError: any) {
        console.warn('Error fetching user limit settings, using defaults:', limitsError);
        // Use default limits if we can't get specific limits
        if (packageName === 'Pro') {
          limits = DEFAULT_PRO_LIMITS;
        } else if (packageName === 'Enterprise') {
          limits = DEFAULT_ENTERPRISE_LIMITS;
        } else {
          // For custom packages, use Pro defaults as fallback
          limits = DEFAULT_PRO_LIMITS;
        }
      }

      const now = Timestamp.now();
      const lastGeneration = usage.lastGenerationDate;

      // Check if last generation was on a different day
      const lastDate = new Date(lastGeneration.seconds * 1000);
      const nowDate = new Date(now.seconds * 1000);

      const isSameDay =
        lastDate.getFullYear() === nowDate.getFullYear() &&
        lastDate.getMonth() === nowDate.getMonth() &&
        lastDate.getDate() === nowDate.getDate();

      // If it's a new day, reset the count
      if (!isSameDay) {
        try {
          await updateDoc(doc(db, 'userUsage', userId), {
            dailyGenerationCount: 0,
            lastGenerationDate: now
          });
        } catch (updateError: any) {
          console.error('Error resetting daily count:', updateError);
          // Continue even if we can't reset the count
        }
        return true;
      }

      // Otherwise check against the daily/monthly limit
      if (packageName === 'Pro') {
        return usage.dailyGenerationCount < limits.monthlyGenerationLimit;
      } else if (packageName === 'Enterprise') {
        // Enterprise users - check if they are team members
        const isUserTeamMember = await isTeamMember(userId);
        if (isUserTeamMember) {
          // For team members, get their specific monthly generation limit
          const teamMemberLimits = await getTeamMemberLimits(userId);
          console.log('Team member monthly generation limit:', teamMemberLimits.monthlyGenerationLimit);
          return usage.dailyGenerationCount < teamMemberLimits.monthlyGenerationLimit;
        } else {
          // For Enterprise Admin Owners, check against their monthly generation limit
          const limitsDocRef = doc(db, 'settings', 'enterpriseAdminOwnerLimits');
          const limitsDoc = await getDoc(limitsDocRef);
          if (limitsDoc.exists()) {
            const adminOwnerLimits = limitsDoc.data();
            return usage.dailyGenerationCount < (adminOwnerLimits.monthlyGenerationLimit || 500);
          }
          return true; // Default fallback
        }
      } else {
        // Custom package - check against their monthly generation limit
        return usage.dailyGenerationCount < limits.monthlyGenerationLimit;
      }
    }

    // For Free users, check against the total generation limit
    // Note: For free users, dailyGenerationCount actually represents their total generations
    let limits;
    try {
      limits = await getLimitSettings();
    } catch (limitsError: any) {
      console.warn('Error fetching limit settings, using defaults:', limitsError);
      limits = DEFAULT_LIMITS;
    }

    // Check against the total limit
    return usage.dailyGenerationCount < limits.totalGenerationLimit;
  } catch (error) {
    console.error('Error checking generation limit:', error);
    // If there's any error in the process, assume user has reached limit for safety
    return false;
  }
};

/**
 * Increment the user's generation count
 *
 * For free users, this increments their total generation count (which has a fixed limit)
 * For Pro/Enterprise users, this increments their daily/monthly count (which resets periodically)
 */
export const incrementDailyGenerationCount = async (userId: string): Promise<void> => {
  try {
    // Get user's package using the new getUserPackage function
    let packageName;
    try {
      packageName = await getUserPackage(userId);
    } catch (packageError: any) {
      console.warn('Error fetching user package, defaulting to Free:', packageError);
      packageName = 'Free';
    }

    const userUsageRef = doc(db, 'userUsage', userId);
    const now = Timestamp.now();

    try {
      // For Pro/Enterprise/Custom users, we track daily/monthly usage
      // For Free and Pay-As-You-Go users, we track total usage (with a fixed limit)
      const isAdvancedPackage = packageName === 'Pro' || packageName === 'Enterprise' ||
        (packageName !== 'Free' && packageName !== 'Pay-As-You-Go');

      if (isAdvancedPackage) {
        await updateDoc(userUsageRef, {
          dailyGenerationCount: increment(1),
          lastGenerationDate: now
        });
      } else {
        // For free and pay-as-you-go users, just increment the count (which represents total generations)
        await updateDoc(userUsageRef, {
          dailyGenerationCount: increment(1),
          lastGenerationDate: now
        });
      }
    } catch (updateError: any) {
      console.error('Error updating user usage:', updateError);
      if (updateError.code === 'permission-denied') {
        console.warn('Permission denied when updating user usage');
      }
      throw updateError;
    }
  } catch (error) {
    console.error('Error incrementing generation count:', error);
    throw error;
  }
};

/**
 * Decrement the user's saved prompts count (when deleting)
 */
export const decrementSavedPromptsCount = async (userId: string): Promise<void> => {
  try {
    const userUsageRef = doc(db, 'userUsage', userId);
    const userUsageDoc = await getDoc(userUsageRef);

    if (userUsageDoc.exists()) {
      const usage = userUsageDoc.data() as UserUsage;
      if (usage.savedPromptsCount > 0) {
        await updateDoc(userUsageRef, {
          savedPromptsCount: increment(-1)
        });
      }
    }
  } catch (error) {
    console.error('Error updating saved prompts count:', error);
    throw error;
  }
};

/**
 * Decrement the user's custom prompts count (when deleting)
 */
export const decrementCustomPromptsCount = async (userId: string): Promise<void> => {
  try {
    const userUsageRef = doc(db, 'userUsage', userId);
    const userUsageDoc = await getDoc(userUsageRef);

    if (userUsageDoc.exists()) {
      const usage = userUsageDoc.data() as UserUsage;
      if (usage.customPromptsCount > 0) {
        await updateDoc(userUsageRef, {
          customPromptsCount: increment(-1)
        });
      }
    }
  } catch (error) {
    console.error('Error updating custom prompts count:', error);
    throw error;
  }
};

/**
 * Decrement the user's uploaded images count (when deleting)
 */
export const decrementUploadedImagesCount = async (userId: string, count: number = 1): Promise<void> => {
  try {
    const userUsageRef = doc(db, 'userUsage', userId);
    const userUsageDoc = await getDoc(userUsageRef);

    if (userUsageDoc.exists()) {
      const usage = userUsageDoc.data() as UserUsage;
      const newCount = Math.max(0, usage.uploadedImagesCount - count);

      await updateDoc(userUsageRef, {
        uploadedImagesCount: newCount
      });
    }
  } catch (error) {
    console.error('Error updating uploaded images count:', error);
    throw error;
  }
};

/**
 * Check if a user has Enterprise-level access (regular Enterprise or Enterprise-based custom package)
 */
export const hasEnterpriseAccess = async (userId: string): Promise<boolean> => {
  try {
    const packageName = await getUserPackage(userId);

    // Regular Enterprise user
    if (packageName === 'Enterprise') {
      return true;
    }

    // Check if it's an Enterprise-based custom package
    if (packageName !== 'Free' && packageName !== 'Pay-As-You-Go' && packageName !== 'Pro') {
      const { getCustomPackageType } = await import('./customPackages');
      const packageType = await getCustomPackageType(packageName);
      return packageType === 'enterprise-based';
    }

    return false;
  } catch (error) {
    console.error('Error checking Enterprise access:', error);
    return false;
  }
};

/**
 * Get Enterprise-level limits for a user (regular Enterprise or Enterprise-based custom package)
 */
export const getEnterpriseLevelLimits = async (userId: string): Promise<CustomEnterpriseLimitSettings | null> => {
  try {
    const packageName = await getUserPackage(userId);

    // Regular Enterprise user - get from enterprise settings
    if (packageName === 'Enterprise') {
      const enterpriseLimits = await getEnterpriseLimitSettings();
      return {
        maxTeamMembers: enterpriseLimits.maxTeamMembers,
        maxImages: enterpriseLimits.maxImagesPerSubUser,
        deleteDelayHours: enterpriseLimits.deleteDelayHoursPerSubUser,
        maxSavedPrompts: enterpriseLimits.maxSavedPromptsPerSubUser,
        maxCustomPrompts: enterpriseLimits.maxCustomPromptsPerSubUser,
        monthlyGenerationLimit: 500 // Default for Enterprise
      };
    }

    // Check if it's an Enterprise-based custom package
    if (packageName !== 'Free' && packageName !== 'Pay-As-You-Go' && packageName !== 'Pro') {
      const { getCustomPackageType, getCustomPackageLimits } = await import('./customPackages');
      const packageType = await getCustomPackageType(packageName);

      if (packageType === 'enterprise-based') {
        const customLimits = await getCustomPackageLimits(packageName);
        if (customLimits && 'maxTeamMembers' in customLimits) {
          return customLimits as CustomEnterpriseLimitSettings;
        }
      }
    }

    return null;
  } catch (error) {
    console.error('Error getting Enterprise-level limits:', error);
    return null;
  }
};