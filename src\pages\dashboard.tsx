import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { User, Package, Calendar, Hash, Upload, FileText, PenTool, AlertTriangle, CheckCircle } from 'lucide-react';
import { db } from '@/lib/firebase';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { useStore } from '@/lib/store';
import { getLimitSettings, getUserUsage, getPayAsYouGoLimitSettings, getProLimitSettings, getEnterpriseLimitSettings, isTeamMember, hasEnterpriseAccess } from '@/lib/userLimits';
import { toast } from 'react-hot-toast';
import { getTeamMemberLimits } from '@/lib/teamMemberLimits';
import { Timestamp } from 'firebase/firestore';

interface UserStats {
  displayName: string;
  email: string;
  packageName: string;
  registrationDate: string;
  promptCount: number;
  uploadedImagesCount: number;
  customPromptsCount: number;
  savedPromptsCount: number;
  dailyGenerationCount: number;
  lastGenerationDate: Timestamp;
  subscriptionExpireDate?: string;
  isTeamMember?: boolean;
  teamCompanyName?: string;
}

interface LimitInfo {
  maxImages: number;
  deleteDelayHours: number;
  maxSavedPrompts: number;
  maxCustomPrompts: number;
  totalGenerationLimit?: number;
  monthlyGenerationLimit?: number;
  maxTeamMembers?: number; // For Enterprise Admin Owners
}

export default function Dashboard() {
  const { user } = useStore();
  const location = useLocation();
  const navigate = useNavigate();
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [limitInfo, setLimitInfo] = useState<LimitInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [teamMemberCount, setTeamMemberCount] = useState<number>(0);

  useEffect(() => {
    if (user) {
      fetchUserData();
    }
  }, [user]);

  // Fetch team member count for Enterprise Admin Owners
  const fetchTeamMemberCount = async () => {
    if (!user) return;

    try {
      // Check if user has Enterprise-level access (regular Enterprise or Enterprise-based custom package)
      const { hasEnterpriseAccess } = await import('@/lib/userLimits');
      const { isEnterpriseAdminOwner } = await import('@/lib/userRoles');

      const hasEnterpriseLevel = await hasEnterpriseAccess(user.id);
      const isAdminOwner = await isEnterpriseAdminOwner(user.id);

      // Only fetch team member count for Enterprise Admin Owners
      if (hasEnterpriseLevel && isAdminOwner) {
        const teamMembersRef = collection(db, 'teamMembers');
        const q = query(teamMembersRef, where('ownerId', '==', user.id));
        const querySnapshot = await getDocs(q);

        console.log('🔍 Dashboard: Team member count fetched:', querySnapshot.size);
        setTeamMemberCount(querySnapshot.size);
      } else {
        setTeamMemberCount(0);
      }
    } catch (error) {
      console.error('Error fetching team member count:', error);
      setTeamMemberCount(0);
    }
  };

  // Check for payment success and refresh data
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const paymentSuccess = urlParams.get('payment_success');
    const fromPayment = urlParams.get('from_payment');

    if (paymentSuccess === 'true' || fromPayment === 'true') {
      // Show success message
      toast.success('Payment successful! Your subscription has been activated.');

      // Trigger payment success event for subscription context
      window.dispatchEvent(new CustomEvent('paymentSuccess'));

      // Force refresh user data multiple times to ensure we get the updated data
      const refreshData = () => {
        if (user) {
          console.log('🔄 Refreshing user data after payment success...');
          fetchUserData();
        }
      };

      // Immediate refresh
      refreshData();

      // Additional refreshes with delays to handle potential timing issues
      setTimeout(refreshData, 2000);
      setTimeout(refreshData, 5000);
      setTimeout(refreshData, 10000);

      // Clean up URL parameters
      const newUrl = window.location.pathname;
      navigate(newUrl, { replace: true });
    }
  }, [location.search, user, navigate]);

  // Also refresh data when the component becomes visible again (user returns from payment)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && user) {
        console.log('🔄 Page became visible, refreshing user data...');
        fetchUserData();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [user]);

  const fetchUserData = async () => {
    if (!user) return;

    setLoading(true);
    try {
      console.log('🔍 Fetching user data for user ID:', user.id);
      console.log('🔍 Expected user ID from webhook logs: W7OUeQyNURXxe4vbE49qVlWW1lC2');
      console.log('🔍 User IDs match:', user.id === 'W7OUeQyNURXxe4vbE49qVlWW1lC2');

      // Fetch user data from Firestore
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('uid', '==', user.id));
      const querySnapshot = await getDocs(q);

      console.log('🔍 Query result - Documents found:', querySnapshot.size);

      let userData: any = {};
      let packageName = 'Free';

      if (!querySnapshot.empty) {
        userData = querySnapshot.docs[0].data();
        packageName = userData.packageName || 'Free';

        // Debug logging for payment verification
        console.log('🔍 Dashboard Debug - User Data:', {
          userId: user.id,
          documentId: querySnapshot.docs[0].id,
          packageName: userData.packageName,
          role: userData.role,
          subscriptionExpireDate: userData.subscriptionExpireDate,
          subscriptionStatus: userData.subscriptionStatus,
          lastPaymentDate: userData.lastPaymentDate,
          paymentMethod: userData.paymentMethod,
          hasSubscriptionObject: !!userData.subscription,
          fullUserData: userData
        });
      } else {
        console.log('❌ No user document found for user ID:', user.id);
      }

      // Get user usage data
      const usage = await getUserUsage(user.id);

      // Count total prompts generated
      const captionsRef = collection(db, 'savedCaptions');
      const captionsQuery = query(captionsRef, where('userId', '==', user.id));
      const captionsSnapshot = await getDocs(captionsQuery);
      const totalPrompts = captionsSnapshot.size;

      // Format registration date
      const registrationDate = userData.createdAt ?
        new Date(userData.createdAt).toLocaleDateString() :
        'N/A';

      // Check if user is a team member
      const isUserTeamMember = userData.isTeamMember || false;

      setUserStats({
        displayName: userData.displayName || user.email.split('@')[0],
        email: user.email,
        packageName: packageName,
        registrationDate: registrationDate,
        promptCount: totalPrompts,
        uploadedImagesCount: usage.uploadedImagesCount || 0,
        customPromptsCount: usage.customPromptsCount || 0,
        savedPromptsCount: usage.savedPromptsCount || 0,
        dailyGenerationCount: usage.dailyGenerationCount || 0,
        lastGenerationDate: usage.lastGenerationDate || Timestamp.now(),
        subscriptionExpireDate: userData.subscriptionExpireDate || null,
        isTeamMember: isUserTeamMember,
        teamCompanyName: userData.teamCompanyName || null
      });

      // Get appropriate limit settings based on package
      let limits: any = {};

      // Check if user is an Enterprise Admin Owner (for both regular Enterprise and Enterprise-based custom packages)
      const hasEnterpriseLevel = await hasEnterpriseAccess(user.id);
      const isEnterpriseAdminOwner = hasEnterpriseLevel && !isUserTeamMember;
      console.log('🔍 Dashboard: Enterprise Admin Owner check:', {
        hasEnterpriseLevel,
        isUserTeamMember,
        isEnterpriseAdminOwner
      });

      // Check if this is a custom package (role starts with "Custom:")
      const isCustomPackage = userData.role && userData.role.startsWith('Custom:');
      console.log('🔍 Custom Package Detection:');
      console.log('  - Role:', userData.role);
      console.log('  - Package Name:', packageName);
      console.log('  - Is Custom Package:', isCustomPackage);

      if (isUserTeamMember) {
        // For team members, get their specific limits from the invite
        const teamMemberLimits = await getTeamMemberLimits(user.id);
        console.log('Team member limits:', teamMemberLimits);

        setLimitInfo({
          maxImages: teamMemberLimits.maxImages,
          deleteDelayHours: teamMemberLimits.deleteDelayHours,
          maxSavedPrompts: teamMemberLimits.maxSavedPrompts,
          maxCustomPrompts: teamMemberLimits.maxCustomPrompts,
          monthlyGenerationLimit: teamMemberLimits.monthlyGenerationLimit
        });
      } else if (isCustomPackage) {
        // For custom packages, get their specific limits
        try {
          const { getCustomPackageLimits } = await import('@/lib/customPackages');
          // Use the packageName (which is the custom package name without "Custom:" prefix)
          console.log('Fetching custom package limits for:', packageName);
          const customLimits = await getCustomPackageLimits(packageName);
          console.log('Custom package limits fetched:', customLimits);

          if (customLimits) {
            setLimitInfo({
              maxImages: customLimits.maxImages,
              deleteDelayHours: customLimits.deleteDelayHours,
              maxSavedPrompts: customLimits.maxSavedPrompts,
              maxCustomPrompts: customLimits.maxCustomPrompts,
              monthlyGenerationLimit: customLimits.monthlyGenerationLimit,
              maxTeamMembers: 'maxTeamMembers' in customLimits ? customLimits.maxTeamMembers : undefined
            });
            console.log('✅ Custom package limits set successfully');
          } else {
            console.warn(`Custom package "${packageName}" not found, using Free limits`);
            limits = await getLimitSettings();
            setLimitInfo({
              maxImages: limits.maxImages,
              deleteDelayHours: limits.deleteDelayHours,
              maxSavedPrompts: limits.maxSavedPrompts,
              maxCustomPrompts: limits.maxCustomPrompts,
              totalGenerationLimit: limits.totalGenerationLimit
            });
          }
        } catch (error) {
          console.error('Error fetching custom package limits:', error);
          // Fallback to Free limits
          limits = await getLimitSettings();
          setLimitInfo({
            maxImages: limits.maxImages,
            deleteDelayHours: limits.deleteDelayHours,
            maxSavedPrompts: limits.maxSavedPrompts,
            maxCustomPrompts: limits.maxCustomPrompts,
            totalGenerationLimit: limits.totalGenerationLimit
          });
        }
      } else if (packageName === 'Free') {
        limits = await getLimitSettings();
        setLimitInfo({
          maxImages: limits.maxImages,
          deleteDelayHours: limits.deleteDelayHours,
          maxSavedPrompts: limits.maxSavedPrompts,
          maxCustomPrompts: limits.maxCustomPrompts,
          totalGenerationLimit: limits.totalGenerationLimit
        });
      } else if (packageName === 'Pay-As-You-Go') {
        limits = await getPayAsYouGoLimitSettings();
        setLimitInfo({
          maxImages: limits.maxImages,
          deleteDelayHours: limits.deleteDelayHours,
          maxSavedPrompts: limits.maxSavedPrompts,
          maxCustomPrompts: limits.maxCustomPrompts,
          totalGenerationLimit: limits.totalGenerationLimit
        });
      } else if (packageName === 'Pro') {
        limits = await getProLimitSettings();
        setLimitInfo({
          maxImages: limits.maxImages,
          deleteDelayHours: limits.deleteDelayHours,
          maxSavedPrompts: limits.maxSavedPrompts,
          maxCustomPrompts: limits.maxCustomPrompts,
          monthlyGenerationLimit: limits.monthlyGenerationLimit
        });
      } else if (packageName === 'Enterprise') {
        // Check if this is an Enterprise Admin Owner (not a team member)
        if (isEnterpriseAdminOwner) {
          // For Enterprise Admin Owners, directly fetch their specific limits from the settings
          const limitsDocRef = doc(db, 'settings', 'enterpriseAdminOwnerLimits');
          const limitsDoc = await getDoc(limitsDocRef);

          if (limitsDoc.exists()) {
            const adminOwnerLimits = limitsDoc.data();
            console.log('Found enterprise admin owner limits:', adminOwnerLimits);

            setLimitInfo({
              maxImages: adminOwnerLimits.maxImages || 200,
              deleteDelayHours: adminOwnerLimits.deleteDelayHours || 0,
              maxSavedPrompts: adminOwnerLimits.maxSavedPrompts || 150,
              maxCustomPrompts: adminOwnerLimits.maxCustomPrompts || 20,
              maxTeamMembers: adminOwnerLimits.maxTeamMembers || 5,
              monthlyGenerationLimit: adminOwnerLimits.monthlyGenerationLimit || 500
            });
          } else {
            // If no document exists, use default values
            console.log('No enterprise admin owner limits found, using defaults');
            setLimitInfo({
              maxImages: 200,
              deleteDelayHours: 0,
              maxSavedPrompts: 150,
              maxCustomPrompts: 20,
              maxTeamMembers: 5,
              monthlyGenerationLimit: 500
            });
          }
        } else {
          // For team members, get enterprise limits
          limits = await getEnterpriseLimitSettings();
          console.log('Enterprise limits fetched for team member:', limits);

          setLimitInfo({
            maxImages: limits.maxImagesPerSubUser,
            deleteDelayHours: limits.deleteDelayHoursPerSubUser,
            maxSavedPrompts: limits.maxSavedPromptsPerSubUser,
            maxCustomPrompts: limits.maxCustomPromptsPerSubUser
          });
        }
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
    } finally {
      setLoading(false);
    }

    // Fetch team member count after user data is loaded
    await fetchTeamMemberCount();
  };

  // Get progress bar color based on lifetime status and remaining amount
  const getProgressBarColor = (remaining: number, isLifetime: boolean = false) => {
    if (isLifetime) {
      return remaining > 0 ? 'bg-gradient-to-r from-yellow-500 to-orange-500' : 'bg-red-500';
    }
    return remaining > 0 ? 'bg-indigo-500' : 'bg-red-500';
  };

  // Calculate remaining limits
  const getRemainingLimits = () => {
    console.log('🔍 getRemainingLimits - userStats:', userStats);
    console.log('🔍 getRemainingLimits - limitInfo:', limitInfo);
    if (!userStats || !limitInfo) {
      console.log('❌ getRemainingLimits returning null - missing userStats or limitInfo');
      return null;
    }

    const remainingImages = limitInfo.maxImages - userStats.uploadedImagesCount;
    const remainingSavedPrompts = limitInfo.maxSavedPrompts - userStats.savedPromptsCount;
    const remainingCustomPrompts = limitInfo.maxCustomPrompts - userStats.customPromptsCount;

    // Calculate remaining generation limit
    let remainingGeneration = 0;
    let generationPeriod = '';

    if ((userStats.packageName === 'Free' || userStats.packageName === 'Pay-As-You-Go') && limitInfo.totalGenerationLimit) {
      // For free and Pay-As-You-Go users, we use a fixed total limit
      remainingGeneration = limitInfo.totalGenerationLimit - userStats.dailyGenerationCount;
      generationPeriod = 'total';
    } else if ((userStats.packageName === 'Pro' || userStats.packageName === 'Enterprise' || (userStats.packageName !== 'Free' && userStats.packageName !== 'Pay-As-You-Go')) && limitInfo.monthlyGenerationLimit) {
      // For Pro, Enterprise, and Custom package users, we track monthly usage
      // This is simplified and would need proper implementation
      remainingGeneration = limitInfo.monthlyGenerationLimit - userStats.dailyGenerationCount;
      generationPeriod = 'this month';
    }

    return {
      remainingImages,
      remainingSavedPrompts,
      remainingCustomPrompts,
      remainingGeneration,
      generationPeriod
    };
  };

  const remainingLimits = getRemainingLimits();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent">
          Your Dashboard
        </h1>
        <div className="flex gap-2">
          <button
            onClick={() => {
              console.log('🔄 Manual refresh triggered');
              fetchUserData();
            }}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm"
          >
            Refresh Data
          </button>
          <button
            onClick={async () => {
              if (!user) return;
              console.log('🔍 Direct Firestore query for user:', user.id);
              try {
                const usersRef = collection(db, 'users');
                const q = query(usersRef, where('uid', '==', user.id));
                const querySnapshot = await getDocs(q);

                if (!querySnapshot.empty) {
                  const userData = querySnapshot.docs[0].data();
                  console.log('📄 Direct Firestore data:', {
                    documentId: querySnapshot.docs[0].id,
                    packageName: userData.packageName,
                    subscriptionStatus: userData.subscriptionStatus,
                    lastPaymentDate: userData.lastPaymentDate,
                    updatedAt: userData.updatedAt,
                    fullData: userData
                  });
                } else {
                  console.log('❌ No user document found');
                }
              } catch (error) {
                console.error('❌ Error querying Firestore:', error);
              }
            }}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm"
          >
            Check Firestore
          </button>
          <button
            onClick={async () => {
              if (!user) return;
              console.log('🔍 Testing custom package fetching...');
              try {
                const { getCustomPackageLimits } = await import('@/lib/customPackages');
                const testPackageName = 'License Tier 1';
                console.log('Testing with package name:', testPackageName);
                const limits = await getCustomPackageLimits(testPackageName);
                console.log('Custom package limits result:', limits);
              } catch (error) {
                console.error('❌ Error testing custom package:', error);
              }
            }}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm"
          >
            Test Custom Package
          </button>
          <button
            onClick={async () => {
              console.log('🔍 Listing all custom packages...');
              try {
                const { getAllCustomPackages } = await import('@/lib/customPackages');
                const packages = await getAllCustomPackages();
                console.log('All custom packages:', packages);
                packages.forEach((pkg, index) => {
                  console.log(`Package ${index + 1}:`, {
                    id: pkg.id,
                    name: pkg.name,
                    type: pkg.type,
                    isActive: pkg.isActive,
                    limits: pkg.limits
                  });
                });
              } catch (error) {
                console.error('❌ Error listing custom packages:', error);
              }
            }}
            className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg text-sm"
          >
            List Packages
          </button>
          <button
            onClick={async () => {
              if (!user) return;
              console.log('🧪 Testing all limit enforcement functions...');
              try {
                const { canUploadMoreImages, canSaveMorePrompts, canCreateMoreCustomPrompts, canGenerateMorePrompts } = await import('@/lib/userLimits');

                console.log('📊 Testing limit functions for user:', user.id);

                const canUpload = await canUploadMoreImages(user.id);
                console.log('🖼️ canUploadMoreImages:', canUpload);

                const canSave = await canSaveMorePrompts(user.id);
                console.log('💾 canSaveMorePrompts:', canSave);

                const canCreateCustom = await canCreateMoreCustomPrompts(user.id);
                console.log('🎨 canCreateMoreCustomPrompts:', canCreateCustom);

                const canGenerate = await canGenerateMorePrompts(user.id);
                console.log('⚡ canGenerateMorePrompts:', canGenerate);

                console.log('✅ All limit tests completed');
              } catch (error) {
                console.error('❌ Error testing limits:', error);
              }
            }}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm"
          >
            Test Limits
          </button>
          <button
            onClick={async () => {
              if (!user) return;
              console.log('🧪 Testing Enterprise-based Custom Package functionality...');
              try {
                const { hasEnterpriseAccess } = await import('@/lib/userLimits');
                const { isEnterpriseAdminOwner } = await import('@/lib/userRoles');
                const { getCustomPackageType } = await import('@/lib/customPackages');

                console.log('🔍 Enterprise-based Custom Package Tests:');

                // Test 1: Enterprise access detection
                const hasEnterprise = await hasEnterpriseAccess(user.id);
                console.log('✅ hasEnterpriseAccess:', hasEnterprise);

                // Test 2: Enterprise Admin Owner detection
                const isAdminOwner = await isEnterpriseAdminOwner(user.id);
                console.log('✅ isEnterpriseAdminOwner:', isAdminOwner);

                // Test 3: Custom package type detection
                const packageName = userStats?.packageName;
                if (packageName && packageName !== 'Free' && packageName !== 'Pay-As-You-Go' && packageName !== 'Pro' && packageName !== 'Enterprise') {
                  const packageType = await getCustomPackageType(packageName);
                  console.log('✅ Custom package type:', packageType);
                  console.log('✅ Is Enterprise-based:', packageType === 'enterprise-based');
                }

                console.log('🎯 Expected Results for Enterprise-based Custom Package:');
                console.log('  - Team Management button should be visible:', hasEnterprise && isAdminOwner);
                console.log('  - Company/Organization field should be visible in Profile Settings');
                console.log('  - All Enterprise features should be accessible');

                console.log('✅ All Enterprise-based Custom Package tests completed');
              } catch (error) {
                console.error('❌ Error testing Enterprise functionality:', error);
              }
            }}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm"
          >
            Test Enterprise Features
          </button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      ) : userStats ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* User Information Card */}
          <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <div className="p-6 border-b border-gray-700">
              <h2 className="text-xl font-semibold text-white mb-4">Account Information</h2>

              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="bg-purple-500 p-2 rounded-full">
                    <User className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">User</p>
                    <p className="font-medium text-white">{userStats.displayName}</p>
                    <p className="text-sm text-gray-300">{userStats.email}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="bg-pink-500 p-2 rounded-full">
                    <Package className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Package</p>
                    <p className="font-medium text-white">{userStats.packageName}</p>
                    {userStats.isTeamMember && userStats.teamCompanyName && (
                      <p className="text-xs text-purple-400">
                        Team member of {userStats.teamCompanyName}
                      </p>
                    )}
                    {(userStats.packageName === 'Pro' || userStats.packageName === 'Enterprise' || userStats.packageName.startsWith('Custom:')) && userStats.subscriptionExpireDate && (
                      <div>
                        {userStats.subscriptionExpireDate === 'lifetime' ? (
                          <p className="text-xs bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent font-semibold flex items-center">
                            <span className="mr-1">✨</span>
                            Lifetime Access
                          </p>
                        ) : (
                          <p className={`text-xs ${new Date(userStats.subscriptionExpireDate) > new Date() ? 'text-green-400' : 'text-red-400'}`}>
                            {new Date(userStats.subscriptionExpireDate) > new Date()
                              ? `Expires: ${new Date(userStats.subscriptionExpireDate).toLocaleDateString()}`
                              : `Expired: ${new Date(userStats.subscriptionExpireDate).toLocaleDateString()}`}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="bg-blue-500 p-2 rounded-full">
                    <Calendar className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Registration Date</p>
                    <p className="font-medium text-white">{userStats.registrationDate}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="bg-green-500 p-2 rounded-full">
                    <Hash className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Total Prompts Generated</p>
                    <p className="font-medium text-white">{userStats.promptCount}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Usage Limits Card */}
          <div className={`bg-gray-800 rounded-lg shadow-lg overflow-hidden ${
            userStats?.subscriptionExpireDate === 'lifetime'
              ? 'ring-2 ring-yellow-500/30 shadow-yellow-500/10'
              : ''
          }`}>
            <div className="p-6 border-b border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-white">Usage Limits</h2>

                {/* Lifetime Deal Indicator */}
                {userStats?.subscriptionExpireDate === 'lifetime' && (
                  <div className="px-3 py-1 rounded-full text-xs bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-300 border border-yellow-500/30 flex items-center space-x-1 animate-pulse">
                    <span>✨</span>
                    <span className="font-medium">Lifetime Access</span>
                  </div>
                )}
              </div>

              {/* Lifetime Deal Banner - Full Width */}
              {userStats?.subscriptionExpireDate === 'lifetime' && (
                <div className="mb-4 p-3 rounded-lg bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/20">
                  <div className="flex items-center space-x-2">
                    <span className="text-yellow-400 text-lg">🎉</span>
                    <div>
                      <p className="text-yellow-300 font-medium text-sm">Lifetime Deal Active</p>
                      <p className="text-yellow-400/80 text-xs">Enjoy unlimited access to your package features forever!</p>
                    </div>
                  </div>
                </div>
              )}

              {remainingLimits && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="bg-indigo-500 p-2 rounded-full">
                      <Upload className="h-5 w-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <p className="text-sm text-gray-400">Images</p>
                        <p className="text-sm text-gray-400">
                          {userStats.uploadedImagesCount} / {limitInfo?.maxImages}
                        </p>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2.5 mt-1">
                        <div
                          className={`h-2.5 rounded-full ${getProgressBarColor(remainingLimits.remainingImages, userStats?.subscriptionExpireDate === 'lifetime')}`}
                          style={{ width: `${Math.min(100, (userStats.uploadedImagesCount / (limitInfo?.maxImages || 1)) * 100)}%` }}
                        ></div>
                      </div>
                      {remainingLimits.remainingImages <= 0 && (
                        <p className="text-xs text-red-400 mt-1 flex items-center">
                          <AlertTriangle className="h-3 w-3 mr-1" /> Upload limit reached
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="bg-orange-500 p-2 rounded-full">
                      <FileText className="h-5 w-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <p className="text-sm text-gray-400">Saved Prompts</p>
                        <p className="text-sm text-gray-400">
                          {userStats.savedPromptsCount} / {limitInfo?.maxSavedPrompts}
                        </p>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2.5 mt-1">
                        <div
                          className={`h-2.5 rounded-full ${getProgressBarColor(remainingLimits.remainingSavedPrompts, userStats?.subscriptionExpireDate === 'lifetime')}`}
                          style={{ width: `${Math.min(100, (userStats.savedPromptsCount / (limitInfo?.maxSavedPrompts || 1)) * 100)}%` }}
                        ></div>
                      </div>
                      {remainingLimits.remainingSavedPrompts <= 0 && (
                        <p className="text-xs text-red-400 mt-1 flex items-center">
                          <AlertTriangle className="h-3 w-3 mr-1" /> Save limit reached
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="bg-yellow-500 p-2 rounded-full">
                      <PenTool className="h-5 w-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <p className="text-sm text-gray-400">Custom Prompts</p>
                        <p className="text-sm text-gray-400">
                          {userStats.customPromptsCount} / {limitInfo?.maxCustomPrompts}
                        </p>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2.5 mt-1">
                        <div
                          className={`h-2.5 rounded-full ${getProgressBarColor(remainingLimits.remainingCustomPrompts, userStats?.subscriptionExpireDate === 'lifetime')}`}
                          style={{ width: `${Math.min(100, (userStats.customPromptsCount / (limitInfo?.maxCustomPrompts || 1)) * 100)}%` }}
                        ></div>
                      </div>
                      {remainingLimits.remainingCustomPrompts <= 0 && (
                        <p className="text-xs text-red-400 mt-1 flex items-center">
                          <AlertTriangle className="h-3 w-3 mr-1" /> Custom prompt limit reached
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Team Members Limit - Only for Enterprise Admin Owners and Custom Enterprise packages */}
                  {((userStats.packageName === 'Enterprise' && !userStats.isTeamMember) ||
                    (userStats.packageName !== 'Free' && userStats.packageName !== 'Pay-As-You-Go' && userStats.packageName !== 'Pro' && userStats.packageName !== 'Enterprise' && limitInfo?.maxTeamMembers !== undefined)) && (
                    <div className="flex items-center space-x-3">
                      <div className="bg-purple-500 p-2 rounded-full">
                        <User className="h-5 w-5 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between">
                          <p className="text-sm text-gray-400">Team Members</p>
                          <p className="text-sm text-gray-400">
                            {teamMemberCount} / {limitInfo?.maxTeamMembers || 0}
                          </p>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2.5 mt-1">
                          <div
                            className={`h-2.5 rounded-full ${userStats?.subscriptionExpireDate === 'lifetime' ? 'bg-gradient-to-r from-yellow-500 to-orange-500' : 'bg-purple-500'}`}
                            style={{ width: `${limitInfo?.maxTeamMembers ? Math.min(100, (teamMemberCount / limitInfo.maxTeamMembers) * 100) : 0}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-gray-400 mt-1">
                          You can invite up to {limitInfo?.maxTeamMembers || 0} team members
                        </p>
                      </div>
                    </div>
                  )}

                  {(userStats.packageName === 'Free' || userStats.packageName === 'Pay-As-You-Go' || userStats.packageName === 'Pro' || userStats.packageName === 'Enterprise' || (userStats.packageName !== 'Free' && userStats.packageName !== 'Pay-As-You-Go' && userStats.packageName !== 'Pro' && userStats.packageName !== 'Enterprise')) && (
                    <div className="flex items-center space-x-3">
                      <div className="bg-teal-500 p-2 rounded-full">
                        <Hash className="h-5 w-5 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between">
                          <p className="text-sm text-gray-400">
                            {userStats.packageName === 'Free' || userStats.packageName === 'Pay-As-You-Go' ? 'Lifetime Prompt Limit' : `Prompts ${remainingLimits.generationPeriod}`}
                          </p>
                          <p className="text-sm text-gray-400">
                            {userStats.dailyGenerationCount} /
                            {userStats.packageName === 'Free' || userStats.packageName === 'Pay-As-You-Go' ? limitInfo?.totalGenerationLimit : limitInfo?.monthlyGenerationLimit}
                          </p>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2.5 mt-1">
                          <div
                            className={`h-2.5 rounded-full ${getProgressBarColor(remainingLimits.remainingGeneration, userStats?.subscriptionExpireDate === 'lifetime')}`}
                            style={{
                              width: `${Math.min(100, (userStats.packageName === 'Free' || userStats.packageName === 'Pay-As-You-Go' ?
                                (userStats.dailyGenerationCount / (limitInfo?.totalGenerationLimit || 1)) :
                                (userStats.dailyGenerationCount / (limitInfo?.monthlyGenerationLimit || 1))) * 100)}%`
                            }}
                          ></div>
                        </div>
                        {remainingLimits.remainingGeneration <= 0 && (
                          <p className="text-xs text-red-400 mt-1 flex items-center">
                            <AlertTriangle className="h-3 w-3 mr-1" /> Generation limit reached
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {(userStats.packageName === 'Free' || userStats.packageName === 'Pay-As-You-Go') && limitInfo?.deleteDelayHours > 0 && (
                    <div className="mt-4 p-3 bg-gray-700 rounded-lg">
                      <div className="flex items-center">
                        <AlertTriangle className="h-5 w-5 text-yellow-400 mr-2" />
                        <p className="text-sm text-gray-300">
                          {userStats.packageName === 'Free' ? 'Free' : 'Pay-As-You-Go'} users must wait {limitInfo.deleteDelayHours} hours before deleting uploaded images
                        </p>
                      </div>
                    </div>
                  )}

                  {userStats.packageName === 'Pay-As-You-Go' && (
                    <div className="mt-4 p-3 bg-gray-700 rounded-lg">
                      <div className="flex items-center">
                        <CheckCircle className="h-5 w-5 text-cyan-400 mr-2" />
                        <p className="text-sm text-gray-300">
                          Pay-As-You-Go users can purchase additional generations as needed. Each $1 provides 15 generations.
                        </p>
                      </div>
                    </div>
                  )}

                  {userStats.packageName === 'Enterprise' && (
                    <div className="mt-4 p-3 bg-gray-700 rounded-lg">
                      <div className="flex items-center">
                        <CheckCircle className="h-5 w-5 text-green-400 mr-2" />
                        <p className="text-sm text-gray-300">
                          Enterprise users have additional benefits. Contact support for more information.
                        </p>
                      </div>

                      {/* Display Team Member Limits for Enterprise Team Members */}
                      {userStats.isTeamMember && (
                        <div className="mt-3 pt-3 border-t border-gray-600">
                          <p className="text-sm font-medium text-white mb-2">Your Team Member Limits:</p>
                          <ul className="text-xs text-gray-300 space-y-1 list-disc pl-4">
                            <li>Max Images: {limitInfo?.maxImages}</li>
                            <li>Max Saved Prompts: {limitInfo?.maxSavedPrompts}</li>
                            <li>Max Custom Prompts: {limitInfo?.maxCustomPrompts}</li>
                            <li>Max Prompts Generated Monthly: {limitInfo?.monthlyGenerationLimit}</li>
                            {limitInfo?.deleteDelayHours > 0 && (
                              <li>Image Delete Delay: {limitInfo?.deleteDelayHours} hours</li>
                            )}
                          </ul>
                        </div>
                      )}

                      {/* Display Team Members limit for Enterprise Admin Owners */}
                      {limitInfo?.maxTeamMembers && (
                        <div className="mt-3 pt-3 border-t border-gray-600">
                          <div className="flex items-center space-x-3">
                            <div className="bg-purple-500 p-2 rounded-full">
                              <User className="h-5 w-5 text-white" />
                            </div>
                            <div className="flex-1">
                              <div className="flex justify-between">
                                <p className="text-sm text-gray-400">Team Members</p>
                                <p className="text-sm text-gray-400">
                                  {/* This would ideally show current team members count / max */}
                                  Max: {limitInfo.maxTeamMembers}
                                </p>
                              </div>
                              <p className="text-xs text-gray-400 mt-1">
                                You can add up to {limitInfo.maxTeamMembers} team members to your Enterprise account
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Display Enterprise Admin Owner Limits */}
                      {limitInfo?.maxTeamMembers && (
                        <div className="mt-3 pt-3 border-t border-gray-600">
                          <p className="text-sm font-medium text-white mb-2">Enterprise Admin Owner Limits:</p>
                          <ul className="text-xs text-gray-300 space-y-1 list-disc pl-4">
                            <li>Max Images: {limitInfo.maxImages}</li>
                            <li>Max Saved Prompts: {limitInfo.maxSavedPrompts}</li>
                            <li>Max Custom Prompts: {limitInfo.maxCustomPrompts}</li>
                            <li>Max Prompts Generated Monthly: {limitInfo.monthlyGenerationLimit}</li>
                            {limitInfo.deleteDelayHours > 0 && (
                              <li>Image Delete Delay: {limitInfo.deleteDelayHours} hours</li>
                            )}
                          </ul>
                          <p className="text-xs text-gray-400 mt-2">
                            <a href="/app/team" className="text-purple-400 hover:text-purple-300">
                              Manage your team →
                            </a>
                          </p>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Subscription expiration warning */}
                  {(userStats.packageName === 'Pro' || userStats.packageName === 'Enterprise' || userStats.packageName.startsWith('Custom:')) &&
                   userStats.subscriptionExpireDate &&
                   userStats.subscriptionExpireDate !== 'lifetime' &&
                   new Date(userStats.subscriptionExpireDate) < new Date() && (
                    <div className="mt-4 p-3 bg-red-900/30 border border-red-700 rounded-lg">
                      <div className="flex items-center">
                        <AlertTriangle className="h-5 w-5 text-red-400 mr-2" />
                        <p className="text-sm text-red-300">
                          Your subscription has expired. Please renew to continue using premium features.
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Lifetime Deal Special Display */}
                  {(userStats.packageName === 'Pro' || userStats.packageName === 'Enterprise' || userStats.packageName.startsWith('Custom:')) &&
                   userStats.subscriptionExpireDate === 'lifetime' && (
                    <div className="mt-4 p-3 bg-gradient-to-r from-yellow-900/30 to-orange-900/30 border border-yellow-600/50 rounded-lg">
                      <div className="flex items-center">
                        <div className="bg-gradient-to-r from-yellow-400 to-orange-500 p-1 rounded-full mr-2">
                          <CheckCircle className="h-4 w-4 text-white" />
                        </div>
                        <p className="text-sm bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent font-semibold">
                          🎉 You have Lifetime Access! No expiration, no renewals needed.
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-12 text-gray-400">
          <p>Unable to load user data. Please try again later.</p>
        </div>
      )}
    </div>
  );
}