# Billing Details Step Implementation

This document describes the implementation of the "Billing Details" step in the subscription upgrade flow.

## Overview

When users click on "Get Started", "Upgrade to Pro", or "Upgrade to Enterprise" buttons, they are now directed to a "Billing Details" step before proceeding to checkout. This step:

1. Shows saved billing details if the user has already filled them out in the "Profile and Settings" page
2. Shows a form to fill out billing details if the user hasn't filled them out yet
3. Displays the selected package details (Pay-As-You-Go, Pro, or Enterprise)

## Files Modified

1. Created new component: `src/components/billing-details-step.tsx`
2. Updated: `src/components/ui/upgrade-plan-modal.tsx`
3. Updated: `src/components/ui/expired-package-modal.tsx`
4. Updated: `src/pages/landing.tsx`

## Testing the Implementation

### Test Case 1: New User Without Billing Details

1. Sign up for a new account
2. Click on "Upgrade Now" in the sidebar
3. Select a plan (Pay-As-You-Go, Pro, or Enterprise)
4. Click on the corresponding button (Get Started, Upgrade to Pro, or Upgrade to Enterprise)
5. Verify that the Billing Details form is displayed
6. Fill out the form and click "Save & Proceed to Checkout"
7. Verify that the billing details are saved to Firestore
8. Verify that the checkout page opens

### Test Case 2: User With Existing Billing Details

1. Sign in with an account that has already filled out billing details
2. Click on "Upgrade Now" in the sidebar
3. Select a plan
4. Click on the corresponding button
5. Verify that the saved billing details are displayed
6. Click "Proceed to Checkout"
7. Verify that the checkout page opens

### Test Case 3: Expired Package Renewal

1. Sign in with an account that has an expired package
2. Verify that the expired package modal is displayed
3. Select a billing period (Monthly or Yearly)
4. Verify that the Billing Details step is displayed
5. Proceed with the checkout process

## Firestore Rules

The existing Firestore rules already include proper access control for the `billingDetails` collection:

```
// Billing details collection - allow users to manage their own billing information
match /billingDetails/{docId} {
  allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;
  allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
  allow update: if isAuthenticated() && resource.data.userId == request.auth.uid;
  allow delete: if isAuthenticated() && resource.data.userId == request.auth.uid;
}
```

To apply these rules, run the `deploy-firestore-rules.bat` script.

## Data Structure

The billing details are stored in the `billingDetails` collection with the following structure:

```json
{
  "userId": "user-id",
  "fullName": "John Doe",
  "address": "123 Main St",
  "city": "New York",
  "state": "NY",
  "zipCode": "10001",
  "country": "United States",
  "createdAt": "2023-06-01T12:00:00.000Z",
  "updatedAt": "2023-06-01T12:00:00.000Z"
}
```

## Future Improvements

1. Add validation for billing details fields
2. Add support for multiple billing addresses
3. Integrate with a payment processor API for real-time validation
4. Add support for saving credit card information securely
