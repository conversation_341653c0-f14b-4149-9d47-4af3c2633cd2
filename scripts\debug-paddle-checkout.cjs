// Debug script to investigate Paddle checkout 403 Forbidden error
// This script will test various aspects of the sandbox setup to identify the root cause

const https = require('https');
require('dotenv').config();

// Sandbox configuration
const SANDBOX_CONFIG = {
  apiKey: process.env.VITE_PADDLE_API_KEY_SANDBOX,
  clientToken: process.env.VITE_PADDLE_CLIENT_SIDE_TOKEN_SANDBOX,
  apiEndpoint: 'https://sandbox-api.paddle.com'
};

// Production configuration for comparison
const PRODUCTION_CONFIG = {
  apiKey: process.env.VITE_PADDLE_API_KEY_PRODUCTION || process.env.VITE_PADDLE_API_KEY,
  clientToken: process.env.VITE_PADDLE_CLIENT_SIDE_TOKEN_PRODUCTION || process.env.VITE_PADDLE_CLIENT_SIDE_TOKEN,
  apiEndpoint: 'https://api.paddle.com'
};

// Test price ID from sandbox
const TEST_PRICE_ID = 'pri_01jxejv3jrz3qpfynwg325zr91'; // Pay-As-You-Go sandbox price

// Make API request
function makeRequest(config, method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, config.apiEndpoint);
    
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: response
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: body
          });
        }
      });
    });

    req.on('error', (error) => reject(error));

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Test API key permissions
async function testAPIKeyPermissions(config, envName) {
  console.log(`\n🔑 Testing ${envName} API Key Permissions...`);
  
  const tests = [
    { path: '/products', name: 'List Products' },
    { path: '/prices', name: 'List Prices' },
    { path: `/prices/${TEST_PRICE_ID}`, name: 'Get Specific Price' },
    { path: '/customers', name: 'List Customers' },
    { path: '/transactions', name: 'List Transactions' }
  ];

  for (const test of tests) {
    try {
      const response = await makeRequest(config, 'GET', test.path);
      
      if (response.statusCode === 200) {
        console.log(`✅ ${test.name}: Success`);
      } else if (response.statusCode === 403) {
        console.log(`❌ ${test.name}: 403 Forbidden - API key lacks permission`);
      } else {
        console.log(`⚠️ ${test.name}: ${response.statusCode} - ${JSON.stringify(response.data).substring(0, 100)}...`);
      }
    } catch (error) {
      console.log(`❌ ${test.name}: Error - ${error.message}`);
    }
  }
}

// Test checkout prerequisites
async function testCheckoutPrerequisites(config, envName) {
  console.log(`\n🛒 Testing ${envName} Checkout Prerequisites...`);
  
  try {
    // Test if we can access the specific price
    const priceResponse = await makeRequest(config, 'GET', `/prices/${TEST_PRICE_ID}`);
    
    if (priceResponse.statusCode === 200) {
      const price = priceResponse.data.data;
      console.log(`✅ Price accessible: ${price.description}`);
      console.log(`   Amount: ${price.unit_price.amount} ${price.unit_price.currency_code}`);
      console.log(`   Status: ${price.status}`);
      console.log(`   Product ID: ${price.product_id}`);
      
      // Test if we can access the product
      const productResponse = await makeRequest(config, 'GET', `/products/${price.product_id}`);
      
      if (productResponse.statusCode === 200) {
        const product = productResponse.data.data;
        console.log(`✅ Product accessible: ${product.name}`);
        console.log(`   Status: ${product.status}`);
        console.log(`   Tax Category: ${product.tax_category}`);
      } else {
        console.log(`❌ Product not accessible: ${productResponse.statusCode}`);
      }
      
    } else {
      console.log(`❌ Price not accessible: ${priceResponse.statusCode}`);
      console.log(`   Response: ${JSON.stringify(priceResponse.data)}`);
    }
  } catch (error) {
    console.log(`❌ Checkout prerequisites test failed: ${error.message}`);
  }
}

// Test token validation
function testTokenValidation() {
  console.log('\n🎫 Testing Token Validation...');
  
  // Test sandbox token
  const sandboxToken = SANDBOX_CONFIG.clientToken;
  const isValidSandboxToken = sandboxToken && sandboxToken.startsWith('test_');
  console.log(`Sandbox Token: ${sandboxToken ? sandboxToken.substring(0, 20) + '...' : 'MISSING'}`);
  console.log(`Sandbox Token Valid: ${isValidSandboxToken ? '✅ YES' : '❌ NO'}`);
  
  // Test production token
  const productionToken = PRODUCTION_CONFIG.clientToken;
  const isValidProductionToken = productionToken && productionToken.startsWith('live_');
  console.log(`Production Token: ${productionToken ? productionToken.substring(0, 20) + '...' : 'MISSING'}`);
  console.log(`Production Token Valid: ${isValidProductionToken ? '✅ YES' : '❌ NO'}`);
  
  return isValidSandboxToken && isValidProductionToken;
}

// Test API key format
function testAPIKeyFormat() {
  console.log('\n🔐 Testing API Key Format...');
  
  // Test sandbox API key
  const sandboxApiKey = SANDBOX_CONFIG.apiKey;
  const isValidSandboxApiKey = sandboxApiKey && sandboxApiKey.startsWith('pdl_sdbx_');
  console.log(`Sandbox API Key: ${sandboxApiKey ? sandboxApiKey.substring(0, 25) + '...' : 'MISSING'}`);
  console.log(`Sandbox API Key Valid: ${isValidSandboxApiKey ? '✅ YES' : '❌ NO'}`);
  
  // Test production API key
  const productionApiKey = PRODUCTION_CONFIG.apiKey;
  const isValidProductionApiKey = productionApiKey && productionApiKey.startsWith('pdl_live_');
  console.log(`Production API Key: ${productionApiKey ? productionApiKey.substring(0, 25) + '...' : 'MISSING'}`);
  console.log(`Production API Key Valid: ${isValidProductionApiKey ? '✅ YES' : '❌ NO'}`);
  
  return isValidSandboxApiKey && isValidProductionApiKey;
}

// Test checkout simulation (without actually creating a checkout)
async function testCheckoutSimulation(config, envName) {
  console.log(`\n🧪 Testing ${envName} Checkout Simulation...`);
  
  // Simulate the data that would be sent to Paddle checkout
  const checkoutData = {
    items: [{ priceId: TEST_PRICE_ID, quantity: 1 }],
    customer: { email: '<EMAIL>' },
    customData: {
      userId: 'test-user-123',
      environment: envName.toLowerCase(),
      testMode: envName === 'Sandbox'
    },
    successUrl: 'http://localhost:5173/payment/success?session_id={checkout.id}'
  };
  
  console.log('📋 Checkout Data Structure:');
  console.log(JSON.stringify(checkoutData, null, 2));
  
  // Validate the price exists and is active
  try {
    const priceResponse = await makeRequest(config, 'GET', `/prices/${TEST_PRICE_ID}`);
    
    if (priceResponse.statusCode === 200) {
      const price = priceResponse.data.data;
      
      if (price.status === 'active') {
        console.log('✅ Price is active and ready for checkout');
      } else {
        console.log(`❌ Price status is not active: ${price.status}`);
      }
      
      // Check if price has any restrictions
      if (price.quantity && price.quantity.minimum > 1) {
        console.log(`⚠️ Price has minimum quantity requirement: ${price.quantity.minimum}`);
      }
      
      if (price.quantity && price.quantity.maximum && price.quantity.maximum < 1) {
        console.log(`❌ Price has maximum quantity restriction: ${price.quantity.maximum}`);
      }
      
    } else {
      console.log(`❌ Cannot validate price for checkout: ${priceResponse.statusCode}`);
    }
  } catch (error) {
    console.log(`❌ Checkout simulation failed: ${error.message}`);
  }
}

// Main debug function
async function main() {
  console.log('🔍 Paddle Checkout 403 Forbidden Debug Analysis\n');
  console.log('=' * 60);
  
  // 1. Test basic configuration
  console.log('1️⃣ CONFIGURATION VALIDATION');
  const tokensValid = testTokenValidation();
  const apiKeysValid = testAPIKeyFormat();
  
  if (!tokensValid || !apiKeysValid) {
    console.log('\n❌ Configuration validation failed. Please check your credentials.');
    return;
  }
  
  // 2. Test API key permissions for both environments
  console.log('\n2️⃣ API KEY PERMISSIONS TEST');
  await testAPIKeyPermissions(SANDBOX_CONFIG, 'Sandbox');
  await testAPIKeyPermissions(PRODUCTION_CONFIG, 'Production');
  
  // 3. Test checkout prerequisites
  console.log('\n3️⃣ CHECKOUT PREREQUISITES TEST');
  await testCheckoutPrerequisites(SANDBOX_CONFIG, 'Sandbox');
  await testCheckoutPrerequisites(PRODUCTION_CONFIG, 'Production');
  
  // 4. Test checkout simulation
  console.log('\n4️⃣ CHECKOUT SIMULATION TEST');
  await testCheckoutSimulation(SANDBOX_CONFIG, 'Sandbox');
  
  console.log('\n' + '=' * 60);
  console.log('🎯 ANALYSIS COMPLETE');
  console.log('\n📋 NEXT STEPS:');
  console.log('1. Review the test results above');
  console.log('2. Check for any 403 Forbidden errors in API permissions');
  console.log('3. Verify that sandbox credentials have checkout permissions');
  console.log('4. If all tests pass, the issue might be in the frontend Paddle SDK setup');
  console.log('5. Check browser console for additional Paddle SDK errors');
  
  console.log('\n🔧 DEBUGGING TIPS:');
  console.log('- 403 Forbidden usually means API key lacks required permissions');
  console.log('- Sandbox API keys might have different permission scopes than production');
  console.log('- Check Paddle dashboard for API key permission settings');
  console.log('- Ensure the client-side token matches the API key environment');
}

// Run the debug analysis
if (require.main === module) {
  main().catch(error => {
    console.error('\n❌ Debug analysis failed:', error.message);
    process.exit(1);
  });
}
