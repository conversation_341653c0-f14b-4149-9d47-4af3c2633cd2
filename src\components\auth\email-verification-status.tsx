import { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { toast } from 'react-hot-toast';
import { Mail, CheckCircle, AlertCircle, RefreshCw, Clock } from 'lucide-react';
import { sendVerificationEmail, checkEmailVerification } from '../../lib/firebase';
import { useAuth } from '../../lib/authProvider';

interface EmailVerificationStatusProps {
  onVerificationComplete?: () => void;
  showTitle?: boolean;
  className?: string;
}

export function EmailVerificationStatus({ 
  onVerificationComplete, 
  showTitle = true,
  className = ""
}: EmailVerificationStatusProps) {
  const [isVerified, setIsVerified] = useState(false);
  const [loading, setLoading] = useState(true);
  const [resendLoading, setResendLoading] = useState(false);
  const [lastResendTime, setLastResendTime] = useState<number | null>(null);
  const [cooldownTime, setCooldownTime] = useState(0);
  const { currentUser } = useAuth();

  // Cooldown timer (60 seconds)
  const RESEND_COOLDOWN = 60;

  useEffect(() => {
    checkVerificationStatus();
  }, [currentUser]);

  useEffect(() => {
    // Cooldown timer
    let interval: NodeJS.Timeout;
    if (cooldownTime > 0) {
      interval = setInterval(() => {
        setCooldownTime(prev => {
          if (prev <= 1) {
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [cooldownTime]);

  const checkVerificationStatus = async () => {
    setLoading(true);
    try {
      const result = await checkEmailVerification();
      if (result.error) {
        toast.error(result.error);
      } else {
        setIsVerified(result.isVerified);
        if (result.isVerified && onVerificationComplete) {
          onVerificationComplete();
        }
      }
    } catch (error) {
      console.error('Error checking verification status:', error);
      toast.error('Failed to check verification status');
    } finally {
      setLoading(false);
    }
  };

  const handleResendVerification = async () => {
    if (cooldownTime > 0) {
      toast.error(`Please wait ${cooldownTime} seconds before resending`);
      return;
    }

    setResendLoading(true);
    try {
      const result = await sendVerificationEmail();
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('Verification email sent! Please check your inbox.');
        setLastResendTime(Date.now());
        setCooldownTime(RESEND_COOLDOWN);
      }
    } catch (error) {
      console.error('Error resending verification email:', error);
      toast.error('Failed to send verification email');
    } finally {
      setResendLoading(false);
    }
  };

  const handleCheckAgain = async () => {
    await checkVerificationStatus();
    if (isVerified) {
      toast.success('Email verified successfully!');
    } else {
      toast.info('Email not yet verified. Please check your inbox and click the verification link.');
    }
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-6 ${className}`}>
        <RefreshCw className="h-6 w-6 animate-spin text-purple-500" />
        <span className="ml-2 text-gray-300">Checking verification status...</span>
      </div>
    );
  }

  if (isVerified) {
    return (
      <div className={`bg-green-900/20 border border-green-500/30 rounded-lg p-6 ${className}`}>
        <div className="flex items-center">
          <CheckCircle className="h-6 w-6 text-green-500 mr-3" />
          <div>
            <h3 className="text-lg font-semibold text-green-400">Email Verified!</h3>
            <p className="text-green-300 text-sm">Your email address has been successfully verified.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-6 ${className}`}>
      {showTitle && (
        <div className="flex items-center mb-4">
          <Mail className="h-6 w-6 text-yellow-500 mr-3" />
          <h3 className="text-lg font-semibold text-yellow-400">Verify Your Email</h3>
        </div>
      )}
      
      <div className="space-y-4">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-gray-300">
            <p className="mb-2">
              We've sent a verification email to <span className="font-medium text-white">{currentUser?.email}</span>
            </p>
            <p className="text-gray-400">
              Please check your inbox and click the verification link to activate your account.
            </p>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            onClick={handleCheckAgain}
            variant="outline"
            className="flex-1 border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/10"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Check Again
          </Button>
          
          <Button
            onClick={handleResendVerification}
            disabled={resendLoading || cooldownTime > 0}
            className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white"
          >
            {resendLoading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Sending...
              </>
            ) : cooldownTime > 0 ? (
              <>
                <Clock className="h-4 w-4 mr-2" />
                Resend in {cooldownTime}s
              </>
            ) : (
              <>
                <Mail className="h-4 w-4 mr-2" />
                Resend Email
              </>
            )}
          </Button>
        </div>

        <div className="text-xs text-gray-500 mt-3">
          <p>Didn't receive the email? Check your spam folder or try resending.</p>
        </div>
      </div>
    </div>
  );
}
