import { useState, useEffect } from 'react';
import { AlertTriangle, CreditCard, Package, Building, Check, ArrowRight, Info, LogOut } from 'lucide-react';
import { Button } from './button';
import { BillingDetailsStep } from '@/components/billing-details-step';
import { useCountry } from '@/lib/countryContext';
import { toast } from 'react-hot-toast';
import { db, auth } from '@/lib/firebase';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { signOut } from 'firebase/auth';
import { useStore } from '@/lib/store';
import { initSSLCommerzPayment } from '@/lib/sslcommerz';
import { useNavigate } from 'react-router-dom';

interface ExpiredPackageModalProps {
  packageName: string;
  expireDate: string;
}

/**
 * Non-dismissible modal that appears when a user's subscription has expired.
 * The modal can only be closed by completing a successful payment and obtaining
 * an active subscription. This ensures users cannot bypass the payment requirement.
 *
 * Features a logout button in the top-right corner to allow users to switch
 * to a different account that might have an active subscription.
 */

export function ExpiredPackageModal({ packageName, expireDate }: ExpiredPackageModalProps) {
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');
  const [showBillingDetails, setShowBillingDetails] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>(packageName); // Default to expired package
  const { formatCurrency, currency } = useCountry();
  const user = useStore(state => state.user);
  const signOutFromStore = useStore((state) => state.signOut);
  const navigate = useNavigate();

  // Prevent body scrolling when modal is open
  useEffect(() => {
    // Save the current overflow value
    const originalOverflow = document.body.style.overflow;
    // Lock scrolling
    document.body.style.overflow = 'hidden';

    // Restore scrolling when component unmounts
    return () => {
      document.body.style.overflow = originalOverflow;
    };
  }, []);

  // Define correct pricing structure (matching upgrade-plan-modal.tsx)
  const prices = {
    payg: { price: 2, unit: 'per 15 generations' },
    pro: { monthly: 10, yearly: 96 },
    enterprise: { monthly: 100, yearly: 960 }
  };

  const handlePlanSelect = (plan: string) => {
    setSelectedPlan(plan);
  };

  const handleUpgrade = () => {
    setShowBillingDetails(true);
  };

  const handleProceedToCheckout = async (discountAmount?: number, couponCode?: string) => {
    if (!user) {
      toast.error('User not authenticated');
      return;
    }

    const loadingToast = toast.loading('Initializing payment...');

    try {
      // Calculate amount based on selected plan and billing period
      // NOTE: Do NOT apply discount here - the Firebase function will handle discount calculation
      let amount = 0;
      let planName = selectedPlan;

      if (selectedPlan === 'Pay-As-You-Go') {
        amount = prices.payg.price;
      } else if (selectedPlan === 'Pro') {
        amount = billingPeriod === 'yearly' ? prices.pro.yearly : prices.pro.monthly;
      } else if (selectedPlan === 'Enterprise') {
        amount = billingPeriod === 'yearly' ? prices.enterprise.yearly : prices.enterprise.monthly;
      }

      // The discount will be applied by the Firebase Cloud Function
      // based on the couponCode and discountAmount parameters

      console.log('Fetching billing details for user:', user.id);

      // Get the billing details from the BillingDetailsStep component
      const billingDetailsRef = collection(db, 'billingDetails');
      const q = query(billingDetailsRef, where('userId', '==', user.id));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        console.error('No billing details found for user:', user.id);
        toast.error('Billing details not found. Please complete your billing details first.');
        toast.dismiss(loadingToast);
        return;
      }

      const billingDetails = querySnapshot.docs[0].data() as any;
      console.log('Billing details found:', billingDetails);

      // Initialize SSLCOMMERZ payment with proper error handling
      try {
        const redirectUrl = await initSSLCommerzPayment({
          userId: user.id,
          userEmail: user.email,
          userName: billingDetails.fullName,
          amount,
          plan: planName,
          billingPeriod: billingPeriod === 'yearly' ? 'yearly' : 'monthly',
          billingDetails,
          couponCode,
          discountAmount
        });

        // Dismiss loading toast
        toast.dismiss(loadingToast);

        // Show success toast
        toast.success(`Redirecting to SSLCOMMERZ payment gateway...`);

        // Redirect to SSLCOMMERZ payment page
        window.location.href = redirectUrl;
      } catch (paymentError: any) {
        console.error('SSLCOMMERZ payment initialization error:', paymentError);
        toast.dismiss(loadingToast);

        if (paymentError.message) {
          toast.error(`Payment error: ${paymentError.message}`);
        } else {
          toast.error('Failed to initialize payment. Please try again.');
        }
      }
    } catch (error) {
      console.error('Error in handleProceedToCheckout:', error);
      toast.dismiss(loadingToast);
      toast.error('An error occurred. Please try again.');
    }
  };

  const handleBackToPlanSelection = () => {
    setShowBillingDetails(false);
  };

  const handleCloseBillingDetails = () => {
    setShowBillingDetails(false);
  };

  const handleLogout = async () => {
    try {
      // Sign out from Firebase
      await signOut(auth);
      // Clear user from store
      signOutFromStore();
      toast.success('Logged out successfully');
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Error logging out');
    }
  };



  return (
    <>
      {showBillingDetails ? (
        <BillingDetailsStep
          isOpen={showBillingDetails}
          onClose={handleCloseBillingDetails}
          selectedPlan={selectedPlan}
          billingPeriod={billingPeriod}
          onProceedToCheckout={handleProceedToCheckout}
          onBack={handleBackToPlanSelection}
        />
      ) : (
        <div
          className="fixed inset-0 z-50 flex items-start md:items-center lg:items-center justify-center bg-black/80 backdrop-blur-sm overflow-hidden"
          onTouchMove={(e) => e.stopPropagation()}
        >
          <div
            className="relative w-full max-w-4xl p-4 md:p-5 lg:p-5 bg-gray-800 rounded-xl shadow-2xl max-h-[90vh] md:max-h-[85vh] lg:h-auto overflow-y-auto md:overflow-y-auto lg:overflow-visible custom-scrollbar my-4 md:my-0"
            onClick={(e) => e.stopPropagation()}
            onTouchMove={(e) => e.stopPropagation()}
          >
            {/* Logout button */}
            <button
              onClick={handleLogout}
              className="fixed md:absolute top-2 right-2 md:top-4 md:right-4 p-2 text-gray-400 hover:text-white rounded-full hover:bg-gray-700 transition-colors bg-gray-800 z-10"
              title="Logout and switch account"
            >
              <LogOut size={20} />
            </button>

            {/* Header with Expiration Warning */}
            <div className="text-center mb-4 md:mb-5 lg:mb-4 pt-4 md:pt-2 lg:pt-0">
              <div className="bg-red-900/30 p-3 rounded-full mb-3 mx-auto w-fit">
                <AlertTriangle className="h-12 w-12 text-red-500" />
              </div>
              <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-red-400 via-orange-500 to-yellow-500 bg-clip-text text-transparent">
                Subscription Expired
              </h2>
              <p className="mt-1 lg:mt-2 text-gray-300 text-sm md:text-base px-2">
                Your {packageName} subscription expired on{' '}
                <span className="text-red-400 font-medium">
                  {new Date(expireDate).toLocaleDateString()}
                </span>
              </p>
              <p className="mt-1 lg:mt-2 text-gray-400 text-sm md:text-base px-2">
                Choose a plan to continue using premium features
              </p>
            </div>

            {/* Billing toggle */}
            <div className="flex justify-center mb-4 md:mb-5 lg:mb-4">
              <div className="flex items-center p-1 bg-gray-700 rounded-lg">
                <button
                  className={`px-3 md:px-4 py-2 rounded-md transition-colors text-sm md:text-base ${billingPeriod === 'monthly' ? 'bg-purple-600 text-white' : 'text-gray-300 hover:text-white'}`}
                  onClick={() => setBillingPeriod('monthly')}
                >
                  Monthly
                </button>
                <button
                  className={`px-3 md:px-4 py-2 rounded-md transition-colors text-sm md:text-base ${billingPeriod === 'yearly' ? 'bg-purple-600 text-white' : 'text-gray-300 hover:text-white'}`}
                  onClick={() => setBillingPeriod('yearly')}
                >
                  Yearly <span className="text-xs text-green-300">Save 20%</span>
                </button>
              </div>
            </div>

            {/* Plans */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-4 lg:gap-3 px-1 md:px-0">
              {/* Pay-As-You-Go Plan */}
              <div
                className={`relative bg-gray-700 rounded-xl overflow-hidden border-2 transition-all flex flex-col ${selectedPlan === 'Pay-As-You-Go' ? 'border-purple-500 md:scale-105 shadow-lg shadow-purple-500/20' : 'border-gray-600 hover:border-gray-500'}`}
                onClick={() => handlePlanSelect('Pay-As-You-Go')}
              >
                <div className="p-4 md:p-5 lg:p-4">
                  <div className="flex justify-center mb-2 md:mb-3 lg:mb-2">
                    <div className="p-2 md:p-3 lg:p-2 bg-purple-500/20 rounded-full">
                      <CreditCard className="h-6 w-6 md:h-7 md:w-7 lg:h-6 lg:w-6 text-purple-400" />
                    </div>
                  </div>
                  <h3 className="text-lg md:text-xl lg:text-lg font-bold text-center text-white mb-1 lg:mb-1">Pay-As-You-Go</h3>
                  <div className="text-center mb-2 md:mb-3 lg:mb-2">
                    <span className="text-2xl md:text-3xl lg:text-2xl font-bold text-white">{formatCurrency(prices.payg.price)}</span>
                    <span className="text-gray-300 ml-1 text-sm md:text-base lg:text-sm">{prices.payg.unit}</span>
                    {currency.code !== 'USD' && (
                      <div className="text-xs text-gray-400 mt-1 flex items-center justify-center">
                        <Info className="h-3 w-3 mr-1" />
                        <span>${prices.payg.price} USD</span>
                      </div>
                    )}
                  </div>
                  <ul className="space-y-1 md:space-y-2 lg:space-y-1 text-sm md:text-base lg:text-sm mb-3 md:mb-4 lg:mb-2">
                    <li className="flex items-start">
                      <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">20 stored images</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">5 custom prompts</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">24h image deletion delay</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">Credits never expire</span>
                    </li>
                  </ul>
                </div>
                <div className="p-3 md:p-4 lg:p-3 bg-gray-800 border-t border-gray-600 mt-auto">
                  <Button
                    className={`w-full py-1.5 md:py-2 lg:py-1.5 text-sm md:text-base lg:text-sm font-medium ${selectedPlan === 'Pay-As-You-Go' ? 'bg-purple-600 hover:bg-purple-700' : 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      handlePlanSelect('Pay-As-You-Go');
                      handleUpgrade();
                    }}
                  >
                    Get Started
                    <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4" />
                  </Button>
                </div>
                {selectedPlan === 'Pay-As-You-Go' && (
                  <div className="absolute top-0 right-0 p-1 md:p-1.5 lg:p-1">
                    <div className="bg-purple-600 text-white text-xs font-bold px-2 py-0.5 lg:py-0.5 rounded-full">
                      Selected
                    </div>
                  </div>
                )}
              </div>

              {/* Pro Plan */}
              <div
                className={`relative bg-gray-700 rounded-xl overflow-hidden border-2 transition-all flex flex-col ${selectedPlan === 'Pro' ? 'border-purple-500 md:scale-105 shadow-lg shadow-purple-500/20' : 'border-gray-600 hover:border-gray-500'}`}
                onClick={() => handlePlanSelect('Pro')}
              >
                <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-center text-xs font-bold py-1">
                  MOST POPULAR
                </div>
                <div className="p-4 md:p-5 lg:p-4 pt-6 md:pt-8 lg:pt-6">
                  <div className="flex justify-center mb-2 md:mb-3 lg:mb-2">
                    <div className="p-2 md:p-3 lg:p-2 bg-purple-500/20 rounded-full">
                      <Package className="h-6 w-6 md:h-7 md:w-7 lg:h-6 lg:w-6 text-purple-400" />
                    </div>
                  </div>
                  <h3 className="text-lg md:text-xl lg:text-lg font-bold text-center text-white mb-1 lg:mb-1">Pro</h3>
                  <div className="text-center mb-2 md:mb-3 lg:mb-2">
                    <span className="text-2xl md:text-3xl lg:text-2xl font-bold text-white">
                      {formatCurrency(billingPeriod === 'monthly' ? prices.pro.monthly : prices.pro.yearly)}
                    </span>
                    <span className="text-gray-300 ml-1 text-sm md:text-base lg:text-sm">/{billingPeriod === 'monthly' ? 'month' : 'year'}</span>
                    {currency.code !== 'USD' && (
                      <div className="text-xs text-gray-400 mt-1 flex items-center justify-center">
                        <Info className="h-3 w-3 mr-1" />
                        <span>${billingPeriod === 'monthly' ? prices.pro.monthly : prices.pro.yearly} USD</span>
                      </div>
                    )}
                  </div>
                  <ul className="space-y-1 md:space-y-2 lg:space-y-1 text-sm md:text-base lg:text-sm mb-3 md:mb-4 lg:mb-2">
                    <li className="flex items-start">
                      <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">50 stored images</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">150 generations per month</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">10 custom prompts</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">Instant image deletion</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">Priority support</span>
                    </li>
                  </ul>
                </div>
                <div className="p-3 md:p-4 lg:p-3 bg-gray-800 border-t border-gray-600 mt-auto">
                  <Button
                    className={`w-full py-1.5 md:py-2 lg:py-1.5 text-sm md:text-base lg:text-sm font-medium ${selectedPlan === 'Pro' ? 'bg-purple-600 hover:bg-purple-700' : 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      handlePlanSelect('Pro');
                      handleUpgrade();
                    }}
                  >
                    {selectedPlan === 'Pro' ? 'Renew Pro' : 'Upgrade to Pro'}
                    <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4" />
                  </Button>
                </div>
                {selectedPlan === 'Pro' && (
                  <div className="absolute top-0 right-0 p-1 md:p-1.5 lg:p-1">
                    <div className="bg-purple-600 text-white text-xs font-bold px-2 py-0.5 lg:py-0.5 rounded-full">
                      Selected
                    </div>
                  </div>
                )}
              </div>

              {/* Enterprise Plan */}
              <div
                className={`relative bg-gray-700 rounded-xl overflow-hidden border-2 transition-all flex flex-col ${selectedPlan === 'Enterprise' ? 'border-purple-500 md:scale-105 shadow-lg shadow-purple-500/20' : 'border-gray-600 hover:border-gray-500'}`}
                onClick={() => handlePlanSelect('Enterprise')}
              >
                <div className="p-4 md:p-5 lg:p-4">
                  <div className="flex justify-center mb-2 md:mb-3 lg:mb-2">
                    <div className="p-2 md:p-3 lg:p-2 bg-purple-500/20 rounded-full">
                      <Building className="h-6 w-6 md:h-7 md:w-7 lg:h-6 lg:w-6 text-purple-400" />
                    </div>
                  </div>
                  <h3 className="text-lg md:text-xl lg:text-lg font-bold text-center text-white mb-1 lg:mb-1">Enterprise</h3>
                  <div className="text-center mb-2 md:mb-3 lg:mb-2">
                    <span className="text-2xl md:text-3xl lg:text-2xl font-bold text-white">
                      {formatCurrency(billingPeriod === 'monthly' ? prices.enterprise.monthly : prices.enterprise.yearly)}
                    </span>
                    <span className="text-gray-300 ml-1 text-sm md:text-base lg:text-sm">/{billingPeriod === 'monthly' ? 'month' : 'year'}</span>
                    {currency.code !== 'USD' && (
                      <div className="text-xs text-gray-400 mt-1 flex items-center justify-center">
                        <Info className="h-3 w-3 mr-1" />
                        <span>${billingPeriod === 'monthly' ? prices.enterprise.monthly : prices.enterprise.yearly} USD</span>
                      </div>
                    )}
                  </div>
                  <ul className="space-y-1 md:space-y-2 lg:space-y-1 text-sm md:text-base lg:text-sm mb-3 md:mb-4 lg:mb-2">
                    <li className="flex items-start">
                      <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">10 team members</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">50 stored images per member</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">200 generations per member</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">Team management dashboard</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4 text-green-400 mr-1.5 lg:mr-1.5 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">Dedicated account manager</span>
                    </li>
                  </ul>
                </div>
                <div className="p-3 md:p-4 lg:p-3 bg-gray-800 border-t border-gray-600 mt-auto">
                  <Button
                    className={`w-full py-1.5 md:py-2 lg:py-1.5 text-sm md:text-base lg:text-sm font-medium ${selectedPlan === 'Enterprise' ? 'bg-purple-600 hover:bg-purple-700' : 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      handlePlanSelect('Enterprise');
                      handleUpgrade();
                    }}
                  >
                    {selectedPlan === 'Enterprise' ? 'Renew Enterprise' : 'Upgrade to Enterprise'}
                    <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5 lg:h-4 lg:w-4" />
                  </Button>
                </div>
                {selectedPlan === 'Enterprise' && (
                  <div className="absolute top-0 right-0 p-1 md:p-1.5 lg:p-1">
                    <div className="bg-purple-600 text-white text-xs font-bold px-2 py-0.5 lg:py-0.5 rounded-full">
                      Selected
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Footer */}
            <div className="mt-4 md:mt-5 lg:mt-3 text-center text-gray-400 text-xs md:text-sm px-2 md:px-0">
              <p>All plans include our core features: AI-powered image analysis, multiple language support, and secure data storage.</p>
              <p className="mt-1 lg:mt-1">Need help choosing? <a href="/footer/contact" className="text-purple-400 hover:text-purple-300">Contact our sales team</a></p>
            </div>
          </div>
        </div>
      )}
    </>
  );
}