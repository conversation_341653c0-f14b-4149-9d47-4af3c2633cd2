/**
 * One-time script to clean up existing canceled payment records from paymentLogs collection
 * Run this script manually to remove canceled payments from user-facing payment history
 */

const admin = require('firebase-admin');

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

async function cleanupCanceledPayments() {
  try {
    console.log('🧹 Starting cleanup of canceled payment records...');
    
    // Query all canceled payment logs
    const canceledPaymentsQuery = await db.collection('paymentLogs')
      .where('status', '==', 'CANCELED')
      .get();
    
    if (canceledPaymentsQuery.empty) {
      console.log('✅ No canceled payment records found to clean up');
      return;
    }
    
    console.log(`📊 Found ${canceledPaymentsQuery.size} canceled payment records to remove`);
    
    // Delete canceled payment logs in batches
    const batch = db.batch();
    let deleteCount = 0;
    
    canceledPaymentsQuery.forEach((doc) => {
      const data = doc.data();
      console.log(`🗑️ Marking for deletion: ${doc.id} (Transaction: ${data.transactionId})`);
      batch.delete(doc.ref);
      deleteCount++;
    });
    
    // Commit the batch delete
    await batch.commit();
    
    console.log(`✅ Successfully deleted ${deleteCount} canceled payment records`);
    console.log('🎉 Cleanup completed! User-facing payment history is now clean.');
    
    // Verify cleanup
    const verifyQuery = await db.collection('paymentLogs')
      .where('status', '==', 'CANCELED')
      .get();
    
    if (verifyQuery.empty) {
      console.log('✅ Verification passed: No canceled payments remain in paymentLogs');
    } else {
      console.warn(`⚠️ Warning: ${verifyQuery.size} canceled payments still exist`);
    }
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    throw error;
  }
}

// Export for use as Cloud Function or run directly
module.exports = { cleanupCanceledPayments };

// If running directly (not as a Cloud Function)
if (require.main === module) {
  cleanupCanceledPayments()
    .then(() => {
      console.log('Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}
