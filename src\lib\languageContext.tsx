import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

type Language = 'en' | 'bn';
type LanguageContextType = {
  language: Language;
  toggleLanguage: () => void;
  isEnglish: boolean;
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Initialize language from localStorage or default to 'en'
  const [language, setLanguage] = useState<Language>(() => {
    const savedLanguage = localStorage.getItem('language');
    return (savedLanguage === 'bn' ? 'bn' : 'en') as Language;
  });

  // Update localStorage when language changes
  useEffect(() => {
    localStorage.setItem('language', language);
  }, [language]);

  // Handle navigation based on language change
  useEffect(() => {
    // Only redirect on the landing page
    if (location.pathname === '/' && language === 'bn') {
      navigate('/bn');
    } else if (location.pathname === '/bn' && language === 'en') {
      navigate('/');
    }
  }, [language, location.pathname, navigate]);

  const toggleLanguage = () => {
    setLanguage(prevLang => (prevLang === 'en' ? 'bn' : 'en'));
  };

  return (
    <LanguageContext.Provider value={{ language, toggleLanguage, isEnglish: language === 'en' }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Language Toggle Button Component
export const LanguageToggle: React.FC = () => {
  const { language, toggleLanguage } = useLanguage();
  
  return (
    <button 
      onClick={toggleLanguage}
      className="relative inline-flex items-center h-6 rounded-full w-12 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900"
      style={{
        backgroundColor: language === 'en' ? '#4f46e5' : '#8b5cf6',
        boxShadow: '0 0 10px rgba(139, 92, 246, 0.5)'
      }}
      aria-label="Toggle language"
    >
      <span className="sr-only">Toggle language</span>
      <span 
        className={`flex items-center justify-center text-xs font-medium h-5 w-5 rounded-full transform transition-transform duration-300 ease-in-out ${
          language === 'en' ? 'translate-x-1 bg-white text-indigo-600' : 'translate-x-6 bg-white text-purple-600'
        }`}
      >
        {language === 'en' ? 'EN' : 'বাং'}
      </span>
    </button>
  );
};