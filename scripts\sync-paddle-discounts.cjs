/**
 * Create Paddle Discount Codes to Match Firebase Coupons
 * This script creates discount codes in Paddle that match your Firebase coupon system
 */

const https = require('https');

// Paddle API configuration - Support both environments
const ENVIRONMENT = process.env.PADDLE_ENVIRONMENT || 'sandbox'; // Default to sandbox for safety
const PADDLE_API_KEY = ENVIRONMENT === 'production'
  ? (process.env.VITE_PADDLE_API_KEY_PRODUCTION || 'pdl_live_apikey_01jxb4n39mr2v9h5j9ge8m31d3_E8kmrRzxqetZVCNRT47wJy_ACc')
  : (process.env.VITE_PADDLE_API_KEY_SANDBOX || 'pdl_sdbx_apikey_01jxhkdd1qcvtqsf4kajykxjdx_G1r7Xtc2edq8HsFn8cg7qZ_AIH');
const PADDLE_API_URL = ENVIRONMENT === 'production' ? 'https://api.paddle.com' : 'https://sandbox-api.paddle.com';

// Your coupon codes from Firebase (these should match your Firebase coupons)
const COUPONS_TO_CREATE = [
  {
    code: 'KKK',
    description: '20% discount coupon',
    discountType: 'percentage',
    discountAmount: 20,
    enabled: true
  },
  {
    code: 'SAVE10',
    description: '10% discount coupon', 
    discountType: 'percentage',
    discountAmount: 10,
    enabled: true
  },
  {
    code: 'WELCOME',
    description: 'Welcome discount 15%',
    discountType: 'percentage', 
    discountAmount: 15,
    enabled: true
  }
];

/**
 * Make API request to Paddle
 */
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(PADDLE_API_URL);
    const options = {
      hostname: url.hostname,
      path: path,
      method: method,
      headers: {
        'Authorization': `Bearer ${PADDLE_API_KEY}`,
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(response);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${JSON.stringify(response)}`));
          }
        } catch (error) {
          reject(new Error(`Invalid JSON response: ${body}`));
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

/**
 * Create a discount in Paddle
 */
async function createDiscount(coupon) {
  console.log(`\n🎫 Creating discount code: ${coupon.code}`);
  
  const discountData = {
    amount: coupon.discountAmount.toString(),
    description: coupon.description,
    enabled_for_checkout: true,
    code: coupon.code,
    type: coupon.discountType,
    restrict_to: null, // Apply to all products
    expires_at: null, // No expiration
    maximum_recurring_intervals: null, // Apply to all billing cycles
    usage_limit: null // No usage limit
  };

  try {
    const response = await makeRequest('POST', '/discounts', discountData);
    console.log(`✅ Created discount: ${coupon.code} (${response.data.id})`);
    return response.data;
  } catch (error) {
    console.error(`❌ Failed to create discount ${coupon.code}:`, error.message);
    return null;
  }
}

/**
 * List existing discounts to avoid duplicates
 */
async function listExistingDiscounts() {
  try {
    const response = await makeRequest('GET', '/discounts?per_page=50');
    return response.data || [];
  } catch (error) {
    console.error('❌ Failed to list existing discounts:', error.message);
    return [];
  }
}

/**
 * Main function to sync coupon codes
 */
async function syncCouponCodes() {
  console.log('🎯 PADDLE DISCOUNT CODE SYNC');
  console.log('=' .repeat(50));
  console.log(`🌍 Environment: ${ENVIRONMENT.toUpperCase()}`);
  console.log(`🔗 API URL: ${PADDLE_API_URL}`);
  console.log(`🔑 API Key: ${PADDLE_API_KEY.substring(0, 20)}...`);
  console.log('=' .repeat(50));
  
  console.log('🔍 Checking existing discount codes...');
  const existingDiscounts = await listExistingDiscounts();
  const existingCodes = existingDiscounts.map(d => d.code);
  
  console.log(`📋 Found ${existingDiscounts.length} existing discount codes`);
  if (existingCodes.length > 0) {
    console.log('Existing codes:', existingCodes.join(', '));
  }

  console.log('\n📦 Creating new discount codes...');
  
  for (const coupon of COUPONS_TO_CREATE) {
    if (existingCodes.includes(coupon.code)) {
      console.log(`⏭️  Skipping ${coupon.code} - already exists`);
      continue;
    }
    
    await createDiscount(coupon);
    
    // Add a small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log('\n✅ SYNC COMPLETE!');
  console.log('\n📋 What to do next:');
  console.log('1. Test the discount codes in Paddle checkout');
  console.log('2. Verify coupon "KKK" now applies 20% discount');
  console.log('3. Check that the price reflects correctly in checkout');
  console.log('4. Test end-to-end payment flow with discounts');

  console.log('\n🧪 Testing Commands:');
  console.log('→ Test Paddle integration: http://localhost:5173/test/paddle');
  console.log('→ Apply coupon "KKK" and verify 20% discount');
  console.log('→ Proceed to Paddle checkout and confirm discounted price');
}

// Run the sync
if (require.main === module) {
  syncCouponCodes().catch(console.error);
}

module.exports = { syncCouponCodes };
