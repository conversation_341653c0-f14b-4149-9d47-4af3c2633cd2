import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>lider } from '@/components/ui/slider';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { TeamMemberLimits, DEFAULT_TEAM_MEMBER_LIMITS, getEnterpriseAdminOwnerQuota } from '@/lib/teamMemberLimits';
import { Settings, AlertCircle, Users, Link as LinkIcon } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

interface TeamMemberLimitsFormProps {
  open: boolean;
  onClose: () => void;
  onSave: (limits: TeamMemberLimits) => void;
  ownerId: string;
}

export function TeamMemberLimitsForm({ open, onClose, onSave, ownerId }: TeamMemberLimitsFormProps) {
  const [limits, setLimits] = useState<TeamMemberLimits>(DEFAULT_TEAM_MEMBER_LIMITS);
  const [maxLimits, setMaxLimits] = useState<TeamMemberLimits>(DEFAULT_TEAM_MEMBER_LIMITS);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (open) {
      fetchOwnerQuota();
    }
  }, [open, ownerId]);

  const fetchOwnerQuota = async () => {
    setLoading(true);
    try {
      const quota = await getEnterpriseAdminOwnerQuota(ownerId);
      setMaxLimits(quota);

      // Set initial limits to half of the available quota or the default, whichever is smaller
      const initialLimits: TeamMemberLimits = {
        maxImages: Math.min(Math.floor(quota.maxImages / 2), DEFAULT_TEAM_MEMBER_LIMITS.maxImages),
        deleteDelayHours: quota.deleteDelayHours,
        maxSavedPrompts: Math.min(Math.floor(quota.maxSavedPrompts / 2), DEFAULT_TEAM_MEMBER_LIMITS.maxSavedPrompts),
        maxCustomPrompts: Math.min(Math.floor(quota.maxCustomPrompts / 2), DEFAULT_TEAM_MEMBER_LIMITS.maxCustomPrompts),
        monthlyGenerationLimit: Math.min(Math.floor(quota.monthlyGenerationLimit / 2), DEFAULT_TEAM_MEMBER_LIMITS.monthlyGenerationLimit),
        isMultipleRegistrations: false // Default to one-time registration
      };

      setLimits(initialLimits);
    } catch (error) {
      console.error('Error fetching owner quota:', error);
      toast.error('Failed to fetch available quota. Using default limits.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof TeamMemberLimits, value: number) => {
    // Ensure the value doesn't exceed the maximum available quota
    const maxValue = maxLimits[field];
    const validValue = Math.min(value, maxValue);

    setLimits(prev => ({
      ...prev,
      [field]: validValue
    }));
  };

  const handleToggleChange = (checked: boolean) => {
    setLimits(prev => ({
      ...prev,
      isMultipleRegistrations: checked
    }));
  };

  const handleSave = () => {
    onSave(limits);
    onClose();
  };

  const resetToDefaults = () => {
    // Set to half of the available quota or the default, whichever is smaller
    const resetLimits: TeamMemberLimits = {
      maxImages: Math.min(Math.floor(maxLimits.maxImages / 2), DEFAULT_TEAM_MEMBER_LIMITS.maxImages),
      deleteDelayHours: maxLimits.deleteDelayHours,
      maxSavedPrompts: Math.min(Math.floor(maxLimits.maxSavedPrompts / 2), DEFAULT_TEAM_MEMBER_LIMITS.maxSavedPrompts),
      maxCustomPrompts: Math.min(Math.floor(maxLimits.maxCustomPrompts / 2), DEFAULT_TEAM_MEMBER_LIMITS.maxCustomPrompts),
      monthlyGenerationLimit: Math.min(Math.floor(maxLimits.monthlyGenerationLimit / 2), DEFAULT_TEAM_MEMBER_LIMITS.monthlyGenerationLimit),
      isMultipleRegistrations: false // Reset to one-time registration
    };

    setLimits(resetLimits);
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="bg-gray-800 text-white border-gray-700 max-w-md p-0 overflow-auto max-h-[90vh]">
        <div className="p-3 pb-2">
          <DialogTitle className="text-lg font-medium text-white mb-1">Set Team Member Limits</DialogTitle>
          <DialogDescription className="text-xs text-gray-400">
            {limits.isMultipleRegistrations
              ? "Set individual limits for each team member, which will take effect upon their registration."
              : "Set individual limits for this individual team member, which will take effect upon their registration."
            }
          </DialogDescription>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-purple-500"></div>
            <span className="ml-3 text-gray-400 text-sm">Loading available quota...</span>
          </div>
        ) : (
          <div className="px-3">
            {/* Available Quota Information */}
            <div className="bg-gray-700/50 p-2 rounded-lg border border-gray-600 mb-3">
              <h3 className="text-xs font-medium text-white flex items-center mb-1">
                <AlertCircle className="h-3 w-3 mr-1 text-yellow-400" />
                Available Quota
              </h3>
              <div className="grid grid-cols-5 gap-x-2 gap-y-1 text-xs">
                  <div>
                  <span className="text-gray-400">Saved: </span>
                  <span className="text-white font-medium">{maxLimits.maxSavedPrompts}</span>
                </div>
                <div>
                  <span className="text-gray-400">Custom: </span>
                  <span className="text-white font-medium">{maxLimits.maxCustomPrompts}</span>
                </div>
                <div>
                  <span className="text-gray-400">Monthly: </span>
                  <span className="text-white font-medium">{maxLimits.monthlyGenerationLimit}</span>
                </div>
              </div>
            </div>

            {/* Registration Type - Moved to appear after Available Quota */}
            <div className="mb-3 border-t border-gray-700 pt-2 mt-3">
              <div className="flex items-center mb-0.5">
                <LinkIcon className="h-3.5 w-3.5 mr-1 text-purple-400" />
                <h3 className="text-xs font-medium text-white">Invite Link Type</h3>
              </div>

              <div className="flex items-center justify-between mt-1.5 bg-gray-700/50 p-2 rounded-lg border border-gray-600">
                <div>
                  <Label htmlFor="registration-type" className="text-xs font-medium text-white">
                    {limits.isMultipleRegistrations ? "Multiple Registrations" : "One-time Registration"}
                  </Label>
                  <p className="text-xs text-gray-400 mt-0.5">
                    {limits.isMultipleRegistrations
                      ? "Can be used multiple times and never expires"
                      : "Can only be used once and expires after 7 days (default)"}
                  </p>
                </div>
                <Switch
                  id="registration-type"
                  checked={limits.isMultipleRegistrations || false}
                  onCheckedChange={handleToggleChange}
                />
              </div>

              <div className="mt-1.5 flex items-start">
                <AlertCircle className="h-3 w-3 text-yellow-400 mr-1.5 mt-0.5 flex-shrink-0" />
                <p className="text-[10px] text-gray-400">
                  {limits.isMultipleRegistrations
                    ? "Multiple registration links never expire and remain active until deleted. Team members will share the same limits."
                    : "One-time registration links automatically expire after use or after 7 days."}
                </p>
              </div>
            </div>

            {/* Max Images */}
            <div className="mb-3">
              <div className="flex items-center mb-0.5">
                <Settings className="h-3.5 w-3.5 mr-1 text-purple-400" />
                <h3 className="text-xs font-medium text-white">
                  {limits.isMultipleRegistrations
                    ? "Image Upload Limit for Each Team Member"
                    : "Image Upload Limit for This Team Member"
                  }
                </h3>
              </div>
              <div className="flex justify-between items-center mb-0.5">
                <span className="text-xs text-gray-400">Current value: {limits.maxImages}</span>
                <Input
                  type="number"
                  value={limits.maxImages}
                  onChange={(e) => handleInputChange('maxImages', parseInt(e.target.value) || 0)}
                  min="1"
                  max={maxLimits.maxImages}
                  className="w-16 h-7 bg-gray-700 border-gray-600 text-white text-xs"
                />
              </div>
              <Slider
                value={[limits.maxImages]}
                min={1}
                max={maxLimits.maxImages}
                step={1}
                onValueChange={(value) => handleInputChange('maxImages', value[0])}
                className="py-0.5"
                style={{
                  '--slider-track': 'hsl(280, 100%, 60%)',
                  '--slider-range': 'linear-gradient(to right, hsl(280, 100%, 60%), hsl(320, 100%, 60%))'
                } as React.CSSProperties}
              />
            </div>

            {/* Image Delete Delay */}
            <div className="mb-3">
              <div className="flex items-center mb-0.5">
                <Settings className="h-3.5 w-3.5 mr-1 text-purple-400" />
                <h3 className="text-xs font-medium text-white">Image Delete Delay (Hours)</h3>
              </div>
              <div className="flex justify-between items-center mb-0.5">
                <span className="text-xs text-gray-400">Current value: {limits.deleteDelayHours} hours</span>
                <Input
                  type="number"
                  value={limits.deleteDelayHours}
                  onChange={(e) => handleInputChange('deleteDelayHours', parseInt(e.target.value) || 0)}
                  min="0"
                  max={maxLimits.deleteDelayHours}
                  className="w-16 h-7 bg-gray-700 border-gray-600 text-white text-xs"
                />
              </div>
              <Slider
                value={[limits.deleteDelayHours]}
                min={0}
                max={maxLimits.deleteDelayHours}
                step={1}
                onValueChange={(value) => handleInputChange('deleteDelayHours', value[0])}
                className="py-0.5"
                style={{
                  '--slider-track': 'hsl(280, 100%, 60%)',
                  '--slider-range': 'linear-gradient(to right, hsl(280, 100%, 60%), hsl(320, 100%, 60%))'
                } as React.CSSProperties}
              />
            </div>

            {/* Max Saved Prompts */}
            <div className="mb-3">
              <div className="flex items-center mb-0.5">
                <Settings className="h-3.5 w-3.5 mr-1 text-purple-400" />
                <h3 className="text-xs font-medium text-white">
                  {limits.isMultipleRegistrations
                    ? "Data Saving Limit for Each Team Member"
                    : "Data Saving Limit for This Team Member"
                  }
                </h3>
              </div>
              <div className="flex justify-between items-center mb-0.5">
                <span className="text-xs text-gray-400">Current value: {limits.maxSavedPrompts}</span>
                <Input
                  type="number"
                  value={limits.maxSavedPrompts}
                  onChange={(e) => handleInputChange('maxSavedPrompts', parseInt(e.target.value) || 0)}
                  min="1"
                  max={maxLimits.maxSavedPrompts}
                  className="w-16 h-7 bg-gray-700 border-gray-600 text-white text-xs"
                />
              </div>
              <Slider
                value={[limits.maxSavedPrompts]}
                min={1}
                max={maxLimits.maxSavedPrompts}
                step={1}
                onValueChange={(value) => handleInputChange('maxSavedPrompts', value[0])}
                className="py-0.5"
                style={{
                  '--slider-track': 'hsl(280, 100%, 60%)',
                  '--slider-range': 'linear-gradient(to right, hsl(280, 100%, 60%), hsl(320, 100%, 60%))'
                } as React.CSSProperties}
              />
            </div>

            {/* Max Custom Prompts */}
            <div className="mb-3">
              <div className="flex items-center mb-0.5">
                <Settings className="h-3.5 w-3.5 mr-1 text-purple-400" />
                <h3 className="text-xs font-medium text-white">
                  {limits.isMultipleRegistrations
                    ? "Custom Prompt Creation Limit for Each Team Member"
                    : "Custom Prompt Creation Limit for This Team Member"
                  }
                </h3>
              </div>
              <div className="flex justify-between items-center mb-0.5">
                <span className="text-xs text-gray-400">Current value: {limits.maxCustomPrompts}</span>
                <Input
                  type="number"
                  value={limits.maxCustomPrompts}
                  onChange={(e) => handleInputChange('maxCustomPrompts', parseInt(e.target.value) || 0)}
                  min="1"
                  max={maxLimits.maxCustomPrompts}
                  className="w-16 h-7 bg-gray-700 border-gray-600 text-white text-xs"
                />
              </div>
              <Slider
                value={[limits.maxCustomPrompts]}
                min={1}
                max={maxLimits.maxCustomPrompts}
                step={1}
                onValueChange={(value) => handleInputChange('maxCustomPrompts', value[0])}
                className="py-0.5"
                style={{
                  '--slider-track': 'hsl(280, 100%, 60%)',
                  '--slider-range': 'linear-gradient(to right, hsl(280, 100%, 60%), hsl(320, 100%, 60%))'
                } as React.CSSProperties}
              />
            </div>

            {/* Max Prompts Generated Monthly */}
            <div className="mb-3">
              <div className="flex items-center mb-0.5">
                <Settings className="h-3.5 w-3.5 mr-1 text-purple-400" />
                <h3 className="text-xs font-medium text-white">
                  {limits.isMultipleRegistrations
                    ? "Descriptions Generation Monthly Limit for Each Team Member"
                    : "Descriptions Generation Monthly Limit for This Team Member"
                  }
                </h3>
              </div>
              <div className="flex justify-between items-center mb-0.5">
                <span className="text-xs text-gray-400">Current value: {limits.monthlyGenerationLimit}</span>
                <Input
                  type="number"
                  value={limits.monthlyGenerationLimit}
                  onChange={(e) => handleInputChange('monthlyGenerationLimit', parseInt(e.target.value) || 0)}
                  min="1"
                  max={maxLimits.monthlyGenerationLimit}
                  className="w-16 h-7 bg-gray-700 border-gray-600 text-white text-xs"
                />
              </div>
              <Slider
                value={[limits.monthlyGenerationLimit]}
                min={1}
                max={maxLimits.monthlyGenerationLimit}
                step={1}
                onValueChange={(value) => handleInputChange('monthlyGenerationLimit', value[0])}
                className="py-0.5"
                style={{
                  '--slider-track': 'hsl(280, 100%, 60%)',
                  '--slider-range': 'linear-gradient(to right, hsl(280, 100%, 60%), hsl(320, 100%, 60%))'
                } as React.CSSProperties}
              />
            </div>
          </div>
        )}

        <div className="flex justify-between p-2 bg-gray-900 border-t border-gray-700">
          <Button
            variant="outline"
            onClick={resetToDefaults}
            className="h-8 text-xs bg-gray-700 border-gray-600 text-gray-200 hover:bg-gray-600 hover:text-white"
            disabled={loading}
          >
            Reset to Defaults
          </Button>
          <div className="space-x-2">
            <Button
              variant="outline"
              onClick={onClose}
              className="h-8 text-xs bg-gray-700 border-gray-600 text-gray-200 hover:bg-gray-600 hover:text-white"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              className="h-8 text-xs bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white"
              disabled={loading}
            >
              Create Link
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
