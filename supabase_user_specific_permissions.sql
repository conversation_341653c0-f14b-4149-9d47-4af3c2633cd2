-- Drop existing policies for the 'images' bucket
DROP POLICY IF EXISTS "Allow public read access to images bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to upload to images bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow anonymous users to upload to images bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to update their own objects in images bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to delete their own objects in images bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to read their own images" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to upload to their own folder" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to update their own images" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to delete their own images" ON storage.objects;

-- Create new policies that allow public access but rely on folder structure for isolation

-- Allow public read access to the 'images' bucket
CREATE POLICY "Allow public read access to images bucket"
ON storage.objects
FOR SELECT
USING (bucket_id = 'images');

-- Allow public upload to the 'images' bucket
CREATE POLICY "Allow public upload to images bucket"
ON storage.objects
FOR INSERT
WITH CHECK (bucket_id = 'images');

-- Allow public update to objects in the 'images' bucket
CREATE POLICY "Allow public update to images bucket"
ON storage.objects
FOR UPDATE
USING (bucket_id = 'images');

-- Allow public delete from the 'images' bucket
CREATE POLICY "Allow public delete from images bucket"
ON storage.objects
FOR DELETE
USING (bucket_id = 'images');
