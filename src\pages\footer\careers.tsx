import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, CheckCircle } from 'lucide-react';
import { Footer } from '../../components/footer';

export function CareersPage() {
  const jobOpenings = [
    {
      title: 'Senior AI Engineer',
      department: 'Engineering',
      location: 'Remote (US/Europe)',
      type: 'Full-time',
      description: 'We\'re looking for an experienced AI engineer to help us improve our image recognition and natural language processing models. You\'ll work on cutting-edge technology that powers our product description generation.',
      requirements: [
        'Experience with computer vision and NLP',
        '5+ years of experience in machine learning',
        'Proficiency in Python and deep learning frameworks',
        'Experience with production ML systems'
      ]
    },
    {
      title: 'Frontend Developer',
      department: 'Engineering',
      location: 'Remote (Worldwide)',
      type: 'Full-time',
      description: 'Join our frontend team to build intuitive, responsive interfaces for our AI-powered platform. You\'ll work with React to create seamless user experiences that make our complex technology accessible.',
      requirements: [
        'Strong experience with React and TypeScript',
        '3+ years of frontend development experience',
        'Experience with responsive design and accessibility',
        'Knowledge of modern CSS frameworks'
      ]
    },
    {
      title: 'Content Marketing Specialist',
      department: 'Marketing',
      location: 'Remote (US/Europe)',
      type: 'Full-time',
      description: 'Help us tell the world about eComEasyAI! We\'re looking for a content marketer who can create compelling blog posts, case studies, and social media content that showcases our platform\'s capabilities.',
      requirements: [
        'Experience in SaaS or AI content marketing',
        'Excellent writing and editing skills',
        'Understanding of SEO best practices',
        'Experience with content management systems'
      ]
    },
    {
      title: 'Customer Success Manager',
      department: 'Customer Success',
      location: 'Remote (Worldwide)',
      type: 'Full-time',
      description: 'As a Customer Success Manager, you\'ll help our customers get the most out of eComEasyAI. You\'ll provide training, support, and strategic guidance to ensure our customers achieve their goals.',
      requirements: [
        'Experience in customer success or account management',
        'Strong communication and presentation skills',
        'Problem-solving mindset',
        'Experience with CRM software'
      ]
    },
  ];

  const benefits = [
    'Competitive salary and equity options',
    'Flexible remote work policy',
    'Unlimited PTO',
    'Health, dental, and vision insurance',
    'Professional development budget',
    'Home office stipend',
    'Regular team retreats',
    'Parental leave'
  ];

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-12">
        <Link to="/" className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Home
        </Link>
        
        <div className="max-w-6xl mx-auto">
          <h1 className="text-4xl font-bold text-white mb-4">Join Our Team</h1>
          <p className="text-gray-300 mb-12 text-xl">
            Help us revolutionize e-commerce content creation with AI technology.
          </p>
          
          <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 p-8 rounded-xl border border-purple-500/20 mb-12">
            <h2 className="text-2xl font-semibold text-white mb-4">Why Work With Us</h2>
            <p className="text-gray-300 mb-6">
              At eComEasyAI, we're building the future of e-commerce content creation. Our team is passionate about using AI to solve real business problems and make life easier for online sellers around the world.
            </p>
            <p className="text-gray-300 mb-6">
              We're a remote-first company with team members across the globe. We value autonomy, creativity, and impact. If you're excited about AI, e-commerce, and building products that customers love, we'd love to hear from you.
            </p>
            
            <h3 className="text-xl font-semibold text-white mb-4 mt-8">Our Benefits</h3>
            <div className="grid md:grid-cols-2 gap-4">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-purple-400 mr-2" />
                  <span className="text-gray-300">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
          
          <h2 className="text-2xl font-semibold text-white mb-6">Open Positions</h2>
          
          <div className="space-y-6">
            {jobOpenings.map((job, index) => (
              <div key={index} className="bg-gray-800 rounded-xl border border-gray-700 overflow-hidden">
                <div className="p-6">
                  <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold text-white">{job.title}</h3>
                    <div className="flex flex-wrap gap-2 mt-2 md:mt-0">
                      <span className="px-3 py-1 bg-gray-700 rounded-full text-sm text-gray-300">{job.department}</span>
                      <span className="px-3 py-1 bg-gray-700 rounded-full text-sm text-gray-300">{job.location}</span>
                      <span className="px-3 py-1 bg-gray-700 rounded-full text-sm text-gray-300">{job.type}</span>
                    </div>
                  </div>
                  
                  <p className="text-gray-400 mb-4">{job.description}</p>
                  
                  <div className="mb-4">
                    <h4 className="text-white font-medium mb-2">Requirements:</h4>
                    <ul className="list-disc pl-5 space-y-1">
                      {job.requirements.map((req, reqIndex) => (
                        <li key={reqIndex} className="text-gray-400">{req}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                    Apply Now
                  </button>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-12 text-center">
            <p className="text-gray-300 mb-4">
              Don't see a position that matches your skills? We're always looking for talented individuals to join our team.
            </p>
            <a href="mailto:<EMAIL>" className="text-purple-400 hover:text-purple-300 transition-colors">
              Contact <NAME_EMAIL>
            </a>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

export default CareersPage;