rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if the user is an admin
    function isAdmin() {
      return request.auth != null && request.auth.token.email == "<EMAIL>";
    }
    
    // Users collection - allow admin to read all users
    match /users/{userId} {
      allow read: if request.auth != null && (resource.data.uid == request.auth.uid || isAdmin());
      allow write: if request.auth != null && (resource.data.uid == request.auth.uid || isAdmin());
      allow create: if request.auth != null;
      allow update: if request.auth != null && (resource.data.uid == request.auth.uid || isAdmin());
      allow delete: if isAdmin();
    }
    
    // Captions collection - allow admin to read all captions
    match /captions/{captionId} {
      allow read: if request.auth != null && (resource.data.userId == request.auth.uid || isAdmin());
      allow write: if request.auth != null && (resource.data.userId == request.auth.uid || isAdmin());
    }
    
    match /uploaded_images/{imageId} {
      allow read: if request.auth != null && (resource.data.userId == request.auth.uid || isAdmin());
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
      allow delete: if request.auth != null && (resource.data.userId == request.auth.uid || isAdmin());
    }
    
    match /customPrompts/{promptId} {
      allow read: if request.auth != null && (resource.data.userId == request.auth.uid || isAdmin());
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
      allow update: if request.auth != null && (resource.data.userId == request.auth.uid || isAdmin());
      allow delete: if request.auth != null && (resource.data.userId == request.auth.uid || isAdmin());
    }
    
    match /savedCaptions/{captionId} {
      allow read: if request.auth != null && (resource.data.userId == request.auth.uid || isAdmin());
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
      allow update: if request.auth != null && (resource.data.userId == request.auth.uid || isAdmin());
      allow delete: if request.auth != null && (resource.data.userId == request.auth.uid || isAdmin());
    }
    
    match /packages/{packageId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && isAdmin();
      allow create: if request.auth != null && isAdmin();
      allow update: if request.auth != null && isAdmin();
      allow delete: if request.auth != null && isAdmin();
    }
    
    // Settings collection - allow admin to write, all authenticated users to read
    // This includes userLimits, proUserLimits, and enterpriseUserLimits documents
    match /settings/{settingId} {
      allow read: if request.auth != null;
      allow write: if isAdmin();
      allow create: if isAdmin();
      allow update: if isAdmin();
      allow delete: if isAdmin();
    }
    
    // User usage collection - allow users to read/write their own usage data
    match /userUsage/{userId} {
      allow read: if request.auth != null && (request.auth.uid == userId || isAdmin());
      allow write: if request.auth != null && (request.auth.uid == userId || isAdmin());
      allow create: if request.auth != null && (request.auth.uid == userId || isAdmin());
      allow update: if request.auth != null && (request.auth.uid == userId || isAdmin());
      allow delete: if request.auth != null && isAdmin();
    }
  }
}