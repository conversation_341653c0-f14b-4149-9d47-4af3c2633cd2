import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { db } from '@/lib/firebase';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { toast } from 'react-hot-toast';
import { Settings, Save, RefreshCw, Users } from 'lucide-react';

interface EnterpriseLimitSettings {
  maxSubUsers: number;
  maxImagesPerSubUser: number;
  deleteDelayHoursPerSubUser: number;
  maxSavedPromptsPerSubUser: number;
  maxCustomPromptsPerSubUser: number;
}

export function EnterpriseUserLimitsManagement() {
  const [limits, setLimits] = useState<EnterpriseLimitSettings>({
    maxSubUsers: 5,
    maxImagesPerSubUser: 200,
    deleteDelayHoursPerSubUser: 0,
    maxSavedPromptsPerSubUser: 150,
    maxCustomPromptsPerSubUser: 20
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchLimits();
  }, []);

  const fetchLimits = async () => {
    setLoading(true);
    try {
      // Import auth to check if user is authenticated
      const { getAuth } = await import('firebase/auth');
      const auth = getAuth();
      const currentUser = auth.currentUser;
      
      if (!currentUser) {
        toast.error('You must be logged in to access this page');
        setLoading(false);
        return;
      }
      
      // Check if limits document exists
      const limitsDocRef = doc(db, 'settings', 'enterpriseUserLimits');
      const limitsDoc = await getDoc(limitsDocRef);
      
      if (limitsDoc.exists()) {
        const data = limitsDoc.data() as EnterpriseLimitSettings;
        setLimits(data);
      } else {
        // If no document exists, create one with default values
        await setDoc(limitsDocRef, limits);
        toast.success('Created default enterprise user limits settings');
      }
    } catch (error: any) {
      console.error('Error fetching enterprise user limits:', error);
      if (error.code === 'permission-denied') {
        toast.error('You do not have permission to access enterprise user limits settings');
      } else {
        toast.error(`Error loading enterprise user limits: ${error.message || 'Unknown error'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSaveLimits = async () => {
    setSaving(true);
    try {
      const limitsDocRef = doc(db, 'settings', 'enterpriseUserLimits');
      await setDoc(limitsDocRef, limits);
      toast.success('Enterprise user limits saved successfully');
    } catch (error) {
      console.error('Error saving enterprise user limits:', error);
      toast.error('Error saving enterprise user limits');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof EnterpriseLimitSettings, value: number) => {
    setLimits(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const resetToDefaults = () => {
    setLimits({
      maxSubUsers: 5,
      maxImagesPerSubUser: 200,
      deleteDelayHoursPerSubUser: 0,
      maxSavedPromptsPerSubUser: 150,
      maxCustomPromptsPerSubUser: 20
    });
    toast.success('Reset to default values');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-white">Enterprise User Limit Settings</h2>
          <p className="text-gray-400 mt-1">Configure limits for enterprise users and their sub-users</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={resetToDefaults}
            className="bg-gray-800 border-gray-700 text-gray-200 hover:bg-gray-700 hover:text-white"
            disabled={loading || saving}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Reset to Defaults
          </Button>
          <Button
            onClick={handleSaveLimits}
            className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white border-0"
            disabled={loading || saving}
          >
            {saving ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-t-transparent"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
          <span className="ml-3 text-gray-400">Loading settings...</span>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Max Sub-Users Limit */}
          <Card className="bg-gray-800 border-gray-700 text-white">
            <CardHeader>
              <CardTitle className="text-lg font-medium flex items-center">
                <Users className="h-5 w-5 mr-2 text-purple-400" />
                Max Sub-Users Limit
              </CardTitle>
              <CardDescription className="text-gray-400">
                Maximum number of sub-users an enterprise user can add
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">Current value: {limits.maxSubUsers}</span>
                  <Input
                    type="number"
                    value={limits.maxSubUsers}
                    onChange={(e) => handleInputChange('maxSubUsers', parseInt(e.target.value) || 0)}
                    min="1"
                    max="50"
                    className="w-20 bg-gray-700 border-gray-600 text-white"
                  />
                </div>
                <Slider
                  value={[limits.maxSubUsers]}
                  min={1}
                  max={50}
                  step={1}
                  onValueChange={(value) => handleInputChange('maxSubUsers', value[0])}
                  className="py-4"
                />
              </div>
            </CardContent>
          </Card>

          {/* Max Images Per Sub-User */}
          <Card className="bg-gray-800 border-gray-700 text-white">
            <CardHeader>
              <CardTitle className="text-lg font-medium flex items-center">
                <Settings className="h-5 w-5 mr-2 text-purple-400" />
                Max Images Per Sub-User
              </CardTitle>
              <CardDescription className="text-gray-400">
                Maximum number of images each sub-user can upload
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">Current value: {limits.maxImagesPerSubUser}</span>
                  <Input
                    type="number"
                    value={limits.maxImagesPerSubUser}
                    onChange={(e) => handleInputChange('maxImagesPerSubUser', parseInt(e.target.value) || 0)}
                    min="1"
                    max="1000"
                    className="w-20 bg-gray-700 border-gray-600 text-white"
                  />
                </div>
                <Slider
                  value={[limits.maxImagesPerSubUser]}
                  min={1}
                  max={1000}
                  step={10}
                  onValueChange={(value) => handleInputChange('maxImagesPerSubUser', value[0])}
                  className="py-4"
                />
              </div>
            </CardContent>
          </Card>

          {/* Delete Delay Hours Per Sub-User */}
          <Card className="bg-gray-800 border-gray-700 text-white">
            <CardHeader>
              <CardTitle className="text-lg font-medium flex items-center">
                <Settings className="h-5 w-5 mr-2 text-purple-400" />
                Image Delete Delay Per Sub-User (Hours)
              </CardTitle>
              <CardDescription className="text-gray-400">
                Hours before a sub-user can delete uploaded images (0 for immediate deletion)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">Current value: {limits.deleteDelayHoursPerSubUser} hours</span>
                  <Input
                    type="number"
                    value={limits.deleteDelayHoursPerSubUser}
                    onChange={(e) => handleInputChange('deleteDelayHoursPerSubUser', parseInt(e.target.value) || 0)}
                    min="0"
                    max="72"
                    className="w-20 bg-gray-700 border-gray-600 text-white"
                  />
                </div>
                <Slider
                  value={[limits.deleteDelayHoursPerSubUser]}
                  min={0}
                  max={72}
                  step={1}
                  onValueChange={(value) => handleInputChange('deleteDelayHoursPerSubUser', value[0])}
                  className="py-4"
                />
              </div>
            </CardContent>
          </Card>

          {/* Max Saved Prompts Per Sub-User */}
          <Card className="bg-gray-800 border-gray-700 text-white">
            <CardHeader>
              <CardTitle className="text-lg font-medium flex items-center">
                <Settings className="h-5 w-5 mr-2 text-purple-400" />
                Max Saved Prompts Per Sub-User
              </CardTitle>
              <CardDescription className="text-gray-400">
                Maximum number of prompts each sub-user can save
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">Current value: {limits.maxSavedPromptsPerSubUser}</span>
                  <Input
                    type="number"
                    value={limits.maxSavedPromptsPerSubUser}
                    onChange={(e) => handleInputChange('maxSavedPromptsPerSubUser', parseInt(e.target.value) || 0)}
                    min="1"
                    max="500"
                    className="w-20 bg-gray-700 border-gray-600 text-white"
                  />
                </div>
                <Slider
                  value={[limits.maxSavedPromptsPerSubUser]}
                  min={1}
                  max={500}
                  step={10}
                  onValueChange={(value) => handleInputChange('maxSavedPromptsPerSubUser', value[0])}
                  className="py-4"
                />
              </div>
            </CardContent>
          </Card>

          {/* Max Custom Prompts Per Sub-User */}
          <Card className="bg-gray-800 border-gray-700 text-white">
            <CardHeader>
              <CardTitle className="text-lg font-medium flex items-center">
                <Settings className="h-5 w-5 mr-2 text-purple-400" />
                Max Custom Prompts Per Sub-User
              </CardTitle>
              <CardDescription className="text-gray-400">
                Maximum number of custom prompts each sub-user can create
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">Current value: {limits.maxCustomPromptsPerSubUser}</span>
                  <Input
                    type="number"
                    value={limits.maxCustomPromptsPerSubUser}
                    onChange={(e) => handleInputChange('maxCustomPromptsPerSubUser', parseInt(e.target.value) || 0)}
                    min="1"
                    max="100"
                    className="w-20 bg-gray-700 border-gray-600 text-white"
                  />
                </div>
                <Slider
                  value={[limits.maxCustomPromptsPerSubUser]}
                  min={1}
                  max={100}
                  step={5}
                  onValueChange={(value) => handleInputChange('maxCustomPromptsPerSubUser', value[0])}
                  className="py-4"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

export default EnterpriseUserLimitsManagement;