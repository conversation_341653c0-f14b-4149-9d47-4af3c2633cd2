import { getFunctions, httpsCallable } from 'firebase/functions';
import { db } from './firebase';
import { collection, query, where, getDocs, addDoc, Timestamp } from 'firebase/firestore';
import { toast } from 'react-hot-toast';
import { getApp } from 'firebase/app';

// Get environment variables
const storeId = import.meta.env.VITE_SSLCOMMERZ_STORE_ID;
const storePassword = import.meta.env.VITE_SSLCOMMERZ_STORE_PASSWORD;
const isLive = import.meta.env.VITE_SSLCOMMERZ_IS_LIVE === 'true';
const appUrl = import.meta.env.VITE_APP_URL || 'https://ecomeasy.ai';
const localAppUrl = import.meta.env.VITE_LOCAL_APP_URL || 'http://localhost:5173';

// Debug flag
const DEBUG = true;

// Define types
interface BillingDetails {
  fullName: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  paymentGateway?: string;
}

interface SSLCommerzPaymentParams {
  userId: string;
  userEmail: string;
  userName?: string;
  userPhone?: string;
  amount: number;
  plan: string;
  billingPeriod: 'monthly' | 'yearly';
  billingDetails: BillingDetails;
  couponCode?: string;
  discountAmount?: number;
}

/**
 * Initializes a payment session with SSLCOMMERZ
 * @param params Payment parameters
 * @returns Promise with the redirect URL
 */
export const initSSLCommerzPayment = async (params: SSLCommerzPaymentParams): Promise<string> => {
  try {
    // Validate required parameters
    if (!params.userId) {
      throw new Error('Missing userId parameter');
    }
    if (!params.userEmail) {
      throw new Error('Missing userEmail parameter');
    }
    if (!params.amount) {
      throw new Error('Missing amount parameter');
    }
    if (!params.plan) {
      throw new Error('Missing plan parameter');
    }
    if (!params.billingPeriod) {
      throw new Error('Missing billingPeriod parameter');
    }
    if (!params.billingDetails) {
      throw new Error('Missing billingDetails parameter');
    }

    // Debug log
    if (DEBUG) {
      console.log('SSLCOMMERZ payment parameters:', JSON.stringify(params, null, 2));
    }

    // Determine if we're in development or production
    const environment = import.meta.env.MODE;

    // Get Firebase Functions instance
    const functions = getFunctions(getApp());

    // Get the callable function
    const initPayment = httpsCallable(functions, 'initSSLCommerzPayment');

    // Call the function with the parameters
    const result = await initPayment({
      ...params,
      environment
    });

    // The result is of type any, so we need to cast it
    const data = result.data as any;

    if (DEBUG) {
      console.log('SSLCOMMERZ payment result:', JSON.stringify(data, null, 2));
    }

    if (data.status !== 'SUCCESS') {
      throw new Error(data.message || 'Payment initialization failed');
    }

    // Return the redirect URL
    return data.data.redirectUrl;
  } catch (error: any) {
    console.error('Error initializing SSLCOMMERZ payment:', error);

    // Display a user-friendly error message
    if (error.message && error.message.includes('Missing')) {
      toast.error(error.message);
    } else if (error.code === 'functions/invalid-argument') {
      toast.error('Missing required payment information. Please check your details.');
    } else if (error.code === 'functions/aborted') {
      toast.error('Payment initialization failed. Please try again later.');
    } else {
      toast.error('Failed to initialize payment. Please try again.');
    }

    throw error;
  }
};

/**
 * Processes a successful payment
 * @param transactionId The transaction ID
 * @returns Promise with the transaction details
 */
export const processSuccessfulPayment = async (transactionId: string) => {
  try {
    // Query Firestore for the transaction
    const transactionsRef = collection(db, 'sslcommerzTransactions');
    const q = query(transactionsRef, where('transactionId', '==', transactionId));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      throw new Error('Transaction not found');
    }

    const transactionData = querySnapshot.docs[0].data();

    // Check if the transaction is completed
    if (transactionData.status !== 'COMPLETED') {
      throw new Error('Transaction is not completed');
    }

    // Return the transaction details
    return {
      transactionId,
      plan: transactionData.plan,
      billingPeriod: transactionData.billingPeriod,
      amount: transactionData.amount,
      status: transactionData.status,
      createdAt: transactionData.createdAt,
      updatedAt: transactionData.updatedAt
    };
  } catch (error) {
    console.error('Error processing successful payment:', error);
    toast.error('Failed to process payment. Please contact support.');
    throw error;
  }
};

/**
 * Processes a failed payment
 * @param transactionId The transaction ID
 * @returns Promise with the transaction details
 */
export const processFailedPayment = async (transactionId: string) => {
  try {
    // Query Firestore for the transaction
    const transactionsRef = collection(db, 'sslcommerzTransactions');
    const q = query(transactionsRef, where('transactionId', '==', transactionId));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      throw new Error('Transaction not found');
    }

    const transactionData = querySnapshot.docs[0].data();

    // Return the transaction details
    return {
      transactionId,
      plan: transactionData.plan,
      billingPeriod: transactionData.billingPeriod,
      amount: transactionData.amount,
      status: transactionData.status,
      createdAt: transactionData.createdAt,
      updatedAt: transactionData.updatedAt,
      error: transactionData.failureDetails
    };
  } catch (error) {
    console.error('Error processing failed payment:', error);
    toast.error('Failed to process payment information. Please contact support.');
    throw error;
  }
};

/**
 * Processes a canceled payment
 * @param transactionId The transaction ID
 * @returns Promise with the transaction details
 */
export const processCanceledPayment = async (transactionId: string) => {
  try {
    // Query Firestore for the transaction
    const transactionsRef = collection(db, 'sslcommerzTransactions');
    const q = query(transactionsRef, where('transactionId', '==', transactionId));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      throw new Error('Transaction not found');
    }

    const transactionData = querySnapshot.docs[0].data();

    // Return the transaction details
    return {
      transactionId,
      plan: transactionData.plan,
      billingPeriod: transactionData.billingPeriod,
      amount: transactionData.amount,
      status: transactionData.status,
      createdAt: transactionData.createdAt,
      updatedAt: transactionData.updatedAt
    };
  } catch (error) {
    console.error('Error processing canceled payment:', error);
    toast.error('Failed to process payment information. Please contact support.');
    throw error;
  }
};
