import { useState, useEffect, useRef, useMemo } from 'react';
import { ImageIcon, Save, Check, Plus, Edit, Trash, BookOpen, Monitor, Tablet, Smartphone, Eye, Sun, Moon, Copy, ChevronDown, ChevronRight, Bookmark as BookmarkIcon } from 'lucide-react';
import { useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { toast } from 'react-hot-toast';
import { supabase, STORAGE_BUCKET } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';
import { useStore } from '@/lib/store';
import { db } from '@/lib/firebase';
import { collection, addDoc, getDocs, updateDoc, deleteDoc, doc, query, where } from 'firebase/firestore';
import { analyzeImage, analyzeMultipleImages, DEFAULT_PRODUCT_PROMPT, formatPrompt, DEFAULT_PROMPTS, DefaultPrompt, DEFAULT_PROMPTS_CATEGORIES, PromptCategory } from '@/lib/gemini';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { GlowingEffect } from "@/components/ui/glowing-effect";
// Quill editor imports removed
import { marked } from 'marked';
import { motion, AnimatePresence } from 'framer-motion';
import { canCreateMoreCustomPrompts, incrementCustomPromptsCount, decrementCustomPromptsCount, canGenerateMorePrompts, incrementDailyGenerationCount, canSaveMorePrompts, incrementSavedPromptsCount } from '@/lib/userLimits';
import { UpgradeModal } from '@/components/ui/upgrade-modal';
import { UpgradePlanModal } from '@/components/ui/upgrade-plan-modal';

// Import CSS for animations
import '@/components/billing-details.css';

interface UploadedImage {
  id: string;
  url: string;
  name: string;
  path: string;
}

interface CustomPrompt {
  id: string;
  name: string;
  text: string;
  userId: string;
  isOwnerPrompt?: boolean; // Flag to indicate if the prompt is from the team owner
}

export function ImageToCaption() {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [selectionMode, setSelectionMode] = useState<'single' | 'multi-single-product'>('single');
  const [caption, setCaption] = useState('');
  // Removing editedCaption state and isEditMode state
  const [loading, setLoading] = useState(false);
  const [generatingDescription, setGeneratingDescription] = useState(false);
  const [customPrompt, setCustomPrompt] = useState(DEFAULT_PRODUCT_PROMPT);
  const [images, setImages] = useState<UploadedImage[]>([]);
  const [fetchingImages, setFetchingImages] = useState(true);
  const [tone, setTone] = useState<string>('Professional');
  const [language, setLanguage] = useState<string>('English');
  const [previewMode, setPreviewMode] = useState<'none' | 'laptop' | 'tablet' | 'smartphone'>('none');
  const [previewTheme, setPreviewTheme] = useState<'dark' | 'light'>('dark');
  const promptSelectRef = useRef<HTMLSelectElement>(null);
  const defaultPromptSelectRef = useRef<HTMLSelectElement>(null);
  const { user } = useStore();

  // Add state to track if current caption has been saved
  const [isCaptionSaved, setIsCaptionSaved] = useState(false);
  const [currentCaptionId, setCurrentCaptionId] = useState<string | null>(null);

  // Add state to track saving operation
  const [isSaving, setIsSaving] = useState(false);

  // Accordion state for categorized prompts
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);
  const [selectedPromptId, setSelectedPromptId] = useState<string>('');

  // State for mobile accordion (Advanced section)
  const [isAdvancedSectionExpanded, setIsAdvancedSectionExpanded] = useState(false);

  // Custom prompt dialog state
  const [isPromptDialogOpen, setIsPromptDialogOpen] = useState(false);
  const [promptName, setPromptName] = useState('');
  const [promptText, setPromptText] = useState('');
  const [savedPrompts, setSavedPrompts] = useState<CustomPrompt[]>([]);
  const [editingPromptId, setEditingPromptId] = useState<string | null>(null);
  const [canCreateMorePrompts, setCanCreateMorePrompts] = useState(true);
  const [multipleResults, setMultipleResults] = useState<string[]>([]);
  const [savingPrompt, setSavingPrompt] = useState(false);

  // Upgrade modal state
  const [isUpgradeModalOpen, setIsUpgradeModalOpen] = useState(false);
  const [isUpgradePlanModalOpen, setIsUpgradePlanModalOpen] = useState(false);
  const [upgradeFeature, setUpgradeFeature] = useState('Custom Prompts');
  const [currentLimit, setCurrentLimit] = useState(0);

  // Delete confirmation dialog state
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [promptToDeleteId, setPromptToDeleteId] = useState<string | null>(null);
  const [promptToDeleteName, setPromptToDeleteName] = useState<string>('');

  // Responsive state for desktop detection
  const [isDesktop, setIsDesktop] = useState(window.innerWidth >= 768);

  const languageDropdownRef = useRef<HTMLDivElement>(null);
  const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState(false);
  const [searchLanguage, setSearchLanguage] = useState('');

  // Cache for performance optimization
  const [userDataCache, setUserDataCache] = useState<any>(null);
  const [lastCacheTime, setLastCacheTime] = useState<number>(0);
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

  // Debounce timer for generation
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastGenerationTimeRef = useRef<number>(0);
  const MIN_GENERATION_INTERVAL = 1000; // Minimum 1 second between generations

  // Available tones and languages
  const tones = ['Professional', 'Friendly', 'Casual', 'Informative', 'Creative', 'Confident', 'Vivid', 'Luxury', 'Engaging'];
  const languages = [
    'Arabic', 'Bangla (Bangladesh)', 'Bengali (India)','Bulgarian', 'Chinese simplified', 'Chinese traditional',
    'Croatian', 'Czech', 'Danish', 'Dutch', 'English', 'Estonian', 'Finnish',
    'French', 'German', 'Greek', 'Hebrew', 'Hindi', 'Hungarian', 'Indonesian',
    'Italian', 'Japanese', 'Korean', 'Latvian', 'Lithuanian', 'Norwegian',
    'Polish', 'Portuguese', 'Romanian', 'Russian', 'Serbian', 'Slovak',
    'Slovenian', 'Spanish', 'Swahili', 'Swedish', 'Thai', 'Turkish',
    'Ukrainian', 'Vietnamese'
  ];

  // Filtered languages based on search
  const filteredLanguages = useMemo(() => {
    return languages.filter(lang =>
      lang.toLowerCase().includes(searchLanguage.toLowerCase())
    );
  }, [languages, searchLanguage]);

  // Handle clicking outside of language dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (languageDropdownRef.current && !languageDropdownRef.current.contains(event.target as Node)) {
        setIsLanguageDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle window resize for responsive detection
  useEffect(() => {
    const handleResize = () => {
      setIsDesktop(window.innerWidth >= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Handle language selection
  const handleLanguageSelect = (lang: string) => {
    setLanguage(lang);
    setIsLanguageDropdownOpen(false);
  };

  // Get location state from react-router
  const location = useLocation();

  useEffect(() => {
    if (user) {
      // Progressive loading: Load essential data first
      const loadEssentialData = async () => {
        // Load images first (most important for user interaction)
        await loadExistingImages();

        // Check if we have selected images from the upload page
        if (location.state && location.state.selectedImageUrls) {
          // If multiple images were selected, set selection mode to multi
          if (location.state.selectedImageUrls.length > 1) {
            setSelectionMode('multi-single-product');
            setSelectedImages(location.state.selectedImageUrls);
          } else if (location.state.selectedImageUrls.length === 1) {
            // If only one image was selected, set selection mode to single
            setSelectionMode('single');
            setSelectedImage(location.state.selectedImageUrls[0]);
            setSelectedImages(location.state.selectedImageUrls);
          }
        }
      };

      // Load secondary data asynchronously (non-blocking)
      const loadSecondaryData = async () => {
        // Load prompts and limits in parallel
        await Promise.all([
          loadSavedPrompts(),
          checkCustomPromptsLimit()
        ]);
      };

      // Execute progressive loading
      loadEssentialData().then(() => {
        // Load secondary data after essential data is loaded
        loadSecondaryData();
      });
    }
  }, [user, location.state]);

  const loadExistingImages = async () => {
    if (!user) return;

    try {
      const userImagesPath = `product-images/${user.id}`;
      const { data, error } = await supabase.storage
        .from(STORAGE_BUCKET)
        .list(userImagesPath, {
          limit: 100,
          offset: 0,
        });

      if (error) {
        if (!error.message.includes('The resource was not found')) {
          throw error;
        }
        return;
      }

      if (data && data.length > 0) {
        // Sort the data array by created_at timestamp (newest first)
        // If created_at is available in the file metadata
        const sortedData = [...data].sort((a, b) => {
          // If created_at is available, use it for sorting
          if (a.created_at && b.created_at) {
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          }
          // If no timestamps available, keep original order (or sort by name as a fallback)
          return a.name.localeCompare(b.name);
        });

        const loadedImages = await Promise.all(
          sortedData.map(async (file) => {
            const { data: publicUrlData } = supabase.storage
              .from(STORAGE_BUCKET)
              .getPublicUrl(`${userImagesPath}/${file.name}`);

            return {
              id: uuidv4(),
              url: publicUrlData.publicUrl,
              name: file.name,
              path: `${userImagesPath}/${file.name}`,
              // Timestamp can be derived from created_at if needed later
              // timestamp: file.created_at,
            };
          })
        );

        setImages(loadedImages);
      }
    } catch (error) {
      console.error('Error loading images:', error);
      toast.error('Error loading existing images');
    } finally {
      setFetchingImages(false);
    }
  };

  const handleImageSelect = (image: UploadedImage) => {
    // Reset save state when image selection changes
    setIsCaptionSaved(false);
    setCurrentCaptionId(null);
    setIsSaving(false);

    if (selectionMode === 'single') {
      if (selectedImages.includes(image.url)) {
        setSelectedImage(null);
        setSelectedImages([]);
      } else {
        setSelectedImage(image.url);
        setSelectedImages([image.url]);
      }
    } else {
      const maxImages = 5; // Maximum 5 images for multi-single-product mode
      if (selectedImages.includes(image.url)) {
        setSelectedImages(prev => prev.filter(url => url !== image.url));
      } else if (selectedImages.length < maxImages) {
        setSelectedImages(prev => [...prev, image.url]);
      } else {
        toast.error(`Maximum ${maxImages} images can be selected in this mode`);
      }
    }
  };

  const handleSelectionModeChange = (mode: 'single' | 'multi-single-product') => {
    setSelectionMode(mode);
    setSelectedImage(null);
    setSelectedImages([]);
    setCaption('');
    // Reset save state when changing selection mode
    setIsCaptionSaved(false);
    setCurrentCaptionId(null);
    setIsSaving(false);
  };

  const checkCustomPromptsLimit = async () => {
    if (!user) return;

    try {
      const canCreate = await canCreateMoreCustomPrompts(user.id);
      setCanCreateMorePrompts(canCreate);

      // Get the actual limit for the upgrade modal
      const { getLimitSettings } = await import('@/lib/userLimits');
      const limits = await getLimitSettings();
      setCurrentLimit(limits.maxCustomPrompts ?? 0);
    } catch (error) {
      console.error('Error checking custom prompts limit:', error);
      setCanCreateMorePrompts(false);
      setCurrentLimit(0);
    }
  };

  const loadSavedPrompts = async () => {
    if (!user) return;

    try {
      // First, load the user's own custom prompts
      const promptsQuery = query(collection(db, 'customPrompts'), where('userId', '==', user.id));
      const querySnapshot = await getDocs(promptsQuery);

      const prompts: CustomPrompt[] = [];
      querySnapshot.forEach((doc) => {
        prompts.push({
          id: doc.id,
          ...doc.data() as Omit<CustomPrompt, 'id'>
        });
      });

      // Check cache for user data to avoid repeated Firebase calls
      const currentTime = Date.now();
      let userData = null;

      if (userDataCache && (currentTime - lastCacheTime) < CACHE_DURATION) {
        // Use cached data
        userData = userDataCache;
      } else {
        // Fetch fresh data and cache it
        const usersRef = collection(db, 'users');
        const userQuery = query(usersRef, where('uid', '==', user.id));
        const userSnapshot = await getDocs(userQuery);

        if (!userSnapshot.empty) {
          userData = userSnapshot.docs[0].data();
          setUserDataCache(userData);
          setLastCacheTime(currentTime);
        }
      }

      if (userData) {
        // If user is a team member, also load the owner's custom prompts
        if (userData.isTeamMember && userData.teamOwnerId) {
          console.log('User is a team member. Loading owner prompts from:', userData.teamOwnerId);

          const ownerPromptsQuery = query(collection(db, 'customPrompts'), where('userId', '==', userData.teamOwnerId));
          const ownerSnapshot = await getDocs(ownerPromptsQuery);

          // Add owner's prompts with a special flag to indicate they're from the owner
          ownerSnapshot.forEach((doc) => {
            const promptData = doc.data() as Omit<CustomPrompt, 'id'>;
            prompts.push({
              id: doc.id,
              ...promptData,
              // Add a prefix to the name to indicate it's from the owner
              name: `👑 ${promptData.name}`,
              // Add a flag to indicate it's from the owner (for UI display)
              isOwnerPrompt: true
            } as CustomPrompt & { isOwnerPrompt?: boolean });
          });

          console.log('Loaded owner prompts:', ownerSnapshot.size);
        }
      }

      setSavedPrompts(prompts);
      // Re-check the limit after loading prompts
      checkCustomPromptsLimit();
    } catch (error) {
      console.error('Error loading saved prompts:', error);
      toast.error('Error loading saved prompts');
    }
  };

  const handleOpenPromptDialog = async (prompt?: CustomPrompt) => {
    if (prompt) {
      // Prevent editing owner prompts
      if (prompt.isOwnerPrompt) {
        toast.error('You cannot edit prompts shared by your team owner');
        return;
      }

      setPromptName(prompt.name);
      setPromptText(prompt.text);
      setEditingPromptId(prompt.id);
    } else {
      // Check if user can create more custom prompts before opening dialog for new prompt
      if (!canCreateMorePrompts) {
        // Show upgrade modal instead of toast
        setUpgradeFeature('Custom Prompts');
        setIsUpgradeModalOpen(true);
        return;
      }
      setPromptName('');
      setPromptText('');
      setEditingPromptId(null);
    }
    setIsPromptDialogOpen(true);
  };

  const handleSavePrompt = async () => {
    if (!user) return;
    if (!promptName.trim() || !promptText.trim()) {
      toast.error('Please provide both a name and text for your prompt');
      return;
    }

    setSavingPrompt(true);

    try {
      if (editingPromptId) {
        // Update existing prompt
        const promptRef = doc(db, 'customPrompts', editingPromptId);
        await updateDoc(promptRef, {
          name: promptName,
          text: promptText,
          updatedAt: new Date()
        });
        toast.success('Prompt updated successfully');
      } else {
        // Check if user can create more custom prompts
        const canCreate = await canCreateMoreCustomPrompts(user.id);
        if (!canCreate) {
          // Show upgrade modal instead of toast
          setUpgradeFeature('Custom Prompts');
          setCurrentLimit(0); // Will be updated with actual limit
          setIsUpgradeModalOpen(true);
          setIsPromptDialogOpen(false); // Close the prompt dialog
          return;
        }

        // Create new prompt
        await addDoc(collection(db, 'customPrompts'), {
          name: promptName,
          text: promptText,
          userId: user.id,
          createdAt: new Date()
        });

        // Increment the user's custom prompts count
        await incrementCustomPromptsCount(user.id);

        toast.success('Prompt saved successfully');
      }

      setIsPromptDialogOpen(false);
      loadSavedPrompts();
      // Re-check the limit after saving
      checkCustomPromptsLimit();
    } catch (error) {
      console.error('Error saving prompt:', error);
      toast.error('Error saving prompt');
    } finally {
      setSavingPrompt(false);
    }
  };

  // Handler to open the delete confirmation dialog
  const handleDeleteClick = (promptId: string) => {
    if (!user) return;

    // Find the prompt to check if it's an owner prompt
    const promptToDelete = savedPrompts.find(p => p.id === promptId);

    // Prevent deleting owner prompts
    if (promptToDelete?.isOwnerPrompt) {
      toast.error('You cannot delete prompts shared by your team owner');
      return;
    }

    // Set the prompt to delete and show confirmation dialog
    setPromptToDeleteId(promptId);
    setPromptToDeleteName(promptToDelete?.name || 'this prompt');
    setShowDeleteConfirm(true);
  };

  // Actual delete function after confirmation
  const confirmDeletePrompt = async () => {
    if (!user || !promptToDeleteId) return;

    try {
      await deleteDoc(doc(db, 'customPrompts', promptToDeleteId));

      // Decrement the user's custom prompts count
      await decrementCustomPromptsCount(user.id);

      toast.success('Prompt deleted successfully');
      loadSavedPrompts();
      // Re-check the limit after deleting
      checkCustomPromptsLimit();
    } catch (error) {
      console.error('Error deleting prompt:', error);
      toast.error('Error deleting prompt');
    } finally {
      // Close dialog and reset state
      setShowDeleteConfirm(false);
      setPromptToDeleteId(null);
      setPromptToDeleteName('');
    }
  };



  const handleSelectPrompt = (prompt: CustomPrompt) => {
    setCustomPrompt(prompt.text);
    setSelectedPromptId(prompt.id);
    // Update the dropdown selection
    if (promptSelectRef.current) {
      promptSelectRef.current.value = prompt.id;
    }
    toast.success(`Prompt "${prompt.name}" selected`);
  };

  const generateCaptionInternal = async () => {
    // Immediate loading state to prevent multiple clicks
    if (loading || generatingDescription) {
      return; // Prevent multiple simultaneous generations
    }

    if (!user) {
      toast.error('Please log in to generate captions');
      return;
    }

    if (selectedImages.length === 0) {
      toast.error('Please select at least one image');
      return;
    }

    // Set loading states immediately for instant feedback
    setLoading(true);
    setGeneratingDescription(true);
    setMultipleResults([]);

    // Reset caption saved state when generating new caption
    setIsCaptionSaved(false);
    setCurrentCaptionId(null);
    setIsSaving(false); // Reset saving state when generating new caption

    // Check if user can generate more prompts today
    try {
      const canGenerate = await canGenerateMorePrompts(user.id);
      if (!canGenerate) {
        toast.error('You have reached your daily generation limit. Please try again tomorrow or upgrade your plan.');
        setLoading(false);
        setGeneratingDescription(false);
        return;
      }
    } catch (error: any) {
      console.error('Error checking generation limit:', error);
      toast.error('Unable to verify generation limits. Please try again later.');
      setLoading(false);
      setGeneratingDescription(false);
      return;
    }

    try {
      // Format the prompt with selected language and tone
      const formattedPrompt = formatPrompt(customPrompt, language, tone);

      // Convert image URLs to base64
      const selectedImageUrls = selectionMode === 'single' ? [selectedImage!] : selectedImages;
      const imagesData = await Promise.all(
        selectedImageUrls.map(async (imageUrl) => {
          const response = await fetch(imageUrl);
          const blob = await response.blob();
          return await blobToBase64(blob);
        })
      );

      let result;

      if (selectionMode === 'single') {
        // Process single image
        result = await analyzeImage(imagesData[0], formattedPrompt);
        const parsedResult = parseMarkdownToHtml(result);
        setCaption(parsedResult);
      } else {
        // Process multiple images as a single product
        result = await analyzeMultipleImages(imagesData, formattedPrompt);
        const parsedResult = parseMarkdownToHtml(result);
        setCaption(parsedResult);
      }

      // Increment the user's daily generation count
      await incrementDailyGenerationCount(user.id);

      toast.success('Caption generated successfully!');
    } catch (error) {
      console.error('Error generating caption:', error);
      toast.error('Error generating caption. Please try again.');
    } finally {
      setLoading(false);
      setGeneratingDescription(false);
    }
  };

  // Debounced wrapper to prevent rapid successive calls
  const generateCaption = () => {
    const currentTime = Date.now();

    // Check if enough time has passed since last generation
    if (currentTime - lastGenerationTimeRef.current < MIN_GENERATION_INTERVAL) {
      toast.error('Please wait a moment before generating again.');
      return;
    }

    // Clear any existing debounce timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Update last generation time
    lastGenerationTimeRef.current = currentTime;

    // Execute generation immediately (no debounce delay for better UX)
    generateCaptionInternal();
  };

  // Function to parse markdown to HTML
  const parseMarkdownToHtml = (markdownText: string): string => { // Add type annotation
    try {
      // Configure marked options
      marked.setOptions({
        breaks: true,        // Convert line breaks to <br>
        gfm: true,           // Use GitHub Flavored Markdown
        // headerIds: false, // Removed invalid option
        // mangle: false,    // Removed invalid option
        // sanitize: false,  // Removed invalid option
      });

      // Parse markdown to HTML and cast to string
      return marked.parse(markdownText) as string;
    } catch (error) {
      console.error('Error parsing markdown:', error);
      return markdownText; // Return original text if parsing fails
    }
  };

  // Helper function to convert Blob to base64
  const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        // Remove the data URL prefix (e.g., 'data:image/jpeg;base64,')
        const base64 = base64String.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  };

  const handleSave = async () => {
    if (!user) {
      toast.error('You must be logged in to save captions');
      return;
    }

    if (!caption) {
      toast.error('No caption to save');
      return;
    }

    // If caption is already saved, don't save it again
    if (isCaptionSaved) {
      toast('This caption has already been saved'); // Use standard toast
      return;
    }

    // Prevent multiple simultaneous save operations
    if (isSaving) {
      return;
    }

    // Set saving state immediately for instant feedback
    setIsSaving(true);

    try {
      // Check if user can save more prompts
      const canSave = await canSaveMorePrompts(user.id);
      if (!canSave) {
        toast.error('You have reached your maximum saved data limit. Please delete some saved data or upgrade your plan.');
        return;
      }
      // Get the current time
      const now = new Date();
      // Format time in AM/PM format
      const hours = now.getHours();
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const ampm = hours >= 12 ? 'PM' : 'AM';
      const formattedHours = (hours % 12 || 12).toString().padStart(2, '0'); // Convert to 12-hour format
      const timeString = `${formattedHours}:${minutes} ${ampm}`;

      // Get the selected prompt name
      let selectedPromptName = '';
      if (promptSelectRef.current?.value) {
        const selectedPrompt = savedPrompts.find(p => p.id === promptSelectRef.current?.value);
        selectedPromptName = selectedPrompt?.name || '';
      } else if (selectedPromptId) {
        // Find the prompt in all categories
        for (const category of DEFAULT_PROMPTS_CATEGORIES) {
          const prompt = category.prompts.find(p => p.id === selectedPromptId);
          if (prompt) {
            selectedPromptName = prompt.name;
            break;
          }
        }
      } else if (defaultPromptSelectRef.current?.value) {
        const category = DEFAULT_PROMPTS_CATEGORIES.find(cat =>
          cat.prompts.some(p => p.id === defaultPromptSelectRef.current?.value)
        );
        const prompt = category?.prompts.find(p => p.id === defaultPromptSelectRef.current?.value);
        selectedPromptName = prompt?.name || '';
      }

      // Prepare the data to save
      const captionData = {
        type: 'caption',
        content: caption,
        image: selectedImage || (selectedImages.length > 0 ? selectedImages[0] : ''),
        images: selectedImages,
        prompt: customPrompt,
        promptName: selectedPromptName,
        savedTime: timeString,
        language,
        tone,
        selectionMode,
        userId: user.id,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Save the caption
      const docRef = await addDoc(collection(db, 'savedCaptions'), captionData);
      setCurrentCaptionId(docRef.id);
      setIsCaptionSaved(true);

      // Increment the user's saved prompts count
      await incrementSavedPromptsCount(user.id);

      toast.success('Caption saved to your collection!');
    } catch (error) {
      console.error('Error saving caption:', error);
      toast.error('Failed to save caption. Please try again.');
    } finally {
      // Always reset saving state
      setIsSaving(false);
    }
  };

  // Removed unused toggleEditMode function

  const copyToClipboard = () => {
    if (!caption) {
      toast.error('No caption to copy');
      return;
    }

    // Use the caption directly, removing edit mode related code
    const textToCopy = caption;

    // Create a temporary div to handle HTML content
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = textToCopy;
    const textContent = tempDiv.textContent || tempDiv.innerText || '';

    navigator.clipboard.writeText(textContent)
      .then(() => toast.success('Caption copied to clipboard'))
      .catch(() => toast.error('Failed to copy caption'));
  };

  const resetToDefaultPrompt = () => {
    setCustomPrompt(DEFAULT_PRODUCT_PROMPT);
    setSelectedPromptId('');
    if (promptSelectRef.current) {
      promptSelectRef.current.value = "";
    }
    if (defaultPromptSelectRef.current) {
      defaultPromptSelectRef.current.value = "";
    }
    toast.success('Reset to default prompt');
  };

  const handleSelectDefaultPrompt = (promptId: string) => {
    const selectedPrompt = DEFAULT_PROMPTS.find(p => p.id === promptId);
    if (selectedPrompt) {
      setCustomPrompt(selectedPrompt.text);
      setSelectedPromptId(promptId);

      // Find the category for this prompt and ensure it's the only expanded category
      const category = DEFAULT_PROMPTS_CATEGORIES.find(cat =>
        cat.prompts.some(p => p.id === promptId)
      );

      if (category) {
        setExpandedCategories([category.id]);
      }

      // Reset user prompt selection
      if (promptSelectRef.current) {
        promptSelectRef.current.value = "";
      }
      toast.success(`Default prompt "${selectedPrompt.name}" selected`);
    }
  };

  // Toggle accordion category - single category expansion behavior
  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => {
      if (prev.includes(categoryId)) {
        // If clicking on the currently expanded category, close it
        return prev.filter(id => id !== categoryId);
      } else {
        // If opening a new category, close all others and open only this one
        return [categoryId];
      }
    });
  };

  // Quill editor configuration removed
  return (
    <>      {/* Description Generation Loading Overlay */}
      {generatingDescription && (
        <div className="fixed inset-0 bg-black/90 flex items-center justify-center z-[60]">
          <div className="text-center description-loading-container">
            <div className="animate-spin rounded-full h-16 w-16 md:h-20 md:w-20 border-t-4 border-b-4 border-purple-500 mx-auto mb-6"></div>
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-2 description-loading-text">
              Generating Description
            </h2>
            <p className="text-gray-300 text-sm md:text-base">
              AI is analyzing your images and creating the perfect description...
            </p>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="space-y-6 p-4 md:p-6"> {/* Add padding */}
      <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent">
        Generate Description
      </h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8"> {/* Adjust gap */}
        {/* Left Column: Image Selection & Prompts */}
        <div className="space-y-4">
          <div className="bg-gray-800 p-4 md:p-6 rounded-lg shadow-lg border border-gray-700 relative"> {/* Adjust padding */}
            <GlowingEffect spread={30} glow={true} disabled={false} proximity={50} inactiveZone={0.01} borderWidth={2} />

            {/* Selection Mode */}
            <div className="mb-4 flex flex-col sm:flex-row gap-2 sm:gap-4"> {/* Stack on mobile */}
                <label className="flex items-center space-x-2 cursor-pointer"> {/* Removed outer label */}
                  <input
                    type="radio"
                    name="selectionMode"
                    checked={selectionMode === 'single'}
                    onChange={() => handleSelectionModeChange('single')}
                    className="form-radio text-purple-500 focus:ring-purple-500"
                  />
                  <span className="text-sm text-gray-200">Single Select</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="selectionMode"
                    checked={selectionMode === 'multi-single-product'}
                    onChange={() => handleSelectionModeChange('multi-single-product')}
                    className="form-radio text-purple-500 focus:ring-purple-500"
                  />
                  <span className="text-sm text-gray-200">Multi Images (Max 5)</span> {/* Updated label */}
                </label>
            </div> {/* Close the selection mode div */}

            {/* Buttons moved below mode selection */}
            <div className="flex flex-wrap gap-2 mb-4 justify-between items-center">
               <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1 border-0 text-xs sm:text-sm bg-gradient-to-r from-purple-500 to-pink-600 text-white hover:from-purple-600 hover:to-pink-700"
                onClick={() => {
                  // Only toggle Advanced section on desktop screens
                  if (isDesktop) {
                    setIsAdvancedSectionExpanded(prev => !prev);
                  }

                  // Handle prompt creation logic (works on all screen sizes)
                  if (!canCreateMorePrompts) {
                    // Show simplified upgrade modal for limit reached
                    setUpgradeFeature('Custom Prompts');
                    setIsUpgradeModalOpen(true);
                  } else {
                    // Open the "Create New Prompt" dialog
                    handleOpenPromptDialog();
                  }
                }}
                title={!canCreateMorePrompts ? 'Custom prompts limit reached. Upgrade to create more.' :
                  (isDesktop ?
                    (isAdvancedSectionExpanded ? "Hide custom prompts section and create new prompt" : "Show custom prompts section and create new prompt") :
                    "Create a new custom prompt"
                  )
                }
              >
                <Plus className="w-3 h-3 sm:w-4 sm:h-4" /> Custom Prompt
              </Button>
               <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-2 py-1 rounded-full text-xs sm:text-sm font-medium">
                {selectedImages.length} selected
              </div>
            </div>

            {/* Image Grid */}
            {fetchingImages ? (
              <div className="flex items-center justify-center h-48 md:h-64"> {/* Adjust height */}
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-2 md:gap-4 max-h-48 md:max-h-64 overflow-y-auto p-2 md:p-4 bg-gray-900 rounded-lg border border-gray-700 relative"> {/* Adjust columns, gap, padding, height */}
                  <GlowingEffect spread={20} glow={true} disabled={false} proximity={40} inactiveZone={0.01} borderWidth={1} />
                  {images.length > 0 ? (
                    images.map((image) => (
                      <div
                        key={image.id}
                        onClick={() => handleImageSelect(image)}
                        className={`aspect-square relative group cursor-pointer rounded-lg overflow-hidden transition-all ${selectedImages.includes(image.url) ? 'ring-2 ring-offset-2 ring-offset-gray-800 ring-[transparent] bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 p-[1px] md:p-[2px]' : 'border border-gray-700 hover:border-purple-400'}`} // Adjust padding and hover
                      >
                        <img
                          src={image.url}
                          alt={image.name}
                          className="w-full h-full object-cover rounded-lg group-hover:scale-105 transition-transform" // Add hover effect
                        />
                        {selectedImages.includes(image.url) && (
                          <div className="absolute bottom-1 right-1 md:bottom-2 md:right-2 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full p-0.5 md:p-1"> {/* Adjust position/padding */}
                            <Check className="w-3 h-3 md:w-4 md:h-4 text-white" /> {/* Adjust size */}
                          </div>                        )}
                      </div>
                    ))
                  ) : (
                    <div className="col-span-full flex flex-col items-center justify-center h-48 md:h-64 border-2 border-dashed border-gray-600 rounded-lg p-4 text-center"> {/* Adjust height/padding */}
                      <ImageIcon className="w-10 h-10 md:w-12 md:h-12 text-gray-300" />
                      <span className="mt-2 text-sm md:text-base text-gray-400">No images found in Uploads</span> {/* Updated text */}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Tone and Language Dropdowns */}
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-white text-sm font-medium whitespace-nowrap">Select Tone</label>
              <select
                value={tone}
                onChange={(e) => setTone(e.target.value)}
                className="flex-1 p-2 border border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-gray-800 text-white"
                style={{ color: "white" }}
              >
                {tones.map((t) => (
                  <option key={t} value={t} style={{ backgroundColor: "#1f2937", color: "white" }}>{t}</option>
                ))}
              </select>
            </div>
            <div className="flex items-center gap-4">
              <label className="text-white text-sm font-medium whitespace-nowrap">Language</label>
              <div className="relative flex-1" ref={languageDropdownRef}>
                <div
                  onClick={() => setIsLanguageDropdownOpen(!isLanguageDropdownOpen)}
                  className="flex items-center justify-between p-2 border border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-gradient-to-r from-gray-800 to-gray-700 text-white cursor-pointer relative"
                >
                  <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
                  <span>{language}</span>
                  <svg className={`w-5 h-5 transition-transform ${isLanguageDropdownOpen ? 'transform rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
                {isLanguageDropdownOpen && (
                  <div className="absolute z-50 w-full mt-1 bg-gray-800 border border-gray-600 rounded-md shadow-lg">
                    <div className="p-2 border-b border-gray-600">
                      <input
                        type="text"
                        value={searchLanguage}
                        onChange={(e) => setSearchLanguage(e.target.value)}
                        placeholder="Search language..."
                        className="w-full p-2 text-white bg-gray-700 border border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        onClick={(e) => e.stopPropagation()}
                      />
                    </div>
                    <div className="max-h-60 overflow-y-auto custom-scrollbar">
                      {filteredLanguages.map((lang) => (
                        <div
                          key={lang}
                          onClick={() => handleLanguageSelect(lang)}
                          className={`p-2 cursor-pointer hover:bg-gray-700 ${language === lang ? 'bg-purple-600' : ''} text-white`}
                        >
                          {lang}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="space-y-4">
            {/* Default Prompts Section - Desktop version (always visible on desktop) */}
            <div className="mb-4 hidden md:block">
              <div className="flex justify-between items-center mb-2">
                <label className="text-sm font-medium text-white flex items-center gap-1">
                  <BookOpen className="w-4 h-4" /> Default Presets
                </label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetToDefaultPrompt}
                  className="flex items-center gap-1 bg-gradient-to-r from-purple-900 to-indigo-800 text-white hover:from-purple-800 hover:to-indigo-700 border-0"
                >
                  Reset
                </Button>
              </div>
              <div className="border border-gray-700 rounded-md bg-gradient-to-r from-gray-900 to-gray-800 overflow-hidden shadow-lg relative">
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
                {DEFAULT_PROMPTS_CATEGORIES.map((category) => (
                  <div key={category.id} className="border-b border-gray-700 last:border-b-0">
                    <div
                      onClick={() => toggleCategory(category.id)}
                      className={`flex justify-between items-center p-3 cursor-pointer transition-colors duration-200 ${
                        expandedCategories.includes(category.id)
                          ? 'bg-gradient-to-r from-purple-900 to-indigo-800'
                          : 'hover:bg-gray-800'
                      }`}
                    >
                      <div className="font-medium text-white flex items-center gap-2">
                        {category.id === 'ecommerce' && <span>🛍️</span>}
                        {category.id === 'social-media' && <span>📱</span>}
                        {category.id === 'ad-copywriting' && <span>📢</span>}
                        {category.id === 'voice-over' && <span>🎤</span>}
                        {category.id === 'blog-article' && <span>📝</span>}
                        {category.id === 'email-copywriting' && <span>📧</span>}
                        {category.name}
                        {/* Show indicator if any prompt in this category is selected */}
                        {!expandedCategories.includes(category.id) &&
                         category.prompts.some(p => p.id === selectedPromptId) && (
                          <span className="ml-2 bg-purple-600 rounded-full w-2 h-2 animate-pulse"></span>
                        )}
                      </div>
                      <div className={`${expandedCategories.includes(category.id) ? 'text-white' : 'text-gray-400'}`}>
                        {expandedCategories.includes(category.id) ?
                          <ChevronDown className="w-5 h-5" /> :
                          <ChevronRight className="w-5 h-5" />
                        }
                      </div>
                    </div>
                    <AnimatePresence>
                      {expandedCategories.includes(category.id) && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="overflow-hidden"
                        >
                          <div className="p-2 space-y-1 bg-gray-900 border-t border-gray-700">
                            {category.prompts.map((prompt) => (
                              <div
                                key={prompt.id}
                                onClick={() => handleSelectDefaultPrompt(prompt.id)}
                                className={`p-2 rounded-md cursor-pointer transition-all duration-200 flex justify-between items-center ${
                                  selectedPromptId === prompt.id
                                    ? 'bg-gradient-to-r from-purple-700 to-indigo-600'
                                    : 'hover:bg-gradient-to-r hover:from-purple-800/40 hover:to-indigo-700/40'
                                }`}
                              >
                                <div className="text-sm text-white">{prompt.name}</div>
                                {selectedPromptId === prompt.id && (
                                  <Check className="w-4 h-4 text-white ml-2 flex-shrink-0" />
                                )}
                              </div>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ))}
              </div>
              <div className="mt-1 text-xs text-purple-300 italic">
                These are pre-defined prompts that cannot be modified or deleted
              </div>
            </div>

            {/* Mobile/Tablet Accordion for Default Prompts */}
            <div className="mb-4 md:hidden">
              <div
                className="advanced-accordion border border-gray-700 rounded-lg bg-gradient-to-r from-purple-900/40 to-indigo-800/40 overflow-hidden shadow-lg relative"
              >
                <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />

                {/* Accordion Header - Advanced Button */}
                <button
                  onClick={() => setIsAdvancedSectionExpanded(prev => !prev)}
                  className={`advanced-accordion-header w-full p-3 flex justify-between items-center cursor-pointer transition-all duration-300 ${isAdvancedSectionExpanded ? 'bg-gradient-to-r from-purple-900/30 to-indigo-800/30' : ''}`}
                >
                  <div className="flex items-center gap-2">
                    <div className={`advanced-accordion-icon bg-gradient-to-r ${isAdvancedSectionExpanded ? 'from-purple-600 to-pink-600' : 'from-purple-500 to-pink-500'} rounded-full p-1.5 shadow-lg`}>
                      <BookOpen className="w-4 h-4 text-white" />
                    </div>
                    <span className="font-medium text-white">Default Presets</span>
                    {selectedPromptId && (
                      <span className="ml-1 bg-purple-500 rounded-full w-2 h-2 animate-pulse"></span>
                    )}
                  </div>
                  <div className={`${isAdvancedSectionExpanded ? 'bg-purple-800/50' : 'bg-gray-800'} rounded-full p-1 transition-all duration-300`} style={{ transform: isAdvancedSectionExpanded ? 'rotate(180deg)' : 'rotate(0deg)' }}>
                    <ChevronDown className={`w-4 h-4 ${isAdvancedSectionExpanded ? 'text-white' : 'text-purple-300'}`} />
                  </div>
                </button>

                {/* Accordion Content */}
                <AnimatePresence initial={false}>
                  {isAdvancedSectionExpanded && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="advanced-accordion-content overflow-hidden border-t border-gray-700"
                    >
                      <div className="p-3 bg-gray-800/50">
                        <div className="flex justify-between items-center mb-2">
                          <label className="text-sm font-medium text-white flex items-center gap-1">
                            <BookOpen className="w-4 h-4" /> Chose Preset
                          </label>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={resetToDefaultPrompt}
                            className="flex items-center gap-1 bg-gradient-to-r from-purple-900 to-indigo-800 text-white hover:from-purple-800 hover:to-indigo-700 border-0"
                          >
                            Reset
                          </Button>
                        </div>
                        <div className="border border-gray-700 rounded-md bg-gradient-to-r from-gray-900 to-gray-800 overflow-hidden shadow-lg relative">
                          <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
                          {DEFAULT_PROMPTS_CATEGORIES.map((category) => (
                            <div key={category.id} className="border-b border-gray-700 last:border-b-0">
                              <div
                                onClick={() => toggleCategory(category.id)}
                                className={`flex justify-between items-center p-3 cursor-pointer transition-colors duration-200 ${
                                  expandedCategories.includes(category.id)
                                    ? 'bg-gradient-to-r from-purple-900 to-indigo-800'
                                    : 'hover:bg-gray-800'
                                }`}
                              >
                                <div className="font-medium text-white flex items-center gap-2">
                                  {category.id === 'ecommerce' && <span>🛍️</span>}
                                  {category.id === 'social-media' && <span>📱</span>}
                                  {category.id === 'ad-copywriting' && <span>📢</span>}
                                  {category.id === 'voice-over' && <span>🎤</span>}
                                  {category.id === 'blog-article' && <span>📝</span>}
                                  {category.id === 'email-copywriting' && <span>📧</span>}
                                  {category.name}
                                  {/* Show indicator if any prompt in this category is selected */}
                                  {!expandedCategories.includes(category.id) &&
                                  category.prompts.some(p => p.id === selectedPromptId) && (
                                    <span className="ml-2 bg-purple-600 rounded-full w-2 h-2 animate-pulse"></span>
                                  )}
                                </div>
                                <div className={`${expandedCategories.includes(category.id) ? 'text-white' : 'text-gray-400'}`}>
                                  {expandedCategories.includes(category.id) ?
                                    <ChevronDown className="w-5 h-5" /> :
                                    <ChevronRight className="w-5 h-5" />
                                  }
                                </div>
                              </div>
                              <AnimatePresence>
                                {expandedCategories.includes(category.id) && (
                                  <motion.div
                                    initial={{ height: 0, opacity: 0 }}
                                    animate={{ height: "auto", opacity: 1 }}
                                    exit={{ height: 0, opacity: 0 }}
                                    transition={{ duration: 0.3, ease: "easeInOut" }}
                                    className="overflow-hidden"
                                  >
                                    <div className="p-2 space-y-1 bg-gray-900 border-t border-gray-700">
                                      {category.prompts.map((prompt) => (
                                        <div
                                          key={prompt.id}
                                          onClick={() => handleSelectDefaultPrompt(prompt.id)}
                                          className={`p-2 rounded-md cursor-pointer transition-all duration-200 flex justify-between items-center ${
                                            selectedPromptId === prompt.id
                                              ? 'bg-gradient-to-r from-purple-700 to-indigo-600'
                                              : 'hover:bg-gradient-to-r hover:from-purple-800/40 hover:to-indigo-700/40'
                                          }`}
                                        >
                                          <div className="text-sm text-white">{prompt.name}</div>
                                          {selectedPromptId === prompt.id && (
                                            <Check className="w-4 h-4 text-white ml-2 flex-shrink-0" />
                                          )}
                                        </div>
                                      ))}
                                    </div>
                                  </motion.div>
                                )}
                              </AnimatePresence>
                            </div>
                          ))}
                        </div>
                        <div className="mt-1 text-xs text-purple-300 italic">
                          These are pre-defined prompts that cannot be modified or deleted
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>

            {/* User Saved Prompts Section - Only show if user has saved prompts */}
            {savedPrompts.length > 0 && (
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <label className="text-sm font-medium text-white flex items-center gap-1">
                    <BookmarkIcon className="w-4 h-4" /> Custom Prompts
                    <span className="ml-1 text-xs bg-gradient-to-r from-purple-500 to-pink-500 px-2 py-0.5 rounded-full text-white">
                      {savedPrompts.filter(p => !p.isOwnerPrompt).length} Own + {savedPrompts.filter(p => p.isOwnerPrompt).length} Shared
                    </span>
                    {!canCreateMorePrompts && (
                      <span className="ml-2 text-xs bg-red-500 px-2 py-0.5 rounded-full text-white animate-pulse">
                        Limit Reached
                      </span>
                    )}
                  </label>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 border-0 bg-gradient-to-r from-indigo-500 to-purple-600 text-white hover:from-indigo-600 hover:to-purple-700"
                    onClick={() => {
                      if (!canCreateMorePrompts) {
                        // Show simplified upgrade modal for limit reached
                        setUpgradeFeature('Custom Prompts');
                        setIsUpgradeModalOpen(true);
                      } else {
                        handleOpenPromptDialog();
                      }
                    }}
                    title={!canCreateMorePrompts ? 'Custom prompts limit reached. Upgrade to create more.' : 'Create a new custom prompt'}
                  >
                    <Plus className="w-4 h-4" /> New Prompt
                    {!canCreateMorePrompts && (
                      <span className="ml-1 text-xs bg-red-500 px-1.5 py-0.5 rounded-full text-white">
                        Limit
                      </span>
                    )}
                  </Button>
                </div>
                <div className="flex items-center gap-2">
                  <select
                    ref={promptSelectRef}
                    onChange={(e) => {
                      const selectedPrompt = savedPrompts.find(p => p.id === e.target.value);
                      if (selectedPrompt) {
                        handleSelectPrompt(selectedPrompt);
                        // Reset default prompt selection
                        if (defaultPromptSelectRef.current) {
                          defaultPromptSelectRef.current.value = "";
                        }
                      }
                    }}
                    className="flex-1 p-2 border border-gray-700 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-gradient-to-r from-purple-900 to-indigo-800 text-white hover:from-purple-800 hover:to-indigo-700 transition-all duration-300 shadow-md relative"
                    defaultValue=""
                  >
                    <GlowingEffect spread={15} glow={true} disabled={false} proximity={30} inactiveZone={0.01} borderWidth={1} />
                    <option value="" disabled>Select a custom prompt</option>

                    {/* User's own prompts group */}
                    {savedPrompts.filter(p => !p.isOwnerPrompt).length > 0 && (
                      <optgroup label="My Prompts">
                        {savedPrompts
                          .filter(p => !p.isOwnerPrompt)
                          .map((prompt) => (
                            <option key={prompt.id} value={prompt.id} className="bg-[#1f2937] text-white" style={{ backgroundColor: "#1f2937", color: "white" }}>
                              {prompt.name}
                            </option>
                          ))
                        }
                      </optgroup>
                    )}

                    {/* Team owner's shared prompts group */}
                    {savedPrompts.filter(p => p.isOwnerPrompt).length > 0 && (
                      <optgroup label="Team Owner's Shared Prompts">
                        {savedPrompts
                          .filter(p => p.isOwnerPrompt)
                          .map((prompt) => (
                            <option key={prompt.id} value={prompt.id} className="bg-[#1f2937] text-white" style={{ backgroundColor: "#1f2937", color: "white" }}>
                              {prompt.name}
                            </option>
                          ))
                        }
                      </optgroup>
                    )}
                  </select>
                  <div className="flex gap-2">
                    <button
                      onClick={resetToDefaultPrompt}
                      className="p-2 rounded bg-gradient-to-r from-purple-900 to-indigo-800 hover:from-purple-800 hover:to-indigo-700 text-white transition-all duration-300 shadow-md"
                      title="Reset to default prompt"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4">
                        <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
                        <path d="M3 3v5h5"></path>
                      </svg>
                    </button>
                    <button
                      onClick={() => {
                        if (promptSelectRef.current && promptSelectRef.current.value) {
                          const selectedPromptId = promptSelectRef.current.value;
                          const selectedPrompt = savedPrompts.find(p => p.id === selectedPromptId);
                          if (selectedPrompt) handleOpenPromptDialog(selectedPrompt);
                        } else {
                          toast.error('Please select a prompt to edit');
                        }
                      }}
                      className="p-2 rounded-md bg-gradient-to-r from-blue-500 to-indigo-600 text-white"
                      title="Edit selected prompt"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => {
                        if (promptSelectRef.current && promptSelectRef.current.value) {
                          const selectedPromptId = promptSelectRef.current.value;
                          const selectedPrompt = savedPrompts.find(p => p.id === selectedPromptId);

                          // Prevent deleting owner prompts
                          if (selectedPrompt?.isOwnerPrompt) {
                            toast.error('You cannot delete prompts shared by your team owner');
                            return;
                          }

                          if (selectedPromptId) handleDeleteClick(selectedPromptId);
                        } else {
                          toast.error('Please select a prompt to delete');
                        }
                      }}
                      className="p-2 rounded-md bg-gradient-to-r from-red-500 to-pink-600 text-white"
                      title="Delete selected prompt"
                    >
                      <Trash className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                {!canCreateMorePrompts && (
                  <div className="mt-2 p-3 bg-red-900/30 border border-red-500/50 rounded-lg">
                    <div className="flex items-start gap-2">
                      <div className="text-red-400 mt-0.5">⚠️</div>
                      <div className="text-sm text-red-200">
                        <div className="font-semibold mb-1">Custom Prompts Limit Reached</div>
                        <div className="text-xs text-red-300">
                          You have reached your maximum custom prompts limit. To create new prompts:
                          <br />• Delete some existing prompts, or
                          <br />• Upgrade to Pro (20 prompts) or Enterprise (unlimited)
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {user && user.email === '<EMAIL>' && (
              <textarea
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                placeholder="Add custom prompt (optional)"
                className="w-full p-3 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-gray-800 text-white relative"
                rows={5}
              >
                <GlowingEffect spread={20} glow={true} disabled={false} proximity={40} inactiveZone={0.01} borderWidth={1} />
              </textarea>
            )}

            {/* Generate Button */}
            <Button
              onClick={generateCaption}
              disabled={loading || generatingDescription || selectedImages.length === 0}
              className="w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white border-0 relative overflow-hidden"
            >
              <GlowingEffect spread={20} glow={true} disabled={loading || generatingDescription || selectedImages.length === 0} proximity={40} inactiveZone={0.01} borderWidth={1} />
              {loading || generatingDescription ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"></div>
                  Generating...
                </>
              ) : (
                <>Generate Product Description</>
              )}
            </Button>

            {/* Removed duplicate preview buttons */}
          </div>
        </div>

        {/* Right Column: Description & Preview */}
        <div className="bg-gray-800 p-4 md:p-6 rounded-lg shadow-lg border border-gray-700 space-y-4 relative"> {/* Adjust padding */}
          <GlowingEffect spread={30} glow={true} disabled={false} proximity={50} inactiveZone={0.01} borderWidth={2} />
          {/* Header: Title and Buttons - Stack vertically by default, row layout on sm+ */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
            <h2 className="text-lg md:text-xl font-semibold text-white bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent flex-shrink-0">Generated Description</h2>
            {/* Button Group - Ensure wrapping within the group */}
            <div className="flex flex-wrap gap-1 md:gap-2 justify-start w-full sm:w-auto sm:justify-end">
              <button
                onClick={copyToClipboard}
                className="p-1.5 md:p-2 rounded-md bg-gradient-to-r from-indigo-400 to-blue-500 text-white hover:opacity-90 transition-opacity"
                title="Copy to Clipboard"
              >
                <Copy className="w-4 h-4 md:w-5 md:h-5" /> {/* Adjust size */}
              </button>
              <button
                onClick={() => setPreviewTheme(previewTheme === 'dark' ? 'light' : 'dark')}
                className={`p-1.5 md:p-2 rounded-md ${previewTheme === 'dark' ? 'bg-gradient-to-r from-yellow-400 to-orange-500' : 'bg-gradient-to-r from-indigo-500 to-purple-600'} text-white hover:opacity-90 transition-opacity`} // Adjust padding
                title={`Switch to ${previewTheme === 'dark' ? 'Light' : 'Dark'} Mode`}
              >
                {previewTheme === 'dark' ? <Sun className="w-4 h-4 md:w-5 md:h-5" /> : <Moon className="w-4 h-4 md:w-5 md:h-5" />} {/* Adjust size */}
              </button>
              {/* Preview Mode Buttons - Hide device previews on mobile */}
              <button
                onClick={() => setPreviewMode('none')}
                className={`p-1.5 md:p-2 rounded-md ${previewMode === 'none' ? 'bg-gradient-to-r from-purple-500 to-pink-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'} transition-colors`}
                title="No View"
              >
                <Eye className="w-4 h-4 md:w-5 md:h-5" />
              </button>
              {/* Hide Laptop, Tablet, Smartphone buttons on screens smaller than 'sm' */}
              <button
                onClick={() => setPreviewMode('laptop')}
                className={`hidden sm:inline-flex p-1.5 md:p-2 rounded-md ${previewMode === 'laptop' ? 'bg-gradient-to-r from-purple-500 to-pink-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'} transition-colors`}
                title="Laptop View"
              >
                <Monitor className="w-4 h-4 md:w-5 md:h-5" />
              </button>
              <button
                onClick={() => setPreviewMode('tablet')}
                className={`hidden sm:inline-flex p-1.5 md:p-2 rounded-md ${previewMode === 'tablet' ? 'bg-gradient-to-r from-purple-500 to-pink-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'} transition-colors`}
                title="Tablet View"
              >
                <Tablet className="w-4 h-4 md:w-5 md:h-5" />
              </button>
              <button
                onClick={() => setPreviewMode('smartphone')}
                className={`hidden sm:inline-flex p-1.5 md:p-2 rounded-md ${previewMode === 'smartphone' ? 'bg-gradient-to-r from-purple-500 to-pink-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'} transition-colors`}
                title="Smartphone View"
              >
                <Smartphone className="w-4 h-4 md:w-5 md:h-5" />
              </button>
            </div>
          </div>
          {caption ? (
            <>
              {/* Removing isEditMode conditional rendering and ReactQuill editor */}
              <div className={`preview-container ${previewMode}`}>
                <div className={`device-frame ${previewMode} ${previewTheme} border rounded-lg overflow-hidden`}>
                  <div className={`device-content ${previewMode} ${previewTheme} p-4`}>
                    <div
                      className={`caption-content`}
                      dangerouslySetInnerHTML={{ __html: caption }}
                    />
                  </div>
                </div>
              </div>
              <Button
                onClick={handleSave}
                disabled={isSaving || isCaptionSaved}
                className="w-full mt-4 bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white border-0 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    {isCaptionSaved ? 'Description Saved' : 'Save Description'}
                  </>
                )}
              </Button>
            </>
          ) : (
            <div className={`preview-container ${previewMode}`}>
              <div className={`device-frame ${previewMode} ${previewTheme} border rounded-lg overflow-hidden`}>
                {/* Add justify-start to align placeholder at the top */}
                <div className={`device-content ${previewMode} ${previewTheme} p-4 flex flex-col justify-start`}>
                  <p className={`italic placeholder-text text-gray-400 dark:text-gray-500`}> {/* Adjusted placeholder text color */}
                    Description will appear here...
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Custom Prompt Dialog */}
      <Dialog open={isPromptDialogOpen} onOpenChange={setIsPromptDialogOpen}>
        <DialogContent className="max-w-2xl bg-gray-800 border border-gray-700 shadow-xl transform transition-all duration-200 ease-in-out">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent">
              {editingPromptId ? 'Edit Prompt' : 'Create New Prompt'}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-6 p-2">
            <div className="space-y-2">
              <label htmlFor="promptName" className="block text-sm font-medium text-gray-200">
                Prompt Name
              </label>
              <Input
                id="promptName"
                value={promptName}
                onChange={(e) => setPromptName(e.target.value)}
                className="w-full bg-gray-700 border-gray-600 text-gray-100 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                placeholder="Enter a descriptive name for your prompt"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="promptText" className="block text-sm font-medium text-gray-200">
                Prompt Text
              </label>
              <div className="relative">
                <Textarea
                  id="promptText"
                  value={promptText}
                  onChange={(e) => setPromptText(e.target.value)}
                  className="w-full h-64 bg-gray-700 border-gray-600 text-gray-100 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 resize-none font-mono"
                  placeholder="Write your prompt text here..."
                />
                <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                  {promptText.length} characters
                </div>
              </div>
            </div>
          </div>
          <DialogFooter className="flex justify-end space-x-2 mt-6 border-t border-gray-700 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsPromptDialogOpen(false)}
              className="bg-gray-700 hover:bg-gray-600 text-gray-200 border-gray-600 transition-all duration-200"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSavePrompt}
              className="bg-gradient-to-r from-purple-500 to-pink-600 text-white hover:from-purple-600 hover:to-pink-700 transition-all duration-200"
              disabled={!promptName.trim() || !promptText.trim() || savingPrompt}
            >
              {savingPrompt ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                editingPromptId ? 'Update' : 'Save'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <DialogContent className="sm:max-w-md bg-gray-800 border-gray-700 text-gray-100">
          <DialogHeader>
            <DialogTitle className="text-gray-100">Confirm Deletion</DialogTitle>
            <DialogDescription className="text-gray-400">
              Are you sure you want to delete the custom prompt "{promptToDeleteName}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:justify-end">
            <Button
              variant="outline"
              onClick={() => setShowDeleteConfirm(false)}
              className="border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-gray-100"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeletePrompt}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Use standard style tag */}
      <style>{`
        /* Mobile Accordion Styles */
        @media (max-width: 768px) {
          .advanced-accordion {
            position: relative;
            overflow: hidden;
          }

          .advanced-accordion:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: linear-gradient(to right, rgba(139, 92, 246, 0.05), rgba(79, 70, 229, 0.05));
            pointer-events: none;
            z-index: 0;
          }

          .advanced-accordion-header {
            position: relative;
            z-index: 1;
          }

          .advanced-accordion-icon {
            filter: drop-shadow(0 0 3px rgba(139, 92, 246, 0.5));
            transition: all 0.3s ease;
          }

          .advanced-accordion-header:hover .advanced-accordion-icon {
            transform: scale(1.1);
            filter: drop-shadow(0 0 5px rgba(139, 92, 246, 0.7));
          }

          .advanced-accordion-content {
            position: relative;
            z-index: 1;
          }

          /* Pulse animation for the accordion icon */
          @keyframes gentle-pulse {
            0% { transform: scale(1); filter: drop-shadow(0 0 3px rgba(139, 92, 246, 0.5)); }
            50% { transform: scale(1.05); filter: drop-shadow(0 0 5px rgba(236, 72, 153, 0.6)); }
            100% { transform: scale(1); filter: drop-shadow(0 0 3px rgba(139, 92, 246, 0.5)); }
          }

          .advanced-accordion-icon {
            animation: gentle-pulse 3s infinite ease-in-out;
          }
        }

        .preview-container {
          width: 100%;
          display: flex;
          justify-content: center;
          margin: 1rem 0;
        }

        .device-frame {
          width: 100%;
          transition: all 0.3s ease;
          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
        }

        .device-frame.dark {
          background-color: #1f2937;
          border-color: #374151;
        }

        .device-frame.light {
          background-color: #ffffff;
          border-color: #e5e7eb;
          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .device-frame.laptop {
          width: 90%;
          max-width: 900px;
          height: 400px;
          border-radius: 8px;
        }

        .device-frame.tablet {
          width: 70%;
          max-width: 600px;
          height: 350px;
          border-radius: 12px;
        }

        .device-frame.smartphone {
          width: 40%;
          max-width: 320px;
          height: 500px;
          border-radius: 20px;
        }

        .device-content {
          height: 100%;
          overflow-y: auto;
          padding: 1rem;
        }

        .device-content.dark {
          background-color: #1f2937;
          color: #e5e7eb;
        }

        .device-content.light {
          background-color: #ffffff;
          color: #1f2937;
        }

        .caption-content {
          font-size: 1rem;
          line-height: 1.6;
        }

        .device-content.dark .caption-content {
          color: #e5e7eb;
        }

        .device-content.light .caption-content {
          color: #1f2937;
        }

        .device-content.dark .placeholder-text {
          color: #9ca3af;
        }

        .device-content.light .placeholder-text {
          color: #6b7280;
        }

        .device-content.smartphone .caption-content {
          font-size: 0.9rem;
        }

        .editor-container {
          min-height: 400px;
          border-radius: 8px;
          overflow: hidden;
        }

                /* Quill editor styles removed */

                /* Additional Quill editor styles removed */
        }

        /* Caption content styling */
        .caption-content {
          font-size: 1rem;
          line-height: 1.6;
          padding: 0.5rem;
        }

        .caption-content h1,
        .caption-content h2,
        .caption-content h3,
        .caption-content h4,
        .caption-content h5,
        .caption-content h6 {
          margin-top: 1.5rem;
          margin-bottom: 1rem;
          font-weight: 600;
        }

        .caption-content h1 {
          font-size: 1.8rem;
        }

        .caption-content h2 {
          font-size: 1.5rem;
        }

        .caption-content h3 {
          font-size: 1.3rem;
        }

        .caption-content p {
          margin-bottom: 1rem;
        }

        .caption-content ul,
        .caption-content ol {
          margin-bottom: 1rem;
          padding-left: 1.5rem;
        }

        .caption-content li {
          margin-bottom: 0.5rem;
        }

        .caption-content blockquote {
          border-left: 4px solid #6B7280;
          padding-left: 1rem;
          margin-left: 0;
          margin-right: 0;
          font-style: italic;
          color: inherit;
          opacity: 0.9;
        }

        .caption-content code {
          background-color: rgba(0, 0, 0, 0.1);
          padding: 0.2rem 0.4rem;
          border-radius: 3px;
          font-family: monospace;
        }

        .caption-content pre {
          background-color: rgba(0, 0, 0, 0.1);
          padding: 1rem;
          border-radius: 5px;
          overflow-x: auto;
          margin-bottom: 1rem;
        }

        .caption-content img {
          max-width: 100%;
          height: auto;
          margin: 1rem 0;
        }

        .caption-content hr {
          margin: 2rem 0;
          border: 0;
          height: 1px;
          background-image: linear-gradient(to right, rgba(107, 114, 128, 0), rgba(107, 114, 128, 0.5), rgba(107, 114, 128, 0));
        }

        .caption-content a {
          color: #8B5CF6;
          text-decoration: underline;
        }

        .device-content.dark .caption-content a {
          color: #A78BFA;
        }

        .device-content.light .caption-content a {
          color: #7C3AED;
        }

        .device-content.dark .caption-content blockquote {
          border-left-color: #6B7280;
        }

        .device-content.light .caption-content blockquote {
          border-left-color: #9CA3AF;
        }

        .device-content.dark .caption-content code,
        .device-content.dark .caption-content pre {
          background-color: rgba(255, 255, 255, 0.1);
        }

        .device-content.light .caption-content code,
        .device-content.light .caption-content pre {
          background-color: rgba(0, 0, 0, 0.05);
        }
      `}</style>

      {/* Upgrade Modal */}
      <UpgradeModal
        isOpen={isUpgradeModalOpen}
        onClose={() => setIsUpgradeModalOpen(false)}
        feature={upgradeFeature}
        currentLimit={currentLimit}
        onUpgrade={handleUpgrade}
        onUpgradeNow={() => {
          setIsUpgradeModalOpen(false);
          setIsUpgradePlanModalOpen(true);
        }}
      />

      {/* Full Upgrade Plan Modal */}
      <UpgradePlanModal
        isOpen={isUpgradePlanModalOpen}        onClose={() => setIsUpgradePlanModalOpen(false)}
      />
      </div>
    </>
  );

  // Handle upgrade plan selection
  function handleUpgrade(plan: 'payAsYouGo' | 'pro' | 'enterprise') {
    setIsUpgradeModalOpen(false);

    // Navigate to payment page with selected plan
    if (plan === 'payAsYouGo') {
      window.location.href = '/payment?plan=payAsYouGo';
    } else if (plan === 'pro') {
      window.location.href = '/payment?plan=pro';
    } else {
      window.location.href = '/payment?plan=enterprise';
    }
  }
}
