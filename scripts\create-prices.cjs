// Create prices for Paddle products
const https = require('https');
require('dotenv').config();

const PADDLE_API_KEY = process.env.VITE_PADDLE_API_KEY;

// Product IDs from previous script
const PRODUCT_IDS = {
  payAsYouGo: 'pro_01jxbvansyepyq95kskd02bx9z',
  pro: 'pro_01jxbvap4t1dg04b9wb9kg6kzh',
  enterprise: 'pro_01jxbvapfjr0gt6yjjdvare06j'
};

function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.paddle.com',
      port: 443,
      path: path,
      method: method,
      headers: {
        'Authorization': `Bearer ${PADDLE_API_KEY}`,
        'Content-Type': 'application/json',
      },
    };

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(response);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${JSON.stringify(response)}`));
          }
        } catch (error) {
          reject(new Error(`Failed to parse response: ${body}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function createPrices() {
  console.log('🚀 Creating Paddle prices...');

  try {
    const prices = {};    // Pay-As-You-Go Price ($2.00)
    console.log('🔄 Creating Pay-As-You-Go price...');
    const payAsYouGoPrice = await makeRequest('POST', '/prices', {
      description: 'Pay-As-You-Go - 15 generations',
      product_id: PRODUCT_IDS.payAsYouGo,
      unit_price: {
        amount: '200', // $2.00 in cents
        currency_code: 'USD'
      },
      type: 'standard'
    });
    prices.payAsYouGo = payAsYouGoPrice.data.id;
    console.log(`✅ Pay-As-You-Go Price ID: ${payAsYouGoPrice.data.id}`);    // Pro Monthly Price ($10.00)
    console.log('🔄 Creating Pro Monthly price...');
    const proMonthlyPrice = await makeRequest('POST', '/prices', {
      description: 'Pro Monthly - 150 generations per month',
      product_id: PRODUCT_IDS.pro,
      unit_price: {
        amount: '1000', // $10.00 in cents
        currency_code: 'USD'
      },
      type: 'standard',
      billing_cycle: {
        interval: 'month',
        frequency: 1
      }
    });
    prices.proMonthly = proMonthlyPrice.data.id;
    console.log(`✅ Pro Monthly Price ID: ${proMonthlyPrice.data.id}`);    // Pro Yearly Price ($96.00 - 20% discount)
    console.log('🔄 Creating Pro Yearly price...');
    const proYearlyPrice = await makeRequest('POST', '/prices', {
      description: 'Pro Yearly - 150 generations per month (20% off)',
      product_id: PRODUCT_IDS.pro,
      unit_price: {
        amount: '9600', // $96.00 in cents
        currency_code: 'USD'
      },
      type: 'standard',
      billing_cycle: {
        interval: 'year',
        frequency: 1
      }
    });
    prices.proYearly = proYearlyPrice.data.id;
    console.log(`✅ Pro Yearly Price ID: ${proYearlyPrice.data.id}`);    // Enterprise Monthly Price ($100.00)
    console.log('🔄 Creating Enterprise Monthly price...');
    const enterpriseMonthlyPrice = await makeRequest('POST', '/prices', {
      description: 'Enterprise Monthly - 200 generations per member per month',
      product_id: PRODUCT_IDS.enterprise,
      unit_price: {
        amount: '10000', // $100.00 in cents
        currency_code: 'USD'
      },
      type: 'standard',
      billing_cycle: {
        interval: 'month',
        frequency: 1
      }
    });
    prices.enterpriseMonthly = enterpriseMonthlyPrice.data.id;
    console.log(`✅ Enterprise Monthly Price ID: ${enterpriseMonthlyPrice.data.id}`);    // Enterprise Yearly Price ($960.00 - 20% discount)
    console.log('🔄 Creating Enterprise Yearly price...');
    const enterpriseYearlyPrice = await makeRequest('POST', '/prices', {
      description: 'Enterprise Yearly - 200 generations per member per month (20% off)',
      product_id: PRODUCT_IDS.enterprise,
      unit_price: {
        amount: '96000', // $960.00 in cents
        currency_code: 'USD'
      },
      type: 'standard',
      billing_cycle: {
        interval: 'year',
        frequency: 1
      }
    });
    prices.enterpriseYearly = enterpriseYearlyPrice.data.id;
    console.log(`✅ Enterprise Yearly Price ID: ${enterpriseYearlyPrice.data.id}`);

    console.log('\n📋 Price IDs Summary:');
    console.log(`Pay-As-You-Go: ${prices.payAsYouGo}`);
    console.log(`Pro Monthly: ${prices.proMonthly}`);
    console.log(`Pro Yearly: ${prices.proYearly}`);
    console.log(`Enterprise Monthly: ${prices.enterpriseMonthly}`);
    console.log(`Enterprise Yearly: ${prices.enterpriseYearly}`);

    console.log('\n🔧 Update your paddle.ts file with these IDs:');
    console.log(`
export const PADDLE_PRODUCT_IDS = {
  payAsYouGo: '${PRODUCT_IDS.payAsYouGo}',
  pro: '${PRODUCT_IDS.pro}',
  enterprise: '${PRODUCT_IDS.enterprise}'
};

export const PADDLE_PRICE_IDS = {
  payAsYouGo: '${prices.payAsYouGo}',
  proMonthly: '${prices.proMonthly}',
  proYearly: '${prices.proYearly}',
  enterpriseMonthly: '${prices.enterpriseMonthly}',
  enterpriseYearly: '${prices.enterpriseYearly}'
};`);

    return prices;

  } catch (error) {
    console.error('❌ Error creating prices:', error.message);
    throw error;
  }
}

// Run the script
createPrices()
  .then((prices) => {
    console.log('\n✅ All prices created successfully!');
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
