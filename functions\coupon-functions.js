const functions = require('firebase-functions');
const admin = require('firebase-admin');

// This function is triggered when a purchase is completed
// It updates the coupon usage and increments the coupon's currentUses count
exports.completeCouponUsage = functions.https.onCall(async (data, context) => {
  // Ensure the user is authenticated
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'The function must be called while authenticated.'
    );
  }

  try {
    const { couponCode, couponUsageId } = data;
    
    if (!couponCode) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'The function must be called with a couponCode.'
      );
    }

    // Get a reference to the Firestore database
    const db = admin.firestore();
    
    // Start a transaction to ensure data consistency
    return db.runTransaction(async (transaction) => {
      // Find the coupon by code
      const couponQuery = await db
        .collection('coupons')
        .where('code', '==', couponCode)
        .limit(1)
        .get();
      
      if (couponQuery.empty) {
        throw new functions.https.HttpsError(
          'not-found',
          `Coupon with code ${couponCode} not found.`
        );
      }
      
      const couponDoc = couponQuery.docs[0];
      const couponData = couponDoc.data();
      
      // Check if the coupon has reached its maximum uses
      if (couponData.currentUses >= couponData.maxUses) {
        throw new functions.https.HttpsError(
          'failed-precondition',
          'This coupon has reached its maximum number of uses.'
        );
      }
      
      // Increment the coupon's currentUses count
      transaction.update(couponDoc.ref, {
        currentUses: admin.firestore.FieldValue.increment(1),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      // If a specific coupon usage ID was provided, update it
      if (couponUsageId) {
        const usageRef = db.collection('couponUsage').doc(couponUsageId);
        transaction.update(usageRef, {
          completed: true,
          completedAt: admin.firestore.FieldValue.serverTimestamp()
        });
      } else {
        // Otherwise, find all pending usages for this user and coupon
        const usageQuery = await db
          .collection('couponUsage')
          .where('userId', '==', context.auth.uid)
          .where('couponCode', '==', couponCode)
          .where('completed', '==', false)
          .get();
        
        // Update all matching usage records
        usageQuery.forEach(doc => {
          transaction.update(doc.ref, {
            completed: true,
            completedAt: admin.firestore.FieldValue.serverTimestamp()
          });
        });
      }
      
      return { success: true };
    });
  } catch (error) {
    console.error('Error completing coupon usage:', error);
    throw new functions.https.HttpsError(
      'internal',
      'An error occurred while completing the coupon usage.',
      error
    );
  }
});

// This function is triggered when a new coupon is created
// It validates the coupon data and ensures the code is unique
exports.validateCoupon = functions.firestore
  .document('coupons/{couponId}')
  .onCreate(async (snapshot, context) => {
    const couponData = snapshot.data();
    const db = admin.firestore();
    
    try {
      // Check if the coupon code already exists (case insensitive)
      const existingCouponsQuery = await db
        .collection('coupons')
        .where('code', '==', couponData.code)
        .get();
      
      // If there's more than one document with this code (including the one we just created)
      if (existingCouponsQuery.size > 1) {
        // Delete the newly created coupon
        await snapshot.ref.delete();
        console.error(`Duplicate coupon code: ${couponData.code}`);
        return { success: false, error: 'Duplicate coupon code' };
      }
      
      return { success: true };
    } catch (error) {
      console.error('Error validating coupon:', error);
      return { success: false, error: error.message };
    }
  });

// This function is triggered when a coupon is updated
// It ensures the coupon's currentUses doesn't exceed maxUses
exports.validateCouponUpdate = functions.firestore
  .document('coupons/{couponId}')
  .onUpdate(async (change, context) => {
    const newData = change.after.data();
    const previousData = change.before.data();
    
    // If currentUses is being updated and would exceed maxUses
    if (newData.currentUses > newData.maxUses && 
        newData.currentUses !== previousData.currentUses) {
      // Reset currentUses to maxUses
      await change.after.ref.update({
        currentUses: newData.maxUses
      });
      
      console.warn(`Coupon ${newData.code} currentUses exceeded maxUses. Reset to maxUses.`);
    }
    
    return { success: true };
  });
