# AppSumo Licensing API (v2) - Licensing Overview

**Source URLs:**
- https://docs.licensing.appsumo.com/licensing/licensing__overview.html
- https://docs.licensing.appsumo.com/licensing/licensing__getting_started.html
- https://docs.licensing.appsumo.com/licensing/licensing__connect.html
- https://docs.licensing.appsumo.com/licensing/licensing__next_steps.html

---

## Overview

OAuth enables AppSumo Partners to utilize our new licensing service.

## How it works

The AppSumo licensing service relies on Open Authorization (OAuth) to give the new customer access to your application. A customer can access your product via their products page on AppSumo. After allowing access to your product, they are redirected to your application. Using a series of tokens, you can validate the license the user has purchased and allow them access.

## OAuth

OAuth (**O**pen **Auth**orization) is an open standard for access delegation, commonly used as a way for Internet users to grant websites or applications access to their information on other websites but without giving them the passwords. This mechanism is used by companies such as Amazon, Google, Facebook, and others to permit the users to share information about their accounts with third-party applications or websites.

![Example OAuth flow](https://docs.licensing.appsumo.com/assets/img/oauth_flow.f7e69b68.png)

---

## Getting Started

### Prerequisites

1. **Approved AppSumo Application:** Your product must be approved on AppSumo.
2. **OAuth Redirect URL:** A URL capable of processing OAuth requests.
3. **OAuth Keys:** `client_id` and `client_secret` keys, which are available once your URLs are validated.

### OAuth Redirect URL

Redirect URLs are essential for the OAuth flow, guiding users back to your app with an `authorization code` after access is granted. Ensure this URL securely handles requests and is validated with a `200 OK` response from the AppSumo Partner Portal.

Because the OAuth Redirect URL will contain sensitive information, it is critical that the service doesn't redirect the user to arbitrary locations.

To add a Redirect URL, visit the [AppSumo Partner Portal](https://www.appsumo.com/partners/products/) and select your product. In order to save the URL, AppSumo must get a successful `200 OK` response.

![Oauth Redirect Config](https://docs.licensing.appsumo.com/assets/img/oauth_redirect_config.fe888aac.png)

**Important:** Ensure that the validated OAuth Redirect URL in AppSumo aligns with the URL on your backend where you want to direct new customers to complete required information and finalize the sign-up process, including providing their email and new account password.

#### Best practice example:

Upon completing a `purchase` on AppSumo, users will `activate` their license and be **redirected to the sign-up page you provided in your OAuth configuration**. On this page, they will input their email, password, and any required details to create an account, ensuring they receive the correct license `tier` and associated feature limits.

![Best Practice Redirect URL](https://docs.licensing.appsumo.com/assets/img/best-practice-redirect-url.d1325580.jpg)

### Testing

When saving your OAuth Redirect URL in the AppSumo Partner Portal:

1. AppSumo will send a `GET` request **without any included payload** to validate it.
2. The URL must respond with a `200 OK` status code to be deemed valid.
3. **Once validated**, **live payloads will be sent** to your URL as detailed in this guide. Live payloads are only sent when interacting with your AppSumo Product Detail Page.

You can test and adjust these URL settings in the [AppSumo Partner Portal](https://www.appsumo.com/partners/products/) when setting up your listing or anytime after submission, even if initial URL validation was not completed.

### OAuth keys

After validating both your Webhook and OAuth Redirect URLs, your OAuth keys (`client_id` and `client_secret`) will be generated.

You can locate these keys on your product page in the [AppSumo Partner Portal](https://www.appsumo.com/partners/products/). The keys are hidden by default; click the eye icon to view them (as shown below).

![Private keys](https://docs.licensing.appsumo.com/assets/img/private_keys.d80967b3.png)

---

## Connect to AppSumo

### Allowed Content Types for `POST` Requests

- `application/json`
- `application/x-www-form-urlencoded`

**All responses from AppSumo are returned in JSON format:**

```json
{
    "access_token": "82b35f3d810f4cf49dd7a52d4b22a594",
    "token_type": "bearer",
    "expires_in": 3600,
    "refresh_token": "0bac2d80d75d46658b0b31d3778039bb",
    "id_token": "eyJhbGciOiJSUzI1NiIsImtpZCI6"
}
```

### Connecting to AppSumo (OAuth)

To set up OAuth and obtain your user's license, follow these four simple steps:

#### 1. Save Your OAuth Redirect URL

Before you start, make sure your OAuth Redirect URL is correctly saved and validated in the [AppSumo Partner Portal](https://www.appsumo.com/partners/products/). For more details, refer to the [OAuth Getting Started](https://docs.licensing.appsumo.com/licensing/licensing__getting_started.html) section.

#### 2. Extract the Code from the OAuth Redirect URL

After a user accepts the OAuth consent, they will be redirected to your specified URL with a `code` parameter included in the query string. You will need this `code` to fetch the `access_token` in the next step.

**Important:**
- The `code` is single-use only and will expire after it's used or the OAuth attempt fails.
- A new `code` will be issued upon each new OAuth authorization attempt.

**Example Redirect URL**:
```text
https://your-url.com/?code=1d512d96ba99465ba9942bdf282233ea
```

**Python Example:**
```python
# Use your urls.py to use this view (This should be your a type of login page)
from django.views.generic import View
from django.http import HttpResponse
from rest_framework.status import HTTP_200_OK

class MyLoginPage(View):
  def get(self, request, *args, **kwargs):
    content = 'YOUR LOGIN PAGE CONTENT'
    code = request.GET.get('code')

    # Use "code" in the next step to get the "access_token"

    return HttpResponse(content)
```

#### 3. Fetch a Temporary Access Token

Use the `code` you extracted along with your `client_id`, `client_secret`, and Redirect URL to make a `POST` request to the AppSumo token endpoint. This request will provide you with a temporary `access_token` and `refresh_token`.

**Endpoint:** `POST https://appsumo.com/openid/token/`

**Required Data:**
- `client_id` and `client_secret` (See [OAuth Getting Started](https://docs.licensing.appsumo.com/licensing/licensing__getting_started.html))
- Your OAuth Redirect URL (must match exactly as saved and validated in the Partner Portal)
- The OAuth `code` from the previous step
- `grant_type`: set to `authorization_code` (constant value)

**Example request:**
```python
import request

url = 'https://appsumo.com/openid/token/'
headers = {'Content-type': 'application/json'}
data = {
    'client_id': '**********',
    'client_secret': '**********abcdef**********abcdef',
    'code': 'fedcba0987654321fedcba0987654321',
    'redirect_uri': 'https://your-url.com/',
    'grant_type': 'authorization_code'
}

response = requests.post(url, headers=headers, json=data)

# Extract the tokens from the response
```

**Example response:**
```json
{
    "access_token": "82b35f3d810f4cf49dd7a52d4b22a594",
    "token_type": "bearer",
    "expires_in": 3600,
    "refresh_token": "0bac2d80d75d46658b0b31d3778039bb",
    "id_token": "eyJhbGciOiJSUzI1NiIsImtpZCI6...",
    "error": ""
}
```

#### 4. Use the Access Token to Fetch the User's License

To fetch a user's license, use their `access_token` and send a `GET` request to `https://appsumo.com/openid/license_key/`. AppSumo will provide the user's license data, which must be linked to the user's new account on your site.

**Endpoint:** `GET https://appsumo.com/openid/license_key/?access_token=YOUR_ACCESS_TOKEN`

**Example request:**
```python
import requests

response = requests.get('https://appsumo.com/openid/license_key/?access_token=82b35f3d810f4cf49dd7a52d4b22a594')

# Use the license data from the response to log in the user
```

**Example response:**
```json
{
  "license_key": "d8bfa201-d8c0-4bc8-a27c-b1c12efa4a5a",
  "status": "active",
  "scopes": ["read_license"]
}
```

### Expiration and Refresh of Access Tokens

If you receive a `401 Unauthorized` error when using an `access_token`, it likely means the token has expired. To get a new `access_token`, use the `refresh_token` and send a `POST` request to `https://appsumo.com/openid/token/`. This will return a new temporary `access_token` and `refresh_token`.

**Endpoint**: `POST https://appsumo.com/openid/token/`

**Example request:**
```python
import request

url = 'https://appsumo.com/openid/token/'
headers = {'Content-type': 'application/json'}
data = {
  'client_id': '**********'
  'client_secret': '**********abcdef**********abcdef'
  'refresh_token': 'fedcba0987654321fedcba0987654321'
  'grant_type': 'refresh_token'
}

response = requests.post(url, headers=headers, json=data)

# Process the response
```

**Example response:**
```json
{
    "access_token": "82b35f3d810f4cf49dd7a52d4b22a594",
    "token_type": "bearer",
    "expires_in": 3600,
    "refresh_token": "0bac2d80d75d46658b0b31d3778039bb",
    "id_token": "eyJhbGciOiJSUzI1NiIsImtpZCI6"
}
```

---

## Next Steps

### What to Do After You Have a User's License Key

Once you have a user's license key, you can verify its `status` and allow the user to access your product. Refer to the "License Key Verification" section below for details.

**Important:** The license key will serve as the primary identifier for both you and AppSumo.

### License Key Verification (Required)

#### New users who don't already have an account with your product

You have flexibility in handling new users, but we suggest displaying a **"Create an Account"** page if the **license key is not found in your database**. This approach lets you collect the information needed to set up their account including email and password.

After the user completes and submits your account creation form, link the license key to their new account and provide access to your product with the appropriate license `tier`.

#### Existing users with an account already set up

For existing users, check the license key when they visit your page. If the license is valid, log them in and provide access to your product.

### Checking a License Status

After fetching a license (see [Licensing: Fetching a license](https://docs.licensing.appsumo.com/licensing/licensing__connect.html#fetching-a-license)), you will receive a `JSON` response indicating the current `status` of the license, which can be one of the following:

- `active`: The license is valid and previously activated. The user's account should already be set up and linked to this license, granting them access.

- `deactivated`: The license is no longer valid, so access to your product should be restricted or disabled.

- `inactive`: The license is valid but not yet activated. You need to create an account for the user and activate the license to provide access.

**Example response JSON:**
```json
{
  "license_key": "d8bfa201-d8c0-4bc8-a27c-b1c12efa4a5a",
  "status": "active",
  "scopes": ["read_license"]
}
```
