rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if the user is an admin
    function isAdmin() {
      return request.auth != null && request.auth.token.email == "<EMAIL>";
    }

    // Helper function to check if the user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Helper function to check if the user has an Enterprise package
    function isEnterpriseUser() {
      return isAuthenticated();
      // We'll check the package in the application code instead of here
      // This simplifies the rules and avoids potential issues with document paths
    }

    // Helper function to check if the user is a team member
    function isTeamMember() {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isTeamMember == true;
    }

    // Helper function to check if the user is a team owner
    function isTeamOwner(memberId) {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/teamMembers/$(memberId)) &&
             get(/databases/$(database)/documents/teamMembers/$(memberId)).data.ownerId == request.auth.uid;
    }

    // We'll use simpler rules that don't rely on complex queries

    // Users collection - allow admin to read all users, users to read/write their own data
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      // Allow Enterprise users to update any user record for team management
      allow update: if isAuthenticated() && (resource.data.uid == request.auth.uid || isAdmin() || isEnterpriseUser());
      allow delete: if isAdmin();
    }

    // Captions collection - allow admin to read all captions
    match /captions/{captionId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated();
    }

    match /uploaded_images/{imageId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow delete: if isAuthenticated() && (resource.data.userId == request.auth.uid || isAdmin());
    }

    match /customPrompts/{promptId} {
      // Allow authenticated users to read prompts
      // Team members can read their own prompts and their owner's prompts
      allow read: if isAuthenticated();

      // Allow creation of custom prompts - limits are enforced in application code
      allow create: if isAuthenticated();

      // Allow users to update their own prompts or admin to update any
      allow update: if isAuthenticated() && (resource.data.userId == request.auth.uid || isAdmin());

      // Allow users to delete their own prompts or admin to delete any
      allow delete: if isAuthenticated() && (resource.data.userId == request.auth.uid || isAdmin());
    }

    match /savedCaptions/{captionId} {
      // Allow public read access for masonry grid on landing pages
      // This enables the inspiring examples feature for non-authenticated users
      allow read: if true;
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (resource.data.userId == request.auth.uid || isAdmin());
      allow delete: if isAuthenticated() && (resource.data.userId == request.auth.uid || isAdmin());
    }

    match /packages/{packageId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
      allow create: if isAdmin();
      allow update: if isAdmin();
      allow delete: if isAdmin();
    }

    // Custom packages collection - allow admin to manage, authenticated users to read
    match /customPackages/{packageId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
      allow create: if isAdmin();
      allow update: if isAdmin();
      allow delete: if isAdmin();
    }

    // Settings collection - allow admin to write, all authenticated users to read
    // This includes userLimits, proUserLimits, and enterpriseAdminOwnerLimits documents
    match /settings/{settingId} {
      // Allow any authenticated user to read settings
      // This is needed for team member limit checks
      allow read: if isAuthenticated();
      allow write: if isAdmin();
      allow create: if isAdmin();
      allow update: if isAdmin();
      allow delete: if isAdmin();
    }

    // User usage collection - allow users to read/write their own usage data
    match /userUsage/{userId} {
      // Allow team members to read their own usage data
      // Also allow team owners to read their team members' usage data
      allow read: if isAuthenticated() && (
        userId == request.auth.uid ||
        isAdmin() ||
        (isTeamMember() && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.teamOwnerId == userId)
      );

      // Allow creation of usage records for any authenticated user
      // This is needed for team member registration
      allow create: if isAuthenticated();

      // Allow users to update their own usage data
      // Note: Usage limits are enforced in application code, not in Firestore rules
      // This is because limit enforcement requires complex queries for custom packages
      // that are not feasible in Firestore security rules
      allow update: if isAuthenticated() && (
        userId == request.auth.uid ||
        isAdmin()
      );

      // Only admin can delete usage records
      allow delete: if isAdmin();
    }

    // Note: We can't reliably check team member limits in Firestore rules
    // because we can't query collections or count documents
    // We'll enforce this in the application code instead

    // Team members collection - allow Enterprise users to manage their team
    match /teamMembers/{memberId} {
      // Allow team members to read their own data
      // Allow team owners to read/write their team members' data
      // Allow any authenticated user to read team members for limit checking
      allow read: if isAuthenticated();

      // Allow creation of team members
      // The team member limit is enforced in the application code
      // Allow any authenticated user to create team member records (for registration via invite)
      allow create: if isAuthenticated();

      allow update: if isEnterpriseUser() && (
        isAdmin() ||
        (resource != null && resource.data.ownerId == request.auth.uid)
      );

      allow delete: if isEnterpriseUser() && (
        isAdmin() ||
        (resource != null && resource.data.ownerId == request.auth.uid)
      );
    }

    // Team invites collection - allow Enterprise users to manage invites
    match /teamInvites/{inviteId} {
      // Allow anyone to read invites for validation purposes
      allow read: if true;
      // Only allow Enterprise users to create invites
      allow create: if isAuthenticated() && isEnterpriseUser();
      // Allow Enterprise users to update their own invites
      allow update: if isAuthenticated() && (
        isAdmin() ||
        (resource != null && resource.data.ownerId == request.auth.uid) ||
        // Allow any authenticated user to update the 'used', 'usedBy', and 'usedAt' fields
        (request.resource.data.diff(resource.data).affectedKeys().hasOnly(['used', 'usedBy', 'usedAt']))
      );
      // Allow Enterprise users to delete their own invites
      allow delete: if isAuthenticated() && (
        isAdmin() ||
        (resource != null && resource.data.ownerId == request.auth.uid)
      );
    }

    // Team member limits collection - allow Enterprise users to manage limits
    match /teamLimits/{ownerId} {
      allow read: if isAuthenticated() && (ownerId == request.auth.uid || isAdmin());
      allow create: if isAuthenticated() && isEnterpriseUser();
      allow update: if isAuthenticated() && (ownerId == request.auth.uid || isAdmin());
      allow delete: if isAuthenticated() && (ownerId == request.auth.uid || isAdmin());
    }

    // Billing details collection - allow users to manage their own billing information
    match /billingDetails/{docId} {
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow update: if isAuthenticated() && resource.data.userId == request.auth.uid;
      allow delete: if isAuthenticated() && resource.data.userId == request.auth.uid;
    }

    // Coupon codes collection - allow admin to manage, all authenticated users to read
    match /coupons/{couponId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
      allow create: if isAdmin();
      allow update: if isAdmin();
      allow delete: if isAdmin();
    }

    // Coupon usage collection - track which users have used which coupons
    match /couponUsage/{usageId} {
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid || isAdmin()
      );
      allow create: if isAuthenticated();
      allow update: if isAdmin();
      allow delete: if isAdmin();
    }

    // SSLCOMMERZ transactions collection - allow reading for payment status verification
    match /sslcommerzTransactions/{transactionId} {
      // Allow anyone to read transaction details for payment status verification
      // This is needed for the payment success/failed pages to work without authentication
      allow read: if true;
      // Only allow Firebase Functions to create and update transactions
      // This is handled by the Firebase Functions with admin privileges
      allow create: if false;
      allow update: if false;
      allow delete: if false;
    }

    // Payment logs collection - users can only read their own payment logs
    match /paymentLogs/{logId} {
      // Users can only read their own payment logs
      allow read: if isAuthenticated() &&
                     resource.data.userId == request.auth.uid;

      // Only Cloud Functions can write payment logs (client-side writes are blocked)
      allow create: if false;
      allow update: if false;
      allow delete: if false;
    }
  }
}