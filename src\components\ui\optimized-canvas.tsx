import { useEffect, useRef, useCallback } from 'react';

interface CanvasConfig {
  trails: number;
  size: number;
  friction: number;
  dampening: number;
  tension: number;
  enabled: boolean;
}

// Reduced configuration for better performance
const DEFAULT_CONFIG: CanvasConfig = {
  trails: 20, // Reduced from 80
  size: 25,   // Reduced from 50
  friction: 0.5,
  dampening: 0.025,
  tension: 0.99,
  enabled: true,
};

// Performance-aware configuration based on device capabilities
const getOptimalConfig = (): CanvasConfig => {
  // Check for reduced motion preference
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  if (prefersReducedMotion) {
    return { ...DEFAULT_CONFIG, enabled: false };
  }

  // Check device performance indicators
  const isLowEndDevice = navigator.hardwareConcurrency <= 2 || 
                        (navigator as any).deviceMemory <= 2 ||
                        navigator.userAgent.includes('Mobile');

  if (isLowEndDevice) {
    return {
      ...DEFAULT_CONFIG,
      trails: 10,
      size: 15,
    };
  }

  return DEFAULT_CONFIG;
};

class OptimizedNode {
  x: number = 0;
  y: number = 0;
  vx: number = 0;
  vy: number = 0;
}

class OptimizedLine {
  private spring: number;
  private friction: number;
  private nodes: OptimizedNode[];

  constructor(config: CanvasConfig) {
    this.spring = 0.45 + Math.random() * 0.025;
    this.friction = config.friction + Math.random() * 0.01 - 0.005;
    this.nodes = [];
    
    for (let i = 0; i < config.size; i++) {
      this.nodes.push(new OptimizedNode());
    }
  }

  update(mousePos: { x: number; y: number }, config: CanvasConfig) {
    const spring = this.spring;
    const firstNode = this.nodes[0];
    
    firstNode.vx += (mousePos.x - firstNode.x) * spring;
    firstNode.vy += (mousePos.y - firstNode.y) * spring;

    for (let i = 0; i < this.nodes.length; i++) {
      const node = this.nodes[i];
      
      if (i > 0) {
        const prevNode = this.nodes[i - 1];
        node.vx += (prevNode.x - node.x) * spring;
        node.vy += (prevNode.y - node.y) * spring;
        node.vx += prevNode.vx * config.dampening;
        node.vy += prevNode.vy * config.dampening;
      }
      
      node.vx *= this.friction;
      node.vy *= this.friction;
      node.x += node.vx;
      node.y += node.vy;
    }
  }

  draw(ctx: CanvasRenderingContext2D) {
    if (this.nodes.length < 2) return;

    ctx.beginPath();
    ctx.moveTo(this.nodes[0].x, this.nodes[0].y);

    for (let i = 1; i < this.nodes.length - 2; i++) {
      const current = this.nodes[i];
      const next = this.nodes[i + 1];
      const midX = (current.x + next.x) * 0.5;
      const midY = (current.y + next.y) * 0.5;
      ctx.quadraticCurveTo(current.x, current.y, midX, midY);
    }

    const lastTwo = this.nodes.slice(-2);
    if (lastTwo.length === 2) {
      ctx.quadraticCurveTo(lastTwo[0].x, lastTwo[0].y, lastTwo[1].x, lastTwo[1].y);
    }

    ctx.stroke();
  }
}

export const OptimizedCanvas: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const linesRef = useRef<OptimizedLine[]>([]);
  const mousePos = useRef({ x: 0, y: 0 });
  const configRef = useRef<CanvasConfig>(getOptimalConfig());
  const isRunning = useRef(false);
  const lastFrameTime = useRef(0);

  const resizeCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.width = window.innerWidth - 20;
    canvas.height = window.innerHeight;
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent | TouchEvent) => {
    if (e instanceof TouchEvent) {
      if (e.touches.length > 0) {
        mousePos.current.x = e.touches[0].pageX;
        mousePos.current.y = e.touches[0].pageY;
      }
    } else {
      mousePos.current.x = e.clientX;
      mousePos.current.y = e.clientY;
    }
  }, []);

  const initializeLines = useCallback(() => {
    const config = configRef.current;
    linesRef.current = [];
    
    for (let i = 0; i < config.trails; i++) {
      linesRef.current.push(new OptimizedLine(config));
    }
  }, []);

  const render = useCallback((currentTime: number) => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    
    if (!ctx || !isRunning.current || !configRef.current.enabled) return;

    // Throttle to 30fps for better performance
    if (currentTime - lastFrameTime.current < 33) {
      animationRef.current = requestAnimationFrame(render);
      return;
    }
    lastFrameTime.current = currentTime;

    // Clear canvas
    ctx.globalCompositeOperation = 'source-over';
    ctx.clearRect(0, 0, canvas!.width, canvas!.height);
    
    // Set drawing style
    ctx.globalCompositeOperation = 'lighter';
    ctx.strokeStyle = `hsla(${Math.sin(currentTime * 0.001) * 60 + 200}, 70%, 50%, 0.1)`;
    ctx.lineWidth = 3; // Reduced from 10
    
    // Update and draw lines
    linesRef.current.forEach(line => {
      line.update(mousePos.current, configRef.current);
      line.draw(ctx);
    });

    animationRef.current = requestAnimationFrame(render);
  }, []);

  useEffect(() => {
    const config = configRef.current;
    if (!config.enabled) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    // Initialize
    resizeCanvas();
    initializeLines();
    isRunning.current = true;

    // Event listeners
    document.addEventListener('mousemove', handleMouseMove, { passive: true });
    document.addEventListener('touchmove', handleMouseMove, { passive: true });
    window.addEventListener('resize', resizeCanvas);

    // Start animation
    animationRef.current = requestAnimationFrame(render);

    // Pause animation when tab is not visible
    const handleVisibilityChange = () => {
      isRunning.current = !document.hidden;
      if (isRunning.current && !animationRef.current) {
        animationRef.current = requestAnimationFrame(render);
      }
    };
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      isRunning.current = false;
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('touchmove', handleMouseMove);
      window.removeEventListener('resize', resizeCanvas);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [handleMouseMove, resizeCanvas, initializeLines, render]);

  if (!configRef.current.enabled) {
    return null;
  }

  return (
    <canvas
      ref={canvasRef}
      id="canvas"
      className="fixed inset-0 pointer-events-none z-0"
      style={{ background: 'transparent' }}
    />
  );
};
