// Check available tax categories in Paddle
const https = require('https');
require('dotenv').config();

const PADDLE_API_KEY = process.env.VITE_PADDLE_API_KEY;

function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.paddle.com',
      port: 443,
      path: path,
      method: method,
      headers: {
        'Authorization': `Bearer ${PADDLE_API_KEY}`,
        'Content-Type': 'application/json',
      },
    };

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(response);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${JSON.stringify(response)}`));
          }
        } catch (error) {
          reject(new Error(`Failed to parse response: ${body}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function checkTaxCategories() {
  console.log('🔍 Checking available tax categories...');
  
  try {
    const response = await makeRequest('GET', '/tax-categories');
    console.log('✅ Available tax categories:');
    console.log(JSON.stringify(response, null, 2));
  } catch (error) {
    console.error('❌ Error fetching tax categories:', error.message);
  }
}

checkTaxCategories();
