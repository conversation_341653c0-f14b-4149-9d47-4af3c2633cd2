import { db } from './firebase';
import { doc, getDoc, setDoc, collection, query, where, getDocs } from 'firebase/firestore';

export interface TeamMemberLimits {
  maxImages: number;
  deleteDelayHours: number;
  maxSavedPrompts: number;
  maxCustomPrompts: number;
  monthlyGenerationLimit: number;
  isMultipleRegistrations?: boolean; // Whether the invite link can be used multiple times
}

// Default team member limits if not set by the enterprise admin owner
export const DEFAULT_TEAM_MEMBER_LIMITS: TeamMemberLimits = {
  maxImages: 100,
  deleteDelayHours: 0,
  maxSavedPrompts: 50,
  maxCustomPrompts: 10,
  monthlyGenerationLimit: 200,
  isMultipleRegistrations: false // Default to one-time registration
};

/**
 * Helper function to get regular Enterprise limits from settings
 */
const getRegularEnterpriseLimits = async (): Promise<TeamMemberLimits> => {
  try {
    // Get the enterprise admin owner's limits from the settings
    const limitsDocRef = doc(db, 'settings', 'enterpriseAdminOwnerLimits');
    const limitsDoc = await getDoc(limitsDocRef);

    if (!limitsDoc.exists()) {
      return DEFAULT_TEAM_MEMBER_LIMITS;
    }

    const enterpriseLimits = limitsDoc.data();

    // Convert enterprise limits to team member limits format
    return {
      maxImages: enterpriseLimits.maxImages || DEFAULT_TEAM_MEMBER_LIMITS.maxImages,
      deleteDelayHours: enterpriseLimits.deleteDelayHours || DEFAULT_TEAM_MEMBER_LIMITS.deleteDelayHours,
      maxSavedPrompts: enterpriseLimits.maxSavedPrompts || DEFAULT_TEAM_MEMBER_LIMITS.maxSavedPrompts,
      maxCustomPrompts: enterpriseLimits.maxCustomPrompts || DEFAULT_TEAM_MEMBER_LIMITS.maxCustomPrompts,
      monthlyGenerationLimit: enterpriseLimits.monthlyGenerationLimit || DEFAULT_TEAM_MEMBER_LIMITS.monthlyGenerationLimit
    };
  } catch (error) {
    console.error('Error fetching regular enterprise limits:', error);
    return DEFAULT_TEAM_MEMBER_LIMITS;
  }
};

/**
 * Get team member limits for a specific invite
 */
export const getTeamMemberLimitsForInvite = async (inviteId: string): Promise<TeamMemberLimits> => {
  try {
    const inviteRef = doc(db, 'teamInvites', inviteId);
    const inviteDoc = await getDoc(inviteRef);

    if (inviteDoc.exists() && inviteDoc.data().limits) {
      return inviteDoc.data().limits as TeamMemberLimits;
    }

    return DEFAULT_TEAM_MEMBER_LIMITS;
  } catch (error) {
    console.error('Error fetching team member limits for invite:', error);
    return DEFAULT_TEAM_MEMBER_LIMITS;
  }
};

/**
 * Get team member limits for a specific team member
 */
export const getTeamMemberLimits = async (userId: string): Promise<TeamMemberLimits> => {
  try {
    console.log('Getting team member limits for user:', userId);

    // First check if the user document exists by ID
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      console.log('User document not found by ID, trying to find by UID');

      // Try to find user document by UID
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('uid', '==', userId));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        console.log('User not found by UID either, returning default limits');
        return DEFAULT_TEAM_MEMBER_LIMITS;
      }

      // Use the first matching document
      const userData = querySnapshot.docs[0].data();

      // If user is not a team member, return default limits
      if (!userData.isTeamMember || !userData.teamOwnerId) {
        console.log('User is not a team member, returning default limits');
        return DEFAULT_TEAM_MEMBER_LIMITS;
      }

      // Check if the user has specific limits set
      if (userData.limits) {
        console.log('Found limits in user document:', userData.limits);
        return userData.limits as TeamMemberLimits;
      }
    } else {
      // User document found by ID
      const userData = userDoc.data();

      // If user is not a team member, return default limits
      if (!userData.isTeamMember || !userData.teamOwnerId) {
        console.log('User is not a team member, returning default limits');
        return DEFAULT_TEAM_MEMBER_LIMITS;
      }

      // Check if the user has specific limits set
      if (userData.limits) {
        console.log('Found limits in user document:', userData.limits);
        return userData.limits as TeamMemberLimits;
      }

      // If no specific limits, check if there are team-wide limits set by the owner
      const teamLimitsRef = doc(db, 'teamLimits', userData.teamOwnerId);
      const teamLimitsDoc = await getDoc(teamLimitsRef);

      if (teamLimitsDoc.exists()) {
        console.log('Found team-wide limits:', teamLimitsDoc.data());
        return teamLimitsDoc.data() as TeamMemberLimits;
      }
    }

    console.log('No specific limits found, returning default limits');
    return DEFAULT_TEAM_MEMBER_LIMITS;
  } catch (error) {
    console.error('Error fetching team member limits:', error);
    return DEFAULT_TEAM_MEMBER_LIMITS;
  }
};

/**
 * Get the enterprise admin owner's available quota for team members
 */
export const getEnterpriseAdminOwnerQuota = async (ownerId: string): Promise<TeamMemberLimits> => {
  try {
    // First check if the owner has an Enterprise-based custom package
    const { getUserPackage } = await import('./userLimits');
    const packageName = await getUserPackage(ownerId);

    let adminLimits: TeamMemberLimits;

    // Check if it's an Enterprise-based custom package
    if (packageName !== 'Free' && packageName !== 'Pay-As-You-Go' && packageName !== 'Pro' && packageName !== 'Enterprise') {
      try {
        const { getCustomPackageType, getCustomPackageLimits } = await import('./customPackages');
        const packageType = await getCustomPackageType(packageName);

        if (packageType === 'enterprise-based') {
          const customLimits = await getCustomPackageLimits(packageName);
          if (customLimits && 'maxTeamMembers' in customLimits) {
            console.log('Using Enterprise-based custom package limits for team member quota:', customLimits);
            // Convert custom enterprise limits to team member limits format
            adminLimits = {
              maxImages: customLimits.maxImages || DEFAULT_TEAM_MEMBER_LIMITS.maxImages,
              deleteDelayHours: customLimits.deleteDelayHours || DEFAULT_TEAM_MEMBER_LIMITS.deleteDelayHours,
              maxSavedPrompts: customLimits.maxSavedPrompts || DEFAULT_TEAM_MEMBER_LIMITS.maxSavedPrompts,
              maxCustomPrompts: customLimits.maxCustomPrompts || DEFAULT_TEAM_MEMBER_LIMITS.maxCustomPrompts,
              monthlyGenerationLimit: customLimits.monthlyGenerationLimit || DEFAULT_TEAM_MEMBER_LIMITS.monthlyGenerationLimit
            };
          } else {
            console.warn('Custom package limits not found, falling back to default');
            adminLimits = DEFAULT_TEAM_MEMBER_LIMITS;
          }
        } else {
          // Not an enterprise-based custom package, fall back to regular enterprise limits
          adminLimits = await getRegularEnterpriseLimits();
        }
      } catch (customPackageError) {
        console.error('Error fetching custom package limits:', customPackageError);
        adminLimits = await getRegularEnterpriseLimits();
      }
    } else {
      // Regular Enterprise user or other standard package
      adminLimits = await getRegularEnterpriseLimits();
    }

    // Get all active team members for this owner
    const teamMembersRef = collection(db, 'teamMembers');
    const q = query(teamMembersRef, where('ownerId', '==', ownerId));
    const querySnapshot = await getDocs(q);

    // Calculate the total used quota
    let totalImagesUsed = 0;
    let totalSavedPromptsUsed = 0;
    let totalCustomPromptsUsed = 0;
    let totalMonthlyGenerationLimitUsed = 0;

    // Get all team members' user IDs to fetch their usage
    const teamMemberUserIds: string[] = [];
    querySnapshot.forEach((doc) => {
      const memberData = doc.data();
      if (memberData.email) {
        teamMemberUserIds.push(doc.id);
      }
    });

    // Get all team members' user records to check their limits
    if (teamMemberUserIds.length > 0) {
      const usersRef = collection(db, 'users');
      const userQuery = query(usersRef, where('isTeamMember', '==', true), where('teamOwnerId', '==', ownerId));
      const userQuerySnapshot = await getDocs(userQuery);

      userQuerySnapshot.forEach((userDoc) => {
        const userData = userDoc.data();
        if (userData.limits) {
          totalImagesUsed += userData.limits.maxImages || 0;
          totalSavedPromptsUsed += userData.limits.maxSavedPrompts || 0;
          totalCustomPromptsUsed += userData.limits.maxCustomPrompts || 0;
          totalMonthlyGenerationLimitUsed += userData.limits.monthlyGenerationLimit || 0;
        }
      });
    }

    // Calculate remaining quota
    const remainingQuota: TeamMemberLimits = {
      maxImages: Math.max(0, adminLimits.maxImages - totalImagesUsed),
      deleteDelayHours: adminLimits.deleteDelayHours, // This doesn't get "used up"
      maxSavedPrompts: Math.max(0, adminLimits.maxSavedPrompts - totalSavedPromptsUsed),
      maxCustomPrompts: Math.max(0, adminLimits.maxCustomPrompts - totalCustomPromptsUsed),
      monthlyGenerationLimit: Math.max(0, adminLimits.monthlyGenerationLimit - totalMonthlyGenerationLimitUsed)
    };

    return remainingQuota;
  } catch (error) {
    console.error('Error calculating enterprise admin owner quota:', error);
    return DEFAULT_TEAM_MEMBER_LIMITS;
  }
};

/**
 * Save team member limits for a specific invite
 */
export const saveTeamMemberLimitsForInvite = async (inviteId: string, limits: TeamMemberLimits): Promise<boolean> => {
  try {
    const inviteRef = doc(db, 'teamInvites', inviteId);
    const inviteDoc = await getDoc(inviteRef);

    if (!inviteDoc.exists()) {
      console.error('Invite not found');
      return false;
    }

    // Update the invite with the limits
    await setDoc(inviteRef, { limits }, { merge: true });
    return true;
  } catch (error) {
    console.error('Error saving team member limits for invite:', error);
    return false;
  }
};
