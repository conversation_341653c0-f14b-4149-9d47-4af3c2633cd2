# AppSumo Licensing API (v2) - Home & Overview

**Source URL:** https://docs.licensing.appsumo.com

---

## Introduction

AppSumo Licensing helps customers seamlessly activate their accounts with your product immediately after purchasing on AppSumo.

**All license keys are exclusively generated and managed by AppSumo** through webhooks triggered by events such as `Purchase, Activate, Upgrade, Downgrade,` and `Deactivate`.

## Overview

When a user purchases your product on AppSumo, they are prompted to activate their license through two key steps:

1. **License Activation Webhook:** AppSumo sends a [webhook notification](https://docs.licensing.appsumo.com/webhook/webhook__connect.html#processing-the-webhook-request) to your system to indicate license activation. Your app should save and mark the license key as active in your database.

2. **OAuth Authentication:** Users are prompted to connect through [OAuth](https://docs.licensing.appsumo.com/licensing/licensing__connect.html#connecting-to-appsumo-oauth), directing them to authenticate on your platform.

![OAuth Flow](https://docs.licensing.appsumo.com/assets/img/oauth_flow_client.44be8db6.png)

### OAuth Flow

- **Authorization:** Users accept OAuth scopes and are redirected to your site via the [redirect url](https://docs.licensing.appsumo.com/licensing/licensing__getting_started.html#oauth-redirect-url) configured in the AppSumo Partner Portal.

- **Access Token Retrieval:** Your app retrieves an access token to fetch the user's license key.

- **User Scenarios:**
  - **New User:** If the license key isn't linked to a user, prompt the customer to sign up or log in. Guide new users through registration and apply the license key.
  - **Existing User:** If already associated, log the user in automatically.

- **License Deactivation:** If a license needs deactivation, a webhook message will notify your system. Mark the license as invalid but ensure reactivation handling is supported.

### Key Considerations

- **Reactivation Handling:** Be prepared to manage potential reactivations to ensure a smooth user experience.
- **Further Learning:** For more details on additional webhooks and APIs, explore the full documentation.

Following these steps helps effectively integrate AppSumo Licensing, streamlining user experience and license management.

---

## Integration Flow Summary

1. **Setup Phase:**
   - Configure OAuth Redirect URL
   - Configure Webhook URL
   - Validate both URLs in AppSumo Partner Portal
   - Obtain OAuth keys (client_id and client_secret)

2. **Purchase Flow:**
   - User purchases on AppSumo
   - AppSumo sends `purchase` webhook to your system
   - Store license key with `inactive` status

3. **Activation Flow:**
   - User clicks activate on AppSumo
   - AppSumo sends `activate` webhook
   - User is redirected to your OAuth URL
   - Exchange OAuth code for access token
   - Use access token to fetch license details
   - Create/login user and activate license

4. **Ongoing Management:**
   - Handle `upgrade`, `downgrade`, and `deactivate` webhooks
   - Use API endpoints to verify license status
   - Manage user access based on license tier and status

---

## Next Steps

- [Quick Start Overview](https://docs.licensing.appsumo.com/quick-start/quick-start__overview.html)
- [Licensing Documentation](https://docs.licensing.appsumo.com/licensing/licensing__overview.html)
- [Webhook Documentation](https://docs.licensing.appsumo.com/webhook/webhook__overview.html)
- [API Documentation](https://docs.licensing.appsumo.com/api/api__overview.html)
