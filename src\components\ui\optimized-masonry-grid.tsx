import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Loader2, Eye, Languages, MessageSquare, FileText } from 'lucide-react';

export interface MasonryItem {
  id: string;
  image: string;
  tone?: string;
  language?: string;
  promptName?: string;
  content?: string;
  type?: 'caption' | 'ecommerce';
}

interface OptimizedMasonryGridProps {
  items: MasonryItem[];
  loading: boolean;
  error?: string | null;
  onItemClick: (item: MasonryItem) => void;
  className?: string;
  maxInitialItems?: number;
}

// Optimized image component with lazy loading and WebP support
const OptimizedImage: React.FC<{
  src: string;
  alt: string;
  onLoad: () => void;
  onError: () => void;
  className?: string;
}> = ({ src, alt, onLoad, onError, className }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    onLoad();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setHasError(true);
    onError();
  }, [onError]);

  // Create WebP version if possible
  const optimizedSrc = useMemo(() => {
    if (src.includes('.jpg') || src.includes('.jpeg') || src.includes('.png')) {
      return src.replace(/\.(jpg|jpeg|png)$/i, '.webp');
    }
    return src;
  }, [src]);

  if (hasError) {
    return (
      <div className={`bg-gray-800 flex items-center justify-center ${className}`}>
        <div className="text-gray-500 text-sm">Image unavailable</div>
      </div>
    );
  }

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {!isLoaded && (
        <div className="absolute inset-0 bg-gray-800 animate-pulse flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
      <picture>
        <source srcSet={optimizedSrc} type="image/webp" />
        <img
          src={src}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          loading="lazy"
          decoding="async"
          className={`w-full h-full object-cover transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          }`}
        />
      </picture>
    </div>
  );
};

// Memoized masonry item component
const MasonryItemCard = React.memo<{
  item: MasonryItem;
  onClick: (item: MasonryItem) => void;
  onImageLoad: (itemId: string) => void;
  onImageError: (itemId: string) => void;
}>(({ item, onClick, onImageLoad, onImageError }) => {
  const handleClick = useCallback(() => {
    onClick(item);
  }, [item, onClick]);

  const handleImageLoad = useCallback(() => {
    onImageLoad(item.id);
  }, [item.id, onImageLoad]);

  const handleImageError = useCallback(() => {
    onImageError(item.id);
  }, [item.id, onImageError]);

  return (
    <div
      className="group cursor-pointer transform transition-all duration-300 hover:scale-[1.02] hover:shadow-lg hover:shadow-purple-500/20"
      onClick={handleClick}
    >
      <div className="bg-gray-800 rounded-xl overflow-hidden border border-gray-700 hover:border-purple-500/50">
        <div className="aspect-[4/3] relative">
          <OptimizedImage
            src={item.image}
            alt={`Generated content example`}
            onLoad={handleImageLoad}
            onError={handleImageError}
            className="w-full h-full"
          />
          
          {/* Overlay with metadata */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div className="absolute bottom-0 left-0 right-0 p-4">
              <div className="flex items-center gap-2 text-white text-sm">
                {item.type === 'ecommerce' ? (
                  <MessageSquare className="w-4 h-4" />
                ) : (
                  <FileText className="w-4 h-4" />
                )}
                <span className="capitalize">{item.type || 'caption'}</span>
                
                {item.language && (
                  <>
                    <Languages className="w-4 h-4 ml-2" />
                    <span>{item.language}</span>
                  </>
                )}
              </div>
              
              {item.promptName && (
                <div className="text-gray-300 text-xs mt-1 truncate">
                  {item.promptName}
                </div>
              )}
            </div>
          </div>
          
          {/* View indicator */}
          <div className="absolute top-2 right-2 bg-black/50 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <Eye className="w-4 h-4 text-white" />
          </div>
        </div>
      </div>
    </div>
  );
});

MasonryItemCard.displayName = 'MasonryItemCard';

export const OptimizedMasonryGrid: React.FC<OptimizedMasonryGridProps> = ({
  items,
  loading,
  error,
  onItemClick,
  className = '',
  maxInitialItems = 6
}) => {
  const [imageLoadStates, setImageLoadStates] = useState<Record<string, boolean>>({});
  const [showAll, setShowAll] = useState(false);

  // Memoized callbacks
  const handleImageLoad = useCallback((itemId: string) => {
    setImageLoadStates(prev => ({ ...prev, [itemId]: true }));
  }, []);

  const handleImageError = useCallback((itemId: string) => {
    setImageLoadStates(prev => ({ ...prev, [itemId]: false }));
  }, []);

  const handleShowMore = useCallback(() => {
    setShowAll(true);
  }, []);

  // Memoized items to display
  const displayItems = useMemo(() => {
    return showAll ? items : items.slice(0, maxInitialItems);
  }, [items, showAll, maxInitialItems]);

  const hasMoreItems = items.length > maxInitialItems && !showAll;

  if (loading) {
    return (
      <div className="flex justify-center items-center py-20">
        <div className="text-center">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur-lg opacity-30 animate-pulse"></div>
            <Loader2 className="relative h-8 w-8 animate-spin text-purple-500 mx-auto mb-4" />
          </div>
          <p className="text-gray-400">Loading inspiring content...</p>
          <p className="text-gray-500 text-sm mt-2">Discovering amazing examples from our community</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-20">
        <div className="text-red-400 mb-4">Failed to load content</div>
        <p className="text-gray-500 text-sm">{error}</p>
      </div>
    );
  }

  if (displayItems.length === 0) {
    return (
      <div className="text-center py-20">
        <div className="text-gray-400 mb-4">No content available</div>
        <p className="text-gray-500 text-sm">Check back later for inspiring examples!</p>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Responsive masonry grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {displayItems.map((item) => (
          <MasonryItemCard
            key={item.id}
            item={item}
            onClick={onItemClick}
            onImageLoad={handleImageLoad}
            onImageError={handleImageError}
          />
        ))}
      </div>

      {/* Show more button */}
      {hasMoreItems && (
        <div className="text-center mt-8">
          <button
            onClick={handleShowMore}
            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg transition-all duration-300 transform hover:scale-105"
          >
            Show More Examples ({items.length - maxInitialItems} more)
          </button>
        </div>
      )}
    </div>
  );
};
