import React, { createContext, useContext, useState, useEffect } from 'react';
import { useStore } from './store';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { db } from './firebase';
import { isSubscriptionExpired } from './userLimits';

interface SubscriptionState {
  isExpired: boolean;
  packageName: string | null;
  expireDate: string | null;
  isLoading: boolean;
  checkSubscriptionStatus: () => Promise<void>;
}

const SubscriptionContext = createContext<SubscriptionState | undefined>(undefined);

export const SubscriptionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useStore();
  const [isExpired, setIsExpired] = useState<boolean>(false);
  const [packageName, setPackageName] = useState<string | null>(null);
  const [expireDate, setExpireDate] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const checkSubscriptionStatus = async () => {
    if (!user) {
      setIsExpired(false);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      // First, check if the user has a subscription that can expire
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('uid', '==', user.id));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        setIsExpired(false);
        setPackageName(null);
        setExpireDate(null);
        setIsLoading(false);
        return;
      }

      const userData = querySnapshot.docs[0].data();
      const userPackageName = userData.packageName || 'Free';
      setPackageName(userPackageName);

      // Only Pro, Enterprise, and Custom packages can expire
      if (userPackageName !== 'Pro' && userPackageName !== 'Enterprise' && !userPackageName.startsWith('Custom:')) {
        setIsExpired(false);
        setExpireDate(null);
        setIsLoading(false);
        return;
      }

      // Check if there's an expiration date
      if (userData.subscriptionExpireDate) {
        setExpireDate(userData.subscriptionExpireDate);

        // Handle lifetime deals
        if (userData.subscriptionExpireDate === 'lifetime') {
          setIsExpired(false);
        } else {
          const expireDate = new Date(userData.subscriptionExpireDate);
          const now = new Date();
          setIsExpired(expireDate < now);
        }
      } else {
        setIsExpired(false);
        setExpireDate(null);
      }
    } catch (error) {
      console.error('Error checking subscription status:', error);
      setIsExpired(false);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      checkSubscriptionStatus();
    } else {
      setIsExpired(false);
      setPackageName(null);
      setExpireDate(null);
      setIsLoading(false);
    }
  }, [user]);

  // Listen for payment success events to refresh subscription status
  useEffect(() => {
    const handlePaymentSuccess = () => {
      if (user) {
        // Delay to ensure Firebase has been updated
        setTimeout(() => {
          checkSubscriptionStatus();
        }, 3000);
      }
    };

    // Listen for custom payment success event
    window.addEventListener('paymentSuccess', handlePaymentSuccess);

    return () => {
      window.removeEventListener('paymentSuccess', handlePaymentSuccess);
    };
  }, [user]);

  return (
    <SubscriptionContext.Provider
      value={{
        isExpired,
        packageName,
        expireDate,
        isLoading,
        checkSubscriptionStatus
      }}
    >
      {children}
    </SubscriptionContext.Provider>
  );
};

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};
