import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Plus, Minus } from 'lucide-react';
import { Footer } from '../../components/footer';

export function FAQPage() {
  const [openIndex, setOpenIndex] = React.useState<number | null>(0);

  const faqs = [
    {
      question: "How does eComEasyAI generate product descriptions?",
      answer: "Our AI technology analyzes your product images to identify key features, materials, colors, and design elements. It then uses this visual information combined with our specialized e-commerce language models to create compelling, accurate product descriptions optimized for conversions."
    },
    {
      question: "What languages does eComEasyAI support?",
      answer: "We currently support over 30 languages including English, Spanish, French, German, Italian, Portuguese, Dutch, Russian, Chinese (Simplified and Traditional), Japanese, Korean, Arabic, and many more. We're constantly adding new languages based on customer demand."
    },
    {
      question: "How accurate are the AI-generated descriptions?",
      answer: "Our AI has been trained on millions of product images and descriptions to ensure high accuracy. However, we always recommend reviewing the generated content before publishing. Our system improves over time as it learns from your edits and preferences."
    },
    {
      question: "Can I customize the tone and style of the descriptions?",
      answer: "Absolutely! You can set brand voice preferences, specify tone (professional, casual, luxury, etc.), and even provide examples of your preferred writing style. Our AI will adapt to match your brand's unique voice."
    },
    {
      question: "How many images can I process per month?",
      answer: "This depends on your subscription plan. Our Starter plan includes 100 images per month, Pro plan includes 1,000 images per month, and Enterprise plans offer unlimited image processing. You can always upgrade your plan as your needs grow."
    },
    {
      question: "Is my data secure?",
      answer: "Yes, we take data security very seriously. All images and generated content are encrypted both in transit and at rest. We never share your data with third parties, and you retain full ownership of all content created on our platform."
    },
    {
      question: "Can I integrate eComEasyAI with my e-commerce platform?",
      answer: "Yes, we offer integrations with major e-commerce platforms including Shopify, WooCommerce, Magento, BigCommerce, and more. We also provide an API for custom integrations with your existing systems."
    },
    {
      question: "Do you offer a free trial?",
      answer: "Yes, we offer a 14-day free trial with access to all features and up to 50 image uploads. No credit card is required to start your trial."
    },
    {
      question: "How do I get started?",
      answer: "Simply sign up for an account, upload a product image, and our AI will generate a description within seconds. You can then edit, save, and export the description to your e-commerce platform."
    },
    {
      question: "What if I'm not satisfied with the results?",
      answer: "We offer a 30-day money-back guarantee if you're not completely satisfied with our service. Our customer success team is also available to help you optimize your results and get the most out of our platform."
    }
  ];

  const toggleFaq = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-12">
        <Link to="/" className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Home
        </Link>
        
        <div className="max-w-3xl mx-auto">
          <h1 className="text-4xl font-bold text-white mb-8">Frequently Asked Questions</h1>
          
          <div className="space-y-4 mb-12">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-gray-800 rounded-xl border border-gray-700 overflow-hidden">
                <button
                  className="w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none"
                  onClick={() => toggleFaq(index)}
                >
                  <span className="text-lg font-medium text-white">{faq.question}</span>
                  {index === openIndex ? (
                    <Minus className="h-5 w-5 text-purple-400" />
                  ) : (
                    <Plus className="h-5 w-5 text-purple-400" />
                  )}
                </button>
                {openIndex === index && (
                  <div className="px-6 py-4 border-t border-gray-700">
                    <p className="text-gray-300">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
          
          <div className="text-center">
            <Link to="/auth?mode=signup" className="inline-block">
              <button className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                Start Your Free Trial
              </button>
            </Link>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

export default FAQPage;