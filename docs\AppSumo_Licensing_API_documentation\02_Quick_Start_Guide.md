# AppSumo Licensing API (v2) - Quick Start Guide

**Source URL:** https://docs.licensing.appsumo.com/quick-start/quick-start__overview.html

---

## Overview

This guide walks you through setting up your product on AppSumo, including configuring both **Webhook** and **OAuth Redirect URLs**, validating them, and obtaining your OAuth keys. For detailed information, refer to [User login (OAuth)](https://docs.licensing.appsumo.com/licensing/licensing__overview.html) and [Webhooks](https://docs.licensing.appsumo.com/webhook/webhook__overview.html).

## Prerequisites

- **OAuth Redirect URL:** Must handle `GET` requests.
- **Webhook URL:** Must handle `POST` requests.
- **OAuth Keys:** `client_id` and `client_secret`
  - This will be accessible once the specified URLs are validated and your listing or settings have been submitted.

## Configuration and URL Validation

**Access Settings:** Go to the [AppSumo Partner Portal](https://www.appsumo.com/partners/products/) and select your product.

- For **AppSumo Select Partners**, click the **"API settings"** link. For **Self-listed partners**, click anywhere in the row (as shown below).

![Select Partners](https://docs.licensing.appsumo.com/assets/img/listings_partner_portal.dce918f4.png)

### Validate both Webhook and OAuth URLs:

#### 1. OAuth Redirect URL Validation

Pre-validate by receiving a `GET` **request without any included payload.**
- This URL must return a `200 OK` status code.

After a user authorizes your application on AppSumo via OAuth, they are redirected back to your app with an `authorization code` in the URL.

**Important:** Ensure that the validated OAuth Redirect URL in AppSumo aligns with the URL on your backend where you want to direct new customers to complete required information and finalize the sign-up process, including providing their email and new account password.

See [User login (OAuth)](https://docs.licensing.appsumo.com/licensing/licensing__overview.html) for more information.

![Oauth Redirect Config](https://docs.licensing.appsumo.com/assets/img/oauth_redirect_config.fe888aac.png)

#### 2. Webhook URL Validation

Pre-validate by receiving a `POST` request with a field called `test` in the body.

- This URL must return a `200 OK` with a JSON response indicating `success` = `true` and the event type it received.
- For the initial test, **you must respond to/with the following** event types:
  1. `activate`
  2. `deactivate`
  3. `purchase` (optional)

![Webhook URL](https://docs.licensing.appsumo.com/assets/img/Webhook_url.74266dc8.png)

#### Example of a successful response to the webhook (in JSON format):

```json
{
  "event": "activate",
  "success": true
}
```

Refer to the [Webhooks](https://docs.licensing.appsumo.com/webhook/webhook__overview.html) section for an overview, and see the guidance on [Sending a successful response](https://docs.licensing.appsumo.com/webhook/webhook__connect.html#sending-a-successful-response) to learn how to process your webhooks effectively.

## Post-Validation

### OAuth keys

After validating both your Webhook and OAuth Redirect URLs, your OAuth keys (`client_id` and `client_secret`) will be generated. You can locate these keys on your product page in the [AppSumo Partner Portal](https://www.appsumo.com/partners/products/). The keys are hidden by default; click the eye icon to view them (as shown below):

![Private keys](https://docs.licensing.appsumo.com/assets/img/private_keys.d80967b3.png)

### Live Webhooks and License history

Now that your URLs are validated and OAuth keys are secured, you can start interacting with the buy button within your [Product Detail Page on AppSumo](https://appsumo.com/products/tidycal/#pricePlans) (_TidyCal used here as an example_) using the **developer credits** assigned to you by our **Launch Operations** team.

**Live payloads** will now be sent (as demonstrated later in this guide) for you to access and store real-time data.

You can access your License History UI in the API settings section of the [AppSumo Partner Portal](https://www.appsumo.com/partners/products/).

![License History Navigation](https://docs.licensing.appsumo.com/assets/img/License_History_Navigation.36d7f1ce.png)

![License History](https://docs.licensing.appsumo.com/assets/img/License_History.9e7680c1.png)

## Next steps

- For more in depth information on validating URLs and what to do after submitting your application:
  - OAuth, see [User login (OAuth)](https://docs.licensing.appsumo.com/licensing/licensing__overview.html)
  - Webhooks, see [Webhooks](https://docs.licensing.appsumo.com/webhook/webhook__overview.html)

---

## Implementation Checklist

- [ ] Create OAuth Redirect URL endpoint (handles GET requests)
- [ ] Create Webhook URL endpoint (handles POST requests)
- [ ] Configure URLs in AppSumo Partner Portal
- [ ] Validate both URLs (receive 200 OK responses)
- [ ] Obtain OAuth keys (client_id and client_secret)
- [ ] Test with developer credits
- [ ] Monitor License History UI
- [ ] Implement full OAuth flow
- [ ] Implement webhook processing
- [ ] Test end-to-end integration
