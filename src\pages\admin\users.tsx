import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Edit, Trash, Search, UserPlus, AlertCircle } from 'lucide-react';
import { db, auth } from '@/lib/firebase';
import { collection, getDocs, doc, updateDoc, deleteDoc, query, where, addDoc } from 'firebase/firestore';
import { deleteUser } from 'firebase/auth';
import { toast } from 'react-hot-toast';
import { getActiveCustomPackages } from '@/lib/customPackages';
import { CustomPackage } from '@/lib/userLimits';

interface User {
  id: string;
  email: string;
  displayName?: string;
  createdAt?: string;
  role?: string;
  packageName?: string;
  status?: 'active' | 'inactive';
  uid?: string;
  subscriptionExpireDate?: string;
}

export function UsersManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [editedUser, setEditedUser] = useState<Partial<User>>({});
  const [customPackages, setCustomPackages] = useState<CustomPackage[]>([]);

  useEffect(() => {
    fetchUsers();
    fetchCustomPackages();
  }, []);

  const fetchCustomPackages = async () => {
    try {
      const packages = await getActiveCustomPackages();
      setCustomPackages(packages);
    } catch (error) {
      console.error('Error fetching custom packages:', error);
      // Don't show error toast as this is not critical for user management
    }
  };

  const fetchUsers = async () => {
    setLoading(true);
    try {
      // Ensure we're authenticated as admin
      if (!auth.currentUser || auth.currentUser.email !== import.meta.env.VITE_ADMIN_EMAIL) {
        throw new Error('Admin authentication required');
      }

      // Get users from Firebase Firestore
      const usersCollection = collection(db, 'users');
      const usersSnapshot = await getDocs(usersCollection);

      // Create a map of existing users by email for quick lookup
      const existingUsersByEmail = new Map();
      const usersData: User[] = [];

      usersSnapshot.forEach((doc) => {
        const userData = doc.data() as Omit<User, 'id'>;
        // Use packageName as primary source, fallback to role for backward compatibility
        let packageName = userData.packageName || 'Free';
        let role = userData.role;

        // If no role is set, derive it from packageName
        if (!role) {
          if (packageName === 'Pro') {
            role = 'Pro User';
          } else if (packageName === 'Enterprise') {
            role = 'Enterprise User';
          } else if (packageName === 'Pay-As-You-Go') {
            role = 'Pay-As-You-Go User';
          } else {
            role = 'Free User';
          }
        }

        // If packageName is not set but role is, derive packageName from role (backward compatibility)
        if (!userData.packageName && userData.role) {
          if (userData.role === 'Pro User') {
            packageName = 'Pro';
          } else if (userData.role === 'Enterprise User') {
            packageName = 'Enterprise';
          } else if (userData.role === 'Pay-As-You-Go User') {
            packageName = 'Pay-As-You-Go';
          } else {
            packageName = 'Free';
          }
        }

        const user = {
          id: doc.id,
          email: userData.email,
          displayName: userData.displayName || 'User',
          createdAt: userData.createdAt || new Date().toISOString(),
          role: role,
          packageName: packageName,
          status: userData.status || 'active',
          uid: userData.uid || '',
          subscriptionExpireDate: userData.subscriptionExpireDate || undefined
        };

        usersData.push(user);
        existingUsersByEmail.set(user.email.toLowerCase(), user);
      });

      // Sync users from Firebase Authentication to Firestore
      // Since we can't directly list all users from client-side Firebase Auth,
      // we'll add a button to manually sync the current user
      const currentUser = auth.currentUser;
      if (currentUser && !existingUsersByEmail.has(currentUser.email?.toLowerCase() || '')) {
        try {
          // Create a Firestore document for the current user
          const userDoc = {
            email: currentUser.email,
            displayName: currentUser.displayName || currentUser.email?.split('@')[0] || 'User',
            createdAt: new Date().toISOString(),
            role: 'Free User',
            packageName: 'Free',
            status: 'active',
            uid: currentUser.uid
          };

          // Add the user to Firestore
          const docRef = await addDoc(collection(db, 'users'), userDoc);

          // Add the user to the local state
          usersData.push({
            id: docRef.id,
            ...userDoc
          } as User);

          toast.success('Added current user to database');
        } catch (error) {
          console.error('Error adding current user to Firestore:', error);
        }
      }

      setUsers(usersData);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleEditUser = (user: User) => {
    setCurrentUser(user);
    setEditedUser({
      displayName: user.displayName,
      role: user.role,
      status: user.status,
      subscriptionExpireDate: user.subscriptionExpireDate
    });
    setIsEditDialogOpen(true);
  };

  const handleDeleteUser = (user: User) => {
    setCurrentUser(user);
    setIsDeleteDialogOpen(true);
  };

  const saveUserChanges = async () => {
    if (!currentUser) return;

    try {
      const userRef = doc(db, 'users', currentUser.id);

      // Determine package name based on role
      let packageName = 'Free';
      if (editedUser.role === 'Pay-As-You-Go User') {
        packageName = 'Pay-As-You-Go';
      } else if (editedUser.role === 'Pro User') {
        packageName = 'Pro';
      } else if (editedUser.role === 'Enterprise User') {
        packageName = 'Enterprise';
      } else if (editedUser.role && editedUser.role.startsWith('Custom:')) {
        // Extract custom package name from role (format: "Custom:PackageName")
        packageName = editedUser.role.substring(7);
      }

      // Only include subscriptionExpireDate if it's set and the user is Pro, Enterprise, or Custom package
      const updateData: any = {
        ...editedUser,
        packageName: packageName,
        updatedAt: new Date().toISOString()
      };

      // If the user is downgraded to Free or Pay-As-You-Go, remove the subscription expiration date
      if (editedUser.role === 'Free User' || editedUser.role === 'Pay-As-You-Go User') {
        updateData.subscriptionExpireDate = null;
      }

      await updateDoc(userRef, updateData);

      // Update local state
      setUsers(users.map(user =>
        user.id === currentUser.id ? { ...user, ...editedUser, packageName } : user
      ));

      toast.success('User updated successfully');
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user');
    }
  };

  const confirmDeleteUser = async () => {
    if (!currentUser) return;

    try {
      // First delete from Firestore
      const userRef = doc(db, 'users', currentUser.id);
      await deleteDoc(userRef);

      // Then attempt to delete from Firebase Authentication if UID is available
      if (currentUser.uid) {
        try {
          // Get the current user's ID token
          const idToken = await auth.currentUser?.getIdToken();

          // Call the Cloud Function to delete the user from Authentication
          const response = await fetch('https://us-central1-product-img-2-ecom.cloudfunctions.net/deleteUserAccount', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${idToken}`
            },
            body: JSON.stringify({ uid: currentUser.uid })
          });

          const result = await response.json();

          if (response.ok) {
            console.log('User deletion result:', result);
            toast.success('User deleted from Authentication successfully');
          } else {
            throw new Error(result.error || 'Failed to delete user from Authentication');
          }
        } catch (authError) {
          console.error('Error deleting user from Authentication:', authError);
          toast.error('Failed to delete user from Authentication. Please check Firebase console.');
          // We continue because we've already deleted from Firestore
        }
      }

      // Update local state
      setUsers(users.filter(user => user.id !== currentUser.id));

      toast.success('User deleted from database successfully');
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error('Failed to delete user');
    }
  };

  // Filter users based on search term
  const filteredUsers = users.filter(user =>
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (user.displayName && user.displayName.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-white">User Management</h2>
          <p className="text-gray-400 mt-1">Manage your application users</p>
        </div>
        <div className="flex space-x-3">
          <Button
            className="bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700"
            onClick={fetchUsers}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Sync Users
          </Button>
          <Button
            className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700"
            onClick={() => toast.error('User creation not implemented in this demo')}
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Add User
          </Button>
        </div>
      </div>

      <div className="bg-gray-800 rounded-lg border border-gray-700 p-4">
        <div className="flex items-center mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-gray-700 border-gray-600 text-white placeholder-gray-400"
            />
          </div>
        </div>

        {loading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-purple-500 border-r-2 border-purple-500 border-b-2 border-transparent"></div>
            <p className="mt-2 text-gray-400">Loading users...</p>
          </div>
        ) : filteredUsers.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            <AlertCircle className="h-12 w-12 mx-auto text-gray-500 mb-2" />
            <p>No users found</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader className="bg-gray-700">
                <TableRow>
                  <TableHead className="text-gray-300">User</TableHead>
                  <TableHead className="text-gray-300">Email</TableHead>
                  <TableHead className="text-gray-300">Role</TableHead>
                  <TableHead className="text-gray-300">Subscription</TableHead>
                  <TableHead className="text-gray-300">Status</TableHead>
                  <TableHead className="text-gray-300">Created</TableHead>
                  <TableHead className="text-gray-300 text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id} className="border-gray-700 hover:bg-gray-700/50">
                    <TableCell className="font-medium text-white">
                      {user.displayName}
                    </TableCell>
                    <TableCell className="text-gray-300">{user.email}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        user.role === 'admin' ? 'bg-purple-500/20 text-purple-300' :
                        user.role === 'Pro User' ? 'bg-green-500/20 text-green-300' :
                        user.role === 'Enterprise User' ? 'bg-amber-500/20 text-amber-300' :
                        user.role === 'Pay-As-You-Go User' ? 'bg-cyan-500/20 text-cyan-300' :
                        user.role && user.role.startsWith('Custom:') ? 'bg-pink-500/20 text-pink-300' :
                        'bg-blue-500/20 text-blue-300'}`}>
                        {user.role || 'Free User'}
                      </span>
                    </TableCell>
                    <TableCell>
                      {(user.role === 'Pro User' || user.role === 'Enterprise User' || (user.role && user.role.startsWith('Custom:'))) && user.subscriptionExpireDate ? (
                        user.subscriptionExpireDate === 'lifetime' ? (
                          <span className="px-2 py-1 rounded-full text-xs bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-300 border border-yellow-500/30">
                            ✨ Lifetime
                          </span>
                        ) : (
                          <span className={`px-2 py-1 rounded-full text-xs ${new Date(user.subscriptionExpireDate) > new Date() ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'}`}>
                            {new Date(user.subscriptionExpireDate).toLocaleDateString()}
                          </span>
                        )
                      ) : (
                        <span className="text-gray-400 text-xs">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs ${user.status === 'active' ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'}`}>
                        {user.status || 'active'}
                      </span>
                    </TableCell>
                    <TableCell className="text-gray-400">
                      {new Date(user.createdAt || '').toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditUser(user)}
                        className="text-gray-400 hover:text-white hover:bg-gray-700"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteUser(user)}
                        className="text-gray-400 hover:text-red-400 hover:bg-gray-700"
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </div>

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="bg-gray-800 text-white border-gray-700">
          <DialogHeader>
            <DialogTitle className="text-white">Edit User</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <label className="text-sm font-medium text-gray-300 mb-1 block">Email</label>
              <Input
                value={currentUser?.email || ''}
                disabled
                className="bg-gray-700 border-gray-600 text-gray-400"
              />
            </div>
            <div>
              <label className="text-sm font-medium text-gray-300 mb-1 block">Display Name</label>
              <Input
                value={editedUser.displayName || ''}
                onChange={(e) => setEditedUser({ ...editedUser, displayName: e.target.value })}
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
            <div>
              <label className="text-sm font-medium text-gray-300 mb-1 block">Role</label>
              <select
                value={editedUser.role || 'user'}
                onChange={(e) => setEditedUser({ ...editedUser, role: e.target.value })}
                className="w-full rounded-md bg-gray-700 border-gray-600 text-white py-2 px-3"
              >
                <option value="Free User">Free User</option>
                <option value="Pay-As-You-Go User">Pay-As-You-Go User</option>
                <option value="Pro User">Pro User</option>
                <option value="Enterprise User">Enterprise User</option>
                <option value="admin">Admin</option>
                {customPackages.length > 0 && (
                  <optgroup label="Custom Packages">
                    {customPackages.map((pkg) => (
                      <option key={pkg.id} value={`Custom:${pkg.name}`}>
                        {pkg.name} ({pkg.type === 'pro-based' ? 'Pro-based' : 'Enterprise-based'})
                      </option>
                    ))}
                  </optgroup>
                )}
              </select>
            </div>

            {/* Subscription Expiration - show for Pro, Enterprise, and Custom package users */}
            {(editedUser.role === 'Pro User' ||
              editedUser.role === 'Enterprise User' ||
              (editedUser.role && editedUser.role.startsWith('Custom:'))) && (
              <div>
                <label className="text-sm font-medium text-gray-300 mb-1 block">Subscription Expiration</label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="expire1day"
                      name="subscriptionExpire"
                      className="text-purple-500 bg-gray-700 border-gray-600"
                      onChange={() => {
                        const expireDate = new Date();
                        expireDate.setDate(expireDate.getDate() + 1);
                        setEditedUser({ ...editedUser, subscriptionExpireDate: expireDate.toISOString() });
                      }}
                      checked={editedUser.subscriptionExpireDate && new Date(editedUser.subscriptionExpireDate).getTime() - new Date().getTime() < 2 * 24 * 60 * 60 * 1000}
                    />
                    <label htmlFor="expire1day" className="text-gray-300">1 Day</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="expire30days"
                      name="subscriptionExpire"
                      className="text-purple-500 bg-gray-700 border-gray-600"
                      onChange={() => {
                        const expireDate = new Date();
                        expireDate.setDate(expireDate.getDate() + 30);
                        setEditedUser({ ...editedUser, subscriptionExpireDate: expireDate.toISOString() });
                      }}
                      checked={editedUser.subscriptionExpireDate &&
                        new Date(editedUser.subscriptionExpireDate).getTime() - new Date().getTime() >= 2 * 24 * 60 * 60 * 1000 &&
                        new Date(editedUser.subscriptionExpireDate).getTime() - new Date().getTime() < 360 * 24 * 60 * 60 * 1000}
                    />
                    <label htmlFor="expire30days" className="text-gray-300">30 Days</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="expire365days"
                      name="subscriptionExpire"
                      className="text-purple-500 bg-gray-700 border-gray-600"
                      onChange={() => {
                        const expireDate = new Date();
                        expireDate.setDate(expireDate.getDate() + 365);
                        setEditedUser({ ...editedUser, subscriptionExpireDate: expireDate.toISOString() });
                      }}
                      checked={editedUser.subscriptionExpireDate &&
                        editedUser.subscriptionExpireDate !== 'lifetime' &&
                        new Date(editedUser.subscriptionExpireDate).getTime() - new Date().getTime() >= 360 * 24 * 60 * 60 * 1000}
                    />
                    <label htmlFor="expire365days" className="text-gray-300">365 Days</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="lifetimeDeal"
                      name="subscriptionExpire"
                      className="text-purple-500 bg-gray-700 border-gray-600"
                      onChange={() => {
                        setEditedUser({ ...editedUser, subscriptionExpireDate: 'lifetime' });
                      }}
                      checked={editedUser.subscriptionExpireDate === 'lifetime'}
                    />
                    <label htmlFor="lifetimeDeal" className="text-gray-300 flex items-center">
                      <span className="bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent font-semibold">
                        Lifetime Deal
                      </span>
                      <span className="ml-2 text-xs text-gray-500">(No expiration)</span>
                    </label>
                  </div>

                  {editedUser.subscriptionExpireDate && (
                    <div className="mt-2 text-sm text-gray-400">
                      {editedUser.subscriptionExpireDate === 'lifetime' ? (
                        <span className="text-yellow-400 font-medium">Lifetime Access - Never Expires</span>
                      ) : (
                        <>Expires on: {new Date(editedUser.subscriptionExpireDate).toLocaleDateString()}</>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            <div>
              <label className="text-sm font-medium text-gray-300 mb-1 block">Status</label>
              <select
                value={editedUser.status || 'active'}
                onChange={(e) => setEditedUser({ ...editedUser, status: e.target.value as 'active' | 'inactive' })}
                className="w-full rounded-md bg-gray-700 border-gray-600 text-white py-2 px-3"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              Cancel
            </Button>
            <Button
              onClick={saveUserChanges}
              className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700"
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete User Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="bg-gray-800 text-white border-gray-700">
          <DialogHeader>
            <DialogTitle className="text-white">Confirm Deletion</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-gray-300">Are you sure you want to delete the user <span className="font-semibold text-white">{currentUser?.email}</span>?</p>
            <p className="text-gray-400 mt-2">This action cannot be undone.</p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              Cancel
            </Button>
            <Button
              onClick={confirmDeleteUser}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Delete User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default UsersManagement;