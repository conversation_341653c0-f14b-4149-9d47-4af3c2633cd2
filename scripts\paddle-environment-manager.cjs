// Paddle Environment Manager
// This script helps manage Paddle integration across different environments
// Usage: node scripts/paddle-environment-manager.cjs [command] [environment]
// Commands: test, list-products, list-prices, validate, switch
// Environments: production, sandbox

const https = require('https');
require('dotenv').config();

// Environment configuration
const ENVIRONMENTS = {
  production: {
    apiKey: process.env.VITE_PADDLE_API_KEY_PRODUCTION || process.env.VITE_PADDLE_API_KEY,
    clientToken: process.env.VITE_PADDLE_CLIENT_SIDE_TOKEN_PRODUCTION || process.env.VITE_PADDLE_CLIENT_SIDE_TOKEN,
    apiEndpoint: 'https://api.paddle.com',
    name: 'Production'
  },
  sandbox: {
    apiKey: process.env.VITE_PADDLE_API_KEY_SANDBOX,
    clientToken: process.env.VITE_PADDLE_CLIENT_SIDE_TOKEN_SANDBOX,
    apiEndpoint: 'https://sandbox-api.paddle.com',
    name: 'Sandbox'
  }
};

// Get current environment from env var or default to production
const getCurrentEnvironment = () => {
  const env = process.env.VITE_PADDLE_ENVIRONMENT?.toLowerCase();
  return env === 'sandbox' ? 'sandbox' : 'production';
};

// Make API request to Paddle
function makeRequest(environment, method, path, data = null) {
  const config = ENVIRONMENTS[environment];
  if (!config) {
    throw new Error(`Unknown environment: ${environment}`);
  }

  if (!config.apiKey) {
    throw new Error(`API key not configured for ${environment} environment`);
  }

  return new Promise((resolve, reject) => {
    const url = new URL(path, config.apiEndpoint);
    
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(response);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${JSON.stringify(response)}`));
          }
        } catch (error) {
          reject(new Error(`Invalid JSON response: ${body}`));
        }
      });
    });

    req.on('error', (error) => reject(error));

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Test environment connection
async function testEnvironment(environment) {
  console.log(`🔍 Testing ${ENVIRONMENTS[environment].name} environment...`);
  
  try {
    const response = await makeRequest(environment, 'GET', '/products?per_page=1');
    console.log(`✅ ${ENVIRONMENTS[environment].name} API connection successful`);
    console.log(`📊 Found ${response.meta?.pagination?.total || 0} products`);
    return true;
  } catch (error) {
    console.error(`❌ ${ENVIRONMENTS[environment].name} API connection failed:`, error.message);
    return false;
  }
}

// List products for environment
async function listProducts(environment) {
  console.log(`📦 Listing products for ${ENVIRONMENTS[environment].name} environment...`);
  
  try {
    const response = await makeRequest(environment, 'GET', '/products?per_page=50');
    
    if (response.data && response.data.length > 0) {
      console.log(`\n📋 Found ${response.data.length} products:`);
      response.data.forEach(product => {
        console.log(`  • ${product.name} (${product.id})`);
        console.log(`    Status: ${product.status}, Tax Category: ${product.tax_category}`);
      });
    } else {
      console.log('📭 No products found');
    }
  } catch (error) {
    console.error(`❌ Failed to list products:`, error.message);
  }
}

// List prices for environment
async function listPrices(environment) {
  console.log(`💰 Listing prices for ${ENVIRONMENTS[environment].name} environment...`);
  
  try {
    const response = await makeRequest(environment, 'GET', '/prices?per_page=50&include=product');
    
    if (response.data && response.data.length > 0) {
      console.log(`\n💵 Found ${response.data.length} prices:`);
      response.data.forEach(price => {
        const product = price.product || {};
        console.log(`  • ${price.description} (${price.id})`);
        console.log(`    Product: ${product.name || 'Unknown'}`);
        console.log(`    Amount: ${price.unit_price.amount} ${price.unit_price.currency_code}`);
        if (price.billing_cycle) {
          console.log(`    Billing: ${price.billing_cycle.frequency} ${price.billing_cycle.interval}(s)`);
        }
      });
    } else {
      console.log('💸 No prices found');
    }
  } catch (error) {
    console.error(`❌ Failed to list prices:`, error.message);
  }
}

// Validate environment setup
function validateEnvironment(environment) {
  console.log(`🔧 Validating ${ENVIRONMENTS[environment].name} environment setup...`);
  
  const config = ENVIRONMENTS[environment];
  const errors = [];
  
  if (!config.apiKey) {
    errors.push(`Missing API key for ${environment}`);
  }
  
  if (!config.clientToken) {
    errors.push(`Missing client side token for ${environment}`);
  }
  
  // Validate token type
  if (config.clientToken) {
    const isLiveToken = config.clientToken.startsWith('live_');
    const isTestToken = config.clientToken.startsWith('test_');
    
    if (environment === 'production' && !isLiveToken) {
      errors.push('Production environment should use live token (starting with "live_")');
    }
    
    if (environment === 'sandbox' && !isTestToken) {
      errors.push('Sandbox environment should use test token (starting with "test_")');
    }
  }
  
  if (errors.length === 0) {
    console.log(`✅ ${ENVIRONMENTS[environment].name} environment is properly configured`);
    console.log(`   API Key: ${config.apiKey?.substring(0, 20)}...`);
    console.log(`   Client Token: ${config.clientToken?.substring(0, 15)}...`);
    console.log(`   API Endpoint: ${config.apiEndpoint}`);
    return true;
  } else {
    console.log(`❌ ${ENVIRONMENTS[environment].name} environment has configuration issues:`);
    errors.forEach(error => console.log(`   • ${error}`));
    return false;
  }
}

// Main function
async function main() {
  const [,, command, targetEnvironment] = process.argv;
  const environment = targetEnvironment || getCurrentEnvironment();
  
  if (!ENVIRONMENTS[environment]) {
    console.error(`❌ Unknown environment: ${environment}`);
    console.log('Available environments: production, sandbox');
    process.exit(1);
  }
  
  console.log(`🌍 Current environment: ${getCurrentEnvironment()}`);
  console.log(`🎯 Target environment: ${environment}\n`);
  
  switch (command) {
    case 'test':
      await testEnvironment(environment);
      break;
      
    case 'list-products':
      await listProducts(environment);
      break;
      
    case 'list-prices':
      await listPrices(environment);
      break;
      
    case 'validate':
      validateEnvironment(environment);
      break;
      
    case 'validate-all':
      console.log('🔧 Validating all environments...\n');
      validateEnvironment('production');
      console.log('');
      validateEnvironment('sandbox');
      break;
      
    case 'test-all':
      console.log('🔍 Testing all environments...\n');
      await testEnvironment('production');
      console.log('');
      await testEnvironment('sandbox');
      break;
      
    default:
      console.log('🚀 Paddle Environment Manager');
      console.log('\nUsage: node scripts/paddle-environment-manager.cjs [command] [environment]');
      console.log('\nCommands:');
      console.log('  test              - Test API connection');
      console.log('  list-products     - List all products');
      console.log('  list-prices       - List all prices');
      console.log('  validate          - Validate environment configuration');
      console.log('  validate-all      - Validate all environments');
      console.log('  test-all          - Test all environments');
      console.log('\nEnvironments:');
      console.log('  production        - Production environment');
      console.log('  sandbox           - Sandbox environment');
      console.log('\nExamples:');
      console.log('  node scripts/paddle-environment-manager.cjs test sandbox');
      console.log('  node scripts/paddle-environment-manager.cjs list-products production');
      console.log('  node scripts/paddle-environment-manager.cjs validate-all');
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  });
}

module.exports = { makeRequest, testEnvironment, listProducts, listPrices, validateEnvironment };
