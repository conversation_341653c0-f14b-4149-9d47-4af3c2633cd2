import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { db } from '@/lib/firebase';
import { collection, getDocs, doc, setDoc, addDoc, updateDoc, deleteDoc, query, where, Timestamp } from 'firebase/firestore';
import { toast } from 'react-hot-toast';
import { Edit, Trash, Plus, Search, Tag, Percent, Calendar, Check, X, RefreshCw } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export interface CouponCode {
  id: string;
  code: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  validFrom: string;
  validUntil: string;
  maxUses: number;
  currentUses: number;
  isActive: boolean;
  applicablePlans: string[];
  createdAt: string;
  updatedAt: string;
  minPurchaseAmount?: number;
  description?: string;
}

interface CouponFormData {
  code: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  validFrom: string;
  validUntil: string;
  maxUses: number;
  isActive: boolean;
  applicablePlans: string[];
  minPurchaseAmount?: number;
  description?: string;
}

export function CouponCodeManagement() {
  // State variables
  const [coupons, setCoupons] = useState<CouponCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingCoupon, setEditingCoupon] = useState<CouponCode | null>(null);
  const [formData, setFormData] = useState<CouponFormData>({
    code: '',
    discountType: 'percentage',
    discountValue: 10,
    validFrom: new Date().toISOString().split('T')[0],
    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    maxUses: 100,
    isActive: true,
    applicablePlans: ['Pay-As-You-Go', 'Pro', 'Enterprise'],
    minPurchaseAmount: 0,
    description: ''
  });

  // Fetch coupons on component mount
  useEffect(() => {
    fetchCoupons();
  }, []);

  // Filter coupons based on search query
  const filteredCoupons = coupons.filter(coupon =>
    coupon.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
    coupon.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Fetch coupons from Firestore
  const fetchCoupons = async () => {
    setLoading(true);
    try {
      const couponsSnapshot = await getDocs(collection(db, 'coupons'));
      const couponsData = couponsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as CouponCode[];

      setCoupons(couponsData);
    } catch (error) {
      console.error('Error fetching coupons:', error);
      toast.error('Failed to load coupon codes');
    } finally {
      setLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;

    if (type === 'number') {
      setFormData(prev => ({
        ...prev,
        [name]: parseFloat(value)
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Handle applicable plans change
  const handlePlanChange = (plan: string) => {
    setFormData(prev => {
      const currentPlans = [...prev.applicablePlans];

      if (currentPlans.includes(plan)) {
        return {
          ...prev,
          applicablePlans: currentPlans.filter(p => p !== plan)
        };
      } else {
        return {
          ...prev,
          applicablePlans: [...currentPlans, plan]
        };
      }
    });
  };

  // Open dialog for creating a new coupon
  const handleCreateCoupon = () => {
    setEditingCoupon(null);
    setFormData({
      code: '',
      discountType: 'percentage',
      discountValue: 10,
      validFrom: new Date().toISOString().split('T')[0],
      validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      maxUses: 100,
      isActive: true,
      applicablePlans: ['Pay-As-You-Go', 'Pro', 'Enterprise'],
      minPurchaseAmount: 0,
      description: ''
    });
    setIsDialogOpen(true);
  };

  // Open dialog for editing an existing coupon
  const handleEditCoupon = (coupon: CouponCode) => {
    setEditingCoupon(coupon);
    setFormData({
      code: coupon.code,
      discountType: coupon.discountType,
      discountValue: coupon.discountValue,
      validFrom: new Date(coupon.validFrom).toISOString().split('T')[0],
      validUntil: new Date(coupon.validUntil).toISOString().split('T')[0],
      maxUses: coupon.maxUses,
      isActive: coupon.isActive,
      applicablePlans: coupon.applicablePlans,
      minPurchaseAmount: coupon.minPurchaseAmount || 0,
      description: coupon.description || ''
    });
    setIsDialogOpen(true);
  };

  // Handle coupon deletion
  const handleDeleteCoupon = async (coupon: CouponCode) => {
    if (window.confirm(`Are you sure you want to delete the coupon code "${coupon.code}"?`)) {
      try {
        await deleteDoc(doc(db, 'coupons', coupon.id));
        toast.success('Coupon code deleted successfully');
        setCoupons(prev => prev.filter(c => c.id !== coupon.id));
      } catch (error) {
        console.error('Error deleting coupon:', error);
        toast.error('Failed to delete coupon code');
      }
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.code.trim()) {
      toast.error('Coupon code is required');
      return;
    }

    if (formData.discountValue <= 0) {
      toast.error('Discount value must be greater than 0');
      return;
    }

    if (formData.discountType === 'percentage' && formData.discountValue > 100) {
      toast.error('Percentage discount cannot exceed 100%');
      return;
    }

    if (new Date(formData.validUntil) < new Date(formData.validFrom)) {
      toast.error('End date must be after start date');
      return;
    }

    if (formData.applicablePlans.length === 0) {
      toast.error('Select at least one applicable plan');
      return;
    }

    setLoading(true);
    try {
      const timestamp = new Date().toISOString();

      if (editingCoupon) {
        // Update existing coupon
        const couponRef = doc(db, 'coupons', editingCoupon.id);
        await updateDoc(couponRef, {
          ...formData,
          updatedAt: timestamp
        });

        toast.success('Coupon code updated successfully');

        // Update local state
        setCoupons(prev =>
          prev.map(coupon =>
            coupon.id === editingCoupon.id
              ? {
                  ...coupon,
                  ...formData,
                  updatedAt: timestamp
                }
              : coupon
          )
        );
      } else {
        // Check if coupon code already exists
        const codeQuery = query(
          collection(db, 'coupons'),
          where('code', '==', formData.code.toUpperCase())
        );
        const existingCodes = await getDocs(codeQuery);

        if (!existingCodes.empty) {
          toast.error('This coupon code already exists');
          setLoading(false);
          return;
        }

        // Create new coupon
        const newCoupon = {
          ...formData,
          code: formData.code.toUpperCase(),
          currentUses: 0,
          createdAt: timestamp,
          updatedAt: timestamp
        };

        // Use addDoc instead of setDoc to get a document reference with an ID
        const docRef = await addDoc(collection(db, 'coupons'), newCoupon);

        toast.success('Coupon code created successfully');

        // Update local state with the new coupon
        const newCouponWithId = {
          id: docRef.id,
          ...newCoupon
        } as CouponCode;

        setCoupons(prev => [...prev, newCouponWithId]);
      }

      // Close the dialog
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Error saving coupon:', error);
      toast.error('Failed to save coupon code');
    } finally {
      setLoading(false);
    }
  };

  // Generate a random coupon code
  const generateRandomCode = () => {
    const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    setFormData(prev => ({ ...prev, code: result }));
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-white">Coupon Code Management</h2>
          <p className="text-gray-400 mt-1">Create and manage discount coupon codes</p>
        </div>
        <div className="flex space-x-3">
          <Button
            className="bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700"
            onClick={fetchCoupons}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700"
            onClick={handleCreateCoupon}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Coupon
          </Button>
        </div>
      </div>

      <Card className="bg-gray-800 border-gray-700 text-white">
        <CardHeader className="pb-3">
          <CardTitle>Coupon Codes</CardTitle>
          <CardDescription className="text-gray-400">
            Manage discount coupons for your subscription plans
          </CardDescription>
          <div className="relative mt-2">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search coupons..."
              className="pl-8 bg-gray-700 border-gray-600 text-white"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
            </div>
          ) : filteredCoupons.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              {searchQuery ? 'No coupons match your search' : 'No coupons found. Create your first coupon!'}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="border-gray-700">
                    <TableHead className="text-gray-300">Code</TableHead>
                    <TableHead className="text-gray-300">Discount</TableHead>
                    <TableHead className="text-gray-300">Valid Period</TableHead>
                    <TableHead className="text-gray-300">Usage</TableHead>
                    <TableHead className="text-gray-300">Status</TableHead>
                    <TableHead className="text-gray-300">Plans</TableHead>
                    <TableHead className="text-gray-300 text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCoupons.map((coupon) => (
                    <TableRow key={coupon.id} className="border-gray-700 hover:bg-gray-700/50">
                      <TableCell className="font-medium text-white">
                        <div className="flex items-center">
                          <Tag className="h-4 w-4 mr-2 text-purple-400" />
                          {coupon.code}
                        </div>
                        {coupon.description && (
                          <div className="text-xs text-gray-400 mt-1">{coupon.description}</div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Percent className="h-4 w-4 mr-2 text-green-400" />
                          {coupon.discountType === 'percentage'
                            ? `${coupon.discountValue}%`
                            : `$${coupon.discountValue.toFixed(2)}`}
                        </div>
                        {coupon.minPurchaseAmount > 0 && (
                          <div className="text-xs text-gray-400 mt-1">
                            Min: ${coupon.minPurchaseAmount.toFixed(2)}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-blue-400" />
                          <div>
                            <div className="text-sm">
                              {new Date(coupon.validFrom).toLocaleDateString()}
                            </div>
                            <div className="text-xs text-gray-400">
                              to {new Date(coupon.validUntil).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {coupon.currentUses} / {coupon.maxUses}
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-1.5 mt-1">
                          <div
                            className="bg-purple-500 h-1.5 rounded-full"
                            style={{ width: `${Math.min(100, (coupon.currentUses / coupon.maxUses) * 100)}%` }}
                          ></div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          coupon.isActive ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'
                        }`}>
                          {coupon.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {coupon.applicablePlans.map(plan => (
                            <span
                              key={plan}
                              className="px-1.5 py-0.5 bg-gray-700 rounded text-xs text-gray-300"
                            >
                              {plan}
                            </span>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditCoupon(coupon)}
                          className="text-gray-400 hover:text-white hover:bg-gray-700"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteCoupon(coupon)}
                          className="text-gray-400 hover:text-red-400 hover:bg-gray-700"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Coupon Form Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="bg-gray-800 text-white border-gray-700 max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingCoupon ? 'Edit Coupon Code' : 'Create New Coupon Code'}
            </DialogTitle>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4 mt-4">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="bg-gray-700 text-gray-400">
                <TabsTrigger value="basic" className="data-[state=active]:bg-gray-600 data-[state=active]:text-white">
                  Basic Info
                </TabsTrigger>
                <TabsTrigger value="restrictions" className="data-[state=active]:bg-gray-600 data-[state=active]:text-white">
                  Restrictions
                </TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4 mt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="code">Coupon Code</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="code"
                        name="code"
                        value={formData.code}
                        onChange={handleInputChange}
                        className="bg-gray-700 border-gray-600 text-white"
                        placeholder="e.g. SUMMER20"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={generateRandomCode}
                        className="border-gray-600 text-gray-300 hover:bg-gray-700"
                      >
                        Generate
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="isActive">Status</Label>
                    <div className="flex items-center space-x-2 h-10 px-3">
                      <Switch
                        id="isActive"
                        checked={formData.isActive}
                        onCheckedChange={(checked) => handleSwitchChange('isActive', checked)}
                      />
                      <Label htmlFor="isActive" className="cursor-pointer">
                        {formData.isActive ? 'Active' : 'Inactive'}
                      </Label>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="discountType">Discount Type</Label>
                    <Select
                      value={formData.discountType}
                      onValueChange={(value) => handleSelectChange('discountType', value)}
                    >
                      <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                        <SelectValue placeholder="Select discount type" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-700 border-gray-600 text-white">
                        <SelectItem value="percentage">Percentage (%)</SelectItem>
                        <SelectItem value="fixed">Fixed Amount ($)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="discountValue">
                      {formData.discountType === 'percentage' ? 'Discount Percentage' : 'Discount Amount'}
                    </Label>
                    <div className="relative">
                      <Input
                        id="discountValue"
                        name="discountValue"
                        type="number"
                        min={0}
                        max={formData.discountType === 'percentage' ? 100 : undefined}
                        value={formData.discountValue}
                        onChange={handleInputChange}
                        className="bg-gray-700 border-gray-600 text-white pl-8"
                      />
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                        {formData.discountType === 'percentage' ? '%' : '$'}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="validFrom">Valid From</Label>
                    <Input
                      id="validFrom"
                      name="validFrom"
                      type="date"
                      value={formData.validFrom}
                      onChange={handleInputChange}
                      className="bg-gray-700 border-gray-600 text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="validUntil">Valid Until</Label>
                    <Input
                      id="validUntil"
                      name="validUntil"
                      type="date"
                      value={formData.validUntil}
                      onChange={handleInputChange}
                      className="bg-gray-700 border-gray-600 text-white"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Input
                    id="description"
                    name="description"
                    value={formData.description || ''}
                    onChange={handleInputChange}
                    className="bg-gray-700 border-gray-600 text-white"
                    placeholder="e.g. Summer sale discount"
                  />
                </div>
              </TabsContent>

              <TabsContent value="restrictions" className="space-y-4 mt-4">
                <div className="space-y-2">
                  <Label htmlFor="maxUses">Maximum Uses</Label>
                  <Input
                    id="maxUses"
                    name="maxUses"
                    type="number"
                    min={1}
                    value={formData.maxUses}
                    onChange={handleInputChange}
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="minPurchaseAmount">Minimum Purchase Amount (Optional)</Label>
                  <div className="relative">
                    <Input
                      id="minPurchaseAmount"
                      name="minPurchaseAmount"
                      type="number"
                      min={0}
                      value={formData.minPurchaseAmount || 0}
                      onChange={handleInputChange}
                      className="bg-gray-700 border-gray-600 text-white pl-8"
                    />
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                      $
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Applicable Plans</Label>
                  <div className="grid grid-cols-3 gap-2">
                    {['Pay-As-You-Go', 'Pro', 'Enterprise'].map(plan => (
                      <div
                        key={plan}
                        className={`
                          flex items-center justify-between p-3 rounded-lg cursor-pointer border
                          ${formData.applicablePlans.includes(plan)
                            ? 'bg-purple-500/20 border-purple-500/50 text-white'
                            : 'bg-gray-700 border-gray-600 text-gray-400 hover:bg-gray-600'}
                        `}
                        onClick={() => handlePlanChange(plan)}
                      >
                        <span>{plan}</span>
                        {formData.applicablePlans.includes(plan) && (
                          <Check className="h-4 w-4 text-purple-400" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <DialogFooter className="mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
                className="border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    {editingCoupon ? 'Update Coupon' : 'Create Coupon'}
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
