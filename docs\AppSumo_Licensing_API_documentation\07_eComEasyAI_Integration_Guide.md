# AppSumo Licensing API (v2) - eComEasyAI Integration Guide

**Created for:** eComEasyAI Application Integration  
**Date:** 2025-07-02  
**Purpose:** Complete integration guide for implementing AppSumo Licensing API v2 in eComEasyAI

---

## Overview

This guide provides step-by-step instructions for integrating AppSumo Licensing API v2 into your eComEasyAI application. The integration will enable seamless license management for AppSumo customers.

## Integration Architecture

```
AppSumo Purchase → Webhook → eComEasyAI Backend → OAuth Flow → User Account Creation/Login
```

## Prerequisites

- [ ] eComEasyAI application approved on AppSumo
- [ ] Access to AppSumo Partner Portal
- [ ] Backend infrastructure capable of handling webhooks
- [ ] OAuth implementation capability
- [ ] Database for license management

---

## Step 1: Database Schema Design

### Recommended Tables

#### 1. AppSumo Licenses Table
```sql
CREATE TABLE appsumo_licenses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    license_key VARCHAR(255) UNIQUE NOT NULL,
    user_id BIGINT NULL,
    status ENUM('inactive', 'active', 'deactivated') NOT NULL,
    tier INT NOT NULL DEFAULT 1,
    prev_license_key VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    activated_at TIMESTAMP NULL,
    deactivated_at TIMESTAMP NULL,
    
    INDEX idx_license_key (license_key),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);
```

#### 2. AppSumo Events Table (for audit trail)
```sql
CREATE TABLE appsumo_events (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    license_key VARCHAR(255) NOT NULL,
    event_type ENUM('purchase', 'activate', 'upgrade', 'downgrade', 'deactivate') NOT NULL,
    event_data JSON,
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    webhook_signature VARCHAR(255),
    
    INDEX idx_license_key (license_key),
    INDEX idx_event_type (event_type),
    INDEX idx_processed_at (processed_at)
);
```

#### 3. User Subscription Tiers Table
```sql
CREATE TABLE user_subscription_tiers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    tier_level INT NOT NULL,
    source ENUM('appsumo', 'direct', 'other') NOT NULL,
    license_key VARCHAR(255) NULL,
    features JSON,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_user_appsumo (user_id, source, license_key),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

---

## Step 2: Environment Configuration

### Environment Variables
```env
# AppSumo Configuration
APPSUMO_CLIENT_ID=your_client_id_here
APPSUMO_CLIENT_SECRET=your_client_secret_here
APPSUMO_API_KEY=your_api_key_here
APPSUMO_WEBHOOK_SECRET=your_webhook_secret_here

# URLs
APPSUMO_OAUTH_REDIRECT_URL=https://ecomeasy.ai/auth/appsumo/callback
APPSUMO_WEBHOOK_URL=https://ecomeasy.ai/webhooks/appsumo

# AppSumo API Endpoints
APPSUMO_TOKEN_URL=https://appsumo.com/openid/token/
APPSUMO_LICENSE_URL=https://appsumo.com/openid/license_key/
APPSUMO_API_BASE_URL=https://api.licensing.appsumo.com/v2/
```

---

## Step 3: Webhook Implementation

### Webhook Endpoint (Node.js/Express Example)
```javascript
const express = require('express');
const crypto = require('crypto');
const router = express.Router();

// Webhook endpoint for AppSumo
router.post('/webhooks/appsumo', async (req, res) => {
    try {
        // 1. Verify webhook signature
        const signature = req.headers['x-appsumo-signature'];
        const timestamp = req.headers['x-appsumo-timestamp'];
        const body = JSON.stringify(req.body);
        
        if (!verifyWebhookSignature(signature, timestamp, body)) {
            return res.status(401).json({ error: 'Invalid signature' });
        }

        // 2. Extract webhook data
        const {
            license_key,
            event,
            license_status,
            tier,
            prev_license_key,
            test,
            extra
        } = req.body;

        // 3. Skip processing for test webhooks but still respond
        if (test) {
            return res.status(200).json({
                success: true,
                event: event,
                message: 'Test webhook received'
            });
        }

        // 4. Process webhook based on event type
        await processAppSumoWebhook({
            license_key,
            event,
            license_status,
            tier,
            prev_license_key,
            extra
        });

        // 5. Log event for audit trail
        await logAppSumoEvent(req.body);

        // 6. Return success response
        res.status(200).json({
            success: true,
            event: event,
            message: 'Webhook processed successfully'
        });

    } catch (error) {
        console.error('AppSumo webhook error:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error'
        });
    }
});

// Verify webhook signature using HMAC SHA256
function verifyWebhookSignature(signature, timestamp, body) {
    const apiKey = process.env.APPSUMO_API_KEY;
    const message = `${timestamp}${body}`;
    
    const expectedSignature = crypto
        .createHmac('sha256', apiKey)
        .update(message)
        .digest('hex');
    
    return signature === expectedSignature;
}

// Process different webhook events
async function processAppSumoWebhook(data) {
    const { license_key, event, license_status, tier, prev_license_key } = data;

    switch (event) {
        case 'purchase':
            await handlePurchaseEvent(license_key, tier);
            break;
        case 'activate':
            await handleActivateEvent(license_key, tier);
            break;
        case 'upgrade':
            await handleUpgradeEvent(license_key, prev_license_key, tier);
            break;
        case 'downgrade':
            await handleDowngradeEvent(license_key, prev_license_key, tier);
            break;
        case 'deactivate':
            await handleDeactivateEvent(license_key);
            break;
        default:
            console.warn(`Unknown event type: ${event}`);
    }
}

// Handle purchase event
async function handlePurchaseEvent(license_key, tier) {
    // Create license record with inactive status
    await db.query(`
        INSERT INTO appsumo_licenses (license_key, status, tier)
        VALUES (?, 'inactive', ?)
        ON DUPLICATE KEY UPDATE tier = VALUES(tier)
    `, [license_key, tier]);
}

// Handle activate event
async function handleActivateEvent(license_key, tier) {
    // Update license status to active
    await db.query(`
        UPDATE appsumo_licenses 
        SET status = 'active', activated_at = NOW()
        WHERE license_key = ?
    `, [license_key]);
}

// Handle upgrade event
async function handleUpgradeEvent(license_key, prev_license_key, tier) {
    // Deactivate old license and create new one
    await db.transaction(async (trx) => {
        // Deactivate previous license
        if (prev_license_key) {
            await trx.query(`
                UPDATE appsumo_licenses 
                SET status = 'deactivated', deactivated_at = NOW()
                WHERE license_key = ?
            `, [prev_license_key]);
        }
        
        // Create new license
        await trx.query(`
            INSERT INTO appsumo_licenses (license_key, status, tier, prev_license_key)
            VALUES (?, 'inactive', ?, ?)
        `, [license_key, tier, prev_license_key]);
    });
}

// Handle downgrade event
async function handleDowngradeEvent(license_key, prev_license_key, tier) {
    // Similar to upgrade but with lower tier
    await handleUpgradeEvent(license_key, prev_license_key, tier);
}

// Handle deactivate event
async function handleDeactivateEvent(license_key) {
    // Deactivate license and user access
    await db.query(`
        UPDATE appsumo_licenses 
        SET status = 'deactivated', deactivated_at = NOW()
        WHERE license_key = ?
    `, [license_key]);
    
    // Update user subscription
    await db.query(`
        UPDATE user_subscription_tiers 
        SET expires_at = NOW()
        WHERE license_key = ? AND source = 'appsumo'
    `, [license_key]);
}

module.exports = router;
```

---

## Step 4: OAuth Implementation

### OAuth Callback Endpoint
```javascript
// OAuth callback endpoint
router.get('/auth/appsumo/callback', async (req, res) => {
    try {
        const { code, error } = req.query;
        
        if (error) {
            return res.redirect('/auth/error?message=' + encodeURIComponent(error));
        }
        
        if (!code) {
            return res.redirect('/auth/error?message=No authorization code received');
        }

        // 1. Exchange code for access token
        const tokenData = await exchangeCodeForToken(code);
        
        // 2. Fetch license information
        const licenseData = await fetchLicenseData(tokenData.access_token);
        
        // 3. Handle user authentication/creation
        const user = await handleUserAuthentication(licenseData);
        
        // 4. Activate license
        await activateLicense(licenseData.license_key, user.id);
        
        // 5. Set user session and redirect
        req.session.userId = user.id;
        res.redirect('/dashboard?appsumo=activated');
        
    } catch (error) {
        console.error('OAuth callback error:', error);
        res.redirect('/auth/error?message=Authentication failed');
    }
});

// Exchange authorization code for access token
async function exchangeCodeForToken(code) {
    const response = await fetch(process.env.APPSUMO_TOKEN_URL, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            client_id: process.env.APPSUMO_CLIENT_ID,
            client_secret: process.env.APPSUMO_CLIENT_SECRET,
            code: code,
            redirect_uri: process.env.APPSUMO_OAUTH_REDIRECT_URL,
            grant_type: 'authorization_code'
        })
    });
    
    if (!response.ok) {
        throw new Error('Failed to exchange code for token');
    }
    
    return await response.json();
}

// Fetch license data using access token
async function fetchLicenseData(accessToken) {
    const response = await fetch(
        `${process.env.APPSUMO_LICENSE_URL}?access_token=${accessToken}`
    );
    
    if (!response.ok) {
        throw new Error('Failed to fetch license data');
    }
    
    return await response.json();
}

// Handle user authentication/creation
async function handleUserAuthentication(licenseData) {
    const { license_key, status } = licenseData;
    
    // Check if license exists and is linked to a user
    const existingLicense = await db.query(`
        SELECT al.*, u.* FROM appsumo_licenses al
        LEFT JOIN users u ON al.user_id = u.id
        WHERE al.license_key = ?
    `, [license_key]);
    
    if (existingLicense.length > 0 && existingLicense[0].user_id) {
        // Existing user - return user data
        return {
            id: existingLicense[0].user_id,
            email: existingLicense[0].email,
            name: existingLicense[0].name
        };
    } else {
        // New user - redirect to registration
        // Store license_key in session for registration process
        return { needsRegistration: true, license_key };
    }
}

// Activate license for user
async function activateLicense(license_key, userId) {
    await db.transaction(async (trx) => {
        // Update license with user ID
        await trx.query(`
            UPDATE appsumo_licenses 
            SET user_id = ?, status = 'active', activated_at = NOW()
            WHERE license_key = ?
        `, [userId, license_key]);
        
        // Create/update user subscription
        const license = await trx.query(`
            SELECT tier FROM appsumo_licenses WHERE license_key = ?
        `, [license_key]);
        
        await trx.query(`
            INSERT INTO user_subscription_tiers (user_id, tier_level, source, license_key, features)
            VALUES (?, ?, 'appsumo', ?, ?)
            ON DUPLICATE KEY UPDATE 
                tier_level = VALUES(tier_level),
                features = VALUES(features),
                updated_at = NOW()
        `, [userId, license[0].tier, license_key, getFeaturesByTier(license[0].tier)]);
    });
}

// Get features by tier
function getFeaturesByTier(tier) {
    const tierFeatures = {
        1: {
            max_products: 100,
            ai_generations_per_month: 1000,
            advanced_analytics: false,
            priority_support: false
        },
        2: {
            max_products: 500,
            ai_generations_per_month: 5000,
            advanced_analytics: true,
            priority_support: false
        },
        3: {
            max_products: -1, // unlimited
            ai_generations_per_month: -1, // unlimited
            advanced_analytics: true,
            priority_support: true
        }
    };
    
    return JSON.stringify(tierFeatures[tier] || tierFeatures[1]);
}
```

---

## Step 5: User Registration Flow

### Registration Page for AppSumo Users
```javascript
// Registration endpoint for AppSumo users
router.post('/auth/appsumo/register', async (req, res) => {
    try {
        const { license_key, email, password, name } = req.body;
        
        // Validate license key
        const license = await validateLicenseKey(license_key);
        if (!license) {
            return res.status(400).json({ error: 'Invalid license key' });
        }
        
        // Check if email already exists
        const existingUser = await db.query(`
            SELECT id FROM users WHERE email = ?
        `, [email]);
        
        if (existingUser.length > 0) {
            return res.status(400).json({ error: 'Email already registered' });
        }
        
        // Create new user
        const hashedPassword = await bcrypt.hash(password, 10);
        const userId = await db.query(`
            INSERT INTO users (email, password, name, created_at)
            VALUES (?, ?, ?, NOW())
        `, [email, hashedPassword, name]);
        
        // Link license to user
        await activateLicense(license_key, userId.insertId);
        
        // Set session and respond
        req.session.userId = userId.insertId;
        res.json({ success: true, redirect: '/dashboard?welcome=appsumo' });
        
    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({ error: 'Registration failed' });
    }
});

async function validateLicenseKey(license_key) {
    const license = await db.query(`
        SELECT * FROM appsumo_licenses 
        WHERE license_key = ? AND user_id IS NULL
    `, [license_key]);
    
    return license.length > 0 ? license[0] : null;
}
```

---

## Step 6: License Management Service

### License Service Class
```javascript
class AppSumoLicenseService {
    constructor() {
        this.apiKey = process.env.APPSUMO_API_KEY;
        this.baseUrl = process.env.APPSUMO_API_BASE_URL;
    }
    
    // Verify license status with AppSumo API
    async verifyLicenseStatus(license_key) {
        try {
            const response = await fetch(`${this.baseUrl}licenses/${license_key}`, {
                headers: {
                    'X-AppSumo-Licensing-Key': this.apiKey
                }
            });
            
            if (!response.ok) {
                throw new Error('Failed to verify license');
            }
            
            return await response.json();
        } catch (error) {
            console.error('License verification error:', error);
            return null;
        }
    }
    
    // Sync all licenses with AppSumo
    async syncAllLicenses() {
        try {
            let page = 1;
            const limit = 100;
            let hasMore = true;
            
            while (hasMore) {
                const response = await fetch(
                    `${this.baseUrl}licenses?page=${page}&limit=${limit}`,
                    {
                        headers: {
                            'X-AppSumo-Licensing-Key': this.apiKey
                        }
                    }
                );
                
                const data = await response.json();
                
                for (const license of data.items) {
                    await this.syncLicense(license);
                }
                
                hasMore = data.items.length === limit;
                page++;
            }
            
        } catch (error) {
            console.error('License sync error:', error);
        }
    }
    
    // Sync individual license
    async syncLicense(licenseData) {
        const { license_key, status, tier, created_at, updated_at } = licenseData;
        
        await db.query(`
            INSERT INTO appsumo_licenses (license_key, status, tier, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
                status = VALUES(status),
                tier = VALUES(tier),
                updated_at = VALUES(updated_at)
        `, [license_key, status, tier, created_at, updated_at]);
    }
    
    // Check user's license status
    async getUserLicenseStatus(userId) {
        const license = await db.query(`
            SELECT al.*, ust.features 
            FROM appsumo_licenses al
            JOIN user_subscription_tiers ust ON al.license_key = ust.license_key
            WHERE al.user_id = ? AND al.status = 'active'
            ORDER BY al.tier DESC
            LIMIT 1
        `, [userId]);
        
        return license.length > 0 ? license[0] : null;
    }
}

module.exports = AppSumoLicenseService;
```

---

## Step 7: Frontend Integration

### React Component for AppSumo Integration
```jsx
import React, { useState, useEffect } from 'react';

const AppSumoIntegration = () => {
    const [licenseStatus, setLicenseStatus] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        checkLicenseStatus();
    }, []);

    const checkLicenseStatus = async () => {
        try {
            const response = await fetch('/api/user/license-status');
            const data = await response.json();
            setLicenseStatus(data);
        } catch (error) {
            console.error('Failed to check license status:', error);
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return <div>Checking license status...</div>;
    }

    if (!licenseStatus) {
        return (
            <div className="appsumo-integration">
                <h3>AppSumo License</h3>
                <p>No active AppSumo license found.</p>
                <a 
                    href="https://appsumo.com/products/ecomeasyai" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="btn btn-primary"
                >
                    Get eComEasyAI on AppSumo
                </a>
            </div>
        );
    }

    return (
        <div className="appsumo-integration">
            <h3>AppSumo License Active</h3>
            <div className="license-details">
                <p><strong>Tier:</strong> {licenseStatus.tier}</p>
                <p><strong>Status:</strong> {licenseStatus.status}</p>
                <p><strong>Features:</strong></p>
                <ul>
                    {Object.entries(JSON.parse(licenseStatus.features)).map(([key, value]) => (
                        <li key={key}>
                            {key.replace(/_/g, ' ')}: {value === -1 ? 'Unlimited' : value.toString()}
                        </li>
                    ))}
                </ul>
            </div>
        </div>
    );
};

export default AppSumoIntegration;
```

---

## Step 8: Testing and Deployment

### Testing Checklist

- [ ] Webhook URL validation in AppSumo Partner Portal
- [ ] OAuth Redirect URL validation in AppSumo Partner Portal
- [ ] Test webhook events (purchase, activate, upgrade, downgrade, deactivate)
- [ ] Test OAuth flow end-to-end
- [ ] Test user registration with AppSumo license
- [ ] Test license verification and feature access
- [ ] Test webhook signature verification
- [ ] Test error handling and edge cases

### Deployment Steps

1. **Configure Production URLs**
   - Update webhook URL in AppSumo Partner Portal
   - Update OAuth redirect URL in AppSumo Partner Portal
   - Ensure HTTPS is enabled for all endpoints

2. **Environment Variables**
   - Set all AppSumo credentials in production environment
   - Verify API keys are correct

3. **Database Migration**
   - Run database migrations for AppSumo tables
   - Set up proper indexes for performance

4. **Monitoring**
   - Set up logging for webhook events
   - Monitor license synchronization
   - Set up alerts for failed webhook processing

---

## Step 9: Maintenance and Monitoring

### Regular Tasks

1. **Daily License Sync**
   ```javascript
   // Cron job to sync licenses daily
   cron.schedule('0 2 * * *', async () => {
       const licenseService = new AppSumoLicenseService();
       await licenseService.syncAllLicenses();
   });
   ```

2. **Webhook Health Check**
   ```javascript
   // Monitor webhook processing
   router.get('/health/appsumo-webhook', async (req, res) => {
       const recentEvents = await db.query(`
           SELECT COUNT(*) as count 
           FROM appsumo_events 
           WHERE processed_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
       `);
       
       res.json({
           status: 'healthy',
           recent_events: recentEvents[0].count
       });
   });
   ```

3. **License Status Monitoring**
   ```javascript
   // Check for license discrepancies
   async function auditLicenses() {
       const localLicenses = await db.query(`
           SELECT license_key, status FROM appsumo_licenses
       `);
       
       for (const license of localLicenses) {
           const appsumoStatus = await licenseService.verifyLicenseStatus(license.license_key);
           if (appsumoStatus && appsumoStatus.status !== license.status) {
               console.warn(`License status mismatch: ${license.license_key}`);
               // Update local status
               await db.query(`
                   UPDATE appsumo_licenses 
                   SET status = ? 
                   WHERE license_key = ?
               `, [appsumoStatus.status, license.license_key]);
           }
       }
   }
   ```

---

## Conclusion

This integration guide provides a complete implementation for AppSumo Licensing API v2 in your eComEasyAI application. The implementation includes:

- Secure webhook processing with signature verification
- Complete OAuth flow for user authentication
- Comprehensive license management
- User registration and account linking
- Frontend integration components
- Monitoring and maintenance procedures

Follow the steps in order and test thoroughly before deploying to production. Remember to keep your API keys secure and monitor the integration regularly for optimal performance.
