<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle Checkout Test</title>
    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Paddle Checkout Debug Test</h1>
        <p>This page tests Paddle checkout functionality with detailed debugging.</p>

        <div id="status" class="test-section">
            <h3>📊 Status</h3>
            <div id="paddle-status">Checking Paddle SDK...</div>
            <div id="environment-status">Checking environment...</div>
        </div>

        <div class="test-section">
            <h3>🧪 Test Buttons</h3>
            <button onclick="testSandboxCheckout()">Test Sandbox Checkout</button>
            <button onclick="testProductionCheckout()">Test Production Checkout</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h3>📝 Debug Log</h3>
            <div id="debug-log" class="log">Initializing...</div>
        </div>
    </div>

    <script>
        // Configuration
        const SANDBOX_CONFIG = {
            clientToken: 'test_412eaf108880b98ce3014ba7114',
            priceId: 'pri_01jxejv3jrz3qpfynwg325zr91',
            environment: 'sandbox'
        };

        const PRODUCTION_CONFIG = {
            clientToken: 'live_e9cde83444d96cefe02015737a3',
            priceId: 'pri_01jxbve4a77pt0545ntrvaea5t',
            environment: 'production'
        };

        let currentConfig = null;
        let debugLog = [];

        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            debugLog.push(logEntry);

            const logElement = document.getElementById('debug-log');
            logElement.textContent = debugLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;

            console.log(logEntry);
        }

        function clearLog() {
            debugLog = [];
            document.getElementById('debug-log').textContent = 'Log cleared.';
        }

        // Initialize Paddle
        function initializePaddle(config) {
            log(`Initializing Paddle for ${config.environment} environment`);
            log(`Using token: ${config.clientToken.substring(0, 20)}...`);

            try {
                if (!window.Paddle) {
                    throw new Error('Paddle SDK not loaded');
                }

                // Set environment for sandbox BEFORE Initialize
                if (config.environment === 'sandbox') {
                    log('🧪 Setting Paddle environment to sandbox');
                    window.Paddle.Environment.set('sandbox');
                }

                window.Paddle.Initialize({
                    token: config.clientToken
                });

                log(`✅ Paddle setup successful for ${config.environment}`);
                currentConfig = config;

                // Update status
                document.getElementById('paddle-status').innerHTML =
                    `<span style="color: green;">✅ Paddle SDK: Ready</span>`;
                document.getElementById('environment-status').innerHTML =
                    `<span style="color: blue;">🌍 Environment: ${config.environment}</span>`;

                return true;
            } catch (error) {
                log(`❌ Paddle setup failed: ${error.message}`, 'error');

                document.getElementById('paddle-status').innerHTML =
                    `<span style="color: red;">❌ Paddle SDK: Error</span>`;
                document.getElementById('environment-status').innerHTML =
                    `<span style="color: red;">❌ Environment: Failed</span>`;

                return false;
            }
        }

        // Test checkout
        function testCheckout(config) {
            if (!initializePaddle(config)) {
                return;
            }

            log(`🛒 Testing checkout for ${config.environment} environment`);

            const checkoutData = {
                items: [{ priceId: config.priceId, quantity: 1 }],
                customer: { email: '<EMAIL>' },
                customData: {
                    userId: 'test-user-123',
                    environment: config.environment,
                    testMode: config.environment === 'sandbox'
                },
                successUrl: `${window.location.origin}/payment/success?session_id={checkout.id}`
            };

            log('📋 Checkout data:');
            log(JSON.stringify(checkoutData, null, 2));

            try {
                log('🚀 Opening Paddle checkout...');

                // Add error event listener
                window.addEventListener('error', function(event) {
                    log(`❌ Global error: ${event.error?.message || event.message}`, 'error');
                    log(`📍 Source: ${event.filename}:${event.lineno}:${event.colno}`, 'error');
                });

                // Add unhandled promise rejection listener
                window.addEventListener('unhandledrejection', function(event) {
                    log(`❌ Unhandled promise rejection: ${event.reason}`, 'error');
                });

                window.Paddle.Checkout.open(checkoutData);
                log('✅ Checkout opened successfully');

            } catch (error) {
                log(`❌ Checkout failed: ${error.message}`, 'error');
                log(`📊 Error details: ${JSON.stringify(error)}`, 'error');

                // Additional debugging for 403 errors
                if (error.message && error.message.includes('403')) {
                    log('🚨 403 Forbidden Error Analysis:', 'error');
                    log('  - Client token may lack checkout permissions', 'error');
                    log('  - Verify token is active in Paddle dashboard', 'error');
                    log('  - Check if account has checkout restrictions', 'error');
                }
            }
        }

        // Test functions
        function testSandboxCheckout() {
            log('=== SANDBOX CHECKOUT TEST ===');
            testCheckout(SANDBOX_CONFIG);
        }

        function testProductionCheckout() {
            log('=== PRODUCTION CHECKOUT TEST ===');
            testCheckout(PRODUCTION_CONFIG);
        }

        // Initialize on page load
        window.addEventListener('load', function() {
            log('🔍 Paddle Checkout Debug Test initialized');
            log('📦 Paddle SDK loaded: ' + (window.Paddle ? 'YES' : 'NO'));

            if (window.Paddle) {
                log('🎯 Paddle SDK version: ' + (window.Paddle.version || 'Unknown'));
                log('⚙️ Available methods: ' + Object.keys(window.Paddle).join(', '));
            }

            // Set initial status
            if (window.Paddle) {
                document.getElementById('paddle-status').innerHTML =
                    '<span style="color: green;">✅ Paddle SDK: Loaded</span>';
            } else {
                document.getElementById('paddle-status').innerHTML =
                    '<span style="color: red;">❌ Paddle SDK: Not Loaded</span>';
            }
        });
    </script>
</body>
</html>