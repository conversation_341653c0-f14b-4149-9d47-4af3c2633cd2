# SSLCOMMERZ Production Deployment Guide

## 🎯 Current Status: ✅ READY FOR PRODUCTION

Your SSLCOMMERZ integration has passed all readiness checks and is ready to go live!

## 📋 Production Readiness Summary

### ✅ Configuration Status
- **Frontend Configuration**: ✅ Properly configured
- **Backend Configuration**: ✅ Properly configured  
- **Credentials Consistency**: ✅ Consistent across frontend and backend
- **API Connectivity**: ✅ Production API working
- **Environment**: 🚀 **PRODUCTION MODE ACTIVE**

### 🔐 Current Credentials
- **Store ID**: `httpsecomeasyai0live`
- **Environment**: Production (Live payments)
- **API Endpoint**: `https://securepay.sslcommerz.com/gwprocess/v4/api.php`
- **Validation URL**: `https://securepay.sslcommerz.com/validator/api/validationserverAPI.php`

## 🔄 Environment Management

### Quick Commands

```bash
# Check current status
node scripts/sslcommerz-environment-manager.cjs status

# Switch to production (live payments)
node scripts/sslcommerz-environment-manager.cjs production

# Switch to sandbox (test payments)
node scripts/sslcommerz-environment-manager.cjs sandbox

# Fix configuration issues
node scripts/sslcommerz-environment-manager.cjs fix

# Run production readiness check
node scripts/sslcommerz-production-setup.cjs check
```

### Environment Switching Guide

#### **To Activate Production Mode:**
```bash
node scripts/sslcommerz-environment-manager.cjs production
```
- ✅ Sets production credentials in both frontend and backend
- ✅ Enables live payment processing
- ⚠️ **Real money will be charged to customers**

#### **To Switch Back to Sandbox Mode:**
```bash
node scripts/sslcommerz-environment-manager.cjs sandbox
```
- ✅ Sets sandbox credentials for testing
- ✅ Safe for development and testing
- 💡 No real charges will be made

#### **To Check Current Environment:**
```bash
node scripts/sslcommerz-environment-manager.cjs status
```

## 🚀 Production Deployment Checklist

### Pre-Deployment Steps

- [x] **Environment Variables Configured**
  - Frontend (.env): Production credentials set
  - Backend (functions/.env): Production credentials set
  - Credentials consistency verified

- [x] **API Connectivity Tested**
  - Production API endpoint accessible
  - Authentication working
  - Session creation successful

- [x] **Configuration Validation**
  - All required parameters present
  - Store ID and password correct
  - Environment flags properly set

### Deployment Steps

#### 1. **Verify Current Configuration**
```bash
node scripts/sslcommerz-production-setup.cjs check
```
Should show: ✅ ALL CHECKS PASSED

#### 2. **Deploy Firebase Functions**
```bash
cd functions
npm run deploy
# or
firebase deploy --only functions
```

#### 3. **Deploy Frontend Application**
```bash
npm run build
# Deploy to your hosting platform (Vercel, Netlify, etc.)
```

#### 4. **Verify Webhook URLs**
Ensure these URLs are accessible from SSLCOMMERZ servers:
- Success: `https://your-functions-url/handleSSLCommerzSuccess`
- Fail: `https://your-functions-url/handleSSLCommerzFail`
- Cancel: `https://your-functions-url/handleSSLCommerzCancel`
- IPN: `https://your-functions-url/handleSSLCommerzIPN`

### Post-Deployment Verification

#### 5. **Test Payment Flow**
1. Create a test order with small amount (10 BDT)
2. Complete payment using real payment method
3. Verify successful payment processing
4. Check user account upgrade
5. Verify webhook delivery

#### 6. **Monitor Logs**
```bash
# Check Firebase Functions logs
firebase functions:log

# Check for any errors or issues
```

## 🔧 Configuration Details

### Environment Variables

#### Frontend (.env)
```bash
# SSLCOMMERZ Configuration
VITE_SSLCOMMERZ_STORE_ID="httpsecomeasyai0live"
VITE_SSLCOMMERZ_STORE_PASSWORD="6808DA135CE7539998"
VITE_SSLCOMMERZ_IS_LIVE=true  # true for production, false for sandbox
VITE_APP_URL="https://ecomeasy.ai"
VITE_LOCAL_APP_URL="http://localhost:5173"
```

#### Backend (functions/.env)
```bash
# SSLCOMMERZ Configuration
SSLCOMMERZ_STORE_ID="httpsecomeasyai0live"
SSLCOMMERZ_STORE_PASSWORD="6808DA135CE7539998"
SSLCOMMERZ_IS_LIVE=true  # true for production, false for sandbox

# Application URLs
APP_URL="https://ecomeasy.ai"
LOCAL_APP_URL="http://localhost:5173"
```

### How Environment Detection Works

The system determines production vs sandbox mode based on:

1. **Primary**: `SSLCOMMERZ_IS_LIVE` environment variable
   - `true` = Production mode (live payments)
   - `false` = Sandbox mode (test payments)

2. **Credentials**: Different store credentials for each environment
   - Production: `httpsecomeasyai0live` / `6808DA135CE7539998`
   - Sandbox: `testbox` / `qwerty`

3. **API Endpoints**: Automatically selected based on environment
   - Production: `https://securepay.sslcommerz.com/`
   - Sandbox: `https://sandbox.sslcommerz.com/`

## 🛠️ Operational Procedures

### Troubleshooting Environment Issues

#### Issue: "Store Credential Error"
```bash
# Check current configuration
node scripts/sslcommerz-environment-manager.cjs status

# Fix configuration inconsistencies
node scripts/sslcommerz-environment-manager.cjs fix

# Test API connectivity
node scripts/sslcommerz-production-setup.cjs test
```

#### Issue: Environment Mismatch
```bash
# Force switch to production
node scripts/sslcommerz-environment-manager.cjs production

# Restart development server
npm run dev

# Redeploy functions
cd functions && npm run deploy
```

### Verifying Successful Payments

#### 1. **Check Firebase Firestore**
- Collection: `sslcommerzTransactions`
- Look for transaction with status: `VALID`

#### 2. **Check User Account**
- Collection: `users`
- Verify `role` and `packageName` updated
- Check `subscriptionExpireDate`

#### 3. **Check Payment History**
- Collection: `paymentHistory`
- Verify payment record created

### Safe Environment Switching

#### **Development to Production:**
1. Complete all testing in sandbox
2. Switch to production: `node scripts/sslcommerz-environment-manager.cjs production`
3. Restart development server
4. Deploy functions: `firebase deploy --only functions`
5. Test with small amount first

#### **Production to Development:**
1. Switch to sandbox: `node scripts/sslcommerz-environment-manager.cjs sandbox`
2. Restart development server
3. Continue development safely

## 🚨 Important Security Notes

### Production Safety
- ⚠️ **Real payments will be processed in production mode**
- 💳 **Real money will be charged to customers**
- 🔒 **Never commit production credentials to version control**
- 📊 **Monitor payment transactions regularly**

### Webhook Security
- ✅ Webhook URLs must be HTTPS in production
- ✅ Implement proper validation in webhook handlers
- ✅ Log all webhook events for monitoring

## 📞 Support and Monitoring

### SSLCOMMERZ Merchant Portal
- URL: https://merchant.sslcommerz.com/
- Monitor transactions and settlements
- Download transaction reports

### Firebase Console
- Monitor function execution logs
- Check Firestore for transaction records
- Monitor function performance

## 📋 Pre-Launch Testing Checklist

### Test in Sandbox Mode First
```bash
# Switch to sandbox for final testing
node scripts/sslcommerz-environment-manager.cjs sandbox

# Test complete payment flow
# 1. Select a plan
# 2. Fill billing details
# 3. Choose SSLCOMMERZ payment
# 4. Complete payment with test credentials
# 5. Verify account upgrade
# 6. Check payment history
```

### Production Launch Checklist
- [ ] All sandbox tests passed
- [ ] Webhook URLs accessible from internet
- [ ] Firebase Functions deployed
- [ ] Environment switched to production
- [ ] Small test payment completed successfully
- [ ] User account upgrade verified
- [ ] Payment history recorded correctly
- [ ] Error monitoring in place

## 🎉 You're Ready for Production!

Your SSLCOMMERZ integration is fully configured and ready for live payments. The system supports:

- ✅ Production and sandbox environments
- ✅ Automatic environment switching
- ✅ Complete payment flow with webhooks
- ✅ User account management
- ✅ Payment history tracking
- ✅ Error handling and logging
- ✅ BDT currency conversion
- ✅ Coupon code support

**Next Step**: Deploy to production and start processing real payments! 🚀

## 🔗 Quick Reference Commands

```bash
# Production readiness check
node scripts/sslcommerz-production-setup.cjs check

# Switch to production
node scripts/sslcommerz-environment-manager.cjs production

# Switch to sandbox
node scripts/sslcommerz-environment-manager.cjs sandbox

# Check current status
node scripts/sslcommerz-environment-manager.cjs status

# Fix configuration issues
node scripts/sslcommerz-environment-manager.cjs fix

# Deploy functions
cd functions && firebase deploy --only functions
```
