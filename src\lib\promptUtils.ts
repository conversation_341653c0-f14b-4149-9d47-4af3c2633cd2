import fs from 'fs';
import path from 'path';

// Define the DefaultPrompt interface (same as in gemini.ts)
export interface DefaultPrompt {
  id: string;
  name: string;
  text: string;
}

// Function to load default prompts from the JSON file
export async function loadDefaultPrompts(): Promise<DefaultPrompt[]> {
  try {
    // In browser environment, we use fetch to get the file
    const response = await fetch('/src/default_prompts/default_prompts.json');
    if (!response.ok) {
      throw new Error(`Failed to load default prompts: ${response.statusText}`);
    }
    const data = await response.json();
    return data.defaultPrompts || [];
  } catch (error) {
    console.error('Error loading default prompts:', error);
    return [];
  }
}

// Function to save a new default prompt to the JSON file
// Note: This would typically be handled by a server-side API
// For client-side only apps, this is for demonstration purposes
export async function saveDefaultPrompt(prompt: DefaultPrompt): Promise<boolean> {
  try {
    // In a real application, you would make an API call to a server endpoint
    // that would handle saving the prompt to the JSON file
    console.log('Would save prompt to JSON file:', prompt);
    
    // For now, we'll just return success
    // In a real implementation, this would update the file on the server
    return true;
  } catch (error) {
    console.error('Error saving default prompt:', error);
    return false;
  }
}

// Export the default product prompt for convenience
export const DEFAULT_PRODUCT_PROMPT = `Write a perfect product description according to following Criteria:

>* Detailed Information: Provide comprehensive information about the product, including features, specifications, materials, dimensions, etc.
>* Benefits and Value Proposition: Focus on how the product benefits the customer and solves their problems.
>* Target Audience: Tailor the language and tone to the target audience.
>* Storytelling and Engagement: Use storytelling techniques to engage the reader and create an emotional connection.
>* Hashtags: Incorporate relevant hashtags naturally throughout the description for social media search optimization.
>* Formatting and Readability: Use formatting (bullet points, headings, bold text) to improve readability and scannability.
>* Emotional Appeal: Use emojis and emoticons to add a touch of personality and engagement.
>* Call to Action: Consider including a subtle call to action to encourage purchase.
>* Addressing Customer Concerns: Anticipate and address potential customer questions or concerns.
>* Uniqueness and Originality: Write original descriptions and avoid simply copying manufacturer descriptions.

write in natural-sounding [Language] native accent, blending with [Tone] tone.`;