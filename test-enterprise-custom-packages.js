/**
 * Test script to verify Enterprise-based custom package integration with team management
 */

const { initializeApp } = require('firebase/app');
const { getFirestore, doc, getDoc, collection, query, where, getDocs } = require('firebase/firestore');

// Firebase configuration (replace with your actual config)
const firebaseConfig = {
  // Your Firebase config here
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Test functions
async function testCustomPackageIntegration() {
  console.log('🧪 Testing Enterprise-based Custom Package Integration with Team Management\n');

  try {
    // Test 1: Check if custom packages exist
    console.log('📋 Test 1: Checking custom packages...');
    const customPackagesRef = collection(db, 'customPackages');
    const customPackagesQuery = query(customPackagesRef, where('isActive', '==', true));
    const customPackagesSnapshot = await getDocs(customPackagesQuery);

    if (customPackagesSnapshot.empty) {
      console.log('❌ No active custom packages found');
      return;
    }

    console.log(`✅ Found ${customPackagesSnapshot.size} active custom packages`);

    // Test 2: Check Enterprise-based custom packages
    console.log('\n📋 Test 2: Checking Enterprise-based custom packages...');
    const enterpriseBasedPackages = [];
    customPackagesSnapshot.forEach(doc => {
      const packageData = doc.data();
      if (packageData.type === 'enterprise-based') {
        enterpriseBasedPackages.push({
          id: doc.id,
          name: packageData.name,
          limits: packageData.limits
        });
      }
    });

    if (enterpriseBasedPackages.length === 0) {
      console.log('❌ No Enterprise-based custom packages found');
      return;
    }

    console.log(`✅ Found ${enterpriseBasedPackages.length} Enterprise-based custom packages:`);
    enterpriseBasedPackages.forEach(pkg => {
      console.log(`  - ${pkg.name}: maxTeamMembers=${pkg.limits.maxTeamMembers}`);
    });

    // Test 3: Check users with Enterprise-based custom packages
    console.log('\n📋 Test 3: Checking users with Enterprise-based custom packages...');
    const usersRef = collection(db, 'users');
    
    for (const pkg of enterpriseBasedPackages) {
      const userQuery = query(usersRef, where('role', '==', `Custom:${pkg.name}`));
      const userSnapshot = await getDocs(userQuery);
      
      console.log(`\n  Package: ${pkg.name}`);
      console.log(`  Users assigned: ${userSnapshot.size}`);
      
      if (!userSnapshot.empty) {
        userSnapshot.forEach(userDoc => {
          const userData = userDoc.data();
          console.log(`    - User: ${userData.email || userDoc.id}`);
          console.log(`      Role: ${userData.role}`);
          console.log(`      Is Team Member: ${userData.isTeamMember || false}`);
        });
      }
    }

    // Test 4: Check team management limits
    console.log('\n📋 Test 4: Checking team management limits...');
    const enterpriseAdminLimitsRef = doc(db, 'settings', 'enterpriseAdminOwnerLimits');
    const enterpriseAdminLimitsDoc = await getDoc(enterpriseAdminLimitsRef);

    if (enterpriseAdminLimitsDoc.exists()) {
      const limits = enterpriseAdminLimitsDoc.data();
      console.log('✅ Enterprise Admin Owner Limits found:');
      console.log(`  - Max Team Members: ${limits.maxTeamMembers}`);
      console.log(`  - Max Images: ${limits.maxImages}`);
      console.log(`  - Max Saved Prompts: ${limits.maxSavedPrompts}`);
    } else {
      console.log('❌ Enterprise Admin Owner Limits document not found');
    }

    console.log('\n🎉 Test completed successfully!');
    console.log('\n📝 Summary:');
    console.log('- Enterprise-based custom packages should now work with team management');
    console.log('- Team member limits should display correctly for custom package users');
    console.log('- Team member limits form should load proper default values');
    console.log('- All team management functionality should work for Enterprise-based custom package users');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testCustomPackageIntegration();
