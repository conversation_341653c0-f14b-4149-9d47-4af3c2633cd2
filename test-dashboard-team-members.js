/**
 * Test script to verify Dashboard team member count integration
 */

const { initializeApp } = require('firebase/app');
const { getFirestore, doc, getDoc, collection, query, where, getDocs } = require('firebase/firestore');

// Firebase configuration (replace with your actual config)
const firebaseConfig = {
  // Your Firebase config here
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Test functions
async function testDashboardTeamMemberIntegration() {
  console.log('🧪 Testing Dashboard Team Member Count Integration\n');

  try {
    // Test 1: Check Enterprise-based custom package users
    console.log('📋 Test 1: Finding Enterprise-based custom package users...');
    const usersRef = collection(db, 'users');
    const customPackageQuery = query(usersRef, where('role', '>=', 'Custom:'), where('role', '<', 'Custom:\uf8ff'));
    const customPackageSnapshot = await getDocs(customPackageQuery);

    if (customPackageSnapshot.empty) {
      console.log('❌ No users with custom packages found');
      return;
    }

    console.log(`✅ Found ${customPackageSnapshot.size} users with custom packages`);

    // Test 2: Check team member counts for each Enterprise-based custom package user
    console.log('\n📋 Test 2: Checking team member counts...');
    
    for (const userDoc of customPackageSnapshot.docs) {
      const userData = userDoc.data();
      const userId = userDoc.id;
      const packageName = userData.role?.substring(7); // Remove "Custom:" prefix
      
      console.log(`\n  User: ${userData.email || userId}`);
      console.log(`  Package: ${packageName}`);
      console.log(`  Role: ${userData.role}`);
      console.log(`  Is Team Member: ${userData.isTeamMember || false}`);

      // Check if this is an Enterprise-based custom package
      if (packageName) {
        const customPackagesRef = collection(db, 'customPackages');
        const packageQuery = query(
          customPackagesRef,
          where('name', '==', packageName),
          where('isActive', '==', true)
        );
        const packageSnapshot = await getDocs(packageQuery);

        if (!packageSnapshot.empty) {
          const packageData = packageSnapshot.docs[0].data();
          console.log(`  Package Type: ${packageData.type}`);
          
          if (packageData.type === 'enterprise-based') {
            console.log(`  Max Team Members: ${packageData.limits.maxTeamMembers}`);
            
            // Count actual team members for this user (if they're not a team member themselves)
            if (!userData.isTeamMember) {
              const teamMembersRef = collection(db, 'teamMembers');
              const teamQuery = query(teamMembersRef, where('ownerId', '==', userId));
              const teamSnapshot = await getDocs(teamQuery);
              
              console.log(`  Current Team Members: ${teamSnapshot.size}`);
              console.log(`  Team Member Details:`);
              
              teamSnapshot.forEach(teamDoc => {
                const teamData = teamDoc.data();
                console.log(`    - ${teamData.email} (${teamData.status || 'active'})`);
              });
              
              // Expected Dashboard display
              console.log(`  📊 Expected Dashboard Display: "Team Members: ${teamSnapshot.size} / ${packageData.limits.maxTeamMembers}"`);
            }
          }
        }
      }
    }

    // Test 3: Verify Dashboard logic components
    console.log('\n📋 Test 3: Verifying Dashboard logic components...');
    
    // Check if enterprise admin owner limits exist
    const enterpriseAdminLimitsRef = doc(db, 'settings', 'enterpriseAdminOwnerLimits');
    const enterpriseAdminLimitsDoc = await getDoc(enterpriseAdminLimitsRef);
    
    if (enterpriseAdminLimitsDoc.exists()) {
      const limits = enterpriseAdminLimitsDoc.data();
      console.log('✅ Enterprise Admin Owner Limits found:');
      console.log(`  - Max Team Members: ${limits.maxTeamMembers}`);
    } else {
      console.log('❌ Enterprise Admin Owner Limits document not found');
    }

    console.log('\n🎉 Dashboard Team Member Integration Test completed!');
    console.log('\n📝 Expected Results:');
    console.log('- Dashboard Usage Limits card should show correct team member count');
    console.log('- Count should match actual team members in teamMembers collection');
    console.log('- Max limit should come from Enterprise-based custom package configuration');
    console.log('- Progress bar should reflect current usage vs. limit');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testDashboardTeamMemberIntegration();
