import { useState, useEffect, useRef } from 'react';
import { CreditCard, Save, Search, ChevronDown, ChevronUp, Check, Edit, CheckCircle, AlertCircle } from 'lucide-react';
import { db } from '@/lib/firebase';
import { collection, query, where, getDocs, updateDoc, doc, setDoc } from 'firebase/firestore';
import { useStore } from '@/lib/store';
import { toast } from 'react-hot-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useOnClickOutside } from '@/hooks/use-on-click-outside';
import { countries } from '@/lib/countries';
import { useCountry } from '@/lib/countryContext';
import './billing-details.css';

interface BillingDetailsProps {
  userId: string;
}

interface BillingInfo {
  fullName: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

interface ValidationErrors {
  fullName: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}


export function BillingDetails({ userId }: BillingDetailsProps) {
  const { country: selectedCountry } = useCountry();
  const [billingInfo, setBillingInfo] = useState<BillingInfo>({
    fullName: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: selectedCountry // Initialize with the user's selected country
  });

  // Initialize validation errors
  const [errors, setErrors] = useState<ValidationErrors>({
    fullName: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: ''
  });

  // Form validity state
  const [isFormValid, setIsFormValid] = useState(false);

  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [isCountryDropdownOpen, setIsCountryDropdownOpen] = useState(false);
  const [countrySearch, setCountrySearch] = useState('');
  const [isEditMode, setIsEditMode] = useState(false);
  const [hasBillingInfo, setHasBillingInfo] = useState(false);
  const [touchedFields, setTouchedFields] = useState<Record<string, boolean>>({});
  const countryDropdownRef = useRef<HTMLDivElement>(null);

  // Filter countries based on search input
  const filteredCountries = countries.filter(country =>
    country.toLowerCase().includes(countrySearch.toLowerCase())
  );

  // Close dropdown when clicking outside
  useOnClickOutside(countryDropdownRef, () => setIsCountryDropdownOpen(false));

  useEffect(() => {
    if (userId) {
      fetchBillingDetails();
    }
  }, [userId]);

  // Update the country field when the selected country changes
  useEffect(() => {
    if (!hasBillingInfo) {
      setBillingInfo(prev => ({ ...prev, country: selectedCountry }));
      validateField('country', selectedCountry);
    }
  }, [selectedCountry, hasBillingInfo]);

  // Validate form whenever billing info changes
  useEffect(() => {
    validateForm();
  }, [billingInfo]);

  // Helper function to add shake animation to an element
  const addShakeAnimation = (elementId: string) => {
    const element = document.getElementById(elementId);
    if (element) {
      element.classList.add('animate-shake');
      setTimeout(() => {
        element.classList.remove('animate-shake');
      }, 500);
    }
  };

  // Validate a single field
  const validateField = (name: keyof BillingInfo, value: string) => {
    let error = '';

    switch (name) {
      case 'fullName':
        if (!value.trim()) {
          error = 'Full name is required';
        } else if (value.trim().length < 3) {
          error = 'Full name must be at least 3 characters';
        }
        break;
      case 'address':
        if (!value.trim()) {
          error = 'Address is required';
        } else if (value.trim().length < 5) {
          error = 'Please enter a valid address';
        }
        break;
      case 'city':
        if (!value.trim()) {
          error = 'City is required';
        }
        break;
      case 'zipCode':
        if (!value.trim()) {
          error = 'ZIP/Postal code is required';
        }
        break;
      case 'country':
        if (!value.trim()) {
          error = 'Country is required';
        }
        break;
      default:
        break;
    }

    setErrors(prev => ({
      ...prev,
      [name]: error
    }));

    return error === '';
  };

  // Validate the entire form
  const validateForm = () => {
    const newErrors: ValidationErrors = {
      fullName: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
      country: ''
    };

    let isValid = true;

    // Only validate fields that have been touched or if form is being submitted
    Object.keys(billingInfo).forEach(key => {
      const fieldName = key as keyof BillingInfo;
      if (touchedFields[fieldName] || Object.values(touchedFields).length === 0) {
        const fieldValue = billingInfo[fieldName];
        const fieldIsValid = validateField(fieldName, fieldValue);
        if (!fieldIsValid) {
          isValid = false;
        }
      }
    });

    setIsFormValid(isValid);
    return isValid;
  };

  const fetchBillingDetails = async () => {
    if (!userId) return;

    setLoading(true);
    try {
      // Fetch billing data from Firestore
      const billingRef = collection(db, 'billingDetails');
      const q = query(billingRef, where('userId', '==', userId));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const data = querySnapshot.docs[0].data() as Omit<BillingInfo, 'id'>;
        setBillingInfo({
          fullName: data.fullName || '',
          address: data.address || '',
          city: data.city || '',
          state: data.state || '',
          zipCode: data.zipCode || '',
          country: data.country || ''
        });
        setHasBillingInfo(true);
      } else {
        setHasBillingInfo(false);
      }
    } catch (error) {
      console.error('Error fetching billing details:', error);
      toast.error('Failed to load billing information');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const fieldName = name as keyof BillingInfo;

    // Mark field as touched
    setTouchedFields(prev => ({
      ...prev,
      [fieldName]: true
    }));

    setBillingInfo(prev => ({
      ...prev,
      [fieldName]: value
    }));

    // Validate the field
    validateField(fieldName, value);
  };

  const toggleEditMode = async () => {
    // If we're currently in edit mode and switching to view mode, save the changes
    if (isEditMode) {
      // Mark all fields as touched to trigger validation
      const allFieldsTouched = Object.keys(billingInfo).reduce((acc, key) => {
        acc[key as keyof BillingInfo] = true;
        return acc;
      }, {} as Record<keyof BillingInfo, boolean>);

      setTouchedFields(allFieldsTouched);

      // Validate the entire form
      const isValid = validateForm();

      if (!isValid) {
        // Show error toast for the first error found
        for (const [field, error] of Object.entries(errors)) {
          if (error) {
            toast.error(error);
            // Add shake animation to the field with error
            addShakeAnimation(field);
            return;
          }
        }
        return;
      }

      setUpdating(true);
      try {
        // Find if billing details already exist
        const billingRef = collection(db, 'billingDetails');
        const q = query(billingRef, where('userId', '==', userId));
        const querySnapshot = await getDocs(q);

        const billingData = {
          userId,
          ...billingInfo,
          updatedAt: new Date().toISOString()
        };

        if (querySnapshot.empty) {
          // Create new billing details
          await setDoc(doc(collection(db, 'billingDetails')), {
            ...billingData,
            createdAt: new Date().toISOString()
          });
        } else {
          // Update existing billing details
          const docRef = querySnapshot.docs[0].ref;
          await updateDoc(docRef, billingData);
        }

        toast.success('Billing details saved successfully');
        setHasBillingInfo(true);
        setIsEditMode(false);
      } catch (error) {
        console.error('Error saving billing details:', error);
        toast.error('Failed to save billing details');
        // Don't toggle edit mode if there was an error
        return;
      } finally {
        setUpdating(false);
      }
    } else {
      // Just toggle to edit mode
      setIsEditMode(true);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!userId) {
      toast.error('User not authenticated');
      return;
    }

    // Mark all fields as touched to trigger validation
    const allFieldsTouched = Object.keys(billingInfo).reduce((acc, key) => {
      acc[key as keyof BillingInfo] = true;
      return acc;
    }, {} as Record<keyof BillingInfo, boolean>);

    setTouchedFields(allFieldsTouched);

    // Validate the entire form
    const isValid = validateForm();

    if (!isValid) {
      // Show error toast for the first error found
      for (const [field, error] of Object.entries(errors)) {
        if (error) {
          toast.error(error);
          // Add shake animation to the field with error
          addShakeAnimation(field);
          return;
        }
      }
      return;
    }

    setUpdating(true);

    try {
      // Find if billing details already exist
      const billingRef = collection(db, 'billingDetails');
      const q = query(billingRef, where('userId', '==', userId));
      const querySnapshot = await getDocs(q);

      const billingData = {
        userId,
        ...billingInfo,
        updatedAt: new Date().toISOString()
      };

      if (querySnapshot.empty) {
        // Create new billing details
        await setDoc(doc(collection(db, 'billingDetails')), {
          ...billingData,
          createdAt: new Date().toISOString()
        });
      } else {
        // Update existing billing details
        const docRef = querySnapshot.docs[0].ref;
        await updateDoc(docRef, billingData);
      }

      toast.success('Billing details updated successfully');
      setHasBillingInfo(true);

      // If we're in edit mode, turn it off
      if (isEditMode) {
        setIsEditMode(false);
      }
    } catch (error) {
      console.error('Error updating billing details:', error);
      toast.error('Failed to update billing details');
    } finally {
      setUpdating(false);
    }
  };

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-white flex items-center">
          <CreditCard className="mr-2 h-5 w-5 text-purple-400" />
          Billing Details
        </h2>

        {hasBillingInfo && !loading && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={toggleEditMode}
            disabled={updating}
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            {updating && isEditMode ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : isEditMode ? (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Done Editing
              </>
            ) : (
              <>
                <Edit className="h-4 w-4 mr-2" />
                Edit Details
              </>
            )}
          </Button>
        )}
      </div>

      {loading ? (
        <div className="flex justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      ) : hasBillingInfo && !isEditMode ? (
        // Read-only view of billing details
        <div className="bg-gray-700 rounded-lg p-4 space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-400 mb-1">Full Name</p>
              <p className="text-white font-medium">{billingInfo.fullName}</p>
            </div>

            <div>
              <p className="text-sm text-gray-400 mb-1">Country</p>
              <p className="text-white font-medium">{billingInfo.country}</p>
            </div>
          </div>

          <div>
            <p className="text-sm text-gray-400 mb-1">Address</p>
            <p className="text-white font-medium">{billingInfo.address}</p>
          </div>

          <div className="grid md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-gray-400 mb-1">City</p>
              <p className="text-white font-medium">{billingInfo.city}</p>
            </div>

            <div>
              <p className="text-sm text-gray-400 mb-1">State/Province</p>
              <p className="text-white font-medium">{billingInfo.state || 'N/A'}</p>
            </div>

            <div>
              <p className="text-sm text-gray-400 mb-1">ZIP/Postal Code</p>
              <p className="text-white font-medium">{billingInfo.zipCode}</p>
            </div>
          </div>
        </div>
      ) : (
        // Editable form
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="fullName" className="text-sm text-gray-400 mb-1 block">
                Full Name <span className="text-red-500">*</span>
              </label>
              <Input
                id="fullName"
                name="fullName"
                type="text"
                value={billingInfo.fullName}
                onChange={handleInputChange}
                placeholder="Full Name"
                className={`bg-gray-700 ${errors.fullName && touchedFields.fullName ? 'border-red-500 focus-visible:ring-red-500' : 'border-gray-600'} text-white`}
                aria-invalid={errors.fullName ? 'true' : 'false'}
                aria-describedby={errors.fullName ? 'fullName-error' : undefined}
                required
              />
              {errors.fullName && touchedFields.fullName && (
                <div id="fullName-error" className="text-red-500 text-xs mt-1 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {errors.fullName}
                </div>
              )}
            </div>

            <div>
              <label htmlFor="country" className="text-sm text-gray-400 mb-1 block">
                Country <span className="text-red-500">*</span>
              </label>
              <div className="relative" ref={countryDropdownRef}>
                <div
                  id="country"
                  className={`flex items-center justify-between bg-gray-700 border ${errors.country && touchedFields.country ? 'border-red-500' : 'border-gray-600'} rounded-md p-2 cursor-pointer`}
                  onClick={() => setIsCountryDropdownOpen(!isCountryDropdownOpen)}
                >
                  <span className={`text-sm ${billingInfo.country ? 'text-white' : 'text-gray-400'}`}>
                    {billingInfo.country || 'Select a country'}
                  </span>
                  {isCountryDropdownOpen ? (
                    <ChevronUp className="h-4 w-4 text-gray-400" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  )}
                </div>
                {errors.country && touchedFields.country && (
                  <div id="country-error" className="text-red-500 text-xs mt-1 flex items-center">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    {errors.country}
                  </div>
                )}

                {isCountryDropdownOpen && (
                  <div className="absolute z-10 mt-1 w-full bg-gray-800 border border-gray-700 rounded-md shadow-lg max-h-60 overflow-y-auto">
                    <div className="sticky top-0 bg-gray-800 p-2 border-b border-gray-700">
                      <div className="relative">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                        <Input
                          type="text"
                          placeholder="Search countries..."
                          className="pl-8 bg-gray-700 border-gray-600 text-white"
                          value={countrySearch}
                          onChange={(e) => setCountrySearch(e.target.value)}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </div>
                    </div>

                    <div className="py-1">
                      {filteredCountries.length > 0 ? (
                        filteredCountries.map((country) => (
                          <div
                            key={country}
                            className={`px-4 py-2 text-sm cursor-pointer flex items-center justify-between ${billingInfo.country === country ? 'bg-purple-600 text-white' : 'text-gray-200 hover:bg-gray-700'}`}
                            onClick={() => {
                              setBillingInfo(prev => ({ ...prev, country }));
                              setTouchedFields(prev => ({ ...prev, country: true }));
                              validateField('country', country);
                              setIsCountryDropdownOpen(false);
                              setCountrySearch('');
                            }}
                          >
                            {country}
                            {billingInfo.country === country && (
                              <Check className="h-4 w-4" />
                            )}
                          </div>
                        ))
                      ) : (
                        <div className="px-4 py-2 text-sm text-gray-400">No countries found</div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div>
            <label htmlFor="address" className="text-sm text-gray-400 mb-1 block">
              Address <span className="text-red-500">*</span>
            </label>
            <Input
              id="address"
              name="address"
              type="text"
              value={billingInfo.address}
              onChange={handleInputChange}
              placeholder="Street Address"
              className={`bg-gray-700 ${errors.address && touchedFields.address ? 'border-red-500 focus-visible:ring-red-500' : 'border-gray-600'} text-white`}
              aria-invalid={errors.address ? 'true' : 'false'}
              aria-describedby={errors.address ? 'address-error' : undefined}
              required
            />
            {errors.address && touchedFields.address && (
              <div id="address-error" className="text-red-500 text-xs mt-1 flex items-center">
                <AlertCircle className="h-3 w-3 mr-1" />
                {errors.address}
              </div>
            )}
          </div>

          <div className="grid md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="city" className="text-sm text-gray-400 mb-1 block">
                City <span className="text-red-500">*</span>
              </label>
              <Input
                id="city"
                name="city"
                type="text"
                value={billingInfo.city}
                onChange={handleInputChange}
                placeholder="City"
                className={`bg-gray-700 ${errors.city && touchedFields.city ? 'border-red-500 focus-visible:ring-red-500' : 'border-gray-600'} text-white`}
                aria-invalid={errors.city ? 'true' : 'false'}
                aria-describedby={errors.city ? 'city-error' : undefined}
                required
              />
              {errors.city && touchedFields.city && (
                <div id="city-error" className="text-red-500 text-xs mt-1 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {errors.city}
                </div>
              )}
            </div>

            <div>
              <label htmlFor="state" className="text-sm text-gray-400 mb-1 block">
                State/Province
              </label>
              <Input
                id="state"
                name="state"
                type="text"
                value={billingInfo.state}
                onChange={handleInputChange}
                placeholder="State/Province"
                className={`bg-gray-700 ${errors.state && touchedFields.state ? 'border-red-500 focus-visible:ring-red-500' : 'border-gray-600'} text-white`}
                aria-invalid={errors.state ? 'true' : 'false'}
                aria-describedby={errors.state ? 'state-error' : undefined}
              />
              {errors.state && touchedFields.state && (
                <div id="state-error" className="text-red-500 text-xs mt-1 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {errors.state}
                </div>
              )}
            </div>

            <div>
              <label htmlFor="zipCode" className="text-sm text-gray-400 mb-1 block">
                ZIP/Postal Code <span className="text-red-500">*</span>
              </label>
              <Input
                id="zipCode"
                name="zipCode"
                type="text"
                value={billingInfo.zipCode}
                onChange={handleInputChange}
                placeholder="ZIP/Postal Code"
                className={`bg-gray-700 ${errors.zipCode && touchedFields.zipCode ? 'border-red-500 focus-visible:ring-red-500' : 'border-gray-600'} text-white`}
                aria-invalid={errors.zipCode ? 'true' : 'false'}
                aria-describedby={errors.zipCode ? 'zipCode-error' : undefined}
                required
              />
              {errors.zipCode && touchedFields.zipCode && (
                <div id="zipCode-error" className="text-red-500 text-xs mt-1 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {errors.zipCode}
                </div>
              )}
            </div>
          </div>

          <div className="pt-2">
            <Button
              type="submit"
              disabled={updating || (!isFormValid && Object.values(touchedFields).some(Boolean))}
              className={`bg-gradient-to-r ${(!isFormValid && Object.values(touchedFields).some(Boolean)) ? 'from-gray-500 to-gray-600 cursor-not-allowed' : 'from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700'} text-white`}
              title={(!isFormValid && Object.values(touchedFields).some(Boolean)) ? 'Please fill in all required fields correctly' : ''}
            >
              {updating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Updating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditMode ? 'Save Changes' : 'Save Billing Details'}
                </>
              )}
            </Button>
            {!isFormValid && Object.values(touchedFields).some(Boolean) && (
              <div className="text-red-400 text-xs mt-2">
                <div className="flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1 flex-shrink-0" />
                  <span>Please fill in all required fields correctly before saving</span>
                </div>
              </div>
            )}
          </div>
        </form>
      )}
    </div>
  );
}