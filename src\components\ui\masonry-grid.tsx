import React, { useState, useEffect } from 'react';
import { Loader2, Eye, Languages, MessageSquare, FileText } from '../ui/optimized-icons';

export interface MasonryItem {
  id: string;
  image: string;
  tone?: string;
  language?: string;
  promptName?: string;
  content?: string;
  type?: 'caption' | 'ecommerce';
}

interface MasonryGridProps {
  items: MasonryItem[];
  loading: boolean;
  error?: string | null;
  onItemClick: (item: MasonryItem) => void;
  className?: string;
}

export const MasonryGrid: React.FC<MasonryGridProps> = ({
  items,
  loading,
  error,
  onItemClick,
  className = ''
}) => {
  const [imageLoadStates, setImageLoadStates] = useState<Record<string, boolean>>({});

  const handleImageLoad = (itemId: string) => {
    setImageLoadStates(prev => ({ ...prev, [itemId]: true }));
  };

  const handleImageError = (itemId: string) => {
    setImageLoadStates(prev => ({ ...prev, [itemId]: false }));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-20">
        <div className="text-center">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur-lg opacity-30 animate-pulse"></div>
            <Loader2 className="relative h-8 w-8 animate-spin text-purple-500 mx-auto mb-4" />
          </div>
          <p className="text-gray-400">Loading inspiring content...</p>
          <p className="text-gray-500 text-sm mt-2">Discovering amazing examples from our community</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-20">
        <div className="text-red-400 mb-4">
          <MessageSquare className="h-8 w-8 mx-auto mb-2" />
          <p>Failed to load content</p>
          <p className="text-sm text-gray-500 mt-2">{error}</p>
        </div>
      </div>
    );
  }

  if (!items || items.length === 0) {
    return (
      <div className="text-center py-20">
        <div className="text-gray-400">
          <FileText className="h-8 w-8 mx-auto mb-2" />
          <p>No content available at the moment</p>
          <p className="text-sm text-gray-500 mt-2">Check back later for inspiring examples!</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`columns-2 md:columns-2 lg:columns-4 xl:columns-5 gap-3 md:gap-4 px-2 md:px-4 ${className}`}
      style={{ columnGap: '0.75rem' }}
    >
      
      {items.map((item) => (
        <div
          key={item.id}
          className="break-inside-avoid mb-3 md:mb-4 inline-block w-full"
        >
          <div
            className="bg-gray-800 rounded-xl border border-gray-700 overflow-hidden cursor-pointer
                       transform transition-all duration-300 hover:scale-[1.02] hover:shadow-lg
                       hover:shadow-purple-500/20 hover:border-purple-500/50 group
                       animate-fade-in-up opacity-0"
            style={{
              animationDelay: `${Math.random() * 0.5}s`,
              animationFillMode: 'forwards'
            }}
            onClick={() => onItemClick(item)}
          >
            {/* Image Container */}
            <div className="relative overflow-hidden">
              {!imageLoadStates[item.id] && (
                <div className="absolute inset-0 bg-gray-700 animate-pulse flex items-center justify-center">
                  <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                </div>
              )}
              <img
                src={item.image}
                alt={item.promptName || 'Product image'}
                className={`w-full h-auto object-cover transition-all duration-300 group-hover:scale-105 ${
                  imageLoadStates[item.id] ? 'opacity-100' : 'opacity-0'
                }`}
                onLoad={() => handleImageLoad(item.id)}
                onError={() => handleImageError(item.id)}
                loading="lazy"
                decoding="async"
              />
              
              {/* Overlay on hover */}
              <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                <Eye className="h-5 w-5 md:h-6 md:w-6 text-white" />
              </div>
            </div>
            
            {/* Content */}
            <div className="p-3 md:p-4">
              {/* Prompt Name */}
              {item.promptName && (
                <h3 className="text-white font-semibold text-xs md:text-sm mb-2 line-clamp-2 group-hover:text-purple-300 transition-colors">
                  {item.promptName}
                </h3>
              )}

              {/* Metadata */}
              <div className="space-y-1 md:space-y-2">
                {item.tone && (
                  <div className="flex items-center text-xs text-gray-400">
                    <MessageSquare className="h-3 w-3 mr-1 flex-shrink-0" />
                    <span className="truncate">{item.tone}</span>
                  </div>
                )}

                {item.language && (
                  <div className="flex items-center text-xs text-gray-400">
                    <Languages className="h-3 w-3 mr-1 flex-shrink-0" />
                    <span className="truncate">{item.language}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
