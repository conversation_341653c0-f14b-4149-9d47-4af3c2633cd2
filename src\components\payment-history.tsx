import { useState, useEffect } from 'react';
import { CreditCard, CheckCircle, XCircle, Clock, Calendar, DollarSign, Package, RefreshCw, Eye } from 'lucide-react';
import { db } from '@/lib/firebase';
import { collection, query, where, orderBy, limit, getDocs, Timestamp } from 'firebase/firestore';
import { toast } from 'react-hot-toast';
import { useNavigate } from 'react-router-dom';

interface PaymentLog {
  id: string;
  transactionId: string;
  status: 'SUCCESS' | 'FAILED' | 'CANCELED';
  packageName: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  billingPeriod: string;
  timestamp: Timestamp;
  userEmail?: string;
  userName?: string;
  source?: string; // 'paddle_webhook', 'paddle_checkout', 'sslcommerz', etc.
}

interface PaymentHistoryProps {
  userId: string;
  showAll?: boolean; // New prop to control full display
}

export function PaymentHistory({ userId, showAll = false }: PaymentHistoryProps) {
  const [paymentLogs, setPaymentLogs] = useState<PaymentLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    if (userId) {
      fetchPaymentHistory();
    }
  }, [userId]);

  const fetchPaymentHistory = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('🔍 Fetching payment history for user:', userId);

      // Query payment logs for the current user, ordered by timestamp (newest first)
      const paymentLogsRef = collection(db, 'paymentLogs');

      let querySnapshot;
      try {
        // Determine limit based on showAll prop
        const recordLimit = showAll ? 50 : 5; // Show 5 for preview, 50 for full view

        // Try the optimized query with index
        const q = query(
          paymentLogsRef,
          where('userId', '==', userId),
          orderBy('timestamp', 'desc'),
          limit(recordLimit)
        );
        querySnapshot = await getDocs(q);
      } catch (indexError: any) {
        console.warn('⚠️ Index not ready, falling back to simple query:', indexError.message);

        // Fallback: Simple query without orderBy (while index is building)
        const recordLimit = showAll ? 50 : 5;
        const fallbackQuery = query(
          paymentLogsRef,
          where('userId', '==', userId),
          limit(recordLimit)
        );
        querySnapshot = await getDocs(fallbackQuery);
      }

      if (querySnapshot.empty) {
        console.log('📝 No payment history found for user');
        setPaymentLogs([]);
        return;
      }

      const logs: PaymentLog[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();

        // Validate required fields
        if (!data.transactionId || !data.status) {
          console.warn('⚠️ Skipping invalid payment log:', doc.id);
          return;
        }

        // Filter out CANCELED payments from user-facing display
        if (data.status === 'CANCELED') {
          console.log('🚫 Filtering out canceled payment:', doc.id);
          return;
        }

        logs.push({
          id: doc.id,
          transactionId: data.transactionId,
          status: data.status,
          packageName: data.packageName || 'Unknown Package',
          amount: data.amount || 0,
          currency: data.currency || 'BDT',
          paymentMethod: data.paymentMethod || 'Unknown',
          billingPeriod: data.billingPeriod || 'monthly',
          timestamp: data.timestamp,
          userEmail: data.userEmail,
          userName: data.userName,
          source: data.source || 'unknown'
        });
      });

      // Sort logs by timestamp (newest first) in case fallback query was used
      logs.sort((a, b) => {
        if (!a.timestamp || !b.timestamp) return 0;
        return b.timestamp.toDate().getTime() - a.timestamp.toDate().getTime();
      });

      console.log('✅ Payment history fetched:', logs.length, 'transactions');
      setPaymentLogs(logs);
    } catch (error: any) {
      console.error('❌ Error fetching payment history:', error);

      // Handle specific Firebase errors
      let errorMessage = 'Failed to load payment history';
      if (error.code === 'permission-denied') {
        errorMessage = 'Permission denied. Please check your access rights.';
      } else if (error.code === 'unavailable') {
        errorMessage = 'Service temporarily unavailable. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case 'FAILED':
        return <XCircle className="h-5 w-5 text-red-400" />;
      case 'CANCELED':
        return <Clock className="h-5 w-5 text-yellow-400" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return 'text-green-400';
      case 'FAILED':
        return 'text-red-400';
      case 'CANCELED':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  const formatAmount = (amount: number, currency: string, paymentMethod?: string, source?: string) => {
    // Handle different amount formats based on payment method and source
    let displayAmount = amount;

    if (currency === 'USD' && paymentMethod === 'Paddle') {
      // Paddle webhook data comes in cents, but frontend stored data comes in dollars
      // We need to detect which format we're dealing with

      if (source === 'paddle_webhook') {
        // Webhook data is in cents, convert to dollars
        displayAmount = amount / 100;
      } else if (source === 'paddle_checkout') {
        // Frontend stored data is already in dollars
        displayAmount = amount;
      } else {
        // For backward compatibility, assume large amounts (>100) are in cents
        // This heuristic works because our plans are all under $100
        displayAmount = amount > 100 ? amount / 100 : amount;
      }
    }
    // For BDT and other currencies (SSLCommerz), amounts are already in the correct unit

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency === 'BDT' ? 'BDT' : 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(displayAmount);
  };

  const formatDate = (timestamp: Timestamp) => {
    if (!timestamp || !timestamp.toDate) {
      return 'Unknown date';
    }

    const date = timestamp.toDate();
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
          <CreditCard className="mr-2 h-5 w-5 text-purple-400" />
          Payment History
        </h2>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
          <CreditCard className="mr-2 h-5 w-5 text-purple-400" />
          Payment History
        </h2>
        <div className="text-center py-8">
          <XCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <p className="text-red-400">{error}</p>
          <button
            onClick={fetchPaymentHistory}
            className="mt-4 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-white flex items-center">
          <CreditCard className="mr-2 h-5 w-5 text-purple-400" />
          Payment History
          {!showAll && paymentLogs.length > 0 && (
            <span className="ml-2 text-sm text-gray-400">(Last 5)</span>
          )}
        </h2>
        <div className="flex items-center space-x-2">
          {!showAll && paymentLogs.length > 0 && (
            <button
              onClick={() => navigate('/payment-history')}
              className="flex items-center space-x-2 px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
              title="View all payment history"
            >
              <Eye className="h-4 w-4" />
              <span className="text-sm">View All</span>
            </button>
          )}
          <button
            onClick={fetchPaymentHistory}
            disabled={loading}
            className="flex items-center space-x-2 px-3 py-1.5 bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Refresh payment history"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            <span className="text-sm">Refresh</span>
          </button>
        </div>
      </div>

      {paymentLogs.length === 0 ? (
        <div className="text-center py-8">
          <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-400 mb-2">No payment history found</p>
          <p className="text-sm text-gray-500">
            Your payment transactions will appear here once you make a purchase.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {paymentLogs.map((log) => (
            <div
              key={log.id}
              className="bg-gray-700 rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className="mt-1">
                    {getStatusIcon(log.status)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className={`font-medium ${getStatusColor(log.status)}`}>
                        {log.status}
                      </span>
                      <span className="text-gray-400">•</span>
                      <span className="text-white font-medium">{log.packageName}</span>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-300">
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-4 w-4 text-green-400" />
                        <span>{formatAmount(log.amount, log.currency, log.paymentMethod, log.source)}</span>
                        <span className="text-gray-500">({log.billingPeriod})</span>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-blue-400" />
                        <span>{formatDate(log.timestamp)}</span>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Package className="h-4 w-4 text-purple-400" />
                        <span>{log.paymentMethod}</span>
                      </div>

                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">
                          ID: {log.transactionId.substring(0, 16)}...
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {paymentLogs.length === 10 && (
            <div className="text-center pt-4">
              <p className="text-sm text-gray-400">
                Showing last 10 transactions. Contact support for complete history.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
