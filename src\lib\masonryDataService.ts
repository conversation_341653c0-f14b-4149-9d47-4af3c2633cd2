import { db } from './firebase';
import { collection, getDocs, query, limit, orderBy } from 'firebase/firestore';
import { getUserPackage } from './userLimits';
import { MasonryItem } from '../components/ui/masonry-grid';

export interface FetchMasonryDataOptions {
  maxItems?: number;
  userId?: string;
}

/**
 * Check if user is a free user
 */
export const isFreeUser = async (userId?: string): Promise<boolean> => {
  if (!userId) return true; // Treat non-authenticated users as free users

  try {
    const packageName = await getUserPackage(userId);
    return packageName === 'Free';
  } catch (error) {
    console.error('Error checking user package:', error);
    return true; // Default to free user on error
  }
};

/**
 * Shuffle array using Fisher-Yates algorithm
 */
const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

/**
 * Fetch random saved captions for free users
 */
export const fetchMasonryData = async (options: FetchMasonryDataOptions = {}): Promise<MasonryItem[]> => {
  const { maxItems = 20, userId } = options;

  try {
    // Check if user is free user
    const userIsFree = await isFreeUser(userId);

    if (!userIsFree) {
      console.log('User is not a free user, not showing masonry grid');
      return [];
    }

    // Fetch saved captions from Firebase
    const captionsQuery = query(
      collection(db, 'savedCaptions'),
      orderBy('createdAt', 'desc'),
      limit(100) // Fetch more items to have a good pool for randomization
    );

    const querySnapshot = await getDocs(captionsQuery);
    
    if (querySnapshot.empty) {
      console.log('No saved captions found');
      return [];
    }

    const allItems: MasonryItem[] = [];
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      
      // Only include items that have required fields and valid images
      if (data.image && data.image.trim() !== '') {
        const item: MasonryItem = {
          id: doc.id,
          image: data.image,
          tone: data.tone || undefined,
          language: data.language || undefined,
          promptName: data.promptName || data.prompt || undefined,
          content: data.content || undefined,
          type: data.type || 'caption'
        };
        
        allItems.push(item);
      }
    });

    if (allItems.length === 0) {
      console.log('No valid items found with images');
      return [];
    }

    // Shuffle the items randomly
    const shuffledItems = shuffleArray(allItems);
    
    // Return the requested number of items
    const finalItems = shuffledItems.slice(0, maxItems);

    // If no items found, return sample data for demonstration
    if (finalItems.length === 0) {
      console.log('No Firebase data found, returning sample data');
      return getSampleMasonryData().slice(0, Math.min(maxItems, 3));
    }

    return finalItems;

  } catch (error) {
    console.error('Error fetching masonry data:', error);
    // Return sample data as fallback
    console.log('Error occurred, returning sample data as fallback');
    return getSampleMasonryData().slice(0, Math.min(maxItems, 3));
  }
};

/**
 * Validate image URL
 */
export const isValidImageUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Filter items with valid images
 */
export const filterValidItems = (items: MasonryItem[]): MasonryItem[] => {
  return items.filter(item => 
    item.image && 
    item.image.trim() !== '' && 
    isValidImageUrl(item.image)
  );
};

/**
 * Get sample data for development/testing
 */
export const getSampleMasonryData = (): MasonryItem[] => {
  return [
    {
      id: 'sample-1',
      image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=600&fit=crop',
      tone: 'Professional',
      language: 'English',
      promptName: 'Luxury Watch Description',
      content: '<p>This <strong>elegant timepiece</strong> combines timeless design with modern functionality.</p><p>Features include:</p><ul><li>Swiss movement precision</li><li>Water-resistant to 100m</li><li>Sapphire crystal glass</li><li>Premium leather strap</li></ul><p>Perfect for both <em>casual and formal occasions</em>. A statement piece that speaks to your refined taste.</p>',
      type: 'caption'
    },
    {
      id: 'sample-2',
      image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=500&fit=crop',
      tone: 'Casual',
      language: 'English',
      promptName: 'Athletic Sneaker Marketing',
      content: '<p>🔥 <strong>Step up your style game</strong> with these ultra-comfortable sneakers!</p><p>Why you\'ll love them:</p><ul><li>All-day comfort technology</li><li>Breathable mesh design</li><li>Durable rubber sole</li><li>Available in 8 colors</li></ul><p><em>Perfect for workouts, casual walks, or everyday adventures.</em> Your feet will thank you!</p>',
      type: 'ecommerce'
    },
    {
      id: 'sample-3',
      image: 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=400&h=450&fit=crop',
      tone: 'Friendly',
      language: 'English',
      promptName: 'Premium Sunglasses',
      content: '<p>☀️ <strong>Protect your eyes in style!</strong></p><p>These premium sunglasses offer:</p><ul><li><strong>100% UV protection</strong></li><li>Polarized lenses</li><li>Lightweight titanium frame</li><li>Anti-scratch coating</li></ul><p>Whether you\'re hitting the beach or cruising the city, these shades will keep you looking <em>cool and confident</em>.</p>',
      type: 'caption'
    },
    {
      id: 'sample-4',
      image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=550&fit=crop',
      tone: 'Enthusiastic',
      language: 'English',
      promptName: 'Wireless Headphones',
      content: '<p>🎵 <strong>Experience music like never before!</strong></p><p>Premium features:</p><ul><li>Active noise cancellation</li><li>30-hour battery life</li><li>Crystal-clear audio</li><li>Comfortable over-ear design</li></ul><p>Perfect for <em>music lovers, gamers, and professionals</em>. Immerse yourself in pure sound quality!</p>',
      type: 'ecommerce'
    },
    {
      id: 'sample-5',
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=500&fit=crop',
      tone: 'Professional',
      language: 'English',
      promptName: 'Business Laptop',
      content: '<p><strong>Power meets portability</strong> in this professional-grade laptop.</p><p>Key specifications:</p><ul><li>Intel Core i7 processor</li><li>16GB RAM, 512GB SSD</li><li>14-inch 4K display</li><li>All-day battery life</li></ul><p>Designed for <em>professionals who demand excellence</em>. Your productivity partner for success.</p>',
      type: 'caption'
    },
    {
      id: 'sample-6',
      image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=600&fit=crop',
      tone: 'Trendy',
      language: 'English',
      promptName: 'Fashion Backpack',
      content: '<p>✨ <strong>Style meets functionality</strong> in this must-have backpack!</p><p>What makes it special:</p><ul><li>Water-resistant material</li><li>Multiple compartments</li><li>Laptop sleeve included</li><li>Ergonomic design</li></ul><p>Perfect for <em>students, travelers, and urban explorers</em>. Carry your world in style!</p>',
      type: 'ecommerce'
    }
  ];
};
