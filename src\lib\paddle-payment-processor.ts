// Paddle Payment Processor
// Handles Paddle payment success, failure, and cancellation processing

import { db } from './firebase';
import { doc, getDoc, collection, addDoc, Timestamp } from 'firebase/firestore';
import { toast } from 'react-hot-toast';

export interface PaddlePaymentDetails {
  sessionId: string;
  transactionId?: string;
  plan?: string;
  billingPeriod?: string;
  amount?: number;
  currency?: string;
  status: 'success' | 'failed' | 'canceled';
  userId?: string;
  userEmail?: string;
  timestamp: Timestamp;
}

/**
 * Process successful Paddle payment
 * @param sessionId Paddle checkout session ID
 * @returns Payment details
 */
export const processPaddleSuccessfulPayment = async (sessionId: string): Promise<PaddlePaymentDetails> => {
  try {
    console.log('🎉 Processing successful Paddle payment for session:', sessionId);
    console.log('🔍 Current URL:', window.location.href);
    console.log('🔍 URL search params:', window.location.search);

    // For now, we'll create a basic payment record
    // The webhook will handle the actual user subscription updates
    const paymentDetails: PaddlePaymentDetails = {
      sessionId,
      status: 'success',
      timestamp: Timestamp.now(),
    };

    // Try to get additional details from URL parameters or local storage
    const urlParams = new URLSearchParams(window.location.search);
    const tempId = urlParams.get('temp_id');

    // First try to get data using temp_id, then fallback to session_id
    // Also try to find any stored payment data that might match
    let storedPaymentData = null;

    // Try temp_id first (most reliable)
    if (tempId) {
      storedPaymentData = localStorage.getItem(`paddle_payment_${tempId}`);
      console.log(`🔍 Checking temp_id storage: paddle_payment_${tempId}`, !!storedPaymentData);
    }

    // Try session_id if temp_id didn't work
    if (!storedPaymentData) {
      storedPaymentData = localStorage.getItem(`paddle_payment_${sessionId}`);
      console.log(`🔍 Checking session_id storage: paddle_payment_${sessionId}`, !!storedPaymentData);
    }

    // If still no data, try to find any paddle payment data in localStorage
    if (!storedPaymentData) {
      console.log('🔍 Searching all localStorage for paddle payment data...');
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('paddle_payment_')) {
          console.log(`🔍 Found stored payment data: ${key}`);
          storedPaymentData = localStorage.getItem(key);
          if (storedPaymentData) {
            // Use the first one we find and clean it up
            localStorage.removeItem(key);
            break;
          }
        }
      }
    }

    if (storedPaymentData) {
      try {
        const parsedData = JSON.parse(storedPaymentData);
        paymentDetails.plan = parsedData.plan;
        paymentDetails.billingPeriod = parsedData.billingPeriod;
        paymentDetails.amount = parsedData.amount;
        paymentDetails.currency = parsedData.currency;
        paymentDetails.userId = parsedData.userId;
        paymentDetails.userEmail = parsedData.userEmail;

        // Clean up stored data
        if (tempId) {
          localStorage.removeItem(`paddle_payment_${tempId}`);
        }
        localStorage.removeItem(`paddle_payment_${sessionId}`);
      } catch (error) {
        console.warn('Failed to parse stored payment data:', error);
      }
    }

    // Log the successful payment for record keeping
    try {
      await addDoc(collection(db, 'paymentLogs'), {
        sessionId,
        transactionId: paymentDetails.transactionId || sessionId,
        status: 'SUCCESS',
        packageName: paymentDetails.plan || 'Unknown',
        amount: paymentDetails.amount || 0,
        currency: paymentDetails.currency || 'USD',
        paymentMethod: 'Paddle',
        billingPeriod: paymentDetails.billingPeriod || 'unknown',
        timestamp: paymentDetails.timestamp,
        userId: paymentDetails.userId,
        userEmail: paymentDetails.userEmail,
        source: 'paddle_checkout'
      });
      
      console.log('✅ Payment log created successfully');
    } catch (error) {
      console.error('❌ Failed to create payment log:', error);
      // Don't throw error here as the payment was still successful
    }

    console.log('✅ Paddle payment processed successfully:', paymentDetails);
    return paymentDetails;

  } catch (error) {
    console.error('❌ Error processing successful Paddle payment:', error);
    throw new Error('Failed to process successful payment');
  }
};

/**
 * Process failed Paddle payment
 * @param sessionId Paddle checkout session ID
 * @returns Payment details
 */
export const processPaddleFailedPayment = async (sessionId: string): Promise<PaddlePaymentDetails> => {
  try {
    console.log('❌ Processing failed Paddle payment for session:', sessionId);

    const paymentDetails: PaddlePaymentDetails = {
      sessionId,
      status: 'failed',
      timestamp: Timestamp.now(),
    };

    // Try to get additional details from stored data
    const urlParams = new URLSearchParams(window.location.search);
    const tempId = urlParams.get('temp_id');

    // First try to get data using temp_id, then fallback to session_id
    let storedPaymentData = null;
    if (tempId) {
      storedPaymentData = localStorage.getItem(`paddle_payment_${tempId}`);
    }
    if (!storedPaymentData) {
      storedPaymentData = localStorage.getItem(`paddle_payment_${sessionId}`);
    }

    if (storedPaymentData) {
      try {
        const parsedData = JSON.parse(storedPaymentData);
        paymentDetails.plan = parsedData.plan;
        paymentDetails.billingPeriod = parsedData.billingPeriod;
        paymentDetails.amount = parsedData.amount;
        paymentDetails.currency = parsedData.currency;
        paymentDetails.userId = parsedData.userId;
        paymentDetails.userEmail = parsedData.userEmail;

        // Clean up stored data
        if (tempId) {
          localStorage.removeItem(`paddle_payment_${tempId}`);
        }
        localStorage.removeItem(`paddle_payment_${sessionId}`);
      } catch (error) {
        console.warn('Failed to parse stored payment data:', error);
      }
    }

    // Log the failed payment
    try {
      await addDoc(collection(db, 'paymentLogs'), {
        sessionId,
        transactionId: sessionId,
        status: 'FAILED',
        packageName: paymentDetails.plan || 'Unknown',
        amount: paymentDetails.amount || 0,
        currency: paymentDetails.currency || 'USD',
        paymentMethod: 'Paddle',
        billingPeriod: paymentDetails.billingPeriod || 'unknown',
        timestamp: paymentDetails.timestamp,
        userId: paymentDetails.userId,
        userEmail: paymentDetails.userEmail,
        source: 'paddle_checkout'
      });
      
      console.log('✅ Failed payment log created successfully');
    } catch (error) {
      console.error('❌ Failed to create failed payment log:', error);
    }

    console.log('❌ Paddle payment failed:', paymentDetails);
    return paymentDetails;

  } catch (error) {
    console.error('❌ Error processing failed Paddle payment:', error);
    throw new Error('Failed to process failed payment');
  }
};

/**
 * Process canceled Paddle payment
 * @param sessionId Paddle checkout session ID
 * @returns Payment details
 */
export const processPaddleCanceledPayment = async (sessionId: string): Promise<PaddlePaymentDetails> => {
  try {
    console.log('⚠️ Processing canceled Paddle payment for session:', sessionId);

    const paymentDetails: PaddlePaymentDetails = {
      sessionId,
      status: 'canceled',
      timestamp: Timestamp.now(),
    };

    // Try to get additional details from stored data
    const urlParams = new URLSearchParams(window.location.search);
    const tempId = urlParams.get('temp_id');

    // First try to get data using temp_id, then fallback to session_id
    let storedPaymentData = null;
    if (tempId) {
      storedPaymentData = localStorage.getItem(`paddle_payment_${tempId}`);
    }
    if (!storedPaymentData) {
      storedPaymentData = localStorage.getItem(`paddle_payment_${sessionId}`);
    }

    if (storedPaymentData) {
      try {
        const parsedData = JSON.parse(storedPaymentData);
        paymentDetails.plan = parsedData.plan;
        paymentDetails.billingPeriod = parsedData.billingPeriod;
        paymentDetails.amount = parsedData.amount;
        paymentDetails.currency = parsedData.currency;
        paymentDetails.userId = parsedData.userId;
        paymentDetails.userEmail = parsedData.userEmail;

        // Clean up stored data
        if (tempId) {
          localStorage.removeItem(`paddle_payment_${tempId}`);
        }
        localStorage.removeItem(`paddle_payment_${sessionId}`);
      } catch (error) {
        console.warn('Failed to parse stored payment data:', error);
      }
    }

    // Log the canceled payment
    try {
      await addDoc(collection(db, 'paymentLogs'), {
        sessionId,
        transactionId: sessionId,
        status: 'CANCELED',
        packageName: paymentDetails.plan || 'Unknown',
        amount: paymentDetails.amount || 0,
        currency: paymentDetails.currency || 'USD',
        paymentMethod: 'Paddle',
        billingPeriod: paymentDetails.billingPeriod || 'unknown',
        timestamp: paymentDetails.timestamp,
        userId: paymentDetails.userId,
        userEmail: paymentDetails.userEmail,
        source: 'paddle_checkout'
      });
      
      console.log('✅ Canceled payment log created successfully');
    } catch (error) {
      console.error('❌ Failed to create canceled payment log:', error);
    }

    console.log('⚠️ Paddle payment canceled:', paymentDetails);
    return paymentDetails;

  } catch (error) {
    console.error('❌ Error processing canceled Paddle payment:', error);
    throw new Error('Failed to process canceled payment');
  }
};

/**
 * Store payment data before opening checkout
 * This helps us track payment details even if the webhook fails
 */
export const storePaddlePaymentData = (sessionId: string, paymentData: {
  plan: string;
  billingPeriod: string;
  amount: number;
  currency: string;
  userId: string;
  userEmail: string;
  priceId: string;
}) => {
  try {
    localStorage.setItem(`paddle_payment_${sessionId}`, JSON.stringify({
      ...paymentData,
      timestamp: new Date().toISOString()
    }));
    
    console.log('💾 Stored payment data for session:', sessionId);
  } catch (error) {
    console.warn('Failed to store payment data:', error);
  }
};

/**
 * Get stored payment data
 */
export const getStoredPaddlePaymentData = (sessionId: string) => {
  try {
    const storedData = localStorage.getItem(`paddle_payment_${sessionId}`);
    if (storedData) {
      return JSON.parse(storedData);
    }
  } catch (error) {
    console.warn('Failed to get stored payment data:', error);
  }
  return null;
};
