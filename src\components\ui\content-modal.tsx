import React, { useEffect } from 'react';
import { X, Languages, MessageSquare, FileText, Copy, Check } from 'lucide-react';
import { Button } from './button';
import { MasonryItem } from './masonry-grid';
import { useState } from 'react';
import { toast } from 'react-hot-toast';

interface ContentModalProps {
  item: MasonryItem | null;
  isOpen: boolean;
  onClose: () => void;
}

export const ContentModal: React.FC<ContentModalProps> = ({
  item,
  isOpen,
  onClose
}) => {
  const [copied, setCopied] = useState(false);

  // Close modal on escape key and manage scroll behavior
  useEffect(() => {
    let originalOverflow: string;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      // Store the original overflow value
      originalOverflow = document.body.style.overflow;

      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      // Restore the original overflow value to prevent scroll jump
      if (originalOverflow !== undefined) {
        document.body.style.overflow = originalOverflow;
      } else {
        document.body.style.overflow = '';
      }
    };
  }, [isOpen, onClose]);

  const handleCopyContent = async () => {
    if (!item?.content) return;

    try {
      // Create a temporary div to strip HTML tags for plain text copy
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = item.content;
      const plainText = tempDiv.textContent || tempDiv.innerText || '';

      await navigator.clipboard.writeText(plainText);
      setCopied(true);
      toast.success('Content copied to clipboard!');
      
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy content:', error);
      toast.error('Failed to copy content');
    }
  };

  if (!isOpen || !item) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center p-4"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/80 backdrop-blur-sm transition-opacity duration-300"
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Modal */}
      <div className="relative bg-gray-900 rounded-2xl border border-gray-700 max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl transform transition-all duration-300 scale-100">
        {/* Header */}
        <div className="flex items-center justify-between p-4 md:p-6 border-b border-gray-700">
          <h2
            id="modal-title"
            className="text-lg md:text-xl font-semibold text-white truncate pr-4"
          >
            {item.promptName || 'Content Details'}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-400 hover:text-white hover:bg-gray-800"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
        
        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
          <div className="p-4 md:p-6 space-y-4 md:space-y-6">
            {/* Image */}
            <div className="flex justify-center">
              <div className="relative max-w-md w-full">
                <img
                  src={item.image}
                  alt={item.promptName || 'Product image'}
                  className="w-full h-auto rounded-lg shadow-lg"
                />
              </div>
            </div>
            
            {/* Metadata */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {item.promptName && (
                <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
                  <div className="flex items-center mb-2">
                    <FileText className="h-4 w-4 text-purple-400 mr-2" />
                    <span className="text-sm font-medium text-gray-300">Prompt Name</span>
                  </div>
                  <p className="text-white">{item.promptName}</p>
                </div>
              )}
              
              {item.tone && (
                <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
                  <div className="flex items-center mb-2">
                    <MessageSquare className="h-4 w-4 text-pink-400 mr-2" />
                    <span className="text-sm font-medium text-gray-300">Tone</span>
                  </div>
                  <p className="text-white">{item.tone}</p>
                </div>
              )}
              
              {item.language && (
                <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
                  <div className="flex items-center mb-2">
                    <Languages className="h-4 w-4 text-blue-400 mr-2" />
                    <span className="text-sm font-medium text-gray-300">Language</span>
                  </div>
                  <p className="text-white">{item.language}</p>
                </div>
              )}
            </div>
            
            {/* Generated Description */}
            {item.content && (
              <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 text-green-400 mr-2" />
                    <span className="font-medium text-white">Generated Description</span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopyContent}
                    className="text-gray-300 hover:text-white border-gray-600 hover:border-gray-500"
                  >
                    {copied ? (
                      <>
                        <Check className="h-4 w-4 mr-2" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4 mr-2" />
                        Copy
                      </>
                    )}
                  </Button>
                </div>
                
                {/* Content with HTML formatting preserved */}
                <div 
                  className="text-gray-300 leading-relaxed prose prose-invert max-w-none
                           prose-headings:text-white prose-strong:text-white prose-em:text-gray-300
                           prose-ul:text-gray-300 prose-ol:text-gray-300 prose-li:text-gray-300"
                  dangerouslySetInnerHTML={{ __html: item.content }}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
