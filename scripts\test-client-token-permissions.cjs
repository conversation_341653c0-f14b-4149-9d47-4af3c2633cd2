// Test script to validate client-side token permissions for checkout
// This script simulates the frontend checkout process to identify permission issues

const https = require('https');
require('dotenv').config();

// Test configurations
const CONFIGS = {
  sandbox: {
    clientToken: process.env.VITE_PADDLE_CLIENT_SIDE_TOKEN_SANDBOX,
    apiKey: process.env.VITE_PADDLE_API_KEY_SANDBOX,
    environment: 'sandbox',
    priceId: 'pri_01jxejv3jrz3qpfynwg325zr91' // Pay-As-You-Go sandbox
  },
  production: {
    clientToken: process.env.VITE_PADDLE_CLIENT_SIDE_TOKEN_PRODUCTION || process.env.VITE_PADDLE_CLIENT_SIDE_TOKEN,
    apiKey: process.env.VITE_PADDLE_API_KEY_PRODUCTION || process.env.VITE_PADDLE_API_KEY,
    environment: 'production',
    priceId: 'pri_01jxbve4a77pt0545ntrvaea5t' // Pay-As-You-Go production
  }
};

// Simulate checkout data that would be sent to Paddle
function createCheckoutData(config) {
  return {
    items: [{ priceId: config.priceId, quantity: 1 }],
    customer: { email: '<EMAIL>' },
    customData: {
      userId: 'test-user-123',
      environment: config.environment,
      testMode: config.environment === 'sandbox'
    },
    successUrl: 'http://localhost:5173/payment/success?session_id={checkout.id}'
  };
}

// Test client token format and validity
function validateClientToken(config) {
  console.log(`\n🎫 Validating ${config.environment} client token...`);
  
  if (!config.clientToken) {
    console.log('❌ Client token missing');
    return false;
  }
  
  const isLiveToken = config.clientToken.startsWith('live_');
  const isTestToken = config.clientToken.startsWith('test_');
  
  console.log(`📋 Token: ${config.clientToken.substring(0, 20)}...`);
  console.log(`🔍 Token type: ${isLiveToken ? 'LIVE' : isTestToken ? 'TEST' : 'UNKNOWN'}`);
  
  // Validate token type matches environment
  if (config.environment === 'production' && !isLiveToken) {
    console.log('❌ Production environment requires live token');
    return false;
  }
  
  if (config.environment === 'sandbox' && !isTestToken) {
    console.log('❌ Sandbox environment requires test token');
    return false;
  }
  
  console.log('✅ Token type matches environment');
  return true;
}

// Test API access with the API key (to verify account permissions)
function testAPIAccess(config) {
  return new Promise((resolve) => {
    console.log(`\n🔑 Testing ${config.environment} API access...`);
    
    const apiEndpoint = config.environment === 'sandbox' 
      ? 'https://sandbox-api.paddle.com' 
      : 'https://api.paddle.com';
    
    const url = new URL('/prices/' + config.priceId, apiEndpoint);
    
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('✅ API access successful');
          try {
            const data = JSON.parse(body);
            console.log(`📦 Price: ${data.data.description}`);
            console.log(`💰 Amount: ${data.data.unit_price.amount} ${data.data.unit_price.currency_code}`);
            console.log(`📊 Status: ${data.data.status}`);
            resolve(true);
          } catch (error) {
            console.log('⚠️ API access successful but response parsing failed');
            resolve(true);
          }
        } else {
          console.log(`❌ API access failed: ${res.statusCode}`);
          console.log(`📄 Response: ${body.substring(0, 200)}...`);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ API request failed: ${error.message}`);
      resolve(false);
    });

    req.end();
  });
}

// Test checkout prerequisites
async function testCheckoutPrerequisites(config) {
  console.log(`\n🛒 Testing ${config.environment} checkout prerequisites...`);
  
  const checkoutData = createCheckoutData(config);
  
  console.log('📋 Checkout data structure:');
  console.log(JSON.stringify(checkoutData, null, 2));
  
  // Validate required fields
  const validations = [
    { field: 'items', valid: checkoutData.items && checkoutData.items.length > 0 },
    { field: 'priceId', valid: checkoutData.items[0].priceId && checkoutData.items[0].priceId.startsWith('pri_') },
    { field: 'quantity', valid: checkoutData.items[0].quantity > 0 },
    { field: 'customer.email', valid: checkoutData.customer && checkoutData.customer.email },
    { field: 'successUrl', valid: checkoutData.successUrl && checkoutData.successUrl.startsWith('http') }
  ];
  
  let allValid = true;
  validations.forEach(validation => {
    if (validation.valid) {
      console.log(`✅ ${validation.field}: Valid`);
    } else {
      console.log(`❌ ${validation.field}: Invalid`);
      allValid = false;
    }
  });
  
  return allValid;
}

// Analyze potential permission issues
function analyzePermissionIssues(config) {
  console.log(`\n🔍 Analyzing potential ${config.environment} permission issues...`);
  
  const issues = [];
  
  // Check token format
  if (!config.clientToken) {
    issues.push('Missing client-side token');
  } else if (config.environment === 'sandbox' && !config.clientToken.startsWith('test_')) {
    issues.push('Sandbox environment requires test token (test_...)');
  } else if (config.environment === 'production' && !config.clientToken.startsWith('live_')) {
    issues.push('Production environment requires live token (live_...)');
  }
  
  // Check API key format
  if (!config.apiKey) {
    issues.push('Missing API key');
  } else if (config.environment === 'sandbox' && !config.apiKey.startsWith('pdl_sdbx_')) {
    issues.push('Sandbox environment requires sandbox API key (pdl_sdbx_...)');
  } else if (config.environment === 'production' && !config.apiKey.startsWith('pdl_live_')) {
    issues.push('Production environment requires live API key (pdl_live_...)');
  }
  
  // Check price ID format
  if (!config.priceId || !config.priceId.startsWith('pri_')) {
    issues.push('Invalid price ID format');
  }
  
  if (issues.length === 0) {
    console.log('✅ No obvious permission issues detected');
    console.log('🔍 The 403 error might be due to:');
    console.log('  - Client token lacks checkout permissions');
    console.log('  - Paddle account restrictions');
    console.log('  - Temporary Paddle service issues');
    console.log('  - Frontend SDK configuration problems');
  } else {
    console.log('❌ Potential permission issues found:');
    issues.forEach(issue => console.log(`  • ${issue}`));
  }
  
  return issues.length === 0;
}

// Main test function
async function main() {
  console.log('🔍 Client Token Permissions Test for Paddle Checkout\n');
  console.log('=' * 70);
  
  // Test both environments
  for (const [envName, config] of Object.entries(CONFIGS)) {
    console.log(`\n🌍 TESTING ${envName.toUpperCase()} ENVIRONMENT`);
    console.log('-' * 50);
    
    // 1. Validate client token
    const tokenValid = validateClientToken(config);
    
    // 2. Test API access
    const apiAccessible = await testAPIAccess(config);
    
    // 3. Test checkout prerequisites
    const checkoutReady = await testCheckoutPrerequisites(config);
    
    // 4. Analyze permission issues
    const noPermissionIssues = analyzePermissionIssues(config);
    
    // Summary for this environment
    console.log(`\n📊 ${envName.toUpperCase()} SUMMARY:`);
    console.log(`  Token Valid: ${tokenValid ? '✅' : '❌'}`);
    console.log(`  API Access: ${apiAccessible ? '✅' : '❌'}`);
    console.log(`  Checkout Ready: ${checkoutReady ? '✅' : '❌'}`);
    console.log(`  No Permission Issues: ${noPermissionIssues ? '✅' : '❌'}`);
    
    if (tokenValid && apiAccessible && checkoutReady && noPermissionIssues) {
      console.log(`🎉 ${envName.toUpperCase()} environment appears to be correctly configured`);
    } else {
      console.log(`⚠️ ${envName.toUpperCase()} environment has configuration issues`);
    }
  }
  
  console.log('\n' + '=' * 70);
  console.log('🎯 OVERALL ANALYSIS');
  console.log('\n💡 RECOMMENDATIONS:');
  console.log('1. If sandbox shows ✅ for all tests but checkout still fails with 403:');
  console.log('   - The client token might lack checkout permissions');
  console.log('   - Check Paddle dashboard for token permission settings');
  console.log('   - Try regenerating the sandbox client token');
  
  console.log('\n2. If API access fails:');
  console.log('   - Verify API key is correct and active');
  console.log('   - Check Paddle account status and permissions');
  
  console.log('\n3. If token validation fails:');
  console.log('   - Ensure environment variables are correctly set');
  console.log('   - Verify token prefixes match environment (live_ vs test_)');
  
  console.log('\n🔧 NEXT STEPS:');
  console.log('1. Review the test results above');
  console.log('2. If all tests pass, the issue is likely in the frontend Paddle SDK');
  console.log('3. Check browser console for additional Paddle SDK errors');
  console.log('4. Consider contacting Paddle support if token permissions are unclear');
}

// Run the test
if (require.main === module) {
  main().catch(error => {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  });
}
