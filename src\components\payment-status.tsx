import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { CheckCircle, XCircle, AlertCircle, ArrowRight, Home, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useStore } from '@/lib/store';
import { processSuccessfulPayment, processFailedPayment, processCanceledPayment } from '@/lib/sslcommerz';
import { processPaddleSuccessfulPayment, processPaddleFailedPayment, processPaddleCanceledPayment } from '@/lib/paddle-payment-processor';
import { useCountry } from '@/lib/countryContext';

interface PaymentStatusProps {
  status: 'success' | 'failed' | 'canceled';
}

export function PaymentStatus({ status }: PaymentStatusProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useStore();
  const { formatCurrency } = useCountry();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [transactionDetails, setTransactionDetails] = useState<any>(null);

  // Get payment identifiers from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const transactionId = queryParams.get('transaction_id'); // SSLCOMMERZ
  const sessionId = queryParams.get('session_id'); // Paddle
  const errorMessage = queryParams.get('error');

  // Determine payment method based on available parameters
  const isSSLCommerz = !!transactionId;
  const isPaddle = !!sessionId;
  const paymentId = transactionId || sessionId;

  useEffect(() => {
    const fetchTransactionDetails = async () => {
      if (!paymentId) {
        setError(errorMessage || 'Payment ID not found');
        setLoading(false);
        return;
      }

      try {
        let details;

        // Process the payment based on the payment method and status
        if (isSSLCommerz) {
          console.log('🏦 Processing SSLCOMMERZ payment:', transactionId);
          if (status === 'success') {
            details = await processSuccessfulPayment(transactionId!);
          } else if (status === 'failed') {
            details = await processFailedPayment(transactionId!);
          } else if (status === 'canceled') {
            details = await processCanceledPayment(transactionId!);
          }
        } else if (isPaddle) {
          console.log('🚣 Processing Paddle payment:', sessionId);
          if (status === 'success') {
            details = await processPaddleSuccessfulPayment(sessionId!);
          } else if (status === 'failed') {
            details = await processPaddleFailedPayment(sessionId!);
          } else if (status === 'canceled') {
            details = await processPaddleCanceledPayment(sessionId!);
          }
        } else {
          throw new Error('Unknown payment method');
        }

        setTransactionDetails(details);
      } catch (error) {
        console.error(`Error processing ${status} payment:`, error);
        setError((error as Error).message || 'An error occurred while processing the payment');
      } finally {
        setLoading(false);
      }
    };

    // Always fetch payment details if we have a payment ID
    // The payment processing functions can work without user authentication
    // since they only read from Firestore using the payment ID
    if (paymentId) {
      fetchTransactionDetails();
    } else {
      // If no payment ID but user is not authenticated, show auth error
      if (!user) {
        setLoading(false);
        setError('User not authenticated');
      } else {
        setLoading(false);
        setError('Payment ID not found');
      }
    }
  }, [paymentId, status, user, errorMessage, isSSLCommerz, isPaddle, transactionId, sessionId]);

  // Render loading state
  if (loading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gray-900">
        <div className="bg-gray-800 rounded-xl p-6 max-w-md w-full shadow-lg">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
            <h2 className="text-xl font-bold text-white mb-2">Processing Payment</h2>
            <p className="text-gray-400 text-center">
              Please wait while we process your payment information...
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gray-900">
        <div className="bg-gray-800 rounded-xl p-6 max-w-md w-full shadow-lg">
          <div className="flex flex-col items-center">
            <AlertCircle className="h-16 w-16 text-yellow-500 mb-4" />
            <h2 className="text-xl font-bold text-white mb-2">Payment Processing Error</h2>
            <p className="text-gray-400 text-center mb-6">{error}</p>
            <div className="flex flex-col sm:flex-row gap-3 w-full">
              <Button
                onClick={() => navigate('/dashboard')}
                variant="outline"
                className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                <Home className="h-4 w-4 mr-2" />
                Dashboard
              </Button>
              <Button
                onClick={() => window.location.reload()}
                className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render success state
  if (status === 'success' && transactionDetails) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gray-900">
        <div className="bg-gray-800 rounded-xl p-6 max-w-md w-full shadow-lg">
          <div className="flex flex-col items-center">
            <div className="bg-green-900/30 p-4 rounded-full mb-4">
              <CheckCircle className="h-16 w-16 text-green-500" />
            </div>
            <h2 className="text-xl font-bold text-white mb-2">Payment Successful!</h2>
            <p className="text-gray-400 text-center mb-6">
              Your payment has been processed successfully. Your subscription is now active.
            </p>

            <div className="w-full bg-gray-700 rounded-lg p-4 mb-6">
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">{isPaddle ? 'Session ID:' : 'Transaction ID:'}</span>
                <span className="text-white font-medium">{transactionDetails.sessionId || transactionDetails.transactionId}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">Plan:</span>
                <span className="text-white font-medium">{transactionDetails.plan}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">Billing Period:</span>
                <span className="text-white font-medium">{transactionDetails.billingPeriod}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Amount:</span>
                <span className="text-white font-medium">{formatCurrency(transactionDetails.amount)}</span>
              </div>
            </div>

            <Button
              onClick={() => navigate('/app/dashboard?payment_success=true')}
              className="w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white"
            >
              Go to Dashboard
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Render failed state
  if (status === 'failed' && transactionDetails) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gray-900">
        <div className="bg-gray-800 rounded-xl p-6 max-w-md w-full shadow-lg">
          <div className="flex flex-col items-center">
            <div className="bg-red-900/30 p-4 rounded-full mb-4">
              <XCircle className="h-16 w-16 text-red-500" />
            </div>
            <h2 className="text-xl font-bold text-white mb-2">Payment Failed</h2>
            <p className="text-gray-400 text-center mb-6">
              Your payment could not be processed. Please try again or contact support.
            </p>

            <div className="w-full bg-gray-700 rounded-lg p-4 mb-6">
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">{isPaddle ? 'Session ID:' : 'Transaction ID:'}</span>
                <span className="text-white font-medium">{transactionDetails.sessionId || transactionDetails.transactionId}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">Plan:</span>
                <span className="text-white font-medium">{transactionDetails.plan}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">Billing Period:</span>
                <span className="text-white font-medium">{transactionDetails.billingPeriod}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Amount:</span>
                <span className="text-white font-medium">{formatCurrency(transactionDetails.amount)}</span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 w-full">
              <Button
                onClick={() => navigate('/app/dashboard')}
                variant="outline"
                className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                <Home className="h-4 w-4 mr-2" />
                Dashboard
              </Button>
              <Button
                onClick={() => navigate('/app/dashboard?openUpgrade=true')}
                className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
              >
                Try Again
                <RefreshCw className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render canceled state
  if (status === 'canceled' && transactionDetails) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gray-900">
        <div className="bg-gray-800 rounded-xl p-6 max-w-md w-full shadow-lg">
          <div className="flex flex-col items-center">
            <div className="bg-yellow-900/30 p-4 rounded-full mb-4">
              <AlertCircle className="h-16 w-16 text-yellow-500" />
            </div>
            <h2 className="text-xl font-bold text-white mb-2">Payment Canceled</h2>
            <p className="text-gray-400 text-center mb-6">
              Your payment was canceled. No charges have been made.
            </p>

            <div className="w-full bg-gray-700 rounded-lg p-4 mb-6">
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">{isPaddle ? 'Session ID:' : 'Transaction ID:'}</span>
                <span className="text-white font-medium">{transactionDetails.sessionId || transactionDetails.transactionId}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">Plan:</span>
                <span className="text-white font-medium">{transactionDetails.plan}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-400">Billing Period:</span>
                <span className="text-white font-medium">{transactionDetails.billingPeriod}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Amount:</span>
                <span className="text-white font-medium">{formatCurrency(transactionDetails.amount)}</span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 w-full">
              <Button
                onClick={() => navigate('/app/dashboard')}
                variant="outline"
                className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                <Home className="h-4 w-4 mr-2" />
                Dashboard
              </Button>
              <Button
                onClick={() => navigate('/app/dashboard?openUpgrade=true')}
                className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
              >
                Try Again
                <RefreshCw className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Fallback for unexpected state
  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gray-900">
      <div className="bg-gray-800 rounded-xl p-6 max-w-md w-full shadow-lg">
        <div className="flex flex-col items-center">
          <AlertCircle className="h-16 w-16 text-yellow-500 mb-4" />
          <h2 className="text-xl font-bold text-white mb-2">Unknown Payment Status</h2>
          <p className="text-gray-400 text-center mb-6">
            We couldn't determine the status of your payment. Please contact support.
          </p>
          <Button
            onClick={() => navigate('/app/dashboard')}
            className="w-full bg-purple-600 hover:bg-purple-700 text-white"
          >
            Go to Dashboard
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
}
